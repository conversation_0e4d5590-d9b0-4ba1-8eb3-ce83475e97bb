#!/usr/bin/env python3
"""
Setup script for multiple Groq API keys.
This script helps you configure multiple API keys for automatic rotation.
"""

import os
import sys

def setup_api_keys():
    """Setup multiple Groq API keys for rotation"""
    print("🔑 Groq API Keys Setup")
    print("=" * 50)
    print("This script will help you set up multiple Groq API keys for automatic rotation.")
    print("When one API key hits its rate limit, the system will automatically switch to the next one.")
    print()
    
    # Check if .env file exists
    env_file = ".env"
    env_exists = os.path.exists(env_file)
    
    if env_exists:
        print(f"📄 Found existing {env_file} file")
        response = input("Do you want to update it? (y/n): ").lower().strip()
        if response != 'y':
            print("❌ Setup cancelled")
            return
    else:
        print(f"📄 Creating new {env_file} file")
    
    # Collect API keys
    api_keys = []
    print("\n🔑 Enter your Groq API keys (press Enter with empty input to finish):")
    print("You can get API keys from: https://console.groq.com/keys")
    print()
    
    for i in range(1, 6):  # Support up to 5 API keys
        while True:
            key = input(f"API Key #{i}: ").strip()
            if not key:
                if i == 1:
                    print("❌ You need at least one API key!")
                    continue
                else:
                    break
            
            # Basic validation
            if not key.startswith("gsk_"):
                print("⚠️  Warning: Groq API keys usually start with 'gsk_'")
                confirm = input("Continue anyway? (y/n): ").lower().strip()
                if confirm != 'y':
                    continue
            
            api_keys.append(key)
            print(f"✅ Added API key #{i}")
            break
        
        if not key:  # Empty input, stop collecting
            break
    
    if not api_keys:
        print("❌ No API keys provided. Setup cancelled.")
        return
    
    # Write to .env file
    try:
        with open(env_file, 'w') as f:
            f.write("# Groq API Keys for automatic rotation\n")
            f.write("# The system will automatically switch between these keys when rate limits are hit\n\n")
            
            for i, key in enumerate(api_keys, 1):
                if i == 1:
                    f.write(f"GROQ_API_KEY={key}\n")
                else:
                    f.write(f"GROQ_API_KEY_{i}={key}\n")
        
        print(f"\n✅ Successfully saved {len(api_keys)} API key(s) to {env_file}")
        print("\n📋 Summary:")
        print(f"   • {len(api_keys)} API key(s) configured")
        print(f"   • Automatic rotation enabled")
        print(f"   • English-only responses enforced")
        
        print("\n🚀 Next steps:")
        print("1. Make sure to accept terms for PlayAI TTS in Groq Console:")
        print("   https://console.groq.com/playground?model=playai-tts")
        print("2. Run the voice assistant:")
        print("   python src/fastrtc_groq_voice_stream.py")
        
    except Exception as e:
        print(f"❌ Error writing to {env_file}: {e}")
        return

def main():
    """Main function"""
    try:
        setup_api_keys()
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
