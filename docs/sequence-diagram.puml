@startuml Samantha Voice Assistant Flow
!theme plain
skinparam backgroundColor #0f0f23
skinparam defaultFontColor #ffffff
skinparam sequenceArrowColor #7c3aed
skinparam sequenceLifeLineBackgroundColor #1a1a2e
skinparam sequenceLifeLineBorderColor #7c3aed
skinparam sequenceParticipantBackgroundColor #1a1a2e
skinparam sequenceParticipantBorderColor #7c3aed
skinparam noteBackgroundColor #2d2d44
skinparam noteBorderColor #7c3aed

title Samantha Voice Assistant - Complete System Flow

actor User as U
participant "Browser\n(Frontend)" as B
participant "Flask Server\n(Backend)" as F
participant "Groq API\n(Transcription)" as GT
participant "Groq API\n(LLM)" as GL
participant "LangGraph\n(Agent)" as LG
participant "ElevenLabs\n(TTS)" as EL

== Initialization ==
U -> B: Open browser to localhost:5001
B -> F: HTTP GET /
F -> B: Return modern_index.html + CSS + JS
B -> F: WebSocket connection (Socket.IO)
F -> B: Connection established
note over B: Request microphone permissions
B -> U: Microphone permission dialog
U -> B: Grant permissions

== Start Conversation ==
U -> B: Click "Start Conversation"
B -> F: emit('start_conversation')
F -> F: Set conversation_active = True
F -> B: emit('conversation_started', {success: true})
B -> B: Update UI to listening state
B -> B: Start MediaRecorder with optimized settings

== Audio Recording Loop ==
loop Continuous Conversation
    note over B: Record audio (max 5 seconds)
    B -> B: MediaRecorder captures audio\n(16kHz, mono, noise suppression)
    B -> B: Convert to base64 (chunked processing)
    B -> F: emit('process_audio', {audio: base64_data})
    
    == Server-Side Processing ==
    F -> F: Decode base64 audio
    F -> F: Create BytesIO stream
    note over F: Direct file processing\n(bypasses numpy conversion)
    
    == Transcription ==
    F -> GT: audio.transcriptions.create()\nfile=BytesIO, model="whisper-large-v3-turbo"
    alt API Success
        GT -> F: Return transcript text
    else API Failure (Rate Limit)
        GT -> F: Error response
        F -> F: Rotate to backup API key
        F -> GT: Retry with new key
        GT -> F: Return transcript text
    end
    
    == AI Response Generation ==
    F -> LG: agent.invoke({messages: [transcript]})
    LG -> GL: Chat completion request\nmodel="llama-3.1-70b-versatile"
    alt API Success
        GL -> LG: Return AI response
        LG -> F: Return structured response
    else API Failure
        GL -> LG: Error response
        LG -> F: Rotate API key and retry
        F -> LG: Retry request
        LG -> GL: Retry with new key
        GL -> LG: Return AI response
        LG -> F: Return structured response
    end
    
    == Text-to-Speech ==
    F -> EL: text_to_speech.convert()\nvoice_id="21m00Tcm4TlvDq8ikWAM" (Rachel)
    alt TTS Success
        EL -> F: Return audio stream
        F -> F: Convert to base64
    else TTS Failure
        EL -> F: Error response
        F -> F: Log error, continue without audio
    end
    
    == Response Delivery ==
    F -> B: emit('response', {\n  text: response_text,\n  audio: base64_audio,\n  transcript: original_transcript\n})
    
    == Frontend Response Handling ==
    B -> B: Add user message to chat
    B -> B: Add assistant message to chat
    B -> B: Create audio blob from base64
    B -> B: Play audio through HTML5 audio element
    B -> U: Display conversation + play Rachel's voice
    
    == Automatic Continuation ==
    B -> B: Audio 'ended' event listener
    B -> B: Wait 1 second delay
    B -> B: Auto-start MediaRecorder again
    note over B: Seamless conversation continues\nwithout user clicking
end

== Stop Conversation ==
U -> B: Click "Stop Conversation" (optional)
B -> F: emit('stop_conversation')
F -> F: Set conversation_active = False
F -> B: emit('conversation_stopped', {success: true})
B -> B: Stop MediaRecorder
B -> B: Update UI to ready state

== Error Handling ==
alt Audio Processing Error
    F -> B: emit('error', {message: error_details})
    B -> B: Display error message
    B -> B: Continue conversation flow
else Network Error
    B -> B: Auto-retry connection
    B -> F: Reconnect WebSocket
else API Rate Limit
    F -> F: Automatic API key rotation
    F -> F: Continue processing
end

note over U, EL
**Key Features:**
• Seamless conversation flow with automatic continuation
• Real-time WebSocket communication
• Multiple API key rotation for reliability
• Direct audio processing (format agnostic)
• Comprehensive error handling and recovery
• Modern Flask UI with responsive design
end note

@enduml
