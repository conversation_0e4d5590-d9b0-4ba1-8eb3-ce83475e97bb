#!/usr/bin/env python3
"""
Modern Flask Voice Assistant Web Application
Uses ElevenLabs TTS for high-quality voice synthesis
"""

import os
import io
import base64
import numpy as np
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
from elevenlabs import ElevenLabs, VoiceSettings
from groq import Groq
import logging
from conversation_assistant import agent, agent_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize clients
elevenlabs_client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))
groq_client = Groq(api_key=os.getenv("GROQ_API_KEY"))

# Available ElevenLabs voices
ELEVENLABS_VOICES = [
    {"id": "<PERSON>", "name": "<PERSON> (Female, American)"},
    {"id": "<PERSON>", "name": "<PERSON> (Male, American)"},
    {"id": "<PERSON>", "name": "<PERSON> (Male, American)"},
    {"id": "<PERSON>", "name": "<PERSON> (Male, American)"},
    {"id": "Domi", "name": "Domi (Female, American)"},
    {"id": "<PERSON>", "name": "<PERSON> (<PERSON>, British)"},
    {"id": "<PERSON>", "name": "<PERSON> (<PERSON>, Irish)"},
    {"id": "<PERSON>", "name": "<PERSON> (Female, American)"},
    {"id": "Antoni", "name": "Antoni (Male, American)"},
    {"id": "Thomas", "name": "Thomas (Male, American)"},
    {"id": "Charlie", "name": "Charlie (Male, Australian)"},
    {"id": "Emily", "name": "Emily (Female, American)"},
    {"id": "Elli", "name": "Elli (Female, American)"},
    {"id": "Callum", "name": "Callum (Male, American)"},
    {"id": "Patrick", "name": "Patrick (Male, American)"},
    {"id": "Harry", "name": "Harry (Male, American)"},
    {"id": "Liam", "name": "Liam (Male, American)"},
    {"id": "Dorothy", "name": "Dorothy (Female, British)"},
    {"id": "Josh", "name": "Josh (Male, American)"},
    {"id": "Arnold", "name": "Arnold (Male, American)"},
    {"id": "Charlotte", "name": "Charlotte (Female, English)"},
    {"id": "Alice", "name": "Alice (Female, British)"},
    {"id": "Matilda", "name": "Matilda (Female, American)"},
    {"id": "James", "name": "James (Male, Australian)"}
]

# Default settings
current_voice = "Rachel"

@app.route('/')
def index():
    """Render the main page"""
    return render_template('index.html', voices=ELEVENLABS_VOICES)

@app.route('/api/voices')
def get_voices():
    """Get available voices"""
    return jsonify(ELEVENLABS_VOICES)

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected")
    emit('status', {'message': 'Connected to Samantha Voice Assistant'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected")

@socketio.on('change_voice')
def handle_voice_change(data):
    """Handle voice change request"""
    global current_voice
    voice_id = data.get('voice_id')
    if voice_id:
        current_voice = voice_id
        logger.info(f"Voice changed to: {current_voice}")
        emit('status', {'message': f'Voice changed to {current_voice}'})

@socketio.on('process_audio')
def handle_audio(data):
    """Process audio input and generate response"""
    try:
        # Get audio data from client
        audio_data = data.get('audio')
        if not audio_data:
            emit('error', {'message': 'No audio data received'})
            return

        logger.info("Processing audio input...")
        emit('status', {'message': 'Processing your message...'})

        # Decode base64 audio data
        audio_bytes = base64.b64decode(audio_data)

        # Create a file-like object for Groq API
        audio_file = io.BytesIO(audio_bytes)
        audio_file.name = "audio.wav"  # Set filename for the API

        # Transcribe audio using Groq Whisper
        logger.info(f"Audio file size: {len(audio_bytes)} bytes")
        transcript = groq_client.audio.transcriptions.create(
            file=audio_file,
            model="whisper-large-v3-turbo",
            response_format="text",
        )

        logger.info(f"Transcribed: '{transcript}'")

        # Generate response using the conversation agent
        logger.info("Generating response...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]},
            config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f"Response: {response_text}")

        # Generate speech using ElevenLabs
        logger.info(f"Generating speech with voice: {current_voice}")
        emit('status', {'message': 'Generating speech...'})

        audio_generator = elevenlabs_client.text_to_speech.convert(
            voice_id=current_voice,
            text=response_text,
            voice_settings=VoiceSettings(
                stability=0.5,
                similarity_boost=0.75,
                style=0.0,
                use_speaker_boost=True
            )
        )

        # Convert audio to base64 for transmission
        audio_bytes = b"".join(audio_generator)
        audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')

        # Send response back to client
        emit('response', {
            'text': response_text,
            'audio': audio_base64,
            'voice': current_voice
        })

        emit('status', {'message': 'Response ready!'})
        logger.info("Response sent successfully")

    except Exception as e:
        import traceback
        logger.error(f"Error processing audio: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        emit('error', {'message': f'Error: {str(e)}'})

if __name__ == '__main__':
    logger.info("Starting Samantha Voice Assistant...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
