<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Samantha - Voice Assistant</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-microphone-alt"></i>
                <h1>Samantha</h1>
            </div>
            <p class="subtitle">Your AI Voice Assistant</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Voice Info -->
            <div class="voice-section">
                <h3><i class="fas fa-user-circle"></i> Voice: Rachel</h3>
                <p>Samantha speaks with Rachel's warm and natural voice</p>
            </div>

            <!-- Chat Interface -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p>Hello! I'm Samantha, your AI voice assistant. Click the microphone button and start talking to me!</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Controls -->
            <div class="controls">
                <button id="recordBtn" class="record-btn">
                    <i class="fas fa-microphone"></i>
                    <span>Start Conversation</span>
                </button>
                <button id="testAudioBtn" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                    🔊 Test Audio Playback
                </button>
                <div class="status" id="status">Ready to chat!</div>
            </div>

            <!-- Audio Visualizer -->
            <div class="audio-visualizer" id="audioVisualizer">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>Powered by <strong>ElevenLabs TTS</strong> & <strong>AI</strong> | Always responds in English</p>
        </footer>
    </div>

    <!-- Audio element for playback -->
    <audio id="audioPlayer" style="display: none;"></audio>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
