<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Multilingual Voice Assistant</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-globe-americas"></i>
                <h1>Samantha</h1>
            </div>
            <p class="subtitle">Multilingual AI Voice Assistant with FastRTC VAD</p>
            <div class="features">
                <span class="feature"><i class="fas fa-language"></i> Multilingual</span>
                <span class="feature"><i class="fas fa-microphone-alt"></i> FastRTC VAD</span>
                <span class="feature"><i class="fas fa-volume-up"></i> ElevenLabs TTS</span>
                <span class="feature"><i class="fas fa-brain"></i> Groq AI</span>
            </div>
        </header>

        <!-- Language Support Section -->
        <section class="language-section">
            <h3><i class="fas fa-globe"></i> Supported Languages</h3>
            <div class="language-grid">
                <div class="language-card" data-lang="en">
                    <div class="flag">🇺🇸</div>
                    <span>English</span>
                </div>
                <div class="language-card" data-lang="es">
                    <div class="flag">🇪🇸</div>
                    <span>Spanish</span>
                </div>
                <div class="language-card" data-lang="fr">
                    <div class="flag">🇫🇷</div>
                    <span>French</span>
                </div>
                <div class="language-card" data-lang="de">
                    <div class="flag">🇩🇪</div>
                    <span>German</span>
                </div>
                <div class="language-card" data-lang="hi">
                    <div class="flag">🇮🇳</div>
                    <span>Hindi</span>
                </div>
                <div class="language-card" data-lang="zh">
                    <div class="flag">🇨🇳</div>
                    <span>Chinese</span>
                </div>
            </div>
            <p class="language-note">Speak in any language - I'll automatically detect and respond in the same language!</p>
        </section>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Chat Interface -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="speaker">Samantha</span>
                                <span class="language-badge">🇺🇸 English</span>
                            </div>
                            <p>Hello! I'm your enhanced multilingual voice assistant. I can understand and respond in English, Spanish, French, German, Hindi, and Chinese. Click "Start Conversation" and speak in any supported language!</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Controls -->
            <div class="controls">
                <div class="button-group">
                    <button id="normalConvoBtn" class="conversation-btn normal-btn">
                        <i class="fas fa-comments"></i>
                        <span>Normal Conversation</span>
                    </button>
                    <button id="fraudDemoBtn" class="conversation-btn fraud-btn">
                        <i class="fas fa-shield-alt"></i>
                        <span>Fraud Detection Demo</span>
                    </button>
                </div>
                <button id="stopBtn" class="stop-btn" style="display: none;">
                    <i class="fas fa-stop"></i>
                    <span>Stop Conversation</span>
                </button>
                <div class="status" id="status">Choose conversation mode to begin!</div>
                <div class="mode-indicator" id="modeIndicator" style="display: none;">
                    <span id="currentMode"></span>
                </div>
            </div>

            <!-- Audio Visualizer -->
            <div class="audio-visualizer" id="audioVisualizer">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>

            <!-- Language Detection Display -->
            <div class="language-detection" id="languageDetection">
                <div class="detection-content">
                    <i class="fas fa-search"></i>
                    <span id="detectedLanguage">Language will be detected automatically</span>
                </div>
            </div>

            <!-- FastRTC Info -->
            <div class="fastrtc-info">
                <h4><i class="fas fa-magic"></i> Enhanced Features</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-microphone-alt"></i>
                        <h5>FastRTC VAD</h5>
                        <p>Reliable voice activity detection with 0.8s silence threshold</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-language"></i>
                        <h5>Auto Language Detection</h5>
                        <p>Automatically detects your language and responds accordingly</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-volume-up"></i>
                        <h5>Multilingual TTS</h5>
                        <p>Natural voice synthesis in multiple languages</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-sync-alt"></i>
                        <h5>Seamless Flow</h5>
                        <p>Continuous conversation without button clicking</p>
                    </div>
                </div>
            </div>

            <!-- Demo Instructions -->
            <div class="demo-instructions">
                <h4><i class="fas fa-play-circle"></i> How to Use</h4>
                <div class="instruction-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h5>Start Conversation</h5>
                            <p>Click the microphone button to begin</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h5>Speak Naturally</h5>
                            <p>Talk in any supported language</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h5>Auto Detection</h5>
                            <p>Language is detected automatically</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h5>Multilingual Response</h5>
                            <p>Get responses in your language</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>Enhanced with <strong>FastRTC VAD</strong> + <strong>Multilingual AI</strong> + <strong>ElevenLabs TTS</strong></p>
            <div class="tech-badges">
                <span class="tech-badge">FastRTC</span>
                <span class="tech-badge">Groq AI</span>
                <span class="tech-badge">ElevenLabs</span>
                <span class="tech-badge">Flask</span>
            </div>
        </footer>
    </div>

    <!-- Audio element for playback -->
    <audio id="audioPlayer" style="display: none;"></audio>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/enhanced_app.js') }}"></script>
</body>
</html>
