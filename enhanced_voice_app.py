#!/usr/bin/env python3
"""
Enhanced Voice Assistant with FastRTC VAD + Multilingual Support
Combines FastRTC's reliable VAD with Flask's custom UI and multilingual capabilities
"""

import os
import io
import base64
import asyncio
import threading
import numpy as np
from typing import Generator, Tu<PERSON>, Dict, Any
from pathlib import Path

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
from elevenlabs import ElevenLabs, VoiceSettings
from groq import Groq
from fastrtc import AlgoOptions, ReplyOnPause, Stream, audio_to_bytes
import logging

from conversation_assistant import agent, agent_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced-voice-assistant-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize clients
elevenlabs_client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))

# Multiple Groq API keys for rotation
GROQ_API_KEYS = [
    os.getenv("GROQ_API_KEY"),
    os.getenv("GROQ_API_KEY_2"),
]

# Filter out None values
GROQ_API_KEYS = [key for key in GROQ_API_KEYS if key is not None]

if not GROQ_API_KEYS:
    raise ValueError("At least one GROQ_API_KEY must be set in environment variables")

logger.info(f"🔑 Loaded {len(GROQ_API_KEYS)} API key(s) for rotation")

# Current API key index and client
current_api_index = 0
groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])

def rotate_api_key():
    """Rotate to the next available API key"""
    global current_api_index, groq_client
    current_api_index = (current_api_index + 1) % len(GROQ_API_KEYS)
    groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])
    logger.info(f"🔄 Rotated to API key #{current_api_index + 1}")

def try_groq_request(func, *args, **kwargs):
    """Try a Groq request with all available API keys until one succeeds"""
    global current_api_index, groq_client

    for attempt in range(len(GROQ_API_KEYS)):
        try:
            result = func(*args, **kwargs)
            if attempt > 0:
                logger.info(f"✅ Successfully used API key #{current_api_index + 1}")
            return result
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"❌ API key #{current_api_index + 1} failed: {error_msg}")

            if "rate_limit_exceeded" in error_msg or "429" in error_msg:
                logger.info(f"⏰ Rate limit hit on API key #{current_api_index + 1}, rotating...")
                rotate_api_key()
            else:
                logger.info(f"🔄 Error on API key #{current_api_index + 1}, trying next...")
                rotate_api_key()

    raise Exception(f"💥 All {len(GROQ_API_KEYS)} API keys failed.")

class MultilingualProcessor:
    """Handles multilingual speech processing and TTS"""

    def __init__(self):
        self.supported_languages = {
            'en': {'name': 'English', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},  # Rachel
            'es': {'name': 'Spanish', 'voice_id': 'VR6AewLTigWG4xSOukaG'},  # Spanish voice
            'fr': {'name': 'French', 'voice_id': 'ThT5KcBeYPX3keUQqHPh'},   # French voice
            'de': {'name': 'German', 'voice_id': 'TxGEqnHWrfWFTfGW9XjX'},   # German voice
            'hi': {'name': 'Hindi', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},    # Fallback to Rachel
            'zh': {'name': 'Chinese', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},  # Fallback to Rachel
        }
        logger.info(f"🌍 Multilingual processor initialized with {len(self.supported_languages)} languages")

    def detect_language(self, audio_file):
        """Detect language from audio and transcribe"""
        try:
            # Use Groq with language detection
            transcript_response = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",
                    response_format="verbose_json"  # Includes language detection
                )
            )

            detected_lang = transcript_response.language
            transcript_text = transcript_response.text

            logger.info(f"🔍 Detected language: {detected_lang} - Text: '{transcript_text[:50]}...'")

            return detected_lang, transcript_text

        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            # Fallback to English
            transcript = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",
                    response_format="text"
                )
            )
            return 'en', transcript

    def generate_multilingual_response(self, transcript, detected_lang):
        """Generate AI response in the detected language"""
        try:
            # Create language-specific prompt
            if detected_lang != 'en':
                language_name = self.supported_languages.get(detected_lang, {}).get('name', detected_lang)
                enhanced_prompt = f"The user spoke in {language_name}. Please respond in {language_name}. User said: {transcript}"
            else:
                enhanced_prompt = transcript

            # Generate response using LangGraph
            agent_response = agent.invoke(
                {"messages": [{"role": "user", "content": enhanced_prompt}]},
                config=agent_config
            )
            response_text = agent_response["messages"][-1].content

            logger.info(f"💬 Generated response in {detected_lang}: '{response_text[:50]}...'")
            return response_text

        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return "I apologize, but I'm having trouble processing your request right now."

    def generate_multilingual_speech(self, text, language):
        """Generate speech in the specified language"""
        try:
            # Get voice for language (fallback to English if not supported)
            voice_config = self.supported_languages.get(language, self.supported_languages['en'])
            voice_id = voice_config['voice_id']

            logger.info(f"🔊 Generating speech in {language} with voice {voice_id}")

            # Use multilingual model for non-English languages
            model_id = "eleven_multilingual_v2" if language != 'en' else "eleven_monolingual_v1"

            audio_generator = elevenlabs_client.text_to_speech.convert(
                voice_id=voice_id,
                text=text,
                voice_settings=VoiceSettings(
                    stability=0.5,
                    similarity_boost=0.75,
                    style=0.0,
                    use_speaker_boost=True
                ),
                model_id=model_id
            )

            # Convert generator to bytes
            audio_bytes = b"".join(audio_generator)
            logger.info(f"✅ Generated {len(audio_bytes)} bytes of audio in {language}")

            return audio_bytes

        except Exception as e:
            logger.error(f"TTS generation failed for {language}: {e}")
            # Fallback to English
            return self.generate_multilingual_speech(text, 'en')

class FastRTCBridge:
    """Bridge between FastRTC VAD and Flask WebSocket"""

    def __init__(self, flask_socketio):
        self.socketio = flask_socketio
        self.multilingual_processor = MultilingualProcessor()
        self.conversation_active = False
        logger.info("🌉 FastRTC Bridge initialized")

    def create_fastrtc_handler(self):
        """Create FastRTC ReplyOnPause handler"""
        handler = ReplyOnPause(
            self.process_audio_with_fastrtc,
            algo_options=AlgoOptions(
                speech_threshold=0.5,  # Voice detection sensitivity
            ),
        )
        handler.min_silence_duration = 0.8  # 800ms silence before processing
        logger.info("🎙️ FastRTC handler created with 0.8s silence detection")
        return handler

    def process_audio_with_fastrtc(self, audio: tuple[int, np.ndarray]) -> Generator[Tuple[int, np.ndarray], None, None]:
        """Process audio using FastRTC with multilingual support"""
        logger.info("🎵 Processing audio with FastRTC + Multilingual support")

        try:
            # Create file-like object for Groq API
            audio_file = io.BytesIO(audio_to_bytes(audio))
            audio_file.name = "audio.wav"

            # Step 1: Detect language and transcribe
            detected_lang, transcript = self.multilingual_processor.detect_language(audio_file)

            # Step 2: Generate response in detected language
            response_text = self.multilingual_processor.generate_multilingual_response(transcript, detected_lang)

            # Step 3: Generate speech in detected language
            tts_audio_bytes = self.multilingual_processor.generate_multilingual_speech(response_text, detected_lang)

            # Step 4: Emit to Flask frontend
            audio_base64 = base64.b64encode(tts_audio_bytes).decode('utf-8')
            self.socketio.emit('multilingual_response', {
                'text': response_text,
                'audio': audio_base64,
                'transcript': transcript,
                'detected_language': detected_lang,
                'language_name': self.multilingual_processor.supported_languages.get(detected_lang, {}).get('name', detected_lang)
            })

            # Step 5: Convert for FastRTC output (for Gradio interface if needed)
            import numpy as np
            audio_array = np.frombuffer(tts_audio_bytes, dtype=np.int16)
            sample_rate = 22050  # ElevenLabs sample rate

            if len(audio_array.shape) == 1:
                audio_array = audio_array.reshape(1, -1)

            yield (sample_rate, audio_array)

        except Exception as e:
            logger.error(f"FastRTC processing error: {e}")
            self.socketio.emit('error', {'message': f'Processing error: {str(e)}'})

# Create global FastRTC bridge
fastrtc_bridge = FastRTCBridge(socketio)

@app.route('/')
def index():
    """Render the enhanced multilingual interface"""
    return render_template('enhanced_index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("🔌 Client connected to enhanced voice assistant")
    emit('status', {'message': 'Connected to Enhanced Multilingual Voice Assistant'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("🔌 Client disconnected")

@socketio.on('start_fastrtc_conversation')
def handle_start_fastrtc():
    """Start FastRTC-powered conversation"""
    try:
        fastrtc_bridge.conversation_active = True
        logger.info("🎤 Starting FastRTC conversation with multilingual support")
        emit('fastrtc_started', {'success': True})
        emit('status', {'message': 'FastRTC conversation started! Speak in any supported language...'})
    except Exception as e:
        logger.error(f"Error starting FastRTC conversation: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

@socketio.on('stop_fastrtc_conversation')
def handle_stop_fastrtc():
    """Stop FastRTC conversation"""
    try:
        fastrtc_bridge.conversation_active = False
        logger.info("🛑 Stopping FastRTC conversation")
        emit('fastrtc_stopped', {'success': True})
        emit('status', {'message': 'FastRTC conversation stopped'})
    except Exception as e:
        logger.error(f"Error stopping FastRTC conversation: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

def create_enhanced_stream():
    """Create FastRTC stream with multilingual support"""
    handler = fastrtc_bridge.create_fastrtc_handler()

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        ui_args={
            "title": "Enhanced Multilingual Voice Assistant",
            "subtitle": "Powered by FastRTC VAD + Multilingual AI",
            "description": "Speak in English, Spanish, French, German, Hindi, or Chinese - I'll respond in your language!",
            "article": "🌍 Multilingual support with reliable FastRTC voice detection",
        }
    )

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced Voice Assistant")
    parser.add_argument("--mode", choices=["flask", "gradio"], default="flask",
                       help="Run mode: flask (custom UI) or gradio (FastRTC UI)")
    args = parser.parse_args()

    if args.mode == "gradio":
        logger.info("🌈 Launching Enhanced Gradio interface with FastRTC...")
        stream = create_enhanced_stream()
        stream.ui.launch()
    else:
        logger.info("🌈 Launching Enhanced Flask interface...")
        socketio.run(app, debug=True, host='0.0.0.0', port=5002)
