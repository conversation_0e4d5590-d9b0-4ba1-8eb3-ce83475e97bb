#!/usr/bin/env python3
"""
Enhanced Voice Assistant with FastRTC VAD + Multilingual Support
Combines FastRTC's reliable VAD with Flask's custom UI and multilingual capabilities
"""

import os
import io
import base64
import asyncio
import threading
import numpy as np
from typing import Generator, Tu<PERSON>, Dict, Any
from pathlib import Path

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
from elevenlabs import ElevenLabs, VoiceSettings
from groq import Groq
from fastrtc import AlgoOptions, ReplyOnPause, Stream, audio_to_bytes
import logging

from conversation_assistant import agent, agent_config
from fraud_management import FraudVerificationAgent, NameDisambiguation, Customer, Transaction, create_sample_data

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced-voice-assistant-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize clients
elevenlabs_client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))

# Multiple Groq API keys for rotation
GROQ_API_KEYS = [
    os.getenv("GROQ_API_KEY"),
    os.getenv("GROQ_API_KEY_2"),
]

# Filter out None values
GROQ_API_KEYS = [key for key in GROQ_API_KEYS if key is not None]

if not GROQ_API_KEYS:
    raise ValueError("At least one GROQ_API_KEY must be set in environment variables")

logger.info(f"🔑 Loaded {len(GROQ_API_KEYS)} API key(s) for rotation")

# Current API key index and client
current_api_index = 0
groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])

def rotate_api_key():
    """Rotate to the next available API key"""
    global current_api_index, groq_client
    current_api_index = (current_api_index + 1) % len(GROQ_API_KEYS)
    groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])
    logger.info(f"🔄 Rotated to API key #{current_api_index + 1}")

def try_groq_request(func, *args, **kwargs):
    """Try a Groq request with all available API keys until one succeeds"""
    global current_api_index, groq_client

    for attempt in range(len(GROQ_API_KEYS)):
        try:
            result = func(*args, **kwargs)
            if attempt > 0:
                logger.info(f"✅ Successfully used API key #{current_api_index + 1}")
            return result
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"❌ API key #{current_api_index + 1} failed: {error_msg}")

            if "rate_limit_exceeded" in error_msg or "429" in error_msg:
                logger.info(f"⏰ Rate limit hit on API key #{current_api_index + 1}, rotating...")
                rotate_api_key()
            else:
                logger.info(f"🔄 Error on API key #{current_api_index + 1}, trying next...")
                rotate_api_key()

    raise Exception(f"💥 All {len(GROQ_API_KEYS)} API keys failed.")

class MultilingualProcessor:
    """Handles multilingual speech processing and TTS"""

    def __init__(self):
        self.supported_languages = {
            'en': {'name': 'English', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},  # Rachel
            'es': {'name': 'Spanish', 'voice_id': 'VR6AewLTigWG4xSOukaG'},  # Spanish voice
            'fr': {'name': 'French', 'voice_id': 'ThT5KcBeYPX3keUQqHPh'},   # French voice
            'de': {'name': 'German', 'voice_id': 'TxGEqnHWrfWFTfGW9XjX'},   # German voice
            'hi': {'name': 'Hindi', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},    # Fallback to Rachel
            'zh': {'name': 'Chinese', 'voice_id': '21m00Tcm4TlvDq8ikWAM'},  # Fallback to Rachel
        }

        # Initialize fraud management components
        self.fraud_agent = FraudVerificationAgent()
        self.name_disambiguator = NameDisambiguation()
        self.customers, self.sample_transactions = create_sample_data()
        self.current_customer = None
        self.fraud_mode = False

        logger.info(f"🌍 Multilingual processor initialized with {len(self.supported_languages)} languages")
        logger.info(f"🏦 Fraud management initialized with {len(self.customers)} sample customers")

    def detect_language(self, audio_file):
        """Detect language from audio and transcribe"""
        try:
            # Use Groq with language detection
            transcript_response = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",
                    response_format="verbose_json"  # Includes language detection
                )
            )

            detected_lang = transcript_response.language
            transcript_text = transcript_response.text

            logger.info(f"🔍 Detected language: {detected_lang} - Text: '{transcript_text[:50]}...'")

            return detected_lang, transcript_text

        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            # Fallback to English
            transcript = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",
                    response_format="text"
                )
            )
            return 'en', transcript

    def detect_fraud_keywords(self, transcript):
        """Detect if conversation is about fraud verification"""
        fraud_keywords = [
            "fraud", "suspicious", "transaction", "verify", "unauthorized",
            "bank", "account", "security", "confirm", "purchase", "charge"
        ]

        transcript_lower = transcript.lower()
        return any(keyword in transcript_lower for keyword in fraud_keywords)

    def extract_customer_name(self, transcript):
        """Extract potential customer name from transcript"""
        # Simple name extraction - in production, use NER
        words = transcript.split()

        # Look for common name patterns
        name_indicators = ["i am", "this is", "my name is", "speaking", "yes this is"]

        for i, word in enumerate(words):
            if any(indicator in " ".join(words[max(0, i-2):i+1]).lower() for indicator in name_indicators):
                # Next word might be the name
                if i + 1 < len(words):
                    potential_name = words[i + 1].strip(".,!?")
                    return potential_name

        # Fallback: look for capitalized words that might be names
        for word in words:
            if word.istitle() and len(word) > 2:
                return word

        return None

    def generate_multilingual_response(self, transcript, detected_lang):
        """Generate AI response in the detected language with fraud detection"""
        try:
            # Check if this is fraud-related conversation
            if self.detect_fraud_keywords(transcript) or self.fraud_mode:
                return self.handle_fraud_conversation(transcript, detected_lang)

            # Check for customer name in transcript
            potential_name = self.extract_customer_name(transcript)
            if potential_name and not self.current_customer:
                return self.handle_name_verification(potential_name, transcript, detected_lang)

            # Regular conversation
            if detected_lang != 'en':
                language_name = self.supported_languages.get(detected_lang, {}).get('name', detected_lang)
                enhanced_prompt = f"The user spoke in {language_name}. Please respond in {language_name}. User said: {transcript}"
            else:
                enhanced_prompt = transcript

            # Generate response using LangGraph
            agent_response = agent.invoke(
                {"messages": [{"role": "user", "content": enhanced_prompt}]},
                config=agent_config
            )
            response_text = agent_response["messages"][-1].content

            logger.info(f"💬 Generated response in {detected_lang}: '{response_text[:50]}...'")
            return response_text

        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return "I apologize, but I'm having trouble processing your request right now."

    def handle_name_verification(self, spoken_name, transcript, detected_lang):
        """Handle customer name verification with disambiguation"""
        try:
            verification_result = self.name_disambiguator.verify_customer_name(spoken_name, self.customers)

            if not verification_result["found"]:
                return f"I don't find a customer named {spoken_name} in our records. Could you please spell your name clearly?"

            elif verification_result.get("multiple_matches"):
                # Multiple customers with similar names
                self.fraud_mode = True
                return verification_result["disambiguation_prompt"]

            else:
                # Single customer found
                self.current_customer = verification_result["customer"]
                self.fraud_mode = True

                # Start fraud verification
                system_prompt = self.fraud_agent.create_fraud_system_prompt(
                    self.current_customer,
                    self.sample_transactions
                )

                return f"Thank you, {self.current_customer.name}. I'm calling from SecureBank's Fraud Prevention Department. For security purposes, I need to verify your identity. Can you please provide the last 4 digits of your Social Security Number?"

        except Exception as e:
            logger.error(f"Name verification failed: {e}")
            return "I'm having trouble verifying your information. Could you please repeat your name?"

    def handle_fraud_conversation(self, transcript, detected_lang):
        """Handle fraud verification conversation flow"""
        try:
            if not self.current_customer:
                return "I need to verify your identity first. What is your name?"

            # Determine current stage based on transcript content
            if any(word in transcript.lower() for word in ["yes", "no", "authorized", "fraud", "didn't"]):
                # Transaction verification response
                stage = "transaction_review"
                transaction_id = "TXN001"  # In production, track which transaction is being discussed
            elif any(char.isdigit() for char in transcript) and len(''.join(filter(str.isdigit, transcript))) == 4:
                # SSN verification
                stage = "identity_verification"
                transaction_id = None
            else:
                # General fraud conversation
                stage = "general"
                transaction_id = None

            # Process the response
            if stage in ["identity_verification", "transaction_review"]:
                response_data = self.fraud_agent.process_customer_response(transcript, stage, transaction_id)

                if stage == "identity_verification" and response_data.get("verified"):
                    # Move to transaction review
                    return f"Thank you. Your identity has been verified. Now let's review the suspicious transactions. I see a transaction for $1250.00 at Amazon Online on January 15th at 2:30 PM. Did you authorize this transaction? Please answer YES or NO."

                elif stage == "transaction_review" and response_data.get("transaction_confirmed"):
                    if response_data["response"] == "AUTHORIZED":
                        return "Thank you for confirming that transaction. Let me check the next one. I see a transaction for $500.00 ATM withdrawal on January 15th at 11:45 PM in New York. Did you authorize this transaction?"
                    elif response_data["response"] == "FRAUD":
                        return "I understand this transaction was not authorized. We'll immediately block your card and start the fraud protection process. You'll receive a new card within 2-3 business days. Is there anything else I can help you with?"
                    else:
                        return response_data.get("message", "Please provide a clear YES or NO answer.")

                else:
                    return response_data.get("message", "Could you please repeat that?")

            else:
                # General fraud conversation
                return "I'm here to help verify some suspicious transactions on your account. Let's start with verifying your identity."

        except Exception as e:
            logger.error(f"Fraud conversation handling failed: {e}")
            return "I'm having trouble processing your response. Could you please repeat that?"

    def generate_multilingual_speech(self, text, language):
        """Generate speech in the specified language"""
        try:
            # Get voice for language (fallback to English if not supported)
            voice_config = self.supported_languages.get(language, self.supported_languages['en'])
            voice_id = voice_config['voice_id']

            logger.info(f"🔊 Generating speech in {language} with voice {voice_id}")

            # Use multilingual model for non-English languages
            model_id = "eleven_multilingual_v2" if language != 'en' else "eleven_monolingual_v1"

            audio_generator = elevenlabs_client.text_to_speech.convert(
                voice_id=voice_id,
                text=text,
                voice_settings=VoiceSettings(
                    stability=0.5,
                    similarity_boost=0.75,
                    style=0.0,
                    use_speaker_boost=True
                ),
                model_id=model_id
            )

            # Convert generator to bytes
            audio_bytes = b"".join(audio_generator)
            logger.info(f"✅ Generated {len(audio_bytes)} bytes of audio in {language}")

            return audio_bytes

        except Exception as e:
            logger.error(f"TTS generation failed for {language}: {e}")
            # Fallback to English
            return self.generate_multilingual_speech(text, 'en')

class FastRTCBridge:
    """Bridge between FastRTC VAD and Flask WebSocket"""

    def __init__(self, flask_socketio):
        self.socketio = flask_socketio
        self.multilingual_processor = MultilingualProcessor()
        self.conversation_active = False
        logger.info("🌉 FastRTC Bridge initialized")

    def create_fastrtc_handler(self):
        """Create FastRTC ReplyOnPause handler"""
        handler = ReplyOnPause(
            self.process_audio_with_fastrtc,
            algo_options=AlgoOptions(
                speech_threshold=0.5,  # Voice detection sensitivity
            ),
        )
        handler.min_silence_duration = 0.8  # 800ms silence before processing
        logger.info("🎙️ FastRTC handler created with 0.8s silence detection")
        return handler

    def process_audio_with_fastrtc(self, audio: tuple[int, np.ndarray]) -> Generator[Tuple[int, np.ndarray], None, None]:
        """Process audio using FastRTC with multilingual support"""
        logger.info("🎵 Processing audio with FastRTC + Multilingual support")

        try:
            # Create file-like object for Groq API
            audio_file = io.BytesIO(audio_to_bytes(audio))
            audio_file.name = "audio.wav"

            # Step 1: Detect language and transcribe
            detected_lang, transcript = self.multilingual_processor.detect_language(audio_file)

            # Step 2: Generate response in detected language
            response_text = self.multilingual_processor.generate_multilingual_response(transcript, detected_lang)

            # Step 3: Generate speech in detected language
            tts_audio_bytes = self.multilingual_processor.generate_multilingual_speech(response_text, detected_lang)

            # Step 4: Emit to Flask frontend
            audio_base64 = base64.b64encode(tts_audio_bytes).decode('utf-8')
            self.socketio.emit('multilingual_response', {
                'text': response_text,
                'audio': audio_base64,
                'transcript': transcript,
                'detected_language': detected_lang,
                'language_name': self.multilingual_processor.supported_languages.get(detected_lang, {}).get('name', detected_lang)
            })

            # Step 5: Convert for FastRTC output (for Gradio interface if needed)
            import numpy as np
            audio_array = np.frombuffer(tts_audio_bytes, dtype=np.int16)
            sample_rate = 22050  # ElevenLabs sample rate

            if len(audio_array.shape) == 1:
                audio_array = audio_array.reshape(1, -1)

            yield (sample_rate, audio_array)

        except Exception as e:
            logger.error(f"FastRTC processing error: {e}")
            self.socketio.emit('error', {'message': f'Processing error: {str(e)}'})

# Create global FastRTC bridge
fastrtc_bridge = FastRTCBridge(socketio)

@app.route('/')
def index():
    """Render the enhanced multilingual interface"""
    return render_template('enhanced_index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("🔌 Client connected to enhanced voice assistant")
    emit('status', {'message': 'Connected to Enhanced Multilingual Voice Assistant'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("🔌 Client disconnected")

@socketio.on('start_conversation')
def handle_start_conversation():
    """Start multilingual conversation with custom VAD"""
    try:
        fastrtc_bridge.conversation_active = True
        logger.info("🎤 Starting multilingual conversation")
        emit('conversation_started', {'success': True})
        emit('status', {'message': 'Conversation started! Speak in any supported language...'})
    except Exception as e:
        logger.error(f"Error starting conversation: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

@socketio.on('stop_conversation')
def handle_stop_conversation():
    """Stop multilingual conversation"""
    try:
        fastrtc_bridge.conversation_active = False
        logger.info("🛑 Stopping multilingual conversation")
        emit('conversation_stopped', {'success': True})
        emit('status', {'message': 'Conversation stopped'})
    except Exception as e:
        logger.error(f"Error stopping conversation: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

@socketio.on('process_audio')
def handle_audio(data):
    """Process audio with multilingual support"""
    try:
        if not fastrtc_bridge.conversation_active:
            return

        audio_data = data.get('audio')
        if not audio_data:
            emit('error', {'message': 'No audio data received'})
            return

        logger.info("Processing audio with multilingual support...")

        # Decode base64 audio data
        audio_bytes = base64.b64decode(audio_data)
        logger.info(f"Audio bytes length: {len(audio_bytes)}")

        # Create file-like object for Groq API
        audio_file = io.BytesIO(audio_bytes)
        audio_file.name = "audio.webm"

        # Process with multilingual pipeline
        try:
            # Step 1: Detect language and transcribe
            detected_lang, transcript = fastrtc_bridge.multilingual_processor.detect_language(audio_file)

            # Step 2: Generate response in detected language
            response_text = fastrtc_bridge.multilingual_processor.generate_multilingual_response(transcript, detected_lang)

            # Step 3: Generate speech in detected language
            tts_audio_bytes = fastrtc_bridge.multilingual_processor.generate_multilingual_speech(response_text, detected_lang)

            # Step 4: Send response
            audio_base64 = base64.b64encode(tts_audio_bytes).decode('utf-8')
            emit('multilingual_response', {
                'text': response_text,
                'audio': audio_base64,
                'transcript': transcript,
                'detected_language': detected_lang,
                'language_name': fastrtc_bridge.multilingual_processor.supported_languages.get(detected_lang, {}).get('name', detected_lang)
            })

        except Exception as processing_error:
            logger.error(f"Audio processing error: {processing_error}")
            emit('error', {'message': f'Processing error: {str(processing_error)}'})

    except Exception as e:
        logger.error(f"Error processing audio: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

def create_enhanced_stream():
    """Create FastRTC stream with multilingual support (for Gradio mode)"""
    handler = fastrtc_bridge.create_fastrtc_handler()

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        ui_args={
            "title": "Enhanced Multilingual Voice Assistant",
            "subtitle": "Powered by FastRTC VAD + Multilingual AI",
            "description": "Speak in English, Spanish, French, German, Hindi, or Chinese - I'll respond in your language!",
            "article": "🌍 Multilingual support with reliable FastRTC voice detection",
        }
    )

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced Voice Assistant")
    parser.add_argument("--mode", choices=["flask", "gradio"], default="flask",
                       help="Run mode: flask (custom UI) or gradio (FastRTC UI)")
    args = parser.parse_args()

    if args.mode == "gradio":
        logger.info("🌈 Launching Enhanced Gradio interface with FastRTC...")
        stream = create_enhanced_stream()
        stream.ui.launch()
    else:
        logger.info("🌈 Launching Enhanced Flask interface...")
        socketio.run(app, debug=True, host='0.0.0.0', port=5002)
