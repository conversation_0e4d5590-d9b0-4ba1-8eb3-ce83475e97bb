#!/usr/bin/env python3
"""
Fraud Management Module for Banking Voice Assistant
Handles customer verification and transaction confirmation
"""

import json
from datetime import datetime
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class Transaction:
    """Transaction data structure"""
    id: str
    amount: float
    merchant: str
    date: str
    time: str
    location: str
    card_last_four: str
    risk_score: float
    status: str = "pending_verification"

@dataclass
class Customer:
    """Customer data structure"""
    customer_id: str
    name: str
    phone: str
    account_number: str
    last_four_ssn: str
    date_of_birth: str
    security_questions: Dict[str, str]
    preferred_language: str = "en"

class FraudVerificationAgent:
    """Handles fraud verification conversations"""
    
    def __init__(self):
        self.verification_stages = [
            "greeting",
            "identity_verification", 
            "transaction_review",
            "confirmation",
            "completion"
        ]
        self.current_stage = "greeting"
        self.verified_identity = False
        self.transactions_reviewed = []
        self.customer_responses = {}
        
    def create_fraud_system_prompt(self, customer: Customer, transactions: List[Transaction]) -> str:
        """Create system prompt for fraud verification"""
        
        transactions_text = "\n".join([
            f"Transaction {i+1}: ${tx.amount} at {tx.merchant} on {tx.date} at {tx.time}, Card ending {tx.card_last_four}"
            for i, tx in enumerate(transactions)
        ])
        
        return f"""
You are a professional fraud verification specialist calling from SecureBank's Fraud Prevention Department.

CUSTOMER INFORMATION:
- Name: {customer.name}
- Phone: {customer.phone}
- Account: {customer.account_number}
- Preferred Language: {customer.preferred_language}

SUSPICIOUS TRANSACTIONS TO VERIFY:
{transactions_text}

YOUR ROLE:
1. Politely introduce yourself and explain why you're calling
2. Verify customer identity using security questions
3. Review each transaction individually and get clear YES/NO confirmation
4. Document all responses accurately
5. Be professional, patient, and clear

CONVERSATION FLOW:
1. GREETING: "Hello, this is [Your Name] calling from SecureBank's Fraud Prevention Department. Am I speaking with {customer.name}?"

2. IDENTITY VERIFICATION: "For security purposes, I need to verify your identity. Can you please provide the last 4 digits of your Social Security Number?"

3. TRANSACTION REVIEW: For each transaction, ask:
   "I see a transaction for $[amount] at [merchant] on [date] at [time]. Did you authorize this transaction? Please answer YES if you made this purchase, or NO if you did not."

4. CONFIRMATION: "Thank you for confirming. Let me summarize what we discussed..."

5. COMPLETION: Explain next steps based on responses.

IMPORTANT GUIDELINES:
- Always wait for clear YES/NO answers for transactions
- If customer says NO to any transaction, mark as potential fraud
- If customer is unsure, ask them to check their records
- Be empathetic if fraud is confirmed
- Speak clearly and at appropriate pace
- Repeat information if customer asks
- Always respond in {customer.preferred_language} if supported

SECURITY NOTES:
- Never ask for full SSN, passwords, or PINs
- Only ask for last 4 digits of SSN for verification
- If identity cannot be verified, escalate to supervisor
"""

    def process_customer_response(self, response: str, stage: str, transaction_id: str = None) -> Dict[str, Any]:
        """Process customer response and determine next action"""
        
        response_lower = response.lower().strip()
        
        if stage == "identity_verification":
            # Check if response contains 4 digits
            digits = ''.join(filter(str.isdigit, response))
            if len(digits) == 4:
                return {
                    "verified": True,
                    "next_stage": "transaction_review",
                    "message": "Thank you. Your identity has been verified. Now let's review the suspicious transactions."
                }
            else:
                return {
                    "verified": False,
                    "next_stage": "identity_verification",
                    "message": "I need the last 4 digits of your Social Security Number for verification. Please provide just the last 4 digits."
                }
        
        elif stage == "transaction_review":
            # Look for YES/NO responses
            if any(word in response_lower for word in ["yes", "yeah", "yep", "correct", "right", "i did", "that's mine"]):
                self.customer_responses[transaction_id] = "AUTHORIZED"
                return {
                    "transaction_confirmed": True,
                    "response": "AUTHORIZED",
                    "message": "Thank you for confirming that transaction."
                }
            elif any(word in response_lower for word in ["no", "nope", "not me", "didn't", "fraud", "unauthorized"]):
                self.customer_responses[transaction_id] = "FRAUD"
                return {
                    "transaction_confirmed": True,
                    "response": "FRAUD",
                    "message": "I understand. We'll mark this as unauthorized and begin the fraud protection process."
                }
            elif any(word in response_lower for word in ["unsure", "not sure", "don't know", "maybe", "check"]):
                self.customer_responses[transaction_id] = "UNCERTAIN"
                return {
                    "transaction_confirmed": True,
                    "response": "UNCERTAIN", 
                    "message": "I understand you're not certain. We'll flag this for further review."
                }
            else:
                return {
                    "transaction_confirmed": False,
                    "message": "I need a clear YES or NO answer. Did you authorize this transaction?"
                }
        
        return {"message": "Please continue with your response."}

class NameDisambiguation:
    """Handles name pronunciation and spelling disambiguation"""
    
    def __init__(self):
        self.name_variations = {
            # Common name variations with phonetic similarities
            "puja": ["puja", "pooja", "pujah"],
            "pooja": ["pooja", "puja", "pujah"],
            "john": ["john", "jon", "johnathan", "jonathan"],
            "catherine": ["catherine", "kathryn", "katherine", "katharine"],
            "michael": ["michael", "micheal", "mikhail"],
            "sarah": ["sarah", "sera", "sara"],
            "david": ["david", "dave", "davide"],
            "maria": ["maria", "mariah", "marie"]
        }
        
        self.phonetic_groups = {
            # Group names by similar pronunciation
            "puja_group": ["puja", "pooja", "pujah"],
            "john_group": ["john", "jon", "johnathan", "jonathan"],
            "catherine_group": ["catherine", "kathryn", "katherine", "katharine"]
        }
    
    def create_name_verification_prompt(self, detected_name: str, possible_names: List[str]) -> str:
        """Create prompt for name disambiguation"""
        
        if len(possible_names) <= 1:
            return f"Am I speaking with {detected_name}?"
        
        # Multiple possible spellings
        name_options = ", ".join(possible_names[:-1]) + f", or {possible_names[-1]}"
        
        return f"""
I heard the name as "{detected_name}" but I want to make sure I have the correct spelling. 
Are you {name_options}? Please tell me the correct spelling of your name.
"""
    
    def find_name_variations(self, spoken_name: str) -> List[str]:
        """Find possible name variations based on pronunciation"""
        spoken_lower = spoken_name.lower().strip()
        
        # Check direct matches first
        if spoken_lower in self.name_variations:
            return self.name_variations[spoken_lower]
        
        # Check phonetic groups
        for group_name, names in self.phonetic_groups.items():
            if spoken_lower in names:
                return names
        
        # If no variations found, return original
        return [spoken_name]
    
    def verify_customer_name(self, spoken_name: str, customer_database: List[Customer]) -> Dict[str, Any]:
        """Verify customer name against database with disambiguation"""
        
        # Find possible name variations
        name_variations = self.find_name_variations(spoken_name)
        
        # Search database for matching customers
        matching_customers = []
        for customer in customer_database:
            customer_name_lower = customer.name.lower().strip()
            if customer_name_lower in [name.lower() for name in name_variations]:
                matching_customers.append(customer)
        
        if len(matching_customers) == 0:
            return {
                "found": False,
                "message": f"I don't find a customer named {spoken_name} in our records. Could you please spell your name?"
            }
        elif len(matching_customers) == 1:
            return {
                "found": True,
                "customer": matching_customers[0],
                "message": f"Thank you, {matching_customers[0].name}."
            }
        else:
            # Multiple matches - need disambiguation
            customer_names = [c.name for c in matching_customers]
            return {
                "found": True,
                "multiple_matches": True,
                "customers": matching_customers,
                "disambiguation_prompt": self.create_name_verification_prompt(spoken_name, customer_names)
            }

# Example usage and test data
def create_sample_data():
    """Create sample customer and transaction data"""
    
    # Sample customers with name variations
    customers = [
        Customer(
            customer_id="CUST001",
            name="Puja Sharma",
            phone="******-0123",
            account_number="****1234",
            last_four_ssn="5678",
            date_of_birth="1990-05-15",
            security_questions={"mother_maiden": "Singh"},
            preferred_language="en"
        ),
        Customer(
            customer_id="CUST002", 
            name="Pooja Patel",
            phone="******-0124",
            account_number="****5678",
            last_four_ssn="9012",
            date_of_birth="1985-08-22",
            security_questions={"mother_maiden": "Gupta"},
            preferred_language="hi"
        )
    ]
    
    # Sample suspicious transactions
    transactions = [
        Transaction(
            id="TXN001",
            amount=1250.00,
            merchant="Amazon Online",
            date="2024-01-15",
            time="2:30 PM",
            location="Online",
            card_last_four="1234",
            risk_score=0.85
        ),
        Transaction(
            id="TXN002",
            amount=500.00,
            merchant="ATM Withdrawal",
            date="2024-01-15", 
            time="11:45 PM",
            location="New York, NY",
            card_last_four="1234",
            risk_score=0.92
        )
    ]
    
    return customers, transactions

def demonstrate_fraud_verification():
    """Demonstrate fraud verification process"""
    
    customers, transactions = create_sample_data()
    fraud_agent = FraudVerificationAgent()
    name_disambiguator = NameDisambiguation()
    
    # Example 1: Name disambiguation
    print("=== NAME DISAMBIGUATION EXAMPLE ===")
    spoken_name = "Puja"  # Could be "Puja" or "Pooja"
    
    verification_result = name_disambiguator.verify_customer_name(spoken_name, customers)
    print(f"Spoken name: {spoken_name}")
    print(f"Verification result: {verification_result}")
    
    # Example 2: Fraud verification conversation
    print("\n=== FRAUD VERIFICATION EXAMPLE ===")
    customer = customers[0]  # Puja Sharma
    
    system_prompt = fraud_agent.create_fraud_system_prompt(customer, transactions)
    print("System prompt created for fraud verification:")
    print(system_prompt[:500] + "...")
    
    # Example customer responses
    print("\n=== SAMPLE CONVERSATION FLOW ===")
    print("Agent: Hello, this is calling from SecureBank's Fraud Prevention Department. Am I speaking with Puja Sharma?")
    print("Customer: Yes, this is Puja.")
    
    print("\nAgent: For security, can you provide the last 4 digits of your Social Security Number?")
    print("Customer: 5678")
    
    identity_check = fraud_agent.process_customer_response("5678", "identity_verification")
    print(f"Identity verification: {identity_check}")
    
    print(f"\nAgent: I see a transaction for $1250.00 at Amazon Online on 2024-01-15 at 2:30 PM. Did you authorize this transaction?")
    print("Customer: Yes, that was me buying a laptop.")
    
    transaction_check = fraud_agent.process_customer_response("Yes, that was me", "transaction_review", "TXN001")
    print(f"Transaction verification: {transaction_check}")

if __name__ == "__main__":
    demonstrate_fraud_verification()
