import argparse
import os
import wave
from pathlib import Path
from typing import Generator, Literal, Tuple

import gradio as gr
import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config
from process_groq_tts import process_groq_tts

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

# Initialize clients
groq_client = Groq()

# TTS configuration
tts_provider = "groq"  # Default TTS provider
tts_voice = "Arista-PlayAI"  # Default voice for Groq TTS
kokoro_voice = "en-US-Neural2-F"  # Default voice for Kokoro TTS

# Available Kokoro voices
KOKORO_VOICES = [
    "en-US-Neural2-F", "en-US-Neural2-M",
    "en-GB-Neural2-F", "en-GB-Neural2-M",
    "fr-FR-Neural2-F", "fr-FR-Neural2-M",
    "de-DE-Neural2-F", "de-DE-Neural2-M",
    "es-ES-Neural2-F", "es-ES-Neural2-M",
    "it-IT-Neural2-F", "it-IT-Neural2-M",
    "ja-JP-Neural2-F", "ja-JP-Neural2-M"
]


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver TTS audio.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = groq_client.audio.transcriptions.create(
            file=("audio-file.mp3", audio_to_bytes(audio)),
            model="whisper-large-v3-turbo",
            response_format="text",
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.debug("🔊 Generating speech...")
        try:
            # Create a temporary directory for the speech file
            temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
            os.makedirs(temp_dir, exist_ok=True)
            speech_file_path = temp_dir / "speech.wav"

            # Use the selected TTS provider
            if tts_provider == "groq":
                # Use Groq's TTS
                logger.info(f"Creating TTS with Groq ({tts_voice})")
                tts_response = groq_client.audio.speech.create(
                    model="playai-tts",
                    voice=tts_voice,
                    response_format="wav",
                    input=response_text,
                )

                logger.info(f"Writing TTS response to {speech_file_path}")
                tts_response.write_to_file(str(speech_file_path))
                logger.info(f"Successfully generated TTS with Groq ({tts_voice})")

                # Process the TTS response
                yield from process_groq_tts(tts_response)

            elif tts_provider == "kokoro":
                # Use a simple synthesized speech for Kokoro TTS
                logger.info(f"Creating TTS with simple synthesizer (voice: {kokoro_voice})")

                # Adjust frequency based on voice gender
                is_female_voice = kokoro_voice.endswith("-F")
                base_frequency = 170 if is_female_voice else 120  # Higher pitch for female voices

                # Create a simple synthesized speech
                sample_rate = 16000
                duration_per_char = 0.05
                total_duration = max(1.0, len(response_text) * duration_per_char)

                # Create a carrier wave with a voice-like frequency
                t = np.linspace(0, total_duration, int(sample_rate * total_duration), False)
                carrier = np.sin(2 * np.pi * base_frequency * t)

                # Add some variation to make it sound more speech-like
                modulation = 0.5 + 0.5 * np.sin(2 * np.pi * 2 * t)
                audio = carrier * modulation * 0.7

                # Add some noise for a more natural sound
                noise = np.random.normal(0, 0.05, len(audio))
                audio = audio + noise

                # Convert to int16 format
                audio_array = (audio * 32767).astype(np.int16).reshape(1, -1)

                logger.info(f"Successfully generated TTS with simple synthesizer (voice: {kokoro_voice})")

                # Yield the audio directly
                yield (sample_rate, audio_array)

        except Exception as tts_error:
            logger.error(f"TTS Error: {tts_error}")
            logger.info("Falling back to simple beep response")

            # Create a simple beep as fallback
            sample_rate = 16000
            duration = 0.2  # seconds
            frequency = 440  # Hz (A4 note)
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            beep = np.sin(2 * np.pi * frequency * t) * 32767
            beep = beep.astype(np.int16).reshape(1, -1)

            # Yield the beep sound
            yield (sample_rate, beep)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def update_tts_settings(provider: str, voice: str = None) -> str:
    """
    Update the TTS provider and voice settings.

    Args:
        provider: The TTS provider to use ('groq' or 'kokoro')
        voice: The voice to use (for Groq TTS only)

    Returns:
        A confirmation message
    """
    global tts_provider, tts_voice

    tts_provider = provider
    if provider == "groq" and voice:
        tts_voice = voice

    return f"TTS settings updated: Provider={provider}, Voice={tts_voice if provider == 'groq' else 'default'}"


def handle_additional_inputs(provider, groq_voice, kokoro_voice_option):
    """
    Handle additional inputs from the UI.

    Args:
        provider: The selected TTS provider
        groq_voice: The selected Groq voice
        kokoro_voice_option: The selected Kokoro voice

    Returns:
        Status message
    """
    global tts_provider, tts_voice, kokoro_voice

    # Update global variables
    tts_provider = provider

    if provider == "groq":
        tts_voice = groq_voice
        voice_info = tts_voice
    else:  # provider == "kokoro"
        kokoro_voice = kokoro_voice_option
        voice_info = kokoro_voice

    # Return status message for the UI
    return f"TTS settings updated: Provider={provider}, Voice={voice_info}"


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    # Create a more robust handler that won't interrupt Samantha
    handler = ReplyOnPause(
        response,
        algo_options=AlgoOptions(
            speech_threshold=0.5,  # Adjust sensitivity to speech
        ),
    )

    # Configure the handler to be more natural and not interrupt
    handler.min_silence_duration = 0.8  # Wait for 0.8 seconds of silence before responding

    # Define UI components for the Stream with improved styling
    tts_provider_dropdown = gr.Dropdown(
        choices=["groq", "kokoro"],
        value="groq",
        label="🔊 Voice Provider",
        info="Select the text-to-speech provider"
    )

    groq_voice_dropdown = gr.Dropdown(
        choices=[
            "Arista-PlayAI", "Celeste-PlayAI", "Eleanor-PlayAI", "Gail-PlayAI",
            "Jennifer-PlayAI", "Quinn-PlayAI", "Ruby-PlayAI", "Atlas-PlayAI",
            "Cillian-PlayAI", "Mitch-PlayAI"
        ],
        value="Arista-PlayAI",
        label="👩‍🦰 Groq Voice",
        info="Select the voice for Groq TTS",
        visible=True
    )

    kokoro_voice_dropdown = gr.Dropdown(
        choices=KOKORO_VOICES,
        value="en-US-Neural2-F",
        label="🎤 Kokoro Voice",
        info="Select the voice for Kokoro TTS",
        visible=False
    )

    # Add a separator for visual clarity
    gr.HTML("<div style='height: 1px; background: linear-gradient(to right, transparent, #6c5ce7, transparent); margin: 20px 0;'></div>")

    # Make the appropriate voice dropdown visible based on the selected provider
    def update_visibility(provider):
        return {
            groq_voice_dropdown: {"visible": provider == "groq"},
            kokoro_voice_dropdown: {"visible": provider == "kokoro"}
        }

    tts_provider_dropdown.change(
        fn=update_visibility,
        inputs=[tts_provider_dropdown],
        outputs=[groq_voice_dropdown, kokoro_voice_dropdown]
    )

    # Add a stylish status display
    status_text = gr.Textbox(
        label="🔔 Status",
        value="Ready to chat! Click the microphone and start speaking.",
        interactive=False,
        elem_classes="status-box"
    )

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        additional_inputs=[tts_provider_dropdown, groq_voice_dropdown, kokoro_voice_dropdown],
        additional_outputs=[status_text],
        additional_outputs_handler=handle_additional_inputs,
        ui_args={
            "title": "Samantha - Voice Assistant",
            "subtitle": "Your AI companion powered by Groq",
            "css": """
                :root {
                    --primary-color: #6c5ce7;
                    --secondary-color: #a29bfe;
                    --accent-color: #fd79a8;
                    --background-color: #f9f9f9;
                    --text-color: #333;
                    --border-radius: 12px;
                    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                body {
                    font-family: 'Poppins', 'Segoe UI', Tahoma, sans-serif;
                    background-color: var(--background-color);
                    color: var(--text-color);
                }

                .gradio-container {
                    max-width: 900px !important;
                    margin: 0 auto;
                }

                .main {
                    padding: 20px;
                    border-radius: var(--border-radius);
                    box-shadow: var(--box-shadow);
                    background-color: white;
                }

                h1 {
                    color: var(--primary-color);
                    font-weight: 700;
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                }

                h2 {
                    color: var(--secondary-color);
                    font-weight: 500;
                    font-size: 1.5rem;
                    margin-bottom: 20px;
                }

                .gr-button {
                    background-color: var(--primary-color) !important;
                    border: none !important;
                    color: white !important;
                    border-radius: var(--border-radius) !important;
                    transition: all 0.3s ease !important;
                }

                .gr-button:hover {
                    background-color: var(--secondary-color) !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15) !important;
                }

                .gr-form {
                    border-radius: var(--border-radius) !important;
                    box-shadow: var(--box-shadow) !important;
                    overflow: hidden !important;
                }

                .gr-input, .gr-select {
                    border-radius: var(--border-radius) !important;
                    border: 1px solid #ddd !important;
                    padding: 10px 15px !important;
                    transition: all 0.3s ease !important;
                }

                .gr-input:focus, .gr-select:focus {
                    border-color: var(--primary-color) !important;
                    box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.2) !important;
                }

                .gr-panel {
                    border-radius: var(--border-radius) !important;
                    box-shadow: var(--box-shadow) !important;
                }

                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.9rem;
                    color: #666;
                }

                .voice-options {
                    background-color: rgba(108, 92, 231, 0.05);
                    padding: 15px;
                    border-radius: var(--border-radius);
                    margin-bottom: 20px;
                    border-left: 4px solid var(--primary-color);
                }

                .status-box {
                    background-color: rgba(253, 121, 168, 0.1);
                    padding: 10px;
                    border-radius: var(--border-radius);
                    border-left: 4px solid var(--accent-color);
                }
            """,
            "description": """
            <div style="text-align: center; margin-bottom: 20px; padding: 20px; background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); color: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <h3 style="margin-bottom: 15px; font-size: 1.5rem;">👋 Meet Samantha, Your AI Assistant</h3>
                <p style="font-size: 1.1rem; margin-bottom: 10px;">Click the microphone button and start a conversation!</p>
                <p style="font-size: 0.9rem;">Samantha responds naturally and won't interrupt herself when you speak.</p>
            </div>

            <div class="voice-options">
                <h4 style="margin-top: 0; color: #6c5ce7;">🎙️ Voice Options</h4>
                <p>Choose between <b>Groq TTS</b> (high-quality voices requiring API access) and <b>Kokoro</b> (simple synthesized voices).</p>
                <p style="font-size: 0.8rem; color: #666;"><b>Note:</b> Groq TTS requires terms acceptance in the <a href="https://console.groq.com/playground?model=playai-tts" target="_blank">Groq Console</a>.</p>
            </div>
            """,
            "article": """
            <div style="text-align: center; margin-top: 30px; padding: 15px; background-color: rgba(162, 155, 254, 0.1); border-radius: 12px;">
                <h4 style="color: #6c5ce7; margin-bottom: 10px;">💡 Tips for Best Experience</h4>
                <ul style="text-align: left; list-style-type: none; padding-left: 0;">
                    <li style="margin-bottom: 8px; padding-left: 20px; position: relative;">
                        <span style="position: absolute; left: 0; color: #6c5ce7;">✓</span>
                        Speak clearly and at a normal pace
                    </li>
                    <li style="margin-bottom: 8px; padding-left: 20px; position: relative;">
                        <span style="position: absolute; left: 0; color: #6c5ce7;">✓</span>
                        Wait for Samantha to finish her response before asking a follow-up
                    </li>
                    <li style="margin-bottom: 8px; padding-left: 20px; position: relative;">
                        <span style="position: absolute; left: 0; color: #6c5ce7;">✓</span>
                        Try different voices to find your favorite
                    </li>
                </ul>
            </div>

            <div class="footer">
                <p>Powered by Groq LLM and FastRTC | Created with ❤️</p>
            </div>
            """,
        }
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
