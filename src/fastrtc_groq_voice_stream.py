import argparse
import os
import wave
from pathlib import Path
from typing import Generator, Literal, <PERSON>ple

import gradio as gr
import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger
from elevenlabs import ElevenLabs, VoiceSettings
import io

from conversation_assistant import agent, agent_config
from process_groq_tts import process_groq_tts

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

# Multiple Groq API keys for rotation
GROQ_API_KEYS = [
    os.getenv("GROQ_API_KEY"),
    os.getenv("GROQ_API_KEY_2"),
    os.getenv("GROQ_API_KEY_3"),
    os.getenv("GROQ_API_KEY_4"),
    os.getenv("GROQ_API_KEY_5")
]

# Filter out None values (in case some API keys are not set)
GROQ_API_KEYS = [key for key in GROQ_API_KEYS if key is not None]

if not GROQ_API_KEYS:
    raise ValueError("At least one GROQ_API_KEY must be set in environment variables")

logger.info(f"🔑 Loaded {len(GROQ_API_KEYS)} API key(s) for rotation")

# Current API key index and client
current_api_index = 0
groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])

# Initialize ElevenLabs client
elevenlabs_client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))

# TTS configuration
tts_provider = "groq"  # Default to Groq TTS (ElevenLabs requires subscription)
tts_voice = "Rachel"  # Default ElevenLabs voice
groq_voice = "Arista-PlayAI"  # Default Groq voice

def rotate_api_key():
    """Rotate to the next available API key"""
    global current_api_index, groq_client
    current_api_index = (current_api_index + 1) % len(GROQ_API_KEYS)
    groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])
    logger.info(f"🔄 Rotated to API key #{current_api_index + 1}")

def try_groq_request(func, *args, **kwargs):
    """Try a Groq request with all available API keys until one succeeds"""
    global current_api_index, groq_client

    for attempt in range(len(GROQ_API_KEYS)):
        try:
            result = func(*args, **kwargs)
            if attempt > 0:  # Only log if we had to rotate
                logger.info(f"✅ Successfully used API key #{current_api_index + 1}")
            return result
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"❌ API key #{current_api_index + 1} failed: {error_msg}")

            # Check if it's a rate limit error
            if "rate_limit_exceeded" in error_msg or "429" in error_msg:
                logger.info(f"⏰ Rate limit hit on API key #{current_api_index + 1}, rotating...")
                rotate_api_key()
            else:
                # For other errors, also try rotating
                logger.info(f"🔄 Error on API key #{current_api_index + 1}, trying next...")
                rotate_api_key()

    # If all API keys failed, raise the last exception
    raise Exception(f"💥 All {len(GROQ_API_KEYS)} API keys failed. Please check your API keys and rate limits.")


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver TTS audio.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = try_groq_request(
            lambda: groq_client.audio.transcriptions.create(
                file=("audio-file.mp3", audio_to_bytes(audio)),
                model="whisper-large-v3-turbo",
                response_format="text",
            )
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.debug("🔊 Generating speech...")
        try:
            if tts_provider == "elevenlabs":
                try:
                    # Use ElevenLabs TTS
                    logger.info(f"🔊 Creating TTS with ElevenLabs ({tts_voice})")

                    # Generate audio using ElevenLabs (correct method)
                    audio_generator = elevenlabs_client.text_to_speech.convert(
                        voice_id=tts_voice,
                        text=response_text,
                        voice_settings=VoiceSettings(
                            stability=0.5,
                            similarity_boost=0.75,
                            style=0.0,
                            use_speaker_boost=True
                        )
                    )

                    # Convert generator to bytes
                    audio_bytes = b"".join(audio_generator)

                    # Convert to numpy array for FastRTC
                    audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
                    sample_rate = 22050  # ElevenLabs default sample rate

                    # Reshape for stereo if needed
                    if len(audio_array.shape) == 1:
                        audio_array = audio_array.reshape(1, -1)

                    logger.info(f"✅ Successfully generated TTS with ElevenLabs ({tts_voice})")
                    yield (sample_rate, audio_array)

                except Exception as elevenlabs_error:
                    logger.warning(f"❌ ElevenLabs TTS failed: {elevenlabs_error}")
                    logger.info("🔄 Falling back to Groq TTS...")

                    # Fallback to Groq TTS
                    temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
                    os.makedirs(temp_dir, exist_ok=True)
                    speech_file_path = temp_dir / "speech.wav"

                    tts_response = try_groq_request(
                        lambda: groq_client.audio.speech.create(
                            model="playai-tts",
                            voice=groq_voice,
                            response_format="wav",
                            input=response_text,
                        )
                    )

                    logger.info(f"💾 Writing TTS response to {speech_file_path}")
                    tts_response.write_to_file(str(speech_file_path))
                    logger.info(f"✅ Successfully generated TTS with Groq fallback ({groq_voice})")

                    # Process the TTS response
                    yield from process_groq_tts(tts_response)

            else:
                # Fallback to Groq TTS
                logger.info(f"🔊 Creating TTS with Groq ({groq_voice})")

                # Create a temporary directory for the speech file
                temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
                os.makedirs(temp_dir, exist_ok=True)
                speech_file_path = temp_dir / "speech.wav"

                tts_response = try_groq_request(
                    lambda: groq_client.audio.speech.create(
                        model="playai-tts",
                        voice=groq_voice,
                        response_format="wav",
                        input=response_text,
                    )
                )

                logger.info(f"💾 Writing TTS response to {speech_file_path}")
                tts_response.write_to_file(str(speech_file_path))
                logger.info(f"✅ Successfully generated TTS with Groq ({groq_voice})")

                # Process the TTS response
                yield from process_groq_tts(tts_response)

        except Exception as tts_error:
            logger.error(f"TTS Error: {tts_error}")
            logger.info("Falling back to simple beep response")

            # Create a simple beep as fallback (only if both TTS methods fail)
            sample_rate = 16000
            duration = 0.2  # seconds
            frequency = 440  # Hz (A4 note)
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            beep = np.sin(2 * np.pi * frequency * t) * 32767
            beep = beep.astype(np.int16).reshape(1, -1)

            # Yield the beep sound
            yield (sample_rate, beep)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def update_tts_settings(provider: str, voice: str = None) -> str:
    """
    Update the TTS provider and voice settings.

    Args:
        provider: The TTS provider to use ('groq' or 'kokoro')
        voice: The voice to use (for Groq TTS only)

    Returns:
        A confirmation message
    """
    global tts_provider, tts_voice

    tts_provider = provider
    if provider == "groq" and voice:
        tts_voice = voice

    return f"TTS settings updated: Provider={provider}, Voice={tts_voice if provider == 'groq' else 'default'}"


def handle_additional_inputs(provider, elevenlabs_voice, groq_voice_option):
    """
    Handle additional inputs from the UI.

    Args:
        provider: The selected TTS provider
        elevenlabs_voice: The selected ElevenLabs voice
        groq_voice_option: The selected Groq voice

    Returns:
        Status message for the UI
    """
    global tts_provider, tts_voice, groq_voice

    # Update global settings
    tts_provider = provider

    if provider == "elevenlabs":
        tts_voice = elevenlabs_voice
        return f"✅ Using ElevenLabs TTS with voice: {tts_voice} (requires subscription)"
    else:
        groq_voice = groq_voice_option
        tts_voice = groq_voice_option  # Update tts_voice for Groq
        return f"✅ Using Groq TTS with voice: {groq_voice} (automatic API rotation)"


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    # Create a more robust handler that won't interrupt Samantha
    handler = ReplyOnPause(
        response,
        algo_options=AlgoOptions(
            speech_threshold=0.5,  # Adjust sensitivity to speech
        ),
    )

    # Configure the handler to be more natural and not interrupt
    handler.min_silence_duration = 0.8  # Wait for 0.8 seconds of silence before responding

    # TTS Provider selection
    tts_provider_dropdown = gr.Dropdown(
        choices=["groq", "elevenlabs"],
        value="groq",
        label="TTS Provider",
        info="Choose the text-to-speech provider (ElevenLabs requires subscription)"
    )

    # ElevenLabs voice selection
    elevenlabs_voice_dropdown = gr.Dropdown(
        choices=[
            "Rachel", "Drew", "Clyde", "Paul", "Domi", "Dave", "Fin", "Sarah",
            "Antoni", "Thomas", "Charlie", "Emily", "Elli", "Callum", "Patrick",
            "Harry", "Liam", "Dorothy", "Josh", "Arnold", "Charlotte", "Alice",
            "Matilda", "James"
        ],
        value="Rachel",
        label="ElevenLabs Voice",
        info="Choose ElevenLabs voice (better quality, generous limits)"
    )

    # Groq voice selection (fallback)
    groq_voice_dropdown = gr.Dropdown(
        choices=[
            "Arista-PlayAI", "Celeste-PlayAI", "Eleanor-PlayAI", "Gail-PlayAI",
            "Jennifer-PlayAI", "Quinn-PlayAI", "Ruby-PlayAI", "Atlas-PlayAI",
            "Cillian-PlayAI", "Mitch-PlayAI"
        ],
        value="Arista-PlayAI",
        label="Groq Voice (Fallback)",
        info="Choose Groq voice (fallback when ElevenLabs fails)"
    )

    # Simple status display
    status_text = gr.Textbox(
        label="Status",
        value="Ready to chat! Using Groq TTS with automatic API key rotation.",
        interactive=False
    )

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        additional_inputs=[tts_provider_dropdown, elevenlabs_voice_dropdown, groq_voice_dropdown],
        additional_outputs=[status_text],
        additional_outputs_handler=handle_additional_inputs,
        ui_args={
            "title": "Samantha - Voice Assistant",
            "subtitle": "Your AI companion",
            "description": "Click the microphone button and start talking to Samantha. She responds in English only using Groq TTS with automatic API key rotation.",
            "article": "Choose between Groq (free, automatic rotation) or ElevenLabs (requires subscription). Start chatting!",
        }
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
