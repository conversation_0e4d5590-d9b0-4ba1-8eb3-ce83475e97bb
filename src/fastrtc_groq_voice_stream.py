import argparse
from typing import Generator, <PERSON><PERSON>

import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

groq_client = Groq()


def generate_speech_from_text(text: str, sample_rate: int = 16000) -> Tuple[int, np.ndarray]:
    """
    Generate speech from text using a simple text-to-speech approach.
    This is a very basic implementation that creates a speech-like pattern.

    Args:
        text: The text to convert to speech
        sample_rate: The sample rate for the audio

    Returns:
        A tuple of (sample_rate, audio_array)
    """
    # Parameters for the speech synthesis
    duration_per_char = 0.05  # seconds per character
    total_duration = max(1.0, len(text) * duration_per_char)  # at least 1 second

    # Create a carrier wave (fundamental frequency for voice)
    fundamental_freq = 150  # Hz - typical for a female voice
    t = np.linspace(0, total_duration, int(sample_rate * total_duration), False)
    carrier = np.sin(2 * np.pi * fundamental_freq * t)

    # Add some variation to make it sound more speech-like
    # This creates amplitude modulation based on the text length
    modulation = 0.5 + 0.5 * np.sin(2 * np.pi * 2 * t)

    # Combine carrier and modulation
    audio = carrier * modulation * 0.7  # 0.7 to avoid clipping

    # Add some noise to make it sound more natural
    noise = np.random.normal(0, 0.05, len(audio))
    audio = audio + noise

    # Convert to int16 format
    audio = (audio * 32767).astype(np.int16).reshape(1, -1)

    return (sample_rate, audio)

def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver audio response.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = groq_client.audio.transcriptions.create(
            file=("audio-file.mp3", audio_to_bytes(audio)),
            model="whisper-large-v3-turbo",
            response_format="text",
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.info("🔊 Generating speech...")
        speech_audio = generate_speech_from_text(response_text)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

        # Yield the speech audio
        yield speech_audio

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    return Stream(
        modality="audio",
        mode="send-receive",
        handler=ReplyOnPause(
            response,
            algo_options=AlgoOptions(
                speech_threshold=0.5,
            ),
        ),
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
