import argparse
import os
import wave
from pathlib import Path
from typing import Generator, Literal, <PERSON>ple

import gradio as gr
import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config
from process_groq_tts import process_groq_tts

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

# Multiple Groq API keys for rotation
GROQ_API_KEYS = [
    os.getenv("GROQ_API_KEY"),
    os.getenv("GROQ_API_KEY_2"),
    os.getenv("GROQ_API_KEY_3"),
    os.getenv("GROQ_API_KEY_4"),
    os.getenv("GROQ_API_KEY_5")
]

# Filter out None values (in case some API keys are not set)
GROQ_API_KEYS = [key for key in GROQ_API_KEYS if key is not None]

if not GROQ_API_KEYS:
    raise ValueError("At least one GROQ_API_KEY must be set in environment variables")

logger.info(f"🔑 Loaded {len(GROQ_API_KEYS)} API key(s) for rotation")

# Current API key index and client
current_api_index = 0
groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])

# TTS configuration
tts_voice = "Arista-PlayAI"  # Default voice for Groq TTS

def rotate_api_key():
    """Rotate to the next available API key"""
    global current_api_index, groq_client
    current_api_index = (current_api_index + 1) % len(GROQ_API_KEYS)
    groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])
    logger.info(f"🔄 Rotated to API key #{current_api_index + 1}")

def try_groq_request(func, *args, **kwargs):
    """Try a Groq request with all available API keys until one succeeds"""
    global current_api_index, groq_client

    for attempt in range(len(GROQ_API_KEYS)):
        try:
            result = func(*args, **kwargs)
            if attempt > 0:  # Only log if we had to rotate
                logger.info(f"✅ Successfully used API key #{current_api_index + 1}")
            return result
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"❌ API key #{current_api_index + 1} failed: {error_msg}")

            # Check if it's a rate limit error
            if "rate_limit_exceeded" in error_msg or "429" in error_msg:
                logger.info(f"⏰ Rate limit hit on API key #{current_api_index + 1}, rotating...")
                rotate_api_key()
            else:
                # For other errors, also try rotating
                logger.info(f"🔄 Error on API key #{current_api_index + 1}, trying next...")
                rotate_api_key()

    # If all API keys failed, raise the last exception
    raise Exception(f"💥 All {len(GROQ_API_KEYS)} API keys failed. Please check your API keys and rate limits.")


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver TTS audio.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = try_groq_request(
            lambda: groq_client.audio.transcriptions.create(
                file=("audio-file.mp3", audio_to_bytes(audio)),
                model="whisper-large-v3-turbo",
                response_format="text",
            )
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.debug("🔊 Generating speech...")
        try:
            # Create a temporary directory for the speech file
            temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
            os.makedirs(temp_dir, exist_ok=True)
            speech_file_path = temp_dir / "speech.wav"

            # Use Groq's TTS with API key rotation
            logger.info(f"🔊 Creating TTS with Groq ({tts_voice})")
            tts_response = try_groq_request(
                lambda: groq_client.audio.speech.create(
                    model="playai-tts",
                    voice=tts_voice,
                    response_format="wav",
                    input=response_text,
                )
            )

            logger.info(f"💾 Writing TTS response to {speech_file_path}")
            tts_response.write_to_file(str(speech_file_path))
            logger.info(f"✅ Successfully generated TTS with Groq ({tts_voice})")

            # Process the TTS response
            yield from process_groq_tts(tts_response)

        except Exception as tts_error:
            logger.error(f"TTS Error: {tts_error}")
            logger.info("Falling back to simple beep response")

            # Create a simple beep as fallback (only if both TTS methods fail)
            sample_rate = 16000
            duration = 0.2  # seconds
            frequency = 440  # Hz (A4 note)
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            beep = np.sin(2 * np.pi * frequency * t) * 32767
            beep = beep.astype(np.int16).reshape(1, -1)

            # Yield the beep sound
            yield (sample_rate, beep)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def update_tts_settings(provider: str, voice: str = None) -> str:
    """
    Update the TTS provider and voice settings.

    Args:
        provider: The TTS provider to use ('groq' or 'kokoro')
        voice: The voice to use (for Groq TTS only)

    Returns:
        A confirmation message
    """
    global tts_provider, tts_voice

    tts_provider = provider
    if provider == "groq" and voice:
        tts_voice = voice

    return f"TTS settings updated: Provider={provider}, Voice={tts_voice if provider == 'groq' else 'default'}"


def handle_additional_inputs(groq_voice):
    """
    Handle additional inputs from the UI.

    Args:
        groq_voice: The selected Groq voice

    Returns:
        Status message for the UI
    """
    global tts_voice

    # Update global voice setting
    tts_voice = groq_voice

    # Return status message for the UI
    return f"Voice settings updated! Using Groq TTS with voice: {tts_voice}"


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    # Create a more robust handler that won't interrupt Samantha
    handler = ReplyOnPause(
        response,
        algo_options=AlgoOptions(
            speech_threshold=0.5,  # Adjust sensitivity to speech
        ),
    )

    # Configure the handler to be more natural and not interrupt
    handler.min_silence_duration = 0.8  # Wait for 0.8 seconds of silence before responding

    # Define UI components for the Stream with improved styling
    # Add a header for voice settings using HTML
    gr.HTML("""
    <div style="margin-bottom: 20px; padding: 15px; border-radius: 12px; background: linear-gradient(to right, rgba(124, 58, 237, 0.05), rgba(236, 72, 153, 0.05)); border: 1px solid rgba(124, 58, 237, 0.1);">
        <h3 style="margin: 0; font-size: 1.2rem; background: linear-gradient(135deg, #7c3aed, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">🎛️ Voice Settings</h3>
    </div>
    """)

    # Create Groq voice dropdown
    groq_voice_dropdown = gr.Dropdown(
        choices=[
            "Arista-PlayAI", "Celeste-PlayAI", "Eleanor-PlayAI", "Gail-PlayAI",
            "Jennifer-PlayAI", "Quinn-PlayAI", "Ruby-PlayAI", "Atlas-PlayAI",
            "Cillian-PlayAI", "Mitch-PlayAI"
        ],
        value="Arista-PlayAI",
        label="🎤 Voice Selection",
        info="Select your preferred voice for Samantha"
    )

    # Add a divider
    gr.HTML("""<div style="height: 1px; background: linear-gradient(to right, transparent, #7c3aed, transparent); margin: 20px 0;"></div>""")

    # Add a welcome message and instructions
    gr.HTML("""
    <div style="text-align: center; margin: 20px 0;">
        <div style="font-size: 4rem; margin-bottom: 20px;">🎙️</div>
        <h2 style="margin-bottom: 15px; font-size: 1.8rem; background: linear-gradient(135deg, #7c3aed, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Start Speaking</h2>
        <p style="color: #6b7280; font-size: 1.1rem; max-width: 500px; margin: 0 auto 20px auto; line-height: 1.6;">
            Click the microphone button below and ask Samantha anything!
        </p>
        <div style="display: inline-block; margin-top: 10px; padding: 10px 20px; background: linear-gradient(to right, rgba(124, 58, 237, 0.1), rgba(236, 72, 153, 0.1)); border-radius: 50px; font-size: 0.9rem; color: #7c3aed;">
            "Hey Samantha, how are you today?"
        </div>
    </div>
    """)

    # Add a status display
    status_text = gr.Textbox(
        label="🔔 Status",
        value="Ready to chat! Click the microphone and start speaking.",
        interactive=False
    )

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        additional_inputs=[groq_voice_dropdown],
        additional_outputs=[status_text],
        additional_outputs_handler=handle_additional_inputs,
        ui_args={
            "title": "Samantha - Voice Assistant",
            "subtitle": "Your AI companion powered by Groq",
            "css": """
                @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

                :root {
                    --primary-color: #7c3aed;
                    --primary-light: #8b5cf6;
                    --secondary-color: #ec4899;
                    --secondary-light: #f472b6;
                    --bg-color: #f5f3ff;
                    --card-bg: #ffffff;
                    --text-color: #1f2937;
                    --text-light: #6b7280;
                    --border-radius: 16px;
                    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                    --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                }

                body {
                    font-family: 'Poppins', sans-serif;
                    background-color: var(--bg-color);
                    color: var(--text-color);
                    line-height: 1.6;
                }

                /* Container styling */
                .gradio-container {
                    max-width: 1000px !important;
                    margin: 0 auto !important;
                }

                /* Header styling */
                h1.gr-text-center {
                    font-size: 3rem !important;
                    font-weight: 700 !important;
                    background: var(--gradient) !important;
                    -webkit-background-clip: text !important;
                    -webkit-text-fill-color: transparent !important;
                    margin-bottom: 0.5rem !important;
                    letter-spacing: -0.025em !important;
                }

                h2.gr-text-center {
                    font-size: 1.5rem !important;
                    font-weight: 500 !important;
                    color: var(--text-light) !important;
                    margin-bottom: 2rem !important;
                }

                /* Main content area */
                .main {
                    background-color: var(--card-bg) !important;
                    border-radius: var(--border-radius) !important;
                    box-shadow: var(--box-shadow) !important;
                    padding: 2rem !important;
                    margin-bottom: 2rem !important;
                }

                /* Microphone button styling */
                .record-button {
                    background: var(--gradient) !important;
                    border: none !important;
                    color: white !important;
                    font-weight: 600 !important;
                    font-size: 1.1rem !important;
                    padding: 0.75rem 1.5rem !important;
                    border-radius: 50px !important;
                    box-shadow: 0 4px 6px rgba(124, 58, 237, 0.3) !important;
                    transition: all 0.3s ease !important;
                    transform: translateY(0) !important;
                }

                .record-button:hover {
                    transform: translateY(-3px) !important;
                    box-shadow: 0 6px 12px rgba(124, 58, 237, 0.4) !important;
                }

                .record-button:active {
                    transform: translateY(1px) !important;
                }

                /* Dropdown styling */
                .gr-dropdown {
                    border: 2px solid #e5e7eb !important;
                    border-radius: 12px !important;
                    padding: 0.5rem !important;
                    transition: all 0.3s ease !important;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
                }

                .gr-dropdown:hover, .gr-dropdown:focus {
                    border-color: var(--primary-light) !important;
                    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2) !important;
                }

                /* Label styling */
                label.gr-label {
                    font-weight: 600 !important;
                    color: var(--primary-color) !important;
                    margin-bottom: 0.5rem !important;
                    font-size: 1rem !important;
                }

                /* Status box styling */
                .gr-input.status-box {
                    background-color: #f8f9fa !important;
                    border-left: 4px solid var(--secondary-color) !important;
                    padding: 1rem !important;
                    font-size: 0.95rem !important;
                    color: var(--text-color) !important;
                    border-radius: 8px !important;
                }

                /* Voice options section */
                .voice-options-container {
                    background: linear-gradient(to right, rgba(124, 58, 237, 0.05), rgba(236, 72, 153, 0.05)) !important;
                    border-radius: 12px !important;
                    padding: 1.5rem !important;
                    margin-bottom: 1.5rem !important;
                    border: 1px solid rgba(124, 58, 237, 0.1) !important;
                }

                /* Animations */
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                .gradio-container {
                    animation: fadeIn 0.6s ease-out;
                }

                /* Custom elements */
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, var(--primary-light), transparent);
                    margin: 1.5rem 0;
                }

                /* Footer styling */
                .footer {
                    text-align: center;
                    color: var(--text-light);
                    font-size: 0.9rem;
                    margin-top: 2rem;
                    padding-top: 1rem;
                    border-top: 1px solid #e5e7eb;
                }

                /* Responsive adjustments */
                @media (max-width: 768px) {
                    h1.gr-text-center {
                        font-size: 2.5rem !important;
                    }

                    .main {
                        padding: 1.5rem !important;
                    }
                }

                /* Audio visualization */
                .audio-viz {
                    background: linear-gradient(to right, var(--primary-light), var(--secondary-light)) !important;
                    height: 40px !important;
                    border-radius: 20px !important;
                    overflow: hidden !important;
                }
            """,
            "description": """
            <div style="text-align: center; margin-bottom: 30px; padding: 30px; background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%); color: white; border-radius: 20px; box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.5), 0 8px 10px -6px rgba(124, 58, 237, 0.3);">
                <h3 style="margin-bottom: 15px; font-size: 2rem; font-weight: 700;">✨ Meet Samantha, Your AI Assistant</h3>
                <p style="font-size: 1.2rem; margin-bottom: 15px; opacity: 0.9;">Click the microphone button and start a conversation!</p>
                <p style="font-size: 1rem; opacity: 0.8;">Samantha responds naturally in English and won't interrupt herself when you speak.</p>
                <div style="margin-top: 20px; display: inline-block; padding: 10px 20px; background-color: rgba(255, 255, 255, 0.2); border-radius: 50px; font-size: 0.9rem;">
                    <span style="margin-right: 8px;">💬</span> "How can I help you today?"
                </div>
            </div>

            <div style="background: linear-gradient(to right, rgba(124, 58, 237, 0.05), rgba(236, 72, 153, 0.05)); border-radius: 16px; padding: 25px; margin-bottom: 30px; border: 1px solid rgba(124, 58, 237, 0.1); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);">
                <h4 style="margin-top: 0; color: #7c3aed; font-size: 1.3rem; display: flex; align-items: center; margin-bottom: 15px;">
                    <span style="background: linear-gradient(135deg, #7c3aed, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-size: 1.5rem; margin-right: 10px;">🎙️</span>
                    Voice Selection
                </h4>
                <p style="font-size: 1.05rem; line-height: 1.6; margin-bottom: 15px;">Choose from a variety of high-quality <b style="color: #7c3aed;">Groq TTS voices</b> to personalize Samantha's speaking style. Multiple API keys are automatically rotated for continuous service.</p>
                <div style="background-color: rgba(255, 255, 255, 0.7); padding: 15px; border-radius: 12px; border-left: 4px solid #ec4899; font-size: 0.95rem; color: #4b5563;">
                    <b style="color: #7c3aed;">Features:</b> Automatic API key rotation, English-only responses, and continuous service even during rate limits. Groq TTS requires terms acceptance in the <a href="https://console.groq.com/playground?model=playai-tts" target="_blank" style="color: #7c3aed; text-decoration: none; border-bottom: 1px dotted #7c3aed;">Groq Console</a>.
                </div>
            </div>
            """,
            "article": """
            <div style="margin-top: 40px; padding: 30px; background: linear-gradient(to right, rgba(124, 58, 237, 0.03), rgba(236, 72, 153, 0.03)); border-radius: 20px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05); border: 1px solid rgba(124, 58, 237, 0.08);">
                <h4 style="color: #7c3aed; margin-bottom: 20px; font-size: 1.4rem; text-align: center; background: linear-gradient(135deg, #7c3aed, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">💡 Tips for Best Experience</h4>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 25px;">
                    <div style="background-color: white; padding: 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); border: 1px solid rgba(124, 58, 237, 0.1);">
                        <div style="font-size: 2rem; margin-bottom: 15px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">🎯</div>
                        <h5 style="margin-top: 0; font-size: 1.1rem; color: #1f2937; margin-bottom: 10px;">Speak Naturally</h5>
                        <p style="color: #6b7280; font-size: 0.95rem; line-height: 1.5;">Speak clearly at a normal pace as you would to another person.</p>
                    </div>

                    <div style="background-color: white; padding: 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); border: 1px solid rgba(124, 58, 237, 0.1);">
                        <div style="font-size: 2rem; margin-bottom: 15px; background: linear-gradient(135deg, #8b5cf6, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">⏱️</div>
                        <h5 style="margin-top: 0; font-size: 1.1rem; color: #1f2937; margin-bottom: 10px;">Be Patient</h5>
                        <p style="color: #6b7280; font-size: 0.95rem; line-height: 1.5;">Allow Samantha to finish her response before asking follow-up questions.</p>
                    </div>

                    <div style="background-color: white; padding: 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); border: 1px solid rgba(124, 58, 237, 0.1);">
                        <div style="font-size: 2rem; margin-bottom: 15px; background: linear-gradient(135deg, #ec4899, #f472b6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">🔊</div>
                        <h5 style="margin-top: 0; font-size: 1.1rem; color: #1f2937; margin-bottom: 10px;">Explore Voices</h5>
                        <p style="color: #6b7280; font-size: 0.95rem; line-height: 1.5;">Try different voices to find your favorite Samantha personality.</p>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; font-size: 0.9rem;">Powered by <span style="color: #7c3aed; font-weight: 500;">Groq LLM</span> and <span style="color: #ec4899; font-weight: 500;">FastRTC</span> | Created with ❤️</p>
            </div>
            """,
        }
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
