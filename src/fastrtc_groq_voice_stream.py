import argparse
import os
from pathlib import Path
from typing import Generator, <PERSON><PERSON>

import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config
from process_groq_tts import process_groq_tts

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

groq_client = Groq()


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver TTS audio.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = groq_client.audio.transcriptions.create(
            file=("audio-file.mp3", audio_to_bytes(audio)),
            model="whisper-large-v3-turbo",
            response_format="text",
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.debug("🔊 Generating speech...")
        try:
            # Create a temporary directory for the speech file
            temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
            os.makedirs(temp_dir, exist_ok=True)
            speech_file_path = temp_dir / "speech.wav"

            # Try using Groq's TTS with Arista-PlayAI voice
            logger.info("Creating TTS with Arista-PlayAI voice")
            tts_response = groq_client.audio.speech.create(
                model="playai-tts",
                voice="Arista-PlayAI",
                response_format="wav",
                input=response_text,
            )

            logger.info(f"Writing TTS response to {speech_file_path}")
            # Use the write_to_file method instead of stream_to_file
            tts_response.write_to_file(str(speech_file_path))
            logger.info("Successfully generated TTS with Arista-PlayAI voice")

            # Process the TTS response
            yield from process_groq_tts(tts_response)

        except Exception as tts_error:
            logger.error(f"TTS Error: {tts_error}")
            logger.info("Falling back to simple beep response")

            # Create a simple beep as fallback
            sample_rate = 16000
            duration = 0.2  # seconds
            frequency = 440  # Hz (A4 note)
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            beep = np.sin(2 * np.pi * frequency * t) * 32767
            beep = beep.astype(np.int16).reshape(1, -1)

            # Yield the beep sound
            yield (sample_rate, beep)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    return Stream(
        modality="audio",
        mode="send-receive",
        handler=ReplyOnPause(
            response,
            algo_options=AlgoOptions(
                speech_threshold=0.5,
                # Allow Samantha to finish speaking even if user starts talking
                allow_interruption=False,
                # Add a small delay before responding to make it more natural
                min_pause_duration=0.8,
                # Ensure Samantha can complete her responses
                max_response_duration=30.0,
            ),
        ),
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
