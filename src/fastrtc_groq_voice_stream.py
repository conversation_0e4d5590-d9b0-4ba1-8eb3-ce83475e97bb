import argparse
from typing import Generator, <PERSON><PERSON>

import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

groq_client = Groq()


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver text response.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback or text response
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = groq_client.audio.transcriptions.create(
            file=("audio-file.mp3", audio_to_bytes(audio)),
            model="whisper-large-v3-turbo",
            response_format="text",
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Create a simple text response
        logger.info("Using text-only response mode")

        # Create a simple sine wave beep to indicate response is ready
        sample_rate = 16000
        duration = 0.2  # seconds
        frequency = 440  # Hz (A4 note)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        beep = np.sin(2 * np.pi * frequency * t) * 32767
        beep = beep.astype(np.int16).reshape(1, -1)

        # Yield the beep sound
        yield (sample_rate, beep)

        # Log the text response for the user to read
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    return Stream(
        modality="audio",
        mode="send-receive",
        handler=ReplyOnPause(
            response,
            algo_options=AlgoOptions(
                speech_threshold=0.5,
            ),
        ),
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
