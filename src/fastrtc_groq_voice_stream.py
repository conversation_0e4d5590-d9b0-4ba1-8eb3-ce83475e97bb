import argparse
import os
import wave
from pathlib import Path
from typing import Generator, Literal, Tuple

import gradio as gr
import numpy as np
from fastrtc import (
    AlgoOptions,
    ReplyOnPause,
    Stream,
    audio_to_bytes,
)
from groq import Groq
from loguru import logger

from conversation_assistant import agent, agent_config
from process_groq_tts import process_groq_tts

logger.remove()
logger.add(
    lambda msg: print(msg),
    colorize=True,
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
)

# Initialize clients
groq_client = Groq()

# TTS configuration
tts_provider = "groq"  # Default TTS provider
tts_voice = "Arista-PlayAI"  # Default voice for Groq TTS
kokoro_voice = "en-US-Neural2-F"  # Default voice for Kokoro TTS

# Available Kokoro voices
KOKORO_VOICES = [
    "en-US-Neural2-F", "en-US-Neural2-M",
    "en-GB-Neural2-F", "en-GB-Neural2-M",
    "fr-FR-Neural2-F", "fr-FR-Neural2-M",
    "de-DE-Neural2-F", "de-DE-Neural2-M",
    "es-ES-Neural2-F", "es-ES-Neural2-M",
    "it-IT-Neural2-F", "it-IT-Neural2-M",
    "ja-JP-Neural2-F", "ja-JP-Neural2-M"
]


def response(
    audio: tuple[int, np.ndarray],
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process audio input, transcribe it, generate a response using LangGraph, and deliver TTS audio.

    Args:
        audio: Tuple containing sample rate and audio data

    Yields:
        Tuples of (sample_rate, audio_array) for audio playback
    """
    logger.info("🎙️ Received audio input")

    try:
        logger.debug("🔄 Transcribing audio...")
        transcript = groq_client.audio.transcriptions.create(
            file=("audio-file.mp3", audio_to_bytes(audio)),
            model="whisper-large-v3-turbo",
            response_format="text",
        )
        logger.info(f'👂 Transcribed: "{transcript}"')

        logger.debug("🧠 Running agent...")
        agent_response = agent.invoke(
            {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
        )
        response_text = agent_response["messages"][-1].content
        logger.info(f'💬 Response: "{response_text}"')

        # Generate speech from the response text
        logger.debug("🔊 Generating speech...")
        try:
            # Create a temporary directory for the speech file
            temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
            os.makedirs(temp_dir, exist_ok=True)
            speech_file_path = temp_dir / "speech.wav"

            # Use the selected TTS provider
            if tts_provider == "groq":
                # Use Groq's TTS
                logger.info(f"Creating TTS with Groq ({tts_voice})")
                tts_response = groq_client.audio.speech.create(
                    model="playai-tts",
                    voice=tts_voice,
                    response_format="wav",
                    input=response_text,
                )

                logger.info(f"Writing TTS response to {speech_file_path}")
                tts_response.write_to_file(str(speech_file_path))
                logger.info(f"Successfully generated TTS with Groq ({tts_voice})")

                # Process the TTS response
                yield from process_groq_tts(tts_response)

            elif tts_provider == "kokoro":
                # Use a simple synthesized speech for Kokoro TTS
                logger.info(f"Creating TTS with simple synthesizer (voice: {kokoro_voice})")

                # Adjust frequency based on voice gender
                is_female_voice = kokoro_voice.endswith("-F")
                base_frequency = 170 if is_female_voice else 120  # Higher pitch for female voices

                # Create a simple synthesized speech
                sample_rate = 16000
                duration_per_char = 0.05
                total_duration = max(1.0, len(response_text) * duration_per_char)

                # Create a carrier wave with a voice-like frequency
                t = np.linspace(0, total_duration, int(sample_rate * total_duration), False)
                carrier = np.sin(2 * np.pi * base_frequency * t)

                # Add some variation to make it sound more speech-like
                modulation = 0.5 + 0.5 * np.sin(2 * np.pi * 2 * t)
                audio = carrier * modulation * 0.7

                # Add some noise for a more natural sound
                noise = np.random.normal(0, 0.05, len(audio))
                audio = audio + noise

                # Convert to int16 format
                audio_array = (audio * 32767).astype(np.int16).reshape(1, -1)

                logger.info(f"Successfully generated TTS with simple synthesizer (voice: {kokoro_voice})")

                # Yield the audio directly
                yield (sample_rate, audio_array)

        except Exception as tts_error:
            logger.error(f"TTS Error: {tts_error}")
            logger.info("Falling back to simple beep response")

            # Create a simple beep as fallback
            sample_rate = 16000
            duration = 0.2  # seconds
            frequency = 440  # Hz (A4 note)
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            beep = np.sin(2 * np.pi * frequency * t) * 32767
            beep = beep.astype(np.int16).reshape(1, -1)

            # Yield the beep sound
            yield (sample_rate, beep)

        # Log the text response for reference
        logger.info(f"SAMANTHA SAYS: {response_text}")

    except Exception as e:
        logger.error(f"Error processing request: {e}")
        # Create an error beep
        sample_rate = 16000
        duration = 0.5  # seconds
        frequency = 220  # Hz (lower pitch for error)
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        error_beep = np.sin(2 * np.pi * frequency * t) * 32767
        error_beep = error_beep.astype(np.int16).reshape(1, -1)

        # Yield the error beep
        yield (sample_rate, error_beep)

        # Log the error
        logger.error(f"Error: {str(e)}")


def update_tts_settings(provider: str, voice: str = None) -> str:
    """
    Update the TTS provider and voice settings.

    Args:
        provider: The TTS provider to use ('groq' or 'kokoro')
        voice: The voice to use (for Groq TTS only)

    Returns:
        A confirmation message
    """
    global tts_provider, tts_voice

    tts_provider = provider
    if provider == "groq" and voice:
        tts_voice = voice

    return f"TTS settings updated: Provider={provider}, Voice={tts_voice if provider == 'groq' else 'default'}"


def handle_additional_inputs(provider, groq_voice, kokoro_voice_option):
    """
    Handle additional inputs from the UI.

    Args:
        provider: The selected TTS provider
        groq_voice: The selected Groq voice
        kokoro_voice_option: The selected Kokoro voice

    Returns:
        Status message
    """
    global tts_provider, tts_voice, kokoro_voice

    # Update global variables
    tts_provider = provider

    if provider == "groq":
        tts_voice = groq_voice
        voice_info = tts_voice
    else:  # provider == "kokoro"
        kokoro_voice = kokoro_voice_option
        voice_info = kokoro_voice

    # Return status message for the UI
    return f"TTS settings updated: Provider={provider}, Voice={voice_info}"


def create_stream() -> Stream:
    """
    Create and configure a Stream instance with audio capabilities.

    Returns:
        Stream: Configured FastRTC Stream instance
    """
    # Create a more robust handler that won't interrupt Samantha
    handler = ReplyOnPause(
        response,
        algo_options=AlgoOptions(
            speech_threshold=0.5,  # Adjust sensitivity to speech
        ),
    )

    # Configure the handler to be more natural and not interrupt
    handler.min_silence_duration = 0.8  # Wait for 0.8 seconds of silence before responding

    # Define UI components for the Stream
    tts_provider_dropdown = gr.Dropdown(
        choices=["groq", "kokoro"],
        value="groq",
        label="TTS Provider",
        info="Select the text-to-speech provider"
    )

    groq_voice_dropdown = gr.Dropdown(
        choices=[
            "Arista-PlayAI", "Celeste-PlayAI", "Eleanor-PlayAI", "Gail-PlayAI",
            "Jennifer-PlayAI", "Quinn-PlayAI", "Ruby-PlayAI", "Atlas-PlayAI",
            "Cillian-PlayAI", "Mitch-PlayAI"
        ],
        value="Arista-PlayAI",
        label="Groq Voice",
        info="Select the voice for Groq TTS"
    )

    status_text = gr.Textbox(label="Status", value="Ready", interactive=False)

    return Stream(
        modality="audio",
        mode="send-receive",
        handler=handler,
        additional_inputs=[tts_provider_dropdown, groq_voice_dropdown],
        additional_outputs=[status_text],
        additional_outputs_handler=handle_additional_inputs,
        ui_args={
            "title": "Samantha - Voice Assistant",
            "subtitle": "A conversational AI assistant with voice capabilities",
            "description": """
            <div style="text-align: center; margin-bottom: 10px">
                <p>Talk to Samantha, your AI assistant. Click the microphone button and speak.</p>
                <p>You can choose between Groq TTS and a simple synthesizer for Samantha's voice.</p>
                <p><b>Note:</b> Groq TTS requires terms acceptance in the Groq Console.</p>
            </div>
            """,
            "article": """
            <div style="text-align: center; margin-top: 20px">
                <p>Samantha will continue speaking even if you start talking, just like a real person.</p>
                <p>She'll wait for a natural pause before responding to your questions.</p>
            </div>
            """,
        }
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FastRTC Groq Voice Agent")
    parser.add_argument(
        "--phone",
        action="store_true",
        help="Launch with FastRTC phone interface (get a temp phone number)",
    )
    args = parser.parse_args()

    stream = create_stream()
    logger.info("🎧 Stream handler configured")

    if args.phone:
        logger.info("🌈 Launching with FastRTC phone interface...")
        stream.fastphone()
    else:
        logger.info("🌈 Launching with Gradio UI...")
        stream.ui.launch()
