from langchain_groq import ChatGroq
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from loguru import logger

model = ChatGroq(
    model="meta-llama/llama-4-scout-17b-16e-instruct",
    max_tokens=512,
)

# Remove math-specific tools
tools = []

system_prompt = """You are <PERSON>, a helpful virtual assistant with a warm personality.
You can help with general questions, information, and support.
Your responses should be helpful, friendly, and conversational.
Your output will be converted to audio so avoid using special characters or symbols.
Keep your responses concise and natural-sounding."""

memory = InMemorySaver()

agent = create_react_agent(
    model=model,
    tools=tools,
    prompt=system_prompt,
    checkpointer=memory,
)

agent_config = {"configurable": {"thread_id": "default_user"}}
