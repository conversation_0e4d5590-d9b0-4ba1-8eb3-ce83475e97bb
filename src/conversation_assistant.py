from langchain_groq import ChatGroq
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from loguru import logger

# Initialize the Groq model
model = ChatGroq(
    model="meta-llama/llama-4-scout-17b-16e-instruct",
    max_tokens=512,
)

# No specialized tools needed for a general conversational assistant
tools = []

# Enhanced system prompt for a conversational assistant
system_prompt = """You are <PERSON>, a helpful virtual assistant with a warm personality.
You can help with general questions, information, and support.
Your responses should be helpful, friendly, and conversational.
Your output will be converted to audio so avoid using special characters or symbols.
Keep your responses concise and natural-sounding, ideally 1-3 sentences.
When appropriate, ask follow-up questions to keep the conversation flowing naturally.
If you don't know something, be honest about it rather than making up information."""

# Set up memory to maintain conversation context
memory = InMemorySaver()

# Create the conversational agent
agent = create_react_agent(
    model=model,
    tools=tools,
    prompt=system_prompt,
    checkpointer=memory,
)

# Configure the agent with a default user thread
agent_config = {"configurable": {"thread_id": "default_user"}}
