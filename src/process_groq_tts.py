import os
import tempfile
import wave
from pathlib import Path
from typing import Any, Generator, <PERSON><PERSON>

import numpy as np


def process_groq_tts(
    tts_response: Any,
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process Groq TTS response into a complete audio segment.

    This function reads the entire audio file and yields it as one piece.

    Args:
        tts_response: Groq TTS API response object

    Yields:
        A single tuple of (sample_rate, audio_array) for audio playback
    """
    # Create a temporary file with .wav extension
    temp_dir = tempfile.gettempdir()
    temp_file_path = Path(temp_dir) / "speech.wav"

    try:
        # Stream the response to the file
        tts_response.stream_to_file(temp_file_path)

        # Read the audio file
        with wave.open(str(temp_file_path), "rb") as wf:
            sample_rate = wf.getframerate()
            n_frames = wf.getnframes()
            audio_data = wf.readframes(n_frames)

        # Convert to numpy array
        audio_array = np.frombuffer(audio_data, dtype=np.int16).reshape(1, -1)
        yield (sample_rate, audio_array)
    finally:
        # Clean up the temporary file
        if temp_file_path.exists():
            os.remove(temp_file_path)
