#!/usr/bin/env python3
"""
Modern Flask Voice Assistant with FastRTC Integration
Combines Flask UI with FastRTC's seamless conversation flow
"""

import os
import io
import base64
import numpy as np
from pathlib import Path
from typing import Generator, Tuple

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
from elevenlabs import ElevenLabs, VoiceSettings
from groq import Groq
from fastrtc import audio_to_bytes
import logging

from conversation_assistant import agent, agent_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize clients
elevenlabs_client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))

# Multiple Groq API keys for rotation
GROQ_API_KEYS = [
    os.getenv("GROQ_API_KEY"),
    os.getenv("GROQ_API_KEY_2"),
]

# Filter out None values
GROQ_API_KEYS = [key for key in GROQ_API_KEYS if key is not None]

if not GROQ_API_KEYS:
    raise ValueError("At least one GROQ_API_KEY must be set in environment variables")

logger.info(f"🔑 Loaded {len(GROQ_API_KEYS)} API key(s) for rotation")

# Current API key index and client
current_api_index = 0
groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])

# TTS configuration
tts_voice = "21m00Tcm4TlvDq8ikWAM"  # Rachel's voice ID

def rotate_api_key():
    """Rotate to the next available API key"""
    global current_api_index, groq_client
    current_api_index = (current_api_index + 1) % len(GROQ_API_KEYS)
    groq_client = Groq(api_key=GROQ_API_KEYS[current_api_index])
    logger.info(f"🔄 Rotated to API key #{current_api_index + 1}")

def try_groq_request(func, *args, **kwargs):
    """Try a Groq request with all available API keys until one succeeds"""
    global current_api_index, groq_client

    for attempt in range(len(GROQ_API_KEYS)):
        try:
            result = func(*args, **kwargs)
            if attempt > 0:
                logger.info(f"✅ Successfully used API key #{current_api_index + 1}")
            return result
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"❌ API key #{current_api_index + 1} failed: {error_msg}")

            if "rate_limit_exceeded" in error_msg or "429" in error_msg:
                logger.info(f"⏰ Rate limit hit on API key #{current_api_index + 1}, rotating...")
                rotate_api_key()
            else:
                logger.info(f"🔄 Error on API key #{current_api_index + 1}, trying next...")
                rotate_api_key()

    raise Exception(f"💥 All {len(GROQ_API_KEYS)} API keys failed.")

class VoiceAssistant:
    """Voice Assistant with FastRTC-style processing"""

    def __init__(self):
        self.conversation_active = False
        logger.info("🎧 Voice Assistant configured")

    def process_audio(self, audio: tuple[int, np.ndarray]) -> Generator[Tuple[int, np.ndarray], None, None]:
        """Process audio using FastRTC logic with ElevenLabs TTS"""
        logger.info("🎙️ Processing audio with FastRTC logic")

        try:
            # Transcribe using Groq
            logger.debug("🔄 Transcribing audio...")
            transcript = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=("audio-file.mp3", audio_to_bytes(audio)),
                    model="whisper-large-v3-turbo",
                    response_format="text",
                )
            )
            logger.info(f'👂 Transcribed: "{transcript}"')

            # Generate response using LangGraph
            logger.debug("🧠 Running agent...")
            agent_response = agent.invoke(
                {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
            )
            response_text = agent_response["messages"][-1].content
            logger.info(f'💬 Response: "{response_text}"')

            # Generate speech using ElevenLabs
            logger.info(f"🔊 Creating TTS with ElevenLabs (Rachel)")

            audio_generator = elevenlabs_client.text_to_speech.convert(
                voice_id=tts_voice,
                text=response_text,
                voice_settings=VoiceSettings(
                    stability=0.5,
                    similarity_boost=0.75,
                    style=0.0,
                    use_speaker_boost=True
                )
            )

            # Convert generator to bytes
            audio_bytes = b"".join(audio_generator)

            # Convert to numpy array for FastRTC
            audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
            sample_rate = 22050  # ElevenLabs default sample rate

            # Reshape for stereo if needed
            if len(audio_array.shape) == 1:
                audio_array = audio_array.reshape(1, -1)

            logger.info(f"✅ Successfully generated TTS with ElevenLabs")

            # Emit to frontend via WebSocket
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            socketio.emit('response', {
                'text': response_text,
                'audio': audio_base64,
                'transcript': transcript
            })

            yield (sample_rate, audio_array)

        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            socketio.emit('error', {'message': f'Error: {str(e)}'})

# Create voice assistant instance
voice_assistant = VoiceAssistant()

@app.route('/')
def index():
    """Render the main page"""
    return render_template('modern_index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected")
    emit('status', {'message': 'Connected to Samantha Voice Assistant'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected")

@socketio.on('start_conversation')
def handle_start_conversation():
    """Start FastRTC conversation"""
    try:
        voice_assistant.conversation_active = True
        logger.info("🎤 Starting FastRTC conversation")
        emit('status', {'message': 'Conversation started! Speak naturally...'})
        emit('conversation_started', {'success': True})
    except Exception as e:
        logger.error(f"Error starting conversation: {e}")
        emit('error', {'message': f'Error starting conversation: {str(e)}'})

@socketio.on('stop_conversation')
def handle_stop_conversation():
    """Stop FastRTC conversation"""
    try:
        voice_assistant.conversation_active = False
        logger.info("🛑 Stopping FastRTC conversation")
        emit('status', {'message': 'Conversation stopped'})
        emit('conversation_stopped', {'success': True})
    except Exception as e:
        logger.error(f"Error stopping conversation: {e}")
        emit('error', {'message': f'Error stopping conversation: {str(e)}'})

@socketio.on('process_audio')
def handle_audio(data):
    """Process audio with proper format conversion"""
    try:
        if not voice_assistant.conversation_active:
            return

        audio_data = data.get('audio')
        if not audio_data:
            emit('error', {'message': 'No audio data received'})
            return

        logger.info("Processing audio...")

        # Decode base64 audio data
        audio_bytes = base64.b64decode(audio_data)
        logger.info(f"Audio bytes length: {len(audio_bytes)}")

        # Save audio to temporary file for proper processing
        import tempfile

        with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name

        try:
            # Use the same audio processing as the working Gradio version
            # Create a file-like object for Groq transcription
            audio_file = io.BytesIO(audio_bytes)
            audio_file.name = "audio.webm"  # Give it a name for Groq

            # Process directly with Groq transcription (skip numpy conversion)
            logger.info("Transcribing audio with Groq...")
            transcript = try_groq_request(
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",
                    response_format="text",
                )
            )
            logger.info(f'👂 Transcribed: "{transcript}"')

            # Generate response using LangGraph
            logger.debug("🧠 Running agent...")
            agent_response = agent.invoke(
                {"messages": [{"role": "user", "content": transcript}]}, config=agent_config
            )
            response_text = agent_response["messages"][-1].content
            logger.info(f'💬 Response: "{response_text}"')

            # Generate speech using ElevenLabs
            logger.info(f"🔊 Creating TTS with ElevenLabs (Rachel)")

            audio_generator = elevenlabs_client.text_to_speech.convert(
                voice_id=tts_voice,
                text=response_text,
                voice_settings=VoiceSettings(
                    stability=0.5,
                    similarity_boost=0.75,
                    style=0.0,
                    use_speaker_boost=True
                )
            )

            # Convert generator to bytes
            tts_audio_bytes = b"".join(audio_generator)

            logger.info(f"✅ Successfully generated TTS with ElevenLabs")

            # Emit to frontend via WebSocket
            audio_base64 = base64.b64encode(tts_audio_bytes).decode('utf-8')
            emit('response', {
                'text': response_text,
                'audio': audio_base64,
                'transcript': transcript
            })

        finally:
            # Clean up temporary file
            import os
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logger.error(f"Error processing audio: {e}")
        emit('error', {'message': f'Error: {str(e)}'})

if __name__ == '__main__':
    logger.info("Starting Samantha Voice Assistant with FastRTC...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5001)
