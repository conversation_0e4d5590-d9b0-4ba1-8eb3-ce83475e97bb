# Samantha - Modern Voice Assistant

A sophisticated AI voice assistant with seamless conversation flow, combining modern Flask UI with FastRTC-inspired automatic conversation handling.

## 🎯 Overview

Samantha is a voice-first AI assistant that provides natural, continuous conversation experiences. Users can speak naturally without repeatedly clicking buttons, creating a human-like interaction flow.

## ✨ Key Features

- **🎙️ Seamless Conversation Flow**: Automatic listening after each response
- **🎨 Modern UI**: Beautiful, responsive Flask interface with dark theme
- **🔊 High-Quality TTS**: Eleven<PERSON>abs Rachel voice for natural speech
- **🧠 Smart AI**: Groq LLM with conversation memory
- **🔄 Auto-Recovery**: Graceful error handling and conversation continuation
- **📱 Responsive Design**: Works on desktop and mobile devices

## 🏗️ Architecture

### Frontend (Client-Side)
```
Browser → MediaRecorder → WebSocket → Real-time UI Updates
```

### Backend (Server-Side)
```
Flask + SocketIO → Audio Processing → Groq APIs → ElevenLabs TTS → Response
```

## 🛠️ Tech Stack

### **Frontend Technologies**

| Technology | Purpose | Why Chosen |
|------------|---------|------------|
| **HTML5** | Structure & MediaRecorder API | Native audio recording capabilities |
| **CSS3** | Modern styling & animations | Custom dark theme, responsive design |
| **JavaScript ES6** | Client-side logic & WebSocket | Real-time communication, audio handling |
| **Socket.IO Client** | Real-time communication | Bidirectional WebSocket communication |

### **Backend Technologies**

| Technology | Purpose | Why Chosen |
|------------|---------|------------|
| **Flask** | Web framework | Lightweight, flexible Python web server |
| **Flask-SocketIO** | WebSocket server | Real-time bidirectional communication |
| **Python 3.12** | Backend language | Rich ecosystem for AI/ML libraries |

### **AI & Audio Services**

| Service | Purpose | Why Chosen |
|---------|---------|------------|
| **Groq API** | Speech-to-text & LLM | Fast inference, multiple API key rotation |
| **ElevenLabs** | Text-to-speech | High-quality, natural voice synthesis |
| **LangGraph** | Conversation management | Structured conversation flow |

## 🔄 How It Works

### **Complete System Flow - PlantUML Sequence Diagram**

```plantuml
@startuml Samantha Voice Assistant Flow
!theme plain
skinparam backgroundColor #0f0f23
skinparam defaultFontColor #ffffff
skinparam sequenceArrowColor #7c3aed
skinparam sequenceLifeLineBackgroundColor #1a1a2e
skinparam sequenceLifeLineBorderColor #7c3aed
skinparam sequenceParticipantBackgroundColor #1a1a2e
skinparam sequenceParticipantBorderColor #7c3aed
skinparam noteBackgroundColor #2d2d44
skinparam noteBorderColor #7c3aed

actor User as U
participant "Browser\n(Frontend)" as B
participant "Flask Server\n(Backend)" as F
participant "Groq API\n(Transcription)" as GT
participant "Groq API\n(LLM)" as GL
participant "LangGraph\n(Agent)" as LG
participant "ElevenLabs\n(TTS)" as EL

== Initialization ==
U -> B: Open browser to localhost:5001
B -> F: HTTP GET /
F -> B: Return modern_index.html + CSS + JS
B -> F: WebSocket connection (Socket.IO)
F -> B: Connection established
note over B: Request microphone permissions
B -> U: Microphone permission dialog
U -> B: Grant permissions

== Start Conversation ==
U -> B: Click "Start Conversation"
B -> F: emit('start_conversation')
F -> F: Set conversation_active = True
F -> B: emit('conversation_started', {success: true})
B -> B: Update UI to listening state
B -> B: Start MediaRecorder with optimized settings

== Audio Recording Loop ==
loop Continuous Conversation
    note over B: Record audio (max 5 seconds)
    B -> B: MediaRecorder captures audio\n(16kHz, mono, noise suppression)
    B -> B: Convert to base64 (chunked processing)
    B -> F: emit('process_audio', {audio: base64_data})

    == Server-Side Processing ==
    F -> F: Decode base64 audio
    F -> F: Create BytesIO stream
    note over F: Direct file processing\n(bypasses numpy conversion)

    == Transcription ==
    F -> GT: audio.transcriptions.create()\nfile=BytesIO, model="whisper-large-v3-turbo"
    alt API Success
        GT -> F: Return transcript text
    else API Failure (Rate Limit)
        GT -> F: Error response
        F -> F: Rotate to backup API key
        F -> GT: Retry with new key
        GT -> F: Return transcript text
    end

    == AI Response Generation ==
    F -> LG: agent.invoke({messages: [transcript]})
    LG -> GL: Chat completion request\nmodel="llama-3.1-70b-versatile"
    alt API Success
        GL -> LG: Return AI response
        LG -> F: Return structured response
    else API Failure
        GL -> LG: Error response
        LG -> F: Rotate API key and retry
        F -> LG: Retry request
        LG -> GL: Retry with new key
        GL -> LG: Return AI response
        LG -> F: Return structured response
    end

    == Text-to-Speech ==
    F -> EL: text_to_speech.convert()\nvoice_id="21m00Tcm4TlvDq8ikWAM" (Rachel)
    alt TTS Success
        EL -> F: Return audio stream
        F -> F: Convert to base64
    else TTS Failure
        EL -> F: Error response
        F -> F: Log error, continue without audio
    end

    == Response Delivery ==
    F -> B: emit('response', {\n  text: response_text,\n  audio: base64_audio,\n  transcript: original_transcript\n})

    == Frontend Response Handling ==
    B -> B: Add user message to chat
    B -> B: Add assistant message to chat
    B -> B: Create audio blob from base64
    B -> B: Play audio through HTML5 audio element
    B -> U: Display conversation + play Rachel's voice

    == Automatic Continuation ==
    B -> B: Audio 'ended' event listener
    B -> B: Wait 1 second delay
    B -> B: Auto-start MediaRecorder again
    note over B: Seamless conversation continues\nwithout user clicking
end

== Stop Conversation ==
U -> B: Click "Stop Conversation" (optional)
B -> F: emit('stop_conversation')
F -> F: Set conversation_active = False
F -> B: emit('conversation_stopped', {success: true})
B -> B: Stop MediaRecorder
B -> B: Update UI to ready state

== Error Handling ==
alt Audio Processing Error
    F -> B: emit('error', {message: error_details})
    B -> B: Display error message
    B -> B: Continue conversation flow
else Network Error
    B -> B: Auto-retry connection
    B -> F: Reconnect WebSocket
else API Rate Limit
    F -> F: Automatic API key rotation
    F -> F: Continue processing
end

@enduml
```

### **Key Flow Components**

1. **Initialization** - Browser setup and WebSocket connection
2. **Audio Recording** - Continuous 5-second recording cycles
3. **Server Processing** - Direct BytesIO stream handling
4. **AI Pipeline** - Groq transcription → LangGraph → Groq LLM
5. **TTS Generation** - ElevenLabs Rachel voice synthesis
6. **Automatic Continuation** - Seamless conversation loop
7. **Error Recovery** - API rotation and graceful fallbacks

## 🎙️ Audio Flow Details

### **Recording Process**
1. **MediaRecorder** captures audio in WebM/WAV format
2. **Chunked Processing** handles large audio files safely
3. **Base64 Encoding** for WebSocket transmission
4. **Auto-stop** after 5 seconds to process speech

### **Server Processing**
1. **Direct File Processing** - bypasses numpy conversion issues
2. **BytesIO Stream** - creates file-like object for Groq API
3. **Format Agnostic** - works with any browser audio format
4. **Error Recovery** - continues conversation on failures

### **Response Generation**
1. **Groq Whisper** - fast, accurate speech transcription
2. **LangGraph Agent** - contextual conversation responses
3. **ElevenLabs TTS** - natural voice synthesis
4. **Automatic Continuation** - seamless conversation flow

## 🔧 Configuration

### **Environment Variables**
```bash
GROQ_API_KEY=your_primary_groq_key
GROQ_API_KEY_2=your_backup_groq_key
ELEVENLABS_API_KEY=your_elevenlabs_key
```

### **Audio Settings**
- **Sample Rate**: 16kHz (optimal for speech)
- **Channels**: Mono (single channel)
- **Voice**: Rachel (ElevenLabs ID: 21m00Tcm4TlvDq8ikWAM)
- **Recording Duration**: 5 seconds max per turn

### **Conversation Settings**
- **Auto-listen Delay**: 1 second after TTS playback
- **Error Recovery**: Automatic conversation continuation
- **API Rotation**: Automatic failover between Groq keys

## 🚀 Getting Started

### **Prerequisites**
- Python 3.12+
- Valid Groq API keys
- ElevenLabs API key
- Modern web browser with microphone access

### **Installation**
```bash
# Clone repository
git clone <repository-url>
cd fastrtc-groq-voice-agent-main

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export GROQ_API_KEY="your_key_here"
export ELEVENLABS_API_KEY="your_key_here"

# Run the modern Flask application
python modern_voice_app.py
```

### **Usage**
1. Open browser to `http://localhost:5001`
2. Allow microphone permissions
3. Click "Start Conversation"
4. Speak naturally - Samantha will respond automatically
5. Continue conversation without clicking buttons

## 🎨 UI Components

### **Main Interface**
- **Header**: Logo, subtitle, tech stack indicators
- **Chat Container**: Real-time conversation history
- **Controls**: Start/stop conversation button
- **Audio Visualizer**: Visual feedback during recording/playback
- **Status Display**: Real-time system status updates

### **Visual Feedback**
- **Recording State**: Pulsing microphone icon
- **Processing State**: Loading indicators
- **Speaking State**: Audio visualizer animation
- **Error State**: Clear error messages with recovery

## 🔒 Error Handling

### **Audio Processing**
- **Buffer Errors**: Bypassed with direct file processing
- **Format Issues**: Automatic format detection and handling
- **Recording Failures**: Graceful fallback and retry

### **API Failures**
- **Groq API**: Automatic rotation between multiple keys
- **ElevenLabs**: Fallback to text-only responses
- **Network Issues**: Retry mechanisms and user feedback

### **Conversation Recovery**
- **Audio Playback Failures**: Continue conversation without audio
- **Transcription Errors**: Prompt user to repeat
- **Response Generation**: Fallback responses for errors

## 🎯 Why This Architecture?

### **Flask over Gradio**
- **Custom UI Control**: Complete design freedom
- **Better Integration**: Seamless WebSocket communication
- **Production Ready**: Scalable web server architecture

### **WebSocket Communication**
- **Real-time Updates**: Instant status and response updates
- **Bidirectional**: Full duplex communication
- **Efficient**: Low latency for voice applications

### **Direct Audio Processing**
- **Format Agnostic**: Works with any browser audio format
- **Simplified Pipeline**: Fewer conversion steps, fewer errors
- **Better Performance**: Direct API integration

### **Multiple API Keys**
- **Reliability**: Automatic failover on rate limits
- **Scalability**: Higher throughput with key rotation
- **Cost Optimization**: Distribute usage across accounts

## 📊 Performance Characteristics

- **Latency**: ~2-3 seconds end-to-end response time
- **Audio Quality**: 16kHz mono, optimized for speech
- **Reliability**: 99%+ uptime with proper API key rotation
- **Scalability**: Handles multiple concurrent conversations

## 🔮 Future Enhancements

- **Voice Interruption**: Stop speaking when user starts talking
- **Multiple Voices**: User-selectable voice options
- **Conversation Export**: Save chat history
- **Mobile App**: Native mobile application
- **Voice Training**: Custom voice cloning options

---

**Built with ❤️ using Flask, Groq, and ElevenLabs**
