# FastRTC Groq Voice Agent

This project demonstrates voice interactions with AI assistants using FastRTC and Groq.

## Setup

1. Set up Python environment and install dependencies:
   ```
   uv venv
   source .venv/bin/activate
   uv sync
   ```

2. Copy the `.env.example` to `.env` and add your Groq API key from [Groq Console](https://console.groq.com/keys)

## Running the Application

Navigate to the src directory:
```
cd src
```

Run with web UI (default):
```
python fastrtc_groq_voice_stream.py
```

Run with phone interface (get a temporary phone number):
```
python fastrtc_groq_voice_stream.py --phone
```

## Usage Examples

### Conversation Examples

- "Hello, how are you today?"
- "Can you tell me about the latest technology trends?"
- "What's the weather like in New York?" (Note: This is a simulated response as the agent doesn't have real-time data access)
- "Tell me a short story."
- "What are some good movies to watch?"
