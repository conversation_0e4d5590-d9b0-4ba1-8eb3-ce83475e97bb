#!/usr/bin/env python3
"""
Fraud Demo Module - Complete Banking Fraud Verification Demo
Collects customer responses and returns structured JSON data
"""

import json
from datetime import datetime
from typing import Dict, List, Any

class FraudDemo:
    """Complete fraud verification demo with structured data collection"""
    
    def __init__(self):
        self.demo_questions = [
            {
                "id": "greeting",
                "question": "Hello, this is <PERSON> calling from SecureBank's Fraud Prevention Department. Am I speaking with the account holder?",
                "expected_response": "name_confirmation",
                "multilingual": True
            },
            {
                "id": "identity_verification",
                "question": "For security purposes, I need to verify your identity. Can you please provide the last 4 digits of your Social Security Number?",
                "expected_response": "4_digits",
                "multilingual": True
            },
            {
                "id": "mothers_maiden_name",
                "question": "Thank you. Now, can you please tell me your mother's maiden name for additional verification?",
                "expected_response": "maiden_name",
                "multilingual": True
            },
            {
                "id": "spell_maiden_name",
                "question": "Could you please spell that word by word for me? For example, if it's '<PERSON>', please say 'S-M-I-T-H'.",
                "expected_response": "spelled_name",
                "multilingual": True
            },
            {
                "id": "transaction_1",
                "question": "Perfect. Now I need to verify some recent transactions. I see a charge for $1,250.00 at Amazon Online on January 15th at 2:30 PM. Did you authorize this transaction?",
                "expected_response": "yes_no",
                "multilingual": True
            },
            {
                "id": "transaction_2", 
                "question": "Thank you. I also see a $500.00 ATM withdrawal on January 15th at 11:45 PM in New York City. Did you make this withdrawal?",
                "expected_response": "yes_no",
                "multilingual": True
            },
            {
                "id": "transaction_3",
                "question": "I have one more transaction to verify. There's a $75.50 charge at Starbucks on January 14th at 8:30 AM. Was this your purchase?",
                "expected_response": "yes_no",
                "multilingual": True
            },
            {
                "id": "additional_info",
                "question": "Have you noticed any other suspicious activity on your account recently? Please describe anything unusual.",
                "expected_response": "open_text",
                "multilingual": True
            },
            {
                "id": "contact_confirmation",
                "question": "Finally, can you confirm the best phone number to reach you if we need to follow up?",
                "expected_response": "phone_number",
                "multilingual": True
            }
        ]
        
        self.current_question_index = 0
        self.collected_data = {
            "session_id": f"FRAUD_DEMO_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "timestamp": datetime.now().isoformat(),
            "customer_responses": {},
            "verification_status": "in_progress",
            "detected_languages": [],
            "fraud_indicators": [],
            "completion_status": "incomplete"
        }
        
        # Multilingual question templates
        self.multilingual_questions = {
            "en": {
                "greeting": "Hello, this is Sarah calling from SecureBank's Fraud Prevention Department. Am I speaking with the account holder?",
                "identity_verification": "For security purposes, I need to verify your identity. Can you please provide the last 4 digits of your Social Security Number?",
                "mothers_maiden_name": "Thank you. Now, can you please tell me your mother's maiden name for additional verification?",
                "spell_maiden_name": "Could you please spell that word by word for me? For example, if it's 'Smith', please say 'S-M-I-T-H'.",
                "transaction_1": "Perfect. Now I need to verify some recent transactions. I see a charge for $1,250.00 at Amazon Online on January 15th at 2:30 PM. Did you authorize this transaction?",
                "transaction_2": "Thank you. I also see a $500.00 ATM withdrawal on January 15th at 11:45 PM in New York City. Did you make this withdrawal?",
                "transaction_3": "I have one more transaction to verify. There's a $75.50 charge at Starbucks on January 14th at 8:30 AM. Was this your purchase?",
                "additional_info": "Have you noticed any other suspicious activity on your account recently? Please describe anything unusual.",
                "contact_confirmation": "Finally, can you confirm the best phone number to reach you if we need to follow up?"
            },
            "es": {
                "greeting": "Hola, soy Sarah llamando del Departamento de Prevención de Fraudes de SecureBank. ¿Estoy hablando con el titular de la cuenta?",
                "identity_verification": "Por motivos de seguridad, necesito verificar su identidad. ¿Puede proporcionarme los últimos 4 dígitos de su Número de Seguro Social?",
                "mothers_maiden_name": "Gracias. Ahora, ¿puede decirme el apellido de soltera de su madre para verificación adicional?",
                "spell_maiden_name": "¿Podría deletrearlo palabra por palabra? Por ejemplo, si es 'García', diga 'G-A-R-C-Í-A'.",
                "transaction_1": "Perfecto. Ahora necesito verificar algunas transacciones recientes. Veo un cargo de $1,250.00 en Amazon Online el 15 de enero a las 2:30 PM. ¿Autorizó esta transacción?",
                "transaction_2": "Gracias. También veo un retiro de $500.00 en cajero automático el 15 de enero a las 11:45 PM en Nueva York. ¿Hizo este retiro?",
                "transaction_3": "Tengo una transacción más para verificar. Hay un cargo de $75.50 en Starbucks el 14 de enero a las 8:30 AM. ¿Fue su compra?",
                "additional_info": "¿Ha notado alguna otra actividad sospechosa en su cuenta recientemente? Describa cualquier cosa inusual.",
                "contact_confirmation": "Finalmente, ¿puede confirmar el mejor número de teléfono para contactarlo si necesitamos hacer seguimiento?"
            },
            "fr": {
                "greeting": "Bonjour, c'est Sarah du Département de Prévention des Fraudes de SecureBank. Est-ce que je parle au titulaire du compte?",
                "identity_verification": "Pour des raisons de sécurité, je dois vérifier votre identité. Pouvez-vous me donner les 4 derniers chiffres de votre numéro de sécurité sociale?",
                "mothers_maiden_name": "Merci. Maintenant, pouvez-vous me dire le nom de jeune fille de votre mère pour vérification supplémentaire?",
                "spell_maiden_name": "Pourriez-vous l'épeler lettre par lettre? Par exemple, si c'est 'Martin', dites 'M-A-R-T-I-N'.",
                "transaction_1": "Parfait. Maintenant je dois vérifier quelques transactions récentes. Je vois un débit de $1,250.00 chez Amazon Online le 15 janvier à 14h30. Avez-vous autorisé cette transaction?",
                "transaction_2": "Merci. Je vois aussi un retrait de $500.00 au distributeur le 15 janvier à 23h45 à New York. Avez-vous fait ce retrait?",
                "transaction_3": "J'ai une dernière transaction à vérifier. Il y a un débit de $75.50 chez Starbucks le 14 janvier à 8h30. Était-ce votre achat?",
                "additional_info": "Avez-vous remarqué d'autres activités suspectes sur votre compte récemment? Décrivez tout ce qui est inhabituel.",
                "contact_confirmation": "Enfin, pouvez-vous confirmer le meilleur numéro de téléphone pour vous joindre si nous devons faire un suivi?"
            }
        }
    
    def get_current_question(self, language="en"):
        """Get the current question in specified language"""
        if self.current_question_index >= len(self.demo_questions):
            return None
            
        question_data = self.demo_questions[self.current_question_index]
        question_id = question_data["id"]
        
        # Get question in specified language, fallback to English
        if language in self.multilingual_questions and question_id in self.multilingual_questions[language]:
            question_text = self.multilingual_questions[language][question_id]
        else:
            question_text = self.multilingual_questions["en"][question_id]
        
        return {
            "question_id": question_id,
            "question_text": question_text,
            "question_number": self.current_question_index + 1,
            "total_questions": len(self.demo_questions),
            "expected_response": question_data["expected_response"],
            "language": language
        }
    
    def process_customer_response(self, response_text, detected_language="en"):
        """Process customer response and move to next question"""
        if self.current_question_index >= len(self.demo_questions):
            return self.complete_demo()
        
        current_question = self.demo_questions[self.current_question_index]
        question_id = current_question["id"]
        
        # Store the response
        self.collected_data["customer_responses"][question_id] = {
            "response": response_text,
            "timestamp": datetime.now().isoformat(),
            "detected_language": detected_language,
            "question_number": self.current_question_index + 1
        }
        
        # Track detected languages
        if detected_language not in self.collected_data["detected_languages"]:
            self.collected_data["detected_languages"].append(detected_language)
        
        # Analyze response for fraud indicators
        self.analyze_fraud_indicators(question_id, response_text)
        
        # Move to next question
        self.current_question_index += 1
        
        # Check if demo is complete
        if self.current_question_index >= len(self.demo_questions):
            return self.complete_demo()
        
        # Return next question
        return self.get_current_question(detected_language)
    
    def analyze_fraud_indicators(self, question_id, response_text):
        """Analyze response for potential fraud indicators"""
        response_lower = response_text.lower()
        
        fraud_indicators = []
        
        if question_id in ["transaction_1", "transaction_2", "transaction_3"]:
            # Check for unauthorized transaction responses
            if any(word in response_lower for word in ["no", "not me", "didn't", "unauthorized", "fraud", "stolen"]):
                fraud_indicators.append({
                    "type": "unauthorized_transaction",
                    "question": question_id,
                    "indicator": "Customer denied authorizing transaction",
                    "severity": "high"
                })
            elif any(word in response_lower for word in ["unsure", "maybe", "don't remember", "not sure"]):
                fraud_indicators.append({
                    "type": "uncertain_transaction",
                    "question": question_id,
                    "indicator": "Customer uncertain about transaction",
                    "severity": "medium"
                })
        
        elif question_id == "additional_info":
            # Check for mentions of suspicious activity
            if any(word in response_lower for word in ["suspicious", "strange", "unusual", "weird", "fraud", "hacked"]):
                fraud_indicators.append({
                    "type": "reported_suspicious_activity",
                    "question": question_id,
                    "indicator": "Customer reported suspicious activity",
                    "severity": "high"
                })
        
        # Add indicators to collected data
        self.collected_data["fraud_indicators"].extend(fraud_indicators)
    
    def complete_demo(self):
        """Complete the fraud demo and return final JSON data"""
        self.collected_data["completion_status"] = "complete"
        self.collected_data["completion_timestamp"] = datetime.now().isoformat()
        
        # Calculate verification status
        fraud_count = len([indicator for indicator in self.collected_data["fraud_indicators"] if indicator["severity"] == "high"])
        
        if fraud_count > 0:
            self.collected_data["verification_status"] = "fraud_detected"
        else:
            self.collected_data["verification_status"] = "verified_clean"
        
        # Generate summary
        self.collected_data["summary"] = {
            "total_questions_answered": len(self.collected_data["customer_responses"]),
            "languages_detected": self.collected_data["detected_languages"],
            "fraud_indicators_count": len(self.collected_data["fraud_indicators"]),
            "high_risk_indicators": len([i for i in self.collected_data["fraud_indicators"] if i["severity"] == "high"]),
            "verification_result": self.collected_data["verification_status"]
        }
        
        return {
            "demo_complete": True,
            "final_data": self.collected_data,
            "message": "Thank you for completing the fraud verification process. All information has been recorded and will be reviewed by our security team."
        }
    
    def get_demo_status(self):
        """Get current demo status"""
        return {
            "current_question": self.current_question_index + 1,
            "total_questions": len(self.demo_questions),
            "progress_percentage": round((self.current_question_index / len(self.demo_questions)) * 100, 1),
            "responses_collected": len(self.collected_data["customer_responses"]),
            "languages_detected": self.collected_data["detected_languages"],
            "fraud_indicators": len(self.collected_data["fraud_indicators"])
        }
    
    def reset_demo(self):
        """Reset demo to start over"""
        self.current_question_index = 0
        self.collected_data = {
            "session_id": f"FRAUD_DEMO_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "timestamp": datetime.now().isoformat(),
            "customer_responses": {},
            "verification_status": "in_progress",
            "detected_languages": [],
            "fraud_indicators": [],
            "completion_status": "incomplete"
        }

# Example usage and testing
def test_fraud_demo():
    """Test the fraud demo functionality"""
    demo = FraudDemo()
    
    print("=== FRAUD DEMO TEST ===")
    
    # Test multilingual questions
    print("\n1. English Question:")
    question = demo.get_current_question("en")
    print(f"Q: {question['question_text']}")
    
    print("\n2. Spanish Question:")
    question = demo.get_current_question("es")
    print(f"Q: {question['question_text']}")
    
    print("\n3. French Question:")
    question = demo.get_current_question("fr")
    print(f"Q: {question['question_text']}")
    
    # Simulate customer responses
    print("\n=== SIMULATED CONVERSATION ===")
    
    responses = [
        ("Yes, this is John Smith", "en"),
        ("5678", "en"),
        ("Johnson", "en"),
        ("J-O-H-N-S-O-N", "en"),
        ("No, I didn't make that purchase", "en"),  # Fraud indicator
        ("Yes, that was me", "en"),
        ("No, I don't go to Starbucks", "en"),  # Fraud indicator
        ("I noticed some strange charges last week", "en"),  # Fraud indicator
        ("555-123-4567", "en")
    ]
    
    for i, (response, lang) in enumerate(responses):
        print(f"\nStep {i+1}:")
        current_q = demo.get_current_question(lang)
        if current_q:
            print(f"Q: {current_q['question_text']}")
            print(f"A: {response}")
            
            result = demo.process_customer_response(response, lang)
            if result and result.get("demo_complete"):
                print("\n=== DEMO COMPLETED ===")
                print("Final JSON Data:")
                print(json.dumps(result["final_data"], indent=2))
                break
    
    return demo.collected_data

if __name__ == "__main__":
    test_fraud_demo()
