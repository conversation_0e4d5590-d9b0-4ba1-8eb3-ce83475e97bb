/* Modern Voice Assistant Styling */
:root {
    --primary-color: #7c3aed;
    --primary-light: #8b5cf6;
    --secondary-color: #ec4899;
    --secondary-light: #f472b6;
    --bg-color: #0f0f23;
    --card-bg: #1a1a2e;
    --text-color: #ffffff;
    --text-light: #a0a0a0;
    --border-color: #2d2d44;
    --success-color: #10b981;
    --error-color: #ef4444;
    --border-radius: 16px;
    --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 3rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.logo i {
    font-size: 3rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 3.5rem;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Voice Selection */
.voice-section {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

.voice-section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-light);
    font-size: 1.1rem;
}

.voice-section p {
    color: var(--text-light);
    font-size: 0.95rem;
    margin: 0;
}

/* Chat Interface */
.chat-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 1rem;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--gradient);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--border-color);
    color: var(--primary-light);
}

.message-content {
    flex: 1;
    background: var(--bg-color);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.user-message .message-content {
    background: var(--primary-color);
    color: white;
}

/* Controls */
.controls {
    text-align: center;
    padding: 2rem;
}

.record-btn {
    background: var(--gradient);
    border: none;
    color: white;
    padding: 1.5rem 3rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto 1rem;
}

.record-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(124, 58, 237, 0.5);
}

.record-btn:active {
    transform: translateY(1px);
}

.record-btn.recording {
    background: var(--error-color);
    animation: pulse 1.5s infinite;
}

.record-btn.recording i {
    animation: spin 2s linear infinite;
}

.status {
    font-size: 1rem;
    color: var(--text-light);
    margin-top: 1rem;
}

/* Audio Visualizer */
.audio-visualizer {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 4px;
    height: 60px;
    margin: 1rem 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.audio-visualizer.active {
    opacity: 1;
}

.bar {
    width: 4px;
    background: var(--gradient);
    border-radius: 2px;
    animation: audioWave 1.5s ease-in-out infinite;
}

.bar:nth-child(2) { animation-delay: 0.1s; }
.bar:nth-child(3) { animation-delay: 0.2s; }
.bar:nth-child(4) { animation-delay: 0.3s; }
.bar:nth-child(5) { animation-delay: 0.4s; }
.bar:nth-child(6) { animation-delay: 0.5s; }
.bar:nth-child(7) { animation-delay: 0.6s; }
.bar:nth-child(8) { animation-delay: 0.7s; }

/* Footer */
.footer {
    text-align: center;
    padding: 2rem 0;
    color: var(--text-light);
    font-size: 0.9rem;
    border-top: 1px solid var(--border-color);
    margin-top: 2rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes audioWave {
    0%, 100% { height: 10px; }
    50% { height: 40px; }
}

/* Success/Error States */
.status.success {
    color: var(--success-color);
}

.status.error {
    color: var(--error-color);
}

.status.processing {
    color: var(--primary-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .logo h1 {
        font-size: 2.5rem;
    }

    .record-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .chat-messages {
        padding: 1rem;
    }
}
