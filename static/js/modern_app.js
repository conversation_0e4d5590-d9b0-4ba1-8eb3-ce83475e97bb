// Modern Voice Assistant with FastRTC Integration
class ModernVoiceAssistant {
    constructor() {
        this.socket = io();
        this.conversationActive = false;
        this.isListening = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentAudioUrl = null;

        // Voice Activity Detection settings
        this.silenceThreshold = 0.01; // Adjust based on testing
        this.silenceDuration = 2000; // 2 seconds of silence before stopping
        this.silenceTimer = null;
        this.audioContext = null;
        this.analyser = null;
        this.dataArray = null;

        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketEvents();
        this.requestMicrophonePermission();
    }

    initializeElements() {
        this.conversationBtn = document.getElementById('conversationBtn');
        this.status = document.getElementById('status');
        this.chatMessages = document.getElementById('chatMessages');
        this.audioPlayer = document.getElementById('audioPlayer');
        this.audioVisualizer = document.getElementById('audioVisualizer');
    }

    setupEventListeners() {
        // Conversation button
        this.conversationBtn.addEventListener('click', () => {
            if (this.conversationActive) {
                this.stopConversation();
            } else {
                this.startConversation();
            }
        });

        // Audio player events
        this.audioPlayer.addEventListener('ended', () => {
            console.log('Audio playback ended');
            this.hideAudioVisualizer();

            // Clean up audio URL
            if (this.currentAudioUrl) {
                URL.revokeObjectURL(this.currentAudioUrl);
                this.currentAudioUrl = null;
            }

            // In FastRTC mode, automatically continue listening
            if (this.conversationActive) {
                setTimeout(() => {
                    this.startListening();
                }, 1000); // 1 second delay before listening again
            }
        });
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('Connected to Samantha with FastRTC', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('Disconnected from server', 'error');
        });

        this.socket.on('status', (data) => {
            this.updateStatus(data.message, 'processing');
        });

        this.socket.on('response', (data) => {
            this.handleResponse(data);
        });

        this.socket.on('error', (data) => {
            this.updateStatus(data.message, 'error');
            this.hideAudioVisualizer();
        });

        this.socket.on('conversation_started', (data) => {
            if (data.success) {
                this.conversationActive = true;
                this.updateConversationButton();
                this.startListening();
            }
        });

        this.socket.on('conversation_stopped', (data) => {
            if (data.success) {
                this.conversationActive = false;
                this.updateConversationButton();
                this.stopListening();
            }
        });
    }

    async requestMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('Microphone ready! Click "Start Conversation" for seamless flow.', 'success');
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.updateStatus('Microphone permission required', 'error');
        }
    }

    startConversation() {
        console.log('Starting FastRTC conversation');
        this.socket.emit('start_conversation');
        this.updateStatus('Starting seamless conversation...', 'processing');
    }

    stopConversation() {
        console.log('Stopping FastRTC conversation');
        this.socket.emit('stop_conversation');
        this.updateStatus('Stopping conversation...', 'processing');
        this.stopListening();
    }

    async startListening() {
        if (!this.conversationActive || this.isListening) {
            return;
        }

        try {
            console.log('Starting to listen with Voice Activity Detection...');
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // Setup Audio Context for Voice Activity Detection
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            const source = this.audioContext.createMediaStreamSource(stream);
            source.connect(this.analyser);

            this.analyser.fftSize = 256;
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);

            // Try to use WAV format if supported
            const options = { mimeType: 'audio/wav' };
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                options.mimeType = 'audio/webm';
                console.log('WAV not supported, using WebM');
            } else {
                console.log('Using WAV format');
            }

            this.mediaRecorder = new MediaRecorder(stream, options);
            this.audioChunks = [];
            this.isListening = true;

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                this.processAudio(audioBlob);
                stream.getTracks().forEach(track => track.stop());
                if (this.audioContext) {
                    this.audioContext.close();
                    this.audioContext = null;
                }
                this.isListening = false;
            };

            // Start recording
            this.mediaRecorder.start();
            this.updateConversationButton();
            this.updateStatus('Listening... Speak as long as you want!', 'processing');
            this.showAudioVisualizer();

            // Start Voice Activity Detection
            this.startVoiceActivityDetection();

        } catch (error) {
            console.error('Error starting to listen:', error);
            this.updateStatus('Error accessing microphone', 'error');
            this.isListening = false;
        }
    }

    startVoiceActivityDetection() {
        const checkAudioLevel = () => {
            if (!this.isListening || !this.analyser) {
                return;
            }

            this.analyser.getByteFrequencyData(this.dataArray);

            // Calculate average volume
            let sum = 0;
            for (let i = 0; i < this.dataArray.length; i++) {
                sum += this.dataArray[i];
            }
            const average = sum / this.dataArray.length;
            const normalizedVolume = average / 255;

            console.log('Audio level:', normalizedVolume.toFixed(3));

            if (normalizedVolume > this.silenceThreshold) {
                // Voice detected - clear silence timer
                if (this.silenceTimer) {
                    clearTimeout(this.silenceTimer);
                    this.silenceTimer = null;
                }
                this.updateStatus('Listening... Keep speaking!', 'processing');
            } else {
                // Silence detected - start timer if not already started
                if (!this.silenceTimer) {
                    console.log('Silence detected, starting timer...');
                    this.updateStatus('Listening... (silence detected)', 'processing');
                    this.silenceTimer = setTimeout(() => {
                        console.log('Silence duration reached, stopping recording');
                        this.stopListening();
                    }, this.silenceDuration);
                }
            }

            // Continue checking
            if (this.isListening) {
                requestAnimationFrame(checkAudioLevel);
            }
        };

        checkAudioLevel();
    }

    stopListening() {
        if (this.mediaRecorder && this.isListening) {
            this.mediaRecorder.stop();
            this.isListening = false;
            this.updateConversationButton();
            this.updateStatus('Processing your message...', 'processing');
            this.hideAudioVisualizer();
        }
    }

    async processAudio(audioBlob) {
        try {
            console.log('Processing audio blob:', audioBlob.size, 'bytes');

            if (audioBlob.size === 0) {
                console.warn('Audio blob is empty');
                if (this.conversationActive) {
                    setTimeout(() => this.startListening(), 1000);
                }
                return;
            }

            // Convert audio blob to WAV format first
            const arrayBuffer = await audioBlob.arrayBuffer();
            console.log('Audio array buffer size:', arrayBuffer.byteLength);

            // Convert to base64 more safely
            const uint8Array = new Uint8Array(arrayBuffer);
            let binaryString = '';
            const chunkSize = 8192; // Process in chunks to avoid stack overflow

            for (let i = 0; i < uint8Array.length; i += chunkSize) {
                const chunk = uint8Array.slice(i, i + chunkSize);
                binaryString += String.fromCharCode.apply(null, chunk);
            }

            const base64Audio = btoa(binaryString);
            console.log('Base64 audio length:', base64Audio.length);

            console.log('Sending audio to server...');
            this.socket.emit('process_audio', { audio: base64Audio });

        } catch (error) {
            console.error('Error processing audio:', error);
            this.updateStatus('Error processing audio', 'error');
            if (this.conversationActive) {
                setTimeout(() => this.startListening(), 1000);
            }
        }
    }

    handleResponse(data) {
        console.log('Received response:', data);

        // Add user message with actual transcript
        const userMessage = data.transcript || 'You spoke to Samantha';
        this.addMessage(userMessage, 'user');

        // Add assistant response
        this.addMessage(data.text, 'assistant');

        // Play audio response
        if (data.audio) {
            this.playAudio(data.audio);
            this.updateStatus('Samantha is speaking...', 'processing');
        } else {
            // If no audio, continue listening
            if (this.conversationActive) {
                setTimeout(() => this.startListening(), 1000);
            }
            this.updateStatus('Ready for your next message...', 'success');
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = `<p>${text}</p>`;

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    playAudio(base64Audio) {
        try {
            console.log('Playing audio, base64 length:', base64Audio.length);

            const audioBlob = this.base64ToBlob(base64Audio, 'audio/mpeg');
            const audioUrl = URL.createObjectURL(audioBlob);

            this.audioPlayer.src = audioUrl;
            this.audioPlayer.volume = 1.0;
            this.audioPlayer.muted = false;

            const playPromise = this.audioPlayer.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Audio playback started successfully');
                    this.showAudioVisualizer();
                }).catch(error => {
                    console.error('Audio playback failed:', error);
                    this.updateStatus('Audio playback failed. Continuing conversation...', 'error');
                    this.simulateAudioEnd();
                });
            }

            this.currentAudioUrl = audioUrl;

        } catch (error) {
            console.error('Error playing audio:', error);
            this.updateStatus('Error playing audio. Continuing conversation...', 'error');
            this.simulateAudioEnd();
        }
    }

    simulateAudioEnd() {
        // Simulate audio ending to continue conversation flow
        setTimeout(() => {
            this.hideAudioVisualizer();
            if (this.conversationActive) {
                setTimeout(() => this.startListening(), 1000);
            }
        }, 2000);
    }

    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    updateConversationButton() {
        if (this.conversationActive) {
            if (this.isListening) {
                this.conversationBtn.classList.add('listening');
                this.conversationBtn.classList.remove('active');
                this.conversationBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Listening...</span>';
            } else {
                this.conversationBtn.classList.add('active');
                this.conversationBtn.classList.remove('listening');
                this.conversationBtn.innerHTML = '<i class="fas fa-stop"></i><span>Stop Conversation</span>';
            }
        } else {
            this.conversationBtn.classList.remove('active', 'listening');
            this.conversationBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Start Conversation</span>';
        }
    }

    updateStatus(message, type = '') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }

    showAudioVisualizer() {
        this.audioVisualizer.classList.add('active');
    }

    hideAudioVisualizer() {
        this.audioVisualizer.classList.remove('active');
    }
}

// Initialize the modern voice assistant when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ModernVoiceAssistant();
});
