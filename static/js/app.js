// Modern Voice Assistant Frontend
class VoiceAssistant {
    constructor() {
        this.socket = io();
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentVoice = '21m00Tcm4TlvDq8ikWAM'; // <PERSON>'s voice ID
        this.conversationActive = false;
        this.autoListenAfterResponse = false; // Disable auto-listen for now
        this.currentAudioUrl = null;

        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketEvents();
        this.requestMicrophonePermission();
    }

    initializeElements() {
        this.recordBtn = document.getElementById('recordBtn');
        this.testAudioBtn = document.getElementById('testAudioBtn');
        this.status = document.getElementById('status');
        this.chatMessages = document.getElementById('chatMessages');
        // Voice selection removed - using single voice
        this.audioPlayer = document.getElementById('audioPlayer');
        this.audioVisualizer = document.getElementById('audioVisualizer');
    }

    setupEventListeners() {
        // Record button - simple click to talk
        this.recordBtn.addEventListener('click', () => {
            if (this.isRecording) {
                this.stopRecording();
            } else {
                this.startRecording();
            }
        });

        // Test audio button
        this.testAudioBtn.addEventListener('click', () => {
            this.testAudioPlayback();
        });

        // Voice selection removed - using single voice (Rachel)

        // Audio player events - handle audio ending
        this.audioPlayer.addEventListener('ended', () => {
            console.log('Audio playback ended');
            this.hideAudioVisualizer();

            // Clean up audio URL
            if (this.currentAudioUrl) {
                URL.revokeObjectURL(this.currentAudioUrl);
                this.currentAudioUrl = null;
            }

            // Simple status update - no auto-listening
            this.updateStatus('Click the microphone to continue talking', 'success');
        });
    }

    startConversation() {
        this.conversationActive = true;
        this.updateRecordButton();
        this.updateStatus('Conversation started! Speak naturally...', 'success');
        this.startRecording();
    }

    stopConversation() {
        this.conversationActive = false;
        this.autoListenAfterResponse = false;
        if (this.isRecording) {
            this.stopRecording();
        }
        this.updateRecordButton();
        this.updateStatus('Conversation ended. Click to start again.', 'success');
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('Connected to Samantha', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('Disconnected from server', 'error');
        });

        this.socket.on('status', (data) => {
            this.updateStatus(data.message, 'processing');
        });

        this.socket.on('response', (data) => {
            console.log('Received response:', data);
            console.log('Response text:', data.text);
            console.log('Audio length:', data.audio ? data.audio.length : 'No audio');
            console.log('Transcript:', data.transcript);
            this.handleResponse(data);
        });

        this.socket.on('error', (data) => {
            console.error('Socket error:', data);
            this.updateStatus(data.message, 'error');
            this.hideAudioVisualizer();
        });
    }

    async requestMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('Ready! Click "Start Conversation" to begin chatting with Samantha.', 'success');
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.updateStatus('Microphone permission required', 'error');
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                this.processAudio(audioBlob);
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.updateRecordButton();
            this.updateStatus('Listening... Click again to stop', 'processing');
            this.showAudioVisualizer();

        } catch (error) {
            console.error('Error starting recording:', error);
            this.updateStatus('Error accessing microphone', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.updateRecordButton();
            this.updateStatus('Processing your message...', 'processing');
            this.hideAudioVisualizer();
        }
    }

    async processAudio(audioBlob) {
        try {
            console.log('Processing audio blob:', audioBlob);
            console.log('Audio blob size:', audioBlob.size);
            console.log('Audio blob type:', audioBlob.type);

            if (audioBlob.size === 0) {
                console.error('Audio blob is empty');
                this.updateStatus('No audio recorded', 'error');
                return;
            }

            // Convert audio blob to base64
            const arrayBuffer = await audioBlob.arrayBuffer();
            console.log('Array buffer size:', arrayBuffer.byteLength);

            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
            console.log('Base64 audio length:', base64Audio.length);

            // Send audio to server
            console.log('Sending audio to server...');
            this.socket.emit('process_audio', { audio: base64Audio });

        } catch (error) {
            console.error('Error processing audio:', error);
            this.updateStatus('Error processing audio', 'error');
        }
    }

    handleResponse(data) {
        // Add user message with actual transcript if available
        const userMessage = data.transcript || 'You spoke to Samantha';
        this.addMessage(userMessage, 'user');

        // Add assistant response
        this.addMessage(data.text, 'assistant');

        // Play audio response
        if (data.audio) {
            this.playAudio(data.audio);
            this.updateStatus('Samantha is speaking...', 'processing');
        } else {
            this.updateStatus('Click the microphone to continue talking', 'success');
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = `<p>${text}</p>`;

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    playAudio(base64Audio) {
        try {
            console.log('Playing audio, base64 length:', base64Audio.length);

            // Try multiple audio formats
            const formats = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
            let audioBlob, audioUrl;

            for (const format of formats) {
                try {
                    audioBlob = this.base64ToBlob(base64Audio, format);
                    audioUrl = URL.createObjectURL(audioBlob);
                    console.log(`Trying audio format: ${format}`);
                    break;
                } catch (e) {
                    console.warn(`Failed to create blob with format ${format}:`, e);
                }
            }

            if (!audioUrl) {
                throw new Error('Failed to create audio URL with any format');
            }

            console.log('Audio URL created:', audioUrl);

            // Reset audio player
            this.audioPlayer.pause();
            this.audioPlayer.currentTime = 0;
            this.audioPlayer.src = audioUrl;
            this.audioPlayer.volume = 1.0;
            this.audioPlayer.muted = false;

            // Add event listeners for debugging
            this.audioPlayer.addEventListener('loadstart', () => console.log('Audio loading started'), { once: true });
            this.audioPlayer.addEventListener('canplay', () => console.log('Audio can play'), { once: true });
            this.audioPlayer.addEventListener('playing', () => console.log('Audio is playing'), { once: true });
            this.audioPlayer.addEventListener('error', (e) => {
                console.error('Audio error:', e);
                this.updateStatus('Audio playback error. Trying to continue conversation...', 'error');
                // Continue conversation even if audio fails
                this.simulateAudioEnd();
            }, { once: true });

            // Try to play
            const playPromise = this.audioPlayer.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Audio playback started successfully');
                    this.showAudioVisualizer();
                    this.updateStatus('Samantha is speaking...', 'processing');
                }).catch(error => {
                    console.error('Audio playback failed:', error);
                    this.updateStatus('Audio playback failed. Continuing conversation...', 'error');
                    // Continue conversation even if audio fails
                    this.simulateAudioEnd();
                });
            }

            // Store URL for cleanup
            this.currentAudioUrl = audioUrl;

        } catch (error) {
            console.error('Error playing audio:', error);
            this.updateStatus('Error playing audio. Continuing conversation...', 'error');
            // Continue conversation even if audio fails
            this.simulateAudioEnd();
        }
    }

    simulateAudioEnd() {
        // Simulate audio ending to continue conversation flow
        console.log('Simulating audio end for continuous flow');
        setTimeout(() => {
            this.hideAudioVisualizer();
            if (this.conversationActive && this.autoListenAfterResponse) {
                console.log('Auto-listening enabled, starting recording...');
                this.updateStatus('Listening for your response...', 'processing');
                setTimeout(() => {
                    this.startRecording();
                }, 1000);
            }
        }, 2000); // Give 2 seconds as if audio played
    }

    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    updateRecordButton() {
        if (this.isRecording) {
            this.recordBtn.classList.add('recording');
            this.recordBtn.classList.remove('conversation-active');
            this.recordBtn.innerHTML = '<i class="fas fa-stop"></i><span>Stop Recording</span>';
        } else {
            this.recordBtn.classList.remove('recording', 'conversation-active');
            this.recordBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Click to Talk</span>';
        }
    }

    updateStatus(message, type = '') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }

    showAudioVisualizer() {
        this.audioVisualizer.classList.add('active');
    }

    hideAudioVisualizer() {
        this.audioVisualizer.classList.remove('active');
    }

    async testAudioPlayback() {
        try {
            this.updateStatus('Testing audio playback...', 'processing');

            // Fetch test audio from server
            const response = await fetch('/api/test-tts');
            const data = await response.json();

            if (data.success && data.audio) {
                console.log('Test audio received, length:', data.audio.length);
                this.updateStatus('Playing test audio...', 'processing');

                // Play the test audio
                this.playAudio(data.audio);

                setTimeout(() => {
                    this.updateStatus('Test audio should be playing. Can you hear it?', 'success');
                }, 1000);
            } else {
                console.error('Test audio failed:', data);
                this.updateStatus('Test audio failed: ' + (data.error || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error testing audio:', error);
            this.updateStatus('Error testing audio: ' + error.message, 'error');
        }
    }
}

// Initialize the voice assistant when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceAssistant();
});
