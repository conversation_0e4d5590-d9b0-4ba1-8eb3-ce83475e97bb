// Modern Voice Assistant Frontend
class VoiceAssistant {
    constructor() {
        this.socket = io();
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentVoice = '21m00Tcm4TlvDq8ikWAM'; // <PERSON>'s voice ID
        this.conversationActive = false;
        this.autoListenAfterResponse = true;

        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketEvents();
        this.requestMicrophonePermission();
    }

    initializeElements() {
        this.recordBtn = document.getElementById('recordBtn');
        this.status = document.getElementById('status');
        this.chatMessages = document.getElementById('chatMessages');
        // Voice selection removed - using single voice
        this.audioPlayer = document.getElementById('audioPlayer');
        this.audioVisualizer = document.getElementById('audioVisualizer');
    }

    setupEventListeners() {
        // Record button
        this.recordBtn.addEventListener('click', () => {
            if (this.conversationActive) {
                this.stopConversation();
            } else {
                this.startConversation();
            }
        });

        // Voice selection removed - using single voice (Rachel)

        // Audio player events
        this.audioPlayer.addEventListener('ended', () => {
            this.hideAudioVisualizer();
            // Automatically start listening again after response
            if (this.conversationActive && this.autoListenAfterResponse) {
                setTimeout(() => {
                    this.startRecording();
                }, 500); // Small delay before starting to listen again
            }
        });
    }

    startConversation() {
        this.conversationActive = true;
        this.updateRecordButton();
        this.updateStatus('Conversation started! Speak naturally...', 'success');
        this.startRecording();
    }

    stopConversation() {
        this.conversationActive = false;
        this.autoListenAfterResponse = false;
        if (this.isRecording) {
            this.stopRecording();
        }
        this.updateRecordButton();
        this.updateStatus('Conversation ended. Click to start again.', 'success');
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('Connected to Samantha', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('Disconnected from server', 'error');
        });

        this.socket.on('status', (data) => {
            this.updateStatus(data.message, 'processing');
        });

        this.socket.on('response', (data) => {
            this.handleResponse(data);
        });

        this.socket.on('error', (data) => {
            this.updateStatus(data.message, 'error');
            this.hideAudioVisualizer();
        });
    }

    async requestMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('Ready! Click "Start Conversation" to begin chatting with Samantha.', 'success');
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.updateStatus('Microphone permission required', 'error');
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                this.processAudio(audioBlob);
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.updateRecordButton();
            this.updateStatus('Listening... Click again to stop', 'processing');
            this.showAudioVisualizer();

        } catch (error) {
            console.error('Error starting recording:', error);
            this.updateStatus('Error accessing microphone', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.updateRecordButton();
            this.updateStatus('Processing your message...', 'processing');
            this.hideAudioVisualizer();
        }
    }

    async processAudio(audioBlob) {
        try {
            console.log('Processing audio blob:', audioBlob);
            console.log('Audio blob size:', audioBlob.size);
            console.log('Audio blob type:', audioBlob.type);

            // Convert audio blob to base64
            const arrayBuffer = await audioBlob.arrayBuffer();
            console.log('Array buffer size:', arrayBuffer.byteLength);

            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
            console.log('Base64 audio length:', base64Audio.length);

            // Send audio to server
            this.socket.emit('process_audio', { audio: base64Audio });

        } catch (error) {
            console.error('Error processing audio:', error);
            this.updateStatus('Error processing audio', 'error');
        }
    }

    handleResponse(data) {
        // Add user message with actual transcript if available
        const userMessage = data.transcript || 'You spoke to Samantha';
        this.addMessage(userMessage, 'user');

        // Add assistant response
        this.addMessage(data.text, 'assistant');

        // Play audio response
        if (data.audio) {
            this.autoListenAfterResponse = true; // Enable auto-listen after this response
            this.playAudio(data.audio);
            this.updateStatus('Samantha is speaking... Will listen again after response.', 'processing');
        } else {
            // If no audio, start listening again immediately
            if (this.conversationActive) {
                setTimeout(() => {
                    this.startRecording();
                }, 500);
            }
            this.updateStatus('Ready for your next message...', 'success');
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = `<p>${text}</p>`;

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    playAudio(base64Audio) {
        try {
            const audioBlob = this.base64ToBlob(base64Audio, 'audio/mpeg');
            const audioUrl = URL.createObjectURL(audioBlob);

            this.audioPlayer.src = audioUrl;
            this.audioPlayer.play();
            this.showAudioVisualizer();

            // Clean up URL after playing
            this.audioPlayer.addEventListener('ended', () => {
                URL.revokeObjectURL(audioUrl);
            }, { once: true });

        } catch (error) {
            console.error('Error playing audio:', error);
            this.updateStatus('Error playing audio response', 'error');
        }
    }

    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    updateRecordButton() {
        if (this.conversationActive) {
            if (this.isRecording) {
                this.recordBtn.classList.add('recording');
                this.recordBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Listening...</span>';
            } else {
                this.recordBtn.classList.remove('recording');
                this.recordBtn.classList.add('conversation-active');
                this.recordBtn.innerHTML = '<i class="fas fa-stop"></i><span>End Conversation</span>';
            }
        } else {
            this.recordBtn.classList.remove('recording', 'conversation-active');
            this.recordBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Start Conversation</span>';
        }
    }

    updateStatus(message, type = '') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }

    showAudioVisualizer() {
        this.audioVisualizer.classList.add('active');
    }

    hideAudioVisualizer() {
        this.audioVisualizer.classList.remove('active');
    }
}

// Initialize the voice assistant when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceAssistant();
});
