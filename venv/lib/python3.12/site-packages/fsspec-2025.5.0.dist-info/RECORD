fsspec-2025.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fsspec-2025.5.0.dist-info/METADATA,sha256=XeOIuCPlMldMZeHAMY9e3Zqy_bwyhd8NE7iTjBPpukA,11697
fsspec-2025.5.0.dist-info/RECORD,,
fsspec-2025.5.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
fsspec-2025.5.0.dist-info/licenses/LICENSE,sha256=LcNUls5TpzB5FcAIqESq1T53K0mzTN0ARFBnaRQH7JQ,1513
fsspec/__init__.py,sha256=L7qwNBU1iMNQd8Of87HYSNFT9gWlNMSESaJC8fY0AaQ,2053
fsspec/__pycache__/__init__.cpython-312.pyc,,
fsspec/__pycache__/_version.cpython-312.pyc,,
fsspec/__pycache__/archive.cpython-312.pyc,,
fsspec/__pycache__/asyn.cpython-312.pyc,,
fsspec/__pycache__/caching.cpython-312.pyc,,
fsspec/__pycache__/callbacks.cpython-312.pyc,,
fsspec/__pycache__/compression.cpython-312.pyc,,
fsspec/__pycache__/config.cpython-312.pyc,,
fsspec/__pycache__/conftest.cpython-312.pyc,,
fsspec/__pycache__/core.cpython-312.pyc,,
fsspec/__pycache__/dircache.cpython-312.pyc,,
fsspec/__pycache__/exceptions.cpython-312.pyc,,
fsspec/__pycache__/fuse.cpython-312.pyc,,
fsspec/__pycache__/generic.cpython-312.pyc,,
fsspec/__pycache__/gui.cpython-312.pyc,,
fsspec/__pycache__/json.cpython-312.pyc,,
fsspec/__pycache__/mapping.cpython-312.pyc,,
fsspec/__pycache__/parquet.cpython-312.pyc,,
fsspec/__pycache__/registry.cpython-312.pyc,,
fsspec/__pycache__/spec.cpython-312.pyc,,
fsspec/__pycache__/transaction.cpython-312.pyc,,
fsspec/__pycache__/utils.cpython-312.pyc,,
fsspec/_version.py,sha256=d5C2cu2VxxYpS-PNB1YmoIJOnoUQMsDSy-TKkHnh768,517
fsspec/archive.py,sha256=vM6t_lgV6lBWbBYwpm3S4ofBQFQxUPr5KkDQrrQcQro,2411
fsspec/asyn.py,sha256=VJ2jBdYgUjV4_dETpKeCp2wF1XHAdeUET95d2HqNZck,36776
fsspec/caching.py,sha256=n_SbdT-l92Kqo3e1BQgef0uEWJD0raP5-Qd8Ewp8CHY,34292
fsspec/callbacks.py,sha256=BDIwLzK6rr_0V5ch557fSzsivCElpdqhXr5dZ9Te-EE,9210
fsspec/compression.py,sha256=jCSUMJu-zSNyrusnHT0wKXgOd1tTJR6vM126i5SR5Zc,4865
fsspec/config.py,sha256=LF4Zmu1vhJW7Je9Q-cwkRc3xP7Rhyy7Xnwj26Z6sv2g,4279
fsspec/conftest.py,sha256=fVfx-NLrH_OZS1TIpYNoPzM7efEcMoL62reHOdYeFCA,1245
fsspec/core.py,sha256=1tLctwr7sF1VO3djc_UkjhJ8IAEy0TUMH_bb07Sw17E,23828
fsspec/dircache.py,sha256=YzogWJrhEastHU7vWz-cJiJ7sdtLXFXhEpInGKd4EcM,2717
fsspec/exceptions.py,sha256=pauSLDMxzTJMOjvX1WEUK0cMyFkrFxpWJsyFywav7A8,331
fsspec/fuse.py,sha256=Q-3NOOyLqBfYa4Db5E19z_ZY36zzYHtIs1mOUasItBQ,10177
fsspec/generic.py,sha256=6NIZX_Un78zkTtw46RoYubDPg_gPgO1blsjxytAVzu4,13449
fsspec/gui.py,sha256=xBnHL2-r0LVwhDAtnHoPpXts7jd4Z32peawCJiI-7lI,13975
fsspec/implementations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fsspec/implementations/__pycache__/__init__.cpython-312.pyc,,
fsspec/implementations/__pycache__/arrow.cpython-312.pyc,,
fsspec/implementations/__pycache__/asyn_wrapper.cpython-312.pyc,,
fsspec/implementations/__pycache__/cache_mapper.cpython-312.pyc,,
fsspec/implementations/__pycache__/cache_metadata.cpython-312.pyc,,
fsspec/implementations/__pycache__/cached.cpython-312.pyc,,
fsspec/implementations/__pycache__/dask.cpython-312.pyc,,
fsspec/implementations/__pycache__/data.cpython-312.pyc,,
fsspec/implementations/__pycache__/dbfs.cpython-312.pyc,,
fsspec/implementations/__pycache__/dirfs.cpython-312.pyc,,
fsspec/implementations/__pycache__/ftp.cpython-312.pyc,,
fsspec/implementations/__pycache__/git.cpython-312.pyc,,
fsspec/implementations/__pycache__/github.cpython-312.pyc,,
fsspec/implementations/__pycache__/http.cpython-312.pyc,,
fsspec/implementations/__pycache__/http_sync.cpython-312.pyc,,
fsspec/implementations/__pycache__/jupyter.cpython-312.pyc,,
fsspec/implementations/__pycache__/libarchive.cpython-312.pyc,,
fsspec/implementations/__pycache__/local.cpython-312.pyc,,
fsspec/implementations/__pycache__/memory.cpython-312.pyc,,
fsspec/implementations/__pycache__/reference.cpython-312.pyc,,
fsspec/implementations/__pycache__/sftp.cpython-312.pyc,,
fsspec/implementations/__pycache__/smb.cpython-312.pyc,,
fsspec/implementations/__pycache__/tar.cpython-312.pyc,,
fsspec/implementations/__pycache__/webhdfs.cpython-312.pyc,,
fsspec/implementations/__pycache__/zip.cpython-312.pyc,,
fsspec/implementations/arrow.py,sha256=721Dikne_lV_0tlgk9jyKmHL6W-5MT0h2LKGvOYQTPI,8623
fsspec/implementations/asyn_wrapper.py,sha256=uZROca8lqiGOf5EILoAUjfalWoUU5CtseiE46l_3lkQ,3326
fsspec/implementations/cache_mapper.py,sha256=W4wlxyPxZbSp9ItJ0pYRVBMh6bw9eFypgP6kUYuuiI4,2421
fsspec/implementations/cache_metadata.py,sha256=pcOJYcBQY5OaC7Yhw0F3wjg08QLYApGmoISCrbs59ks,8511
fsspec/implementations/cached.py,sha256=FKEstAQxn5CyA5yM8NSl154ffm_k2wQ6_Za6C7ygIWg,33592
fsspec/implementations/dask.py,sha256=CXZbJzIVOhKV8ILcxuy3bTvcacCueAbyQxmvAkbPkrk,4466
fsspec/implementations/data.py,sha256=LDLczxRh8h7x39Zjrd-GgzdQHr78yYxDlrv2C9Uxb5E,1658
fsspec/implementations/dbfs.py,sha256=2Bp-0m9SqlaroDa0KbXxb5BobCyBJ7_5YQBISf3fxbQ,15145
fsspec/implementations/dirfs.py,sha256=f1sGnQ9Vf0xTxrXo4jDeBy4Qfq3RTqAEemqBSeb0hwY,12108
fsspec/implementations/ftp.py,sha256=sorsczLp_2J3ukONsbZY-11sRZP6H5a3V7XXf6o6ip0,11936
fsspec/implementations/git.py,sha256=4SElW9U5d3k3_ITlvUAx59Yk7XLNRTqkGa2C3hCUkWM,3754
fsspec/implementations/github.py,sha256=Lu0TIFAXzEMq60P495NqvaGFlqWh6be8uzKCiQ9sqw4,11702
fsspec/implementations/http.py,sha256=_gLt0yGbVOYWvE9pK81WCC-3TgbOMOKJYllBU72ALo8,30138
fsspec/implementations/http_sync.py,sha256=UydDqSdUBdhiJ1KufzV8rKGrTftFR4QmNV0safILb8g,30133
fsspec/implementations/jupyter.py,sha256=B2uj7OEm7yIk-vRSsO37_ND0t0EBvn4B-Su43ibN4Pg,3811
fsspec/implementations/libarchive.py,sha256=5_I2DiLXwQ1JC8x-K7jXu-tBwhO9dj7tFLnb0bTnVMQ,7102
fsspec/implementations/local.py,sha256=38ylhASzWXSF0X41Bw8pYXyiM8xVu4UWDgE7l3Em8Uc,16768
fsspec/implementations/memory.py,sha256=cLNrK9wk97sl4Tre9uVDXWj6mEHvvVVIgaVgNA5KVIg,10527
fsspec/implementations/reference.py,sha256=t23prs_5ugXJnYhLxLlPLPyagrx4_ofZWR_oyX9wd3Q,48703
fsspec/implementations/sftp.py,sha256=fMY9XZcmpjszQ2tCqO_TPaJesaeD_Dv7ptYzgUPGoO0,5631
fsspec/implementations/smb.py,sha256=5fhu8h06nOLBPh2c48aT7WBRqh9cEcbIwtyu06wTjec,15236
fsspec/implementations/tar.py,sha256=dam78Tp_CozybNqCY2JYgGBS3Uc9FuJUAT9oB0lolOs,4111
fsspec/implementations/webhdfs.py,sha256=G9wGywj7BkZk4Mu9zXu6HaDlEqX4F8Gw1i4k46CP_-o,16769
fsspec/implementations/zip.py,sha256=9LBMHPft2OutJl2Ft-r9u_z3GptLkc2n91ur2A3bCbg,6072
fsspec/json.py,sha256=65sQ0Y7mTj33u_Y4IId5up4abQ3bAel4E4QzbKMiQSg,3826
fsspec/mapping.py,sha256=m2ndB_gtRBXYmNJg0Ie1-BVR75TFleHmIQBzC-yWhjU,8343
fsspec/parquet.py,sha256=6ibAmG527L5JNFS0VO8BDNlxHdA3bVYqdByeiFgpUVM,19448
fsspec/registry.py,sha256=9C8Ru2CU_d495yZBrXcP7H6fF_WfAqtfwx7ZTPUnGwE,11743
fsspec/spec.py,sha256=xD7fd3nGIQPPljic-sS0ckWoO9jIqsr613UjHf8sOtE,77276
fsspec/tests/abstract/__init__.py,sha256=4xUJrv7gDgc85xAOz1p-V_K1hrsdMWTSa0rviALlJk8,10181
fsspec/tests/abstract/__pycache__/__init__.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/common.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/copy.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/get.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/mv.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/open.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/pipe.cpython-312.pyc,,
fsspec/tests/abstract/__pycache__/put.cpython-312.pyc,,
fsspec/tests/abstract/common.py,sha256=1GQwNo5AONzAnzZj0fWgn8NJPLXALehbsuGxS3FzWVU,4973
fsspec/tests/abstract/copy.py,sha256=gU5-d97U3RSde35Vp4RxPY4rWwL744HiSrJ8IBOp9-8,19967
fsspec/tests/abstract/get.py,sha256=vNR4HztvTR7Cj56AMo7_tx7TeYz1Jgr_2Wb8Lv-UiBY,20755
fsspec/tests/abstract/mv.py,sha256=k8eUEBIrRrGMsBY5OOaDXdGnQUKGwDIfQyduB6YD3Ns,1982
fsspec/tests/abstract/open.py,sha256=Fi2PBPYLbRqysF8cFm0rwnB41kMdQVYjq8cGyDXp3BU,329
fsspec/tests/abstract/pipe.py,sha256=LFzIrLCB5GLXf9rzFKJmE8AdG7LQ_h4bJo70r8FLPqM,402
fsspec/tests/abstract/put.py,sha256=7aih17OKB_IZZh1Mkq1eBDIjobhtMQmI8x-Pw-S_aZk,21201
fsspec/transaction.py,sha256=xliRG6U2Zf3khG4xcw9WiB-yAoqJSHEGK_VjHOdtgo0,2398
fsspec/utils.py,sha256=A11t25RnpiQ30RO6xeR0Qqlu3fGj8bnc40jg08tlYSI,22980
