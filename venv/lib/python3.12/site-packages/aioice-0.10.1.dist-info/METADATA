Metadata-Version: 2.4
Name: aioice
Version: 0.10.1
Summary: An implementation of Interactive Connectivity Establishment (RFC 5245)
Author-email: <PERSON> <<EMAIL>>
License-Expression: BSD-3-Clause
Project-URL: Homepage, https://github.com/aiortc/aioice
Project-URL: Changelog, https://aioice.readthedocs.io/en/stable/changelog.html
Project-URL: Documentation, https://aioice.readthedocs.io/
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: dnspython>=2.0.0
Requires-Dist: ifaddr>=0.2.0
Provides-Extra: dev
Requires-Dist: coverage[toml]>=7.2.2; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pyopenssl; extra == "dev"
Requires-Dist: ruff; extra == "dev"
Requires-Dist: websockets; extra == "dev"
Dynamic: license-file

aioice
======

|rtd| |pypi-v| |pypi-pyversions| |pypi-l| |pypi-wheel| |tests| |codecov|

.. |rtd| image:: https://readthedocs.org/projects/aioice/badge/?version=latest
   :target: https://aioice.readthedocs.io/

.. |pypi-v| image:: https://img.shields.io/pypi/v/aioice.svg
    :target: https://pypi.python.org/pypi/aioice

.. |pypi-pyversions| image:: https://img.shields.io/pypi/pyversions/aioice.svg
    :target: https://pypi.python.org/pypi/aioice

.. |pypi-l| image:: https://img.shields.io/pypi/l/aioice.svg
    :target: https://pypi.python.org/pypi/aioice

.. |pypi-wheel| image:: https://img.shields.io/pypi/wheel/aioice.svg
    :target: https://pypi.python.org/pypi/aioice

.. |tests| image:: https://github.com/aiortc/aioice/workflows/tests/badge.svg
    :target: https://github.com/aiortc/aioice/actions

.. |codecov| image:: https://img.shields.io/codecov/c/github/aiortc/aioice.svg
    :target: https://codecov.io/gh/aiortc/aioice

What is ``aioice``?
-------------------

``aioice`` is a library for Interactive Connectivity Establishment (RFC 5245)
in Python. It is built on top of ``asyncio``, Python's standard asynchronous
I/O framework.

Interactive Connectivity Establishment (ICE) is useful for applications that
establish peer-to-peer UDP data streams, as it facilitates NAT traversal.
Typical usecases include SIP and WebRTC.

To learn more about ``aioice`` please `read the documentation`_.

.. _read the documentation: https://aioice.readthedocs.io/en/stable/

Example
-------

.. code:: python

    import asyncio
    import aioice

    async def connect_using_ice():
        connection = aioice.Connection(ice_controlling=True)

        # gather local candidates
        await connection.gather_candidates()

        # send your information to the remote party using your signaling method
        send_local_info(
            connection.local_candidates,
            connection.local_username,
            connection.local_password)

        # receive remote information using your signaling method
        remote_candidates, remote_username, remote_password = get_remote_info()

        # perform ICE handshake
        for candidate in remote_candidates:
            await connection.add_remote_candidate(candidate)
        await connection.add_remote_candidate(None)
        connection.remote_username = remote_username
        connection.remote_password = remote_password
        await connection.connect()

        # send and receive data
        await connection.sendto(b'1234', 1)
        data, component = await connection.recvfrom()

        # close connection
        await connection.close()

    asyncio.run(connect_using_ice())

License
-------

``aioice`` is released under the `BSD license`_.

.. _BSD license: https://aioice.readthedocs.io/en/stable/license.html
