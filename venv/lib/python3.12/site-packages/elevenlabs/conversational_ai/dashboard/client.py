# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ...core.client_wrapper import Async<PERSON><PERSON>Wrapper, SyncClientWrapper
from .raw_client import AsyncRawDashboardClient, RawDashboardClient
from .settings.client import AsyncSettingsClient, SettingsClient


class DashboardClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawDashboardClient(client_wrapper=client_wrapper)
        self.settings = SettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawDashboardClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawDashboardClient
        """
        return self._raw_client


class AsyncDashboardClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawDashboardClient(client_wrapper=client_wrapper)
        self.settings = AsyncSettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawDashboardClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawDashboardClient
        """
        return self._raw_client
