# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.http_response import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.serialization import convert_and_respect_annotation_metadata
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.conversation_initiation_client_data_request_input import ConversationInitiationClientDataRequestInput
from ...types.http_validation_error import HttpValidationError
from ...types.sip_trunk_outbound_call_response import SipTrunkOutboundCallResponse

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawSipTrunkClient:
    def __init__(self, *, client_wrapper: Sync<PERSON>lientWrapper):
        self._client_wrapper = client_wrapper

    def outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[SipTrunkOutboundCallResponse]:
        """
        Handle an outbound call via SIP trunk

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[SipTrunkOutboundCallResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/sip-trunk/outbound-call",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "agent_id": agent_id,
                "agent_phone_number_id": agent_phone_number_id,
                "to_number": to_number,
                "conversation_initiation_client_data": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data,
                    annotation=ConversationInitiationClientDataRequestInput,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    SipTrunkOutboundCallResponse,
                    construct_type(
                        type_=SipTrunkOutboundCallResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawSipTrunkClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[SipTrunkOutboundCallResponse]:
        """
        Handle an outbound call via SIP trunk

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[SipTrunkOutboundCallResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/sip-trunk/outbound-call",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "agent_id": agent_id,
                "agent_phone_number_id": agent_phone_number_id,
                "to_number": to_number,
                "conversation_initiation_client_data": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data,
                    annotation=ConversationInitiationClientDataRequestInput,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    SipTrunkOutboundCallResponse,
                    construct_type(
                        type_=SipTrunkOutboundCallResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
