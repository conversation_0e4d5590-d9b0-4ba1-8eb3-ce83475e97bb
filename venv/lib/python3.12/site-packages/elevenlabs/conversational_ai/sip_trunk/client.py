# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.conversation_initiation_client_data_request_input import ConversationInitiationClientDataRequestInput
from ...types.sip_trunk_outbound_call_response import SipTrunkOutboundCallResponse
from .raw_client import AsyncRawSipTrunkClient, RawSipTrunkClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SipTrunkClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSipTrunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSipTrunkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSipTrunkClient
        """
        return self._raw_client

    def outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SipTrunkOutboundCallResponse:
        """
        Handle an outbound call via SIP trunk

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SipTrunkOutboundCallResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.sip_trunk.outbound_call(
            agent_id="agent_id",
            agent_phone_number_id="agent_phone_number_id",
            to_number="to_number",
        )
        """
        _response = self._raw_client.outbound_call(
            agent_id=agent_id,
            agent_phone_number_id=agent_phone_number_id,
            to_number=to_number,
            conversation_initiation_client_data=conversation_initiation_client_data,
            request_options=request_options,
        )
        return _response.data


class AsyncSipTrunkClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSipTrunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSipTrunkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSipTrunkClient
        """
        return self._raw_client

    async def outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SipTrunkOutboundCallResponse:
        """
        Handle an outbound call via SIP trunk

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SipTrunkOutboundCallResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.sip_trunk.outbound_call(
                agent_id="agent_id",
                agent_phone_number_id="agent_phone_number_id",
                to_number="to_number",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.outbound_call(
            agent_id=agent_id,
            agent_phone_number_id=agent_phone_number_id,
            to_number=to_number,
            conversation_initiation_client_data=conversation_initiation_client_data,
            request_options=request_options,
        )
        return _response.data
