# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .... import core
from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.add_knowledge_base_response_model import AddKnowledgeBaseResponseModel
from ....types.get_knowledge_base_dependent_agents_response_model import GetKnowledgeBaseDependentAgentsResponseModel
from .chunk.client import AsyncChunkClient, ChunkClient
from .raw_client import AsyncRawDocumentsClient, RawDocumentsClient
from .types.documents_get_response import DocumentsGetResponse
from .types.documents_update_response import DocumentsUpdateResponse

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class DocumentsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawDocumentsClient(client_wrapper=client_wrapper)
        self.chunk = ChunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawDocumentsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawDocumentsClient
        """
        return self._raw_client

    def create_from_url(
        self, *, url: str, name: typing.Optional[str] = OMIT, request_options: typing.Optional[RequestOptions] = None
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated by scraping the given webpage.

        Parameters
        ----------
        url : str
            URL to a page of documentation that the agent will have access to in order to interact with users.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.create_from_url(
            url="url",
        )
        """
        _response = self._raw_client.create_from_url(url=url, name=name, request_options=request_options)
        return _response.data

    def create_from_file(
        self,
        *,
        file: core.File,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated form the uploaded file.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.create_from_file()
        """
        _response = self._raw_client.create_from_file(file=file, name=name, request_options=request_options)
        return _response.data

    def create_from_text(
        self, *, text: str, name: typing.Optional[str] = OMIT, request_options: typing.Optional[RequestOptions] = None
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document containing the provided text.

        Parameters
        ----------
        text : str
            Text content to be added to the knowledge base.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.create_from_text(
            text="text",
        )
        """
        _response = self._raw_client.create_from_text(text=text, name=name, request_options=request_options)
        return _response.data

    def get(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DocumentsGetResponse:
        """
        Get details about a specific documentation making up the agent's knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DocumentsGetResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.get(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(documentation_id, request_options=request_options)
        return _response.data

    def delete(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.delete(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.delete(documentation_id, request_options=request_options)
        return _response.data

    def update(
        self, documentation_id: str, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DocumentsUpdateResponse:
        """
        Update the name of a document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        name : str
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DocumentsUpdateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.update(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
            name="name",
        )
        """
        _response = self._raw_client.update(documentation_id, name=name, request_options=request_options)
        return _response.data

    def get_agents(
        self,
        documentation_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseDependentAgentsResponseModel:
        """
        Get a list of agents depending on this knowledge base document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.get_agents(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get_agents(
            documentation_id, cursor=cursor, page_size=page_size, request_options=request_options
        )
        return _response.data

    def get_content(self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Get the entire content of a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.get_content(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get_content(documentation_id, request_options=request_options)
        return _response.data


class AsyncDocumentsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawDocumentsClient(client_wrapper=client_wrapper)
        self.chunk = AsyncChunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawDocumentsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawDocumentsClient
        """
        return self._raw_client

    async def create_from_url(
        self, *, url: str, name: typing.Optional[str] = OMIT, request_options: typing.Optional[RequestOptions] = None
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated by scraping the given webpage.

        Parameters
        ----------
        url : str
            URL to a page of documentation that the agent will have access to in order to interact with users.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.create_from_url(
                url="url",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_from_url(url=url, name=name, request_options=request_options)
        return _response.data

    async def create_from_file(
        self,
        *,
        file: core.File,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated form the uploaded file.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.create_from_file()


        asyncio.run(main())
        """
        _response = await self._raw_client.create_from_file(file=file, name=name, request_options=request_options)
        return _response.data

    async def create_from_text(
        self, *, text: str, name: typing.Optional[str] = OMIT, request_options: typing.Optional[RequestOptions] = None
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document containing the provided text.

        Parameters
        ----------
        text : str
            Text content to be added to the knowledge base.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.create_from_text(
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_from_text(text=text, name=name, request_options=request_options)
        return _response.data

    async def get(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DocumentsGetResponse:
        """
        Get details about a specific documentation making up the agent's knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DocumentsGetResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.get(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(documentation_id, request_options=request_options)
        return _response.data

    async def delete(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.delete(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(documentation_id, request_options=request_options)
        return _response.data

    async def update(
        self, documentation_id: str, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DocumentsUpdateResponse:
        """
        Update the name of a document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        name : str
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DocumentsUpdateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.update(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(documentation_id, name=name, request_options=request_options)
        return _response.data

    async def get_agents(
        self,
        documentation_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseDependentAgentsResponseModel:
        """
        Get a list of agents depending on this knowledge base document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.get_agents(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_agents(
            documentation_id, cursor=cursor, page_size=page_size, request_options=request_options
        )
        return _response.data

    async def get_content(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> None:
        """
        Get the entire content of a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.get_content(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_content(documentation_id, request_options=request_options)
        return _response.data
