# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.get_knowledge_base_list_response_model import GetKnowledgeBaseListResponseModel
from ...types.knowledge_base_document_type import KnowledgeBaseDocumentType
from .document.client import AsyncDocumentClient, DocumentClient
from .documents.client import AsyncDocumentsClient, DocumentsClient
from .raw_client import AsyncRawKnowledgeBaseClient, RawKnowledgeBaseClient


class KnowledgeBaseClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawKnowledgeBaseClient(client_wrapper=client_wrapper)
        self.documents = DocumentsClient(client_wrapper=client_wrapper)

        self.document = DocumentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawKnowledgeBaseClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawKnowledgeBaseClient
        """
        return self._raw_client

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        types: typing.Optional[
            typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]
        ] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseListResponseModel:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        types : typing.Optional[typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]]
            If present, the endpoint will return only documents of the given types.

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseListResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.list()
        """
        _response = self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            search=search,
            show_only_owned_documents=show_only_owned_documents,
            types=types,
            use_typesense=use_typesense,
            request_options=request_options,
        )
        return _response.data


class AsyncKnowledgeBaseClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawKnowledgeBaseClient(client_wrapper=client_wrapper)
        self.documents = AsyncDocumentsClient(client_wrapper=client_wrapper)

        self.document = AsyncDocumentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawKnowledgeBaseClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawKnowledgeBaseClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        types: typing.Optional[
            typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]
        ] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseListResponseModel:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        types : typing.Optional[typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]]
            If present, the endpoint will return only documents of the given types.

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseListResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            search=search,
            show_only_owned_documents=show_only_owned_documents,
            types=types,
            use_typesense=use_typesense,
            request_options=request_options,
        )
        return _response.data
