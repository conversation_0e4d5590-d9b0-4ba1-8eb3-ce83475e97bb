# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from ....types.sip_media_encryption_enum import SipMediaEncryptionEnum
from ....types.sip_trunk_credentials import SipTrunkCredentials
from ....types.sip_trunk_transport_enum import SipTrunkTransportEnum


class PhoneNumbersCreateRequestBody_Twilio(UncheckedBaseModel):
    """
    Create Phone Request Information
    """

    provider: typing.Literal["twilio"] = "twilio"
    phone_number: str
    label: str
    sid: str
    token: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PhoneNumbersCreateRequestBody_SipTrunk(UncheckedBaseModel):
    """
    Create Phone Request Information
    """

    provider: typing.Literal["sip_trunk"] = "sip_trunk"
    phone_number: str
    label: str
    termination_uri: str
    address: typing.Optional[str] = None
    transport: typing.Optional[SipTrunkTransportEnum] = None
    media_encryption: typing.Optional[SipMediaEncryptionEnum] = None
    headers: typing.Optional[typing.Dict[str, str]] = None
    credentials: typing.Optional[SipTrunkCredentials] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PhoneNumbersCreateRequestBody = typing_extensions.Annotated[
    typing.Union[PhoneNumbersCreateRequestBody_Twilio, PhoneNumbersCreateRequestBody_SipTrunk],
    UnionMetadata(discriminant="provider"),
]
