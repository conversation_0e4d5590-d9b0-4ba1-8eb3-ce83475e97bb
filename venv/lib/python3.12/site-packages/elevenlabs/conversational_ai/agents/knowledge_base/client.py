# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.get_agent_knowledgebase_size_response_model import GetAgentKnowledgebaseSizeResponseModel
from .raw_client import Async<PERSON>aw<PERSON>nowledgeBaseClient, RawKnowledgeBaseClient


class KnowledgeBaseClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawKnowledgeBaseClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawKnowledgeBaseClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawKnowledgeBaseClient
        """
        return self._raw_client

    def size(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentKnowledgebaseSizeResponseModel:
        """
        Returns the number of pages in the agent's knowledge base.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentKnowledgebaseSizeResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.agents.knowledge_base.size(
            agent_id="agent_id",
        )
        """
        _response = self._raw_client.size(agent_id, request_options=request_options)
        return _response.data


class AsyncKnowledgeBaseClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawKnowledgeBaseClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawKnowledgeBaseClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawKnowledgeBaseClient
        """
        return self._raw_client

    async def size(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentKnowledgebaseSizeResponseModel:
        """
        Returns the number of pages in the agent's knowledge base.

        Parameters
        ----------
        agent_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentKnowledgebaseSizeResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.agents.knowledge_base.size(
                agent_id="agent_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.size(agent_id, request_options=request_options)
        return _response.data
