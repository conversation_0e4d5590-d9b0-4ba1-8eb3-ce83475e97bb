# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..... import core
from .....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.post_agent_avatar_response_model import PostAgentAvatarResponseModel
from .raw_client import AsyncRawAvatarClient, RawAvatarClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class AvatarClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawAvatarClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawAvatarClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawAvatarClient
        """
        return self._raw_client

    def create(
        self, agent_id: str, *, avatar_file: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> PostAgentAvatarResponseModel:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostAgentAvatarResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.agents.widget.avatar.create(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.create(agent_id, avatar_file=avatar_file, request_options=request_options)
        return _response.data


class AsyncAvatarClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawAvatarClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawAvatarClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawAvatarClient
        """
        return self._raw_client

    async def create(
        self, agent_id: str, *, avatar_file: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> PostAgentAvatarResponseModel:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostAgentAvatarResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.agents.widget.avatar.create(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(agent_id, avatar_file=avatar_file, request_options=request_options)
        return _response.data
