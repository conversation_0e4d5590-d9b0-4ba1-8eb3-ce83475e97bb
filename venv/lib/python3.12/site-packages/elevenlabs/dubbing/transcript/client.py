# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rap<PERSON>, Sync<PERSON><PERSON><PERSON>rapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRawTranscriptClient, RawTranscriptClient
from .types.transcript_get_transcript_for_dub_request_format_type import TranscriptGetTranscriptForDubRequestFormatType


class TranscriptClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawTranscriptClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawTranscriptClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawTranscriptClient
        """
        return self._raw_client

    def get_transcript_for_dub(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        format_type: typing.Optional[TranscriptGetTranscriptForDubRequestFormatType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Returns transcript for the dub as an SRT or WEBVTT file.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        format_type : typing.Optional[TranscriptGetTranscriptForDubRequestFormatType]
            Format to use for the subtitle file, either 'srt' or 'webvtt'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.transcript.get_transcript_for_dub(
            dubbing_id="dubbing_id",
            language_code="language_code",
        )
        """
        _response = self._raw_client.get_transcript_for_dub(
            dubbing_id, language_code, format_type=format_type, request_options=request_options
        )
        return _response.data


class AsyncTranscriptClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawTranscriptClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawTranscriptClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawTranscriptClient
        """
        return self._raw_client

    async def get_transcript_for_dub(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        format_type: typing.Optional[TranscriptGetTranscriptForDubRequestFormatType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Returns transcript for the dub as an SRT or WEBVTT file.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        format_type : typing.Optional[TranscriptGetTranscriptForDubRequestFormatType]
            Format to use for the subtitle file, either 'srt' or 'webvtt'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.transcript.get_transcript_for_dub(
                dubbing_id="dubbing_id",
                language_code="language_code",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_transcript_for_dub(
            dubbing_id, language_code, format_type=format_type, request_options=request_options
        )
        return _response.data
