# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class PronunciationDictionaryRule_Alias(UncheckedBaseModel):
    type: typing.Literal["alias"] = "alias"
    string_to_replace: str
    alias: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PronunciationDictionaryRule_Phoneme(UncheckedBaseModel):
    type: typing.Literal["phoneme"] = "phoneme"
    string_to_replace: str
    phoneme: str
    alphabet: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PronunciationDictionaryRule = typing_extensions.Annotated[
    typing.Union[PronunciationDictionaryRule_Alias, PronunciationDictionaryRule_Phoneme],
    UnionMetadata(discriminant="type"),
]
