# This file was auto-generated by <PERSON><PERSON> from our API Definition.

# isort: skip_file

from .body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_rules_item import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme,
)
from .body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_workspace_access import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
)
from .pronunciation_dictionaries_create_from_file_request_workspace_access import (
    PronunciationDictionariesCreateFromFileRequestWorkspaceAccess,
)
from .pronunciation_dictionaries_list_request_sort import PronunciationDictionariesListRequestSort

__all__ = [
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess",
    "PronunciationDictionariesCreateFromFileRequestWorkspaceAccess",
    "PronunciationDictionariesListRequestSort",
]
