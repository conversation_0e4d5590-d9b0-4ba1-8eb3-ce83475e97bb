# This file was auto-generated by <PERSON><PERSON> from our API Definition.

# isort: skip_file

from .speech_to_speech_convert_request_file_format import SpeechToSpeechConvertRequestFileFormat
from .speech_to_speech_convert_request_output_format import SpeechToSpeechConvertRequestOutputFormat
from .speech_to_speech_stream_request_file_format import SpeechToSpeechStreamRequestFileFormat
from .speech_to_speech_stream_request_output_format import SpeechToSpeechStreamRequestOutputFormat

__all__ = [
    "SpeechToSpeechConvertRequestFileFormat",
    "SpeechToSpeechConvertRequestOutputFormat",
    "SpeechToSpeechStreamRequestFileFormat",
    "SpeechToSpeechStreamRequestOutputFormat",
]
