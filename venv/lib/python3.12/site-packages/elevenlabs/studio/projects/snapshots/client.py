# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import <PERSON>yncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.project_snapshot_extended_response_model import ProjectSnapshotExtendedResponseModel
from ....types.project_snapshots_response import ProjectSnapshotsResponse
from .raw_client import AsyncRawSnapshotsClient, RawSnapshotsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SnapshotsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSnapshotsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSnapshotsClient
        """
        return self._raw_client

    def list(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectSnapshotsResponse:
        """
        Retrieves a list of snapshots for a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.snapshots.list(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.list(project_id, request_options=request_options)
        return _response.data

    def get(
        self, project_id: str, project_snapshot_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectSnapshotExtendedResponseModel:
        """
        Returns the project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotExtendedResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.snapshots.get(
            project_id="21m00Tcm4TlvDq8ikWAM",
            project_snapshot_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(project_id, project_snapshot_id, request_options=request_options)
        return _response.data

    def stream(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Stream the audio from a Studio project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Successful Response
        """
        with self._raw_client.stream(
            project_id, project_snapshot_id, convert_to_mpeg=convert_to_mpeg, request_options=request_options
        ) as r:
            yield from r.data

    def stream_archive(
        self, project_id: str, project_snapshot_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[bytes]:
        """
        Returns a compressed archive of the Studio project's audio.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Streaming archive data
        """
        with self._raw_client.stream_archive(project_id, project_snapshot_id, request_options=request_options) as r:
            yield from r.data


class AsyncSnapshotsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSnapshotsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSnapshotsClient
        """
        return self._raw_client

    async def list(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectSnapshotsResponse:
        """
        Retrieves a list of snapshots for a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.snapshots.list(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(project_id, request_options=request_options)
        return _response.data

    async def get(
        self, project_id: str, project_snapshot_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectSnapshotExtendedResponseModel:
        """
        Returns the project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotExtendedResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.snapshots.get(
                project_id="21m00Tcm4TlvDq8ikWAM",
                project_snapshot_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(project_id, project_snapshot_id, request_options=request_options)
        return _response.data

    async def stream(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Stream the audio from a Studio project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Successful Response
        """
        async with self._raw_client.stream(
            project_id, project_snapshot_id, convert_to_mpeg=convert_to_mpeg, request_options=request_options
        ) as r:
            async for _chunk in r.data:
                yield _chunk

    async def stream_archive(
        self, project_id: str, project_snapshot_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[bytes]:
        """
        Returns a compressed archive of the Studio project's audio.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Streaming archive data
        """
        async with self._raw_client.stream_archive(
            project_id, project_snapshot_id, request_options=request_options
        ) as r:
            async for _chunk in r.data:
                yield _chunk
