# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .embedding_model_enum import EmbeddingModelEnum


class RagConfig(UncheckedBaseModel):
    enabled: typing.Optional[bool] = None
    embedding_model: typing.Optional[EmbeddingModelEnum] = None
    max_vector_distance: typing.Optional[float] = pydantic.Field(default=None)
    """
    Maximum vector distance of retrieved chunks.
    """

    max_documents_length: typing.Optional[int] = pydantic.Field(default=None)
    """
    Maximum total length of document chunks retrieved from RAG.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
