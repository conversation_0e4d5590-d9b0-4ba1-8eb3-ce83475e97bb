# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .safety_evaluation import SafetyEvaluation


class SafetyCommonModel(UncheckedBaseModel):
    """
    Safety object that has the information of safety evaluations based on used voice.
    """

    ivc: typing.Optional[SafetyEvaluation] = None
    non_ivc: typing.Optional[SafetyEvaluation] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
