# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conv_ai_stored_secret_dependencies import ConvAiStoredSecretDependencies


class ConvAiWorkspaceStoredSecretConfig(UncheckedBaseModel):
    type: typing.Literal["stored"] = "stored"
    secret_id: str
    name: str
    used_by: ConvAiStoredSecretDependencies

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
