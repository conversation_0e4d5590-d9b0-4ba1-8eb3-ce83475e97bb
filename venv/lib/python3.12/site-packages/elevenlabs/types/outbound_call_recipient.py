# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_initiation_client_data_request_input import ConversationInitiationClientDataRequestInput


class OutboundCallRecipient(UncheckedBaseModel):
    id: typing.Optional[str] = None
    phone_number: str
    conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
