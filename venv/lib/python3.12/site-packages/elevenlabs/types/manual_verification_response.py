# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .manual_verification_file_response import ManualVerificationFileResponse


class ManualVerificationResponse(UncheckedBaseModel):
    extra_text: str = pydantic.Field()
    """
    The extra text of the manual verification.
    """

    request_time_unix: int = pydantic.Field()
    """
    The date of the manual verification in Unix time.
    """

    files: typing.List[ManualVerificationFileResponse] = pydantic.Field()
    """
    The files of the manual verification.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
