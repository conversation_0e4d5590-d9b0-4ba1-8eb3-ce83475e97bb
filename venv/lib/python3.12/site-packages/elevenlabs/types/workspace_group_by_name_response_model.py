# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class WorkspaceGroupByNameResponseModel(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The name of the workspace group.
    """

    id: str = pydantic.Field()
    """
    The ID of the workspace group.
    """

    members_emails: typing.List[str] = pydantic.Field()
    """
    The emails of the members of the workspace group.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
