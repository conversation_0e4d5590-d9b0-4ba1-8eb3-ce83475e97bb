# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .dubbing_media_reference import DubbingMediaReference
from .render_status import RenderStatus
from .render_type import RenderType


class Render(UncheckedBaseModel):
    id: str
    version: int
    language: typing.Optional[str] = None
    type: typing.Optional[RenderType] = None
    media_ref: typing.Optional[DubbingMediaReference] = None
    status: RenderStatus

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
