# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .literal_json_schema_property import LiteralJsonSchemaProperty
from .query_params_json_schema import QueryParamsJsonSchema
from .webhook_tool_api_schema_config_input_method import WebhookToolApiSchemaConfigInputMethod
from .webhook_tool_api_schema_config_input_request_headers_value import (
    WebhookToolApiSchemaConfigInputRequestHeadersValue,
)


class WebhookToolApiSchemaConfigInput(UncheckedBaseModel):
    """
    Configuration for a webhook that will be called by an LLM tool.
    """

    url: str = pydantic.Field()
    """
    The URL that the webhook will be sent to. May include path parameters, e.g. https://example.com/agents/{agent_id}
    """

    method: typing.Optional[WebhookToolApiSchemaConfigInputMethod] = pydantic.Field(default=None)
    """
    The HTTP method to use for the webhook
    """

    path_params_schema: typing.Optional[typing.Dict[str, LiteralJsonSchemaProperty]] = pydantic.Field(default=None)
    """
    Schema for path parameters, if any. The keys should match the placeholders in the URL.
    """

    query_params_schema: typing.Optional[QueryParamsJsonSchema] = pydantic.Field(default=None)
    """
    Schema for any query params, if any. These will be added to end of the URL as query params. Note: properties in a query param must all be literal types
    """

    request_body_schema: typing.Optional["ObjectJsonSchemaPropertyInput"] = pydantic.Field(default=None)
    """
    Schema for the body parameters, if any. Used for POST/PATCH/PUT requests. The schema should be an object which will be sent as the json body
    """

    request_headers: typing.Optional[typing.Dict[str, WebhookToolApiSchemaConfigInputRequestHeadersValue]] = (
        pydantic.Field(default=None)
    )
    """
    Headers that should be included in the request
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(WebhookToolApiSchemaConfigInput)
