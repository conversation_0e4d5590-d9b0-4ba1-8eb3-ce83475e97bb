# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class CloseSocket(UncheckedBaseModel):
    """
    Payload to signal closing the entire WebSocket connection.
    """

    close_socket: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, closes all contexts and closes the entire WebSocket connection. Any context that was previously set to flush will wait to flush before closing.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
