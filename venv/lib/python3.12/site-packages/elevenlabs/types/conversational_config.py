# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config import AgentConfig
from .asr_conversational_config import AsrConversationalConfig
from .conversation_config import ConversationConfig
from .language_preset_output import LanguagePresetOutput
from .tts_conversational_config import TtsConversationalConfig
from .turn_config import TurnConfig


class ConversationalConfig(UncheckedBaseModel):
    asr: typing.Optional[AsrConversationalConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational transcription
    """

    turn: typing.Optional[TurnConfig] = pydantic.Field(default=None)
    """
    Configuration for turn detection
    """

    tts: typing.Optional[TtsConversationalConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational text to speech
    """

    conversation: typing.Optional[ConversationConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational events
    """

    language_presets: typing.Optional[typing.Dict[str, LanguagePresetOutput]] = pydantic.Field(default=None)
    """
    Language presets for conversations
    """

    agent: typing.Optional[AgentConfig] = pydantic.Field(default=None)
    """
    Agent specific configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(ConversationalConfig)
