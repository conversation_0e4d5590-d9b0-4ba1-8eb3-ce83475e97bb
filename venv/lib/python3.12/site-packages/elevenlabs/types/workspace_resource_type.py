# This file was auto-generated by Fern from our API Definition.

import typing

WorkspaceResourceType = typing.Union[
    typing.Literal[
        "voice",
        "voice_collection",
        "pronunciation_dictionary",
        "dubbing",
        "project",
        "convai_agents",
        "convai_knowledge_base_documents",
        "convai_tools",
        "convai_settings",
        "convai_secrets",
        "music_latent",
        "convai_phone_numbers",
        "convai_mcps",
        "convai_batch_calls",
    ],
    typing.Any,
]
