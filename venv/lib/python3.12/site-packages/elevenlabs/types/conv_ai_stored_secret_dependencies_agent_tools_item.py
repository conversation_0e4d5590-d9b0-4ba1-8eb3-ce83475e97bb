# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dependent_available_agent_tool_identifier_access_level import DependentAvailableAgentToolIdentifierAccessLevel


class ConvAiStoredSecretDependenciesAgentToolsItem_Available(UncheckedBaseModel):
    type: typing.Literal["available"] = "available"
    agent_id: str
    agent_name: str
    used_by: typing.List[str]
    created_at_unix_secs: int
    access_level: DependentAvailableAgentToolIdentifierAccessLevel

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ConvAiStoredSecretDependenciesAgentToolsItem_Unknown(UncheckedBaseModel):
    type: typing.Literal["unknown"] = "unknown"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


ConvAiStoredSecretDependenciesAgentToolsItem = typing_extensions.Annotated[
    typing.Union[
        ConvAiStoredSecretDependenciesAgentToolsItem_Available, ConvAiStoredSecretDependenciesAgentToolsItem_Unknown
    ],
    UnionMetadata(discriminant="type"),
]
