# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .podcast_bulletin_mode_data import PodcastBulletinModeData
from .podcast_conversation_mode_data import PodcastConversationModeData


class BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation(UncheckedBaseModel):
    """
    The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.
    """

    type: typing.Literal["conversation"] = "conversation"
    conversation: PodcastConversationModeData

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin(UncheckedBaseModel):
    """
    The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.
    """

    type: typing.Literal["bulletin"] = "bulletin"
    bulletin: PodcastBulletinModeData

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


BodyCreatePodcastV1ProjectsPodcastCreatePostMode = typing_extensions.Annotated[
    typing.Union[
        BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation,
        BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin,
    ],
    UnionMetadata(discriminant="type"),
]
