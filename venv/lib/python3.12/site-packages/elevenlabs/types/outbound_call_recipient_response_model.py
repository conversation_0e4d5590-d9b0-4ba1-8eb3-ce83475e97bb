# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .batch_call_recipient_status import BatchCallRecipientStatus
from .conversation_initiation_client_data_internal import ConversationInitiationClientDataInternal


class OutboundCallRecipientResponseModel(UncheckedBaseModel):
    id: str
    phone_number: str
    status: BatchCallRecipientStatus
    created_at_unix: int
    updated_at_unix: int
    conversation_id: typing.Optional[str] = None
    conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataInternal] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
