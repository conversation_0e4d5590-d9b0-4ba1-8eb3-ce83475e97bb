# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ProjectSnapshotResponse(UncheckedBaseModel):
    project_snapshot_id: str = pydantic.Field()
    """
    The ID of the project snapshot.
    """

    project_id: str = pydantic.Field()
    """
    The ID of the project.
    """

    created_at_unix: int = pydantic.Field()
    """
    The creation date of the project snapshot.
    """

    name: str = pydantic.Field()
    """
    The name of the project snapshot.
    """

    audio_upload: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = pydantic.Field(default=None)
    """
    (Deprecated)
    """

    zip_upload: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = pydantic.Field(default=None)
    """
    (Deprecated)
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
