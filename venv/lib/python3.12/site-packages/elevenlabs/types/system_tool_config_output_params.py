# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .agent_transfer import AgentTransfer
from .phone_number_transfer import PhoneNumberTransfer


class SystemToolConfigOutputParams_EndCall(UncheckedBaseModel):
    system_tool_type: typing.Literal["end_call"] = "end_call"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class SystemToolConfigOutputParams_LanguageDetection(UncheckedBaseModel):
    system_tool_type: typing.Literal["language_detection"] = "language_detection"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class SystemToolConfigOutputParams_TransferToAgent(UncheckedBaseModel):
    system_tool_type: typing.Literal["transfer_to_agent"] = "transfer_to_agent"
    transfers: typing.List[AgentTransfer]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class SystemToolConfigOutputParams_TransferToNumber(UncheckedBaseModel):
    system_tool_type: typing.Literal["transfer_to_number"] = "transfer_to_number"
    transfers: typing.List[PhoneNumberTransfer]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


SystemToolConfigOutputParams = typing_extensions.Annotated[
    typing.Union[
        SystemToolConfigOutputParams_EndCall,
        SystemToolConfigOutputParams_LanguageDetection,
        SystemToolConfigOutputParams_TransferToAgent,
        SystemToolConfigOutputParams_TransferToNumber,
    ],
    UnionMetadata(discriminant="system_tool_type"),
]
