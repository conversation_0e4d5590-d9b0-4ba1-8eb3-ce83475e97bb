# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .additional_format_response_model import AdditionalFormatResponseModel
from .speech_to_text_word_response_model import SpeechToTextWordResponseModel


class SpeechToTextChunkResponseModel(UncheckedBaseModel):
    """
    Chunk-level detail of the transcription with timing information.
    """

    language_code: str = pydantic.Field()
    """
    The detected language code (e.g. 'eng' for English).
    """

    language_probability: float = pydantic.Field()
    """
    The confidence score of the language detection (0 to 1).
    """

    text: str = pydantic.Field()
    """
    The raw text of the transcription.
    """

    words: typing.List[SpeechToTextWordResponseModel] = pydantic.Field()
    """
    List of words with their timing information.
    """

    additional_formats: typing.Optional[typing.List[typing.Optional[AdditionalFormatResponseModel]]] = pydantic.Field(
        default=None
    )
    """
    Requested additional formats of the transcript.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
