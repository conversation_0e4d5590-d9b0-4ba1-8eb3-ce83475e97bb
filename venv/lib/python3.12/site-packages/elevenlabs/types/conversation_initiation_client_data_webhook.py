# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_initiation_client_data_webhook_request_headers_value import (
    ConversationInitiationClientDataWebhookRequestHeadersValue,
)


class ConversationInitiationClientDataWebhook(UncheckedBaseModel):
    url: str = pydantic.Field()
    """
    The URL to send the webhook to
    """

    request_headers: typing.Dict[str, ConversationInitiationClientDataWebhookRequestHeadersValue] = pydantic.Field()
    """
    The headers to send with the webhook request
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
