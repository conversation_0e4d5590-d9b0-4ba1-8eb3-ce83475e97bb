# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class InvoiceResponse(UncheckedBaseModel):
    amount_due_cents: int = pydantic.Field()
    """
    The amount due in cents.
    """

    discount_percent_off: typing.Optional[float] = pydantic.Field(default=None)
    """
    The discount applied to the invoice. E.g. [20.0f] for 20% off.
    """

    next_payment_attempt_unix: int = pydantic.Field()
    """
    The Unix timestamp of the next payment attempt.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
