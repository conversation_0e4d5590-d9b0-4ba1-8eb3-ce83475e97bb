# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .reader_resource_response_model_resource_type import ReaderResourceResponseModelResourceType


class ReaderResourceResponseModel(UncheckedBaseModel):
    resource_type: ReaderResourceResponseModelResourceType = pydantic.Field()
    """
    The type of resource.
    """

    resource_id: str = pydantic.Field()
    """
    The ID of the resource.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
