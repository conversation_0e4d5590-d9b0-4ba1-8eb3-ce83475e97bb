# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.forced_alignment_response_model import ForcedAlignmentResponseModel
from .raw_client import AsyncRawForcedAlignmentClient, RawForcedAlignmentClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ForcedAlignmentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawForcedAlignmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawForcedAlignmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawForcedAlignmentClient
        """
        return self._raw_client

    def create(
        self,
        *,
        file: core.File,
        text: str,
        enabled_spooled_file: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ForcedAlignmentResponseModel:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio. The input text can be in any format, however diarization is not supported at this time.

        enabled_spooled_file : typing.Optional[bool]
            If true, the file will be streamed to the server and processed in chunks. This is useful for large files that cannot be loaded into memory. The default is false.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ForcedAlignmentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.forced_alignment.create(
            text="text",
        )
        """
        _response = self._raw_client.create(
            file=file, text=text, enabled_spooled_file=enabled_spooled_file, request_options=request_options
        )
        return _response.data


class AsyncForcedAlignmentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawForcedAlignmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawForcedAlignmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawForcedAlignmentClient
        """
        return self._raw_client

    async def create(
        self,
        *,
        file: core.File,
        text: str,
        enabled_spooled_file: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ForcedAlignmentResponseModel:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio. The input text can be in any format, however diarization is not supported at this time.

        enabled_spooled_file : typing.Optional[bool]
            If true, the file will be streamed to the server and processed in chunks. This is useful for large files that cannot be loaded into memory. The default is false.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ForcedAlignmentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.forced_alignment.create(
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            file=file, text=text, enabled_spooled_file=enabled_spooled_file, request_options=request_options
        )
        return _response.data
