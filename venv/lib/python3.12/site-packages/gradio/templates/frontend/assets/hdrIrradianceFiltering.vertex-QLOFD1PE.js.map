{"version": 3, "file": "hdrIrradianceFiltering.vertex-QLOFD1PE.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.vertex.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"hdrIrradianceFilteringVertexShader\";\nconst shader = `attribute vec2 position;varying vec3 direction;uniform vec3 up;uniform vec3 right;uniform vec3 front;\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\nmat3 view=mat3(up,right,front);direction=view*vec3(position,1.0);gl_Position=vec4(position,0.0,1.0);\n#define CUSTOM_VERTEX_MAIN_END\n}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const hdrIrradianceFilteringVertexShader = { name, shader };\n//# sourceMappingURL=hdrIrradianceFiltering.vertex.js.map"], "names": ["name", "shader", "ShaderStore", "hdrIrradianceFilteringVertexShader"], "mappings": "+FAEA,MAAMA,EAAO,qCACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAqC,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}