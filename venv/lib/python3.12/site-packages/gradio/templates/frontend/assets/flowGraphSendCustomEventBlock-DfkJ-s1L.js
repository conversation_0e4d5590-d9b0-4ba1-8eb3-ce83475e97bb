import{b as i}from"./KHR_interactivity-DVSiPm30.js";import{R as n}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./declarationMapper-r-RREw_K.js";import"./objectModelMapping-D3Nr8hfO.js";class s extends i{constructor(t){super(t),this.config=t;for(const e in this.config.eventData)this.registerDataInput(e,this.config.eventData[e].type,this.config.eventData[e].value)}_execute(t){const e=this.config.eventId,o={};this.dataInputs.forEach(a=>{o[a.name]=a.getValue(t)}),t.configuration.coordinator.notifyCustomEvent(e,o),this.out._activateSignal(t)}getClassName(){return"FlowGraphReceiveCustomEventBlock"}}n("FlowGraphReceiveCustomEventBlock",s);export{s as FlowGraphSendCustomEventBlock};
//# sourceMappingURL=flowGraphSendCustomEventBlock-DfkJ-s1L.js.map
