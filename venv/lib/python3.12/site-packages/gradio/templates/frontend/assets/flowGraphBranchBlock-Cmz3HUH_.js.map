{"version": 3, "file": "flowGraphBranchBlock-Cmz3HUH_.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphBranchBlock.js"], "sourcesContent": ["import { RichTypeBoolean } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphExecutionBlock } from \"../../../flowGraphExecutionBlock.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * A block that evaluates a condition and activates one of two branches.\n */\nexport class FlowGraphBranchBlock extends FlowGraphExecutionBlock {\n    constructor(config) {\n        super(config);\n        this.condition = this.registerDataInput(\"condition\", RichTypeBoolean);\n        this.onTrue = this._registerSignalOutput(\"onTrue\");\n        this.onFalse = this._registerSignalOutput(\"onFalse\");\n    }\n    _execute(context) {\n        if (this.condition.getValue(context)) {\n            this.onTrue._activateSignal(context);\n        }\n        else {\n            this.onFalse._activateSignal(context);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphBranchBlock\" /* FlowGraphBlockNames.Branch */;\n    }\n}\nRegisterClass(\"FlowGraphBranchBlock\" /* FlowGraphBlockNames.Branch */, FlowGraphBranchBlock);\n//# sourceMappingURL=flowGraphBranchBlock.js.map"], "names": ["FlowGraphBranchBlock", "FlowGraphExecutionBlock", "config", "RichTypeBoolean", "context", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAA6BC,CAAwB,CAC9D,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,UAAY,KAAK,kBAAkB,YAAaC,CAAe,EACpE,KAAK,OAAS,KAAK,sBAAsB,QAAQ,EACjD,KAAK,QAAU,KAAK,sBAAsB,SAAS,CACtD,CACD,SAASC,EAAS,CACV,KAAK,UAAU,SAASA,CAAO,EAC/B,KAAK,OAAO,gBAAgBA,CAAO,EAGnC,KAAK,QAAQ,gBAAgBA,CAAO,CAE3C,CAID,cAAe,CACX,MAAO,sBACV,CACL,CACAC,EAAc,uBAAyDL,CAAoB", "x_google_ignoreList": [0]}