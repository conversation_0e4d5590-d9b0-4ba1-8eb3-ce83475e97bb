const __vite__fileDeps=["./rgbdDecode.fragment-BdCjlJKZ.js","./index-Cb4A4-Xi.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./helperFunctions-DzxrWFCN.js","./rgbdDecode.fragment-CEbhyVHR.js","./helperFunctions-DvZkArRr.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as D}from"./index-Ccc2t4AG.js";import{h as P,I as C,B as O,b as G,ag as z,ah as V,ai as k,V as d}from"./index-Cb4A4-Xi.js";import"./dumpTools-BnnULfWw.js";const v="image/png",E=2,S=[134,22,135,150,246,214,150,54];function J(e){const a=new DataView(e.buffer,e.byteOffset,e.byteLength);let t=0;for(let s=0;s<S.length;s++)if(a.getUint8(t++)!==S[s])return P.Error("Not a babylon environment map"),null;let n="",o=0;for(;o=a.getUint8(t++);)n+=String.fromCharCode(o);let r=JSON.parse(n);return r=x(r),r.binaryDataPosition=t,r.specular&&(r.specular.lodGenerationScale=r.specular.lodGenerationScale||.8),r}function x(e){if(e.version>E)throw new Error(`Unsupported babylon environment map version "${e.version}". Latest supported version is "${E}".`);return e.version===2||(e={...e,version:2,imageType:v}),e}function H(e,a){a=x(a);const t=a.specular;let n=Math.log2(a.width);if(n=Math.round(n)+1,t.mipmaps.length!==6*n)throw new Error(`Unsupported specular mipmaps number "${t.mipmaps.length}"`);const o=new Array(n);for(let r=0;r<n;r++){o[r]=new Array(6);for(let s=0;s<6;s++){const l=t.mipmaps[r*6+s];o[r][s]=new Uint8Array(e.buffer,e.byteOffset+a.binaryDataPosition+l.position,l.length)}}return o}function Y(e,a){a=x(a);const t=new Array(6),n=a.irradiance?.irradianceTexture;if(n){if(n.faces.length!==6)throw new Error(`Incorrect irradiance texture faces number "${n.faces.length}"`);for(let o=0;o<6;o++){const r=n.faces[o];t[o]=new Uint8Array(e.buffer,e.byteOffset+a.binaryDataPosition+r.position,r.length)}}return t}function q(e,a,t){t=x(t);const n=t.specular;if(!n)return Promise.resolve([]);e._lodGenerationScale=n.lodGenerationScale;const o=[],r=H(a,t);o.push(F(e,r,t.imageType));const s=t.irradiance?.irradianceTexture;if(s){const l=Y(a,t);o.push($(e,l,s.size,t.imageType))}return Promise.all(o)}function U(e,a,t,n,o,r,s,l,h,f,w){return new Promise((m,I)=>{if(t){const y=a.createTexture(null,!0,!0,null,1,null,i=>{I(i)},e);n?.onEffectCreatedObservable.addOnce(i=>{i.executeWhenCompiled(()=>{n.externalTextureSamplerBinding=!0,n.onApply=c=>{c._bindTexture("textureSampler",y),c.setFloat2("scale",1,a._features.needsInvertingBitmap&&e instanceof ImageBitmap?-1:1)},a.scenes.length&&(a.scenes[0].postProcessManager.directRender([n],f,!0,r,s),a.restoreDefaultFramebuffer(),y.dispose(),URL.revokeObjectURL(o),m())})})}else{if(a._uploadImageToTexture(w,e,r,s),l){const y=h[s];y&&a._uploadImageToTexture(y._texture,e,r,0)}m()}})}async function F(e,a,t=v){const n=e.getEngine();e.format=5,e.type=0,e.generateMipMaps=!0,e._cachedAnisotropicFilteringLevel=null,n.updateTextureSamplingMode(3,e),await B(e,a,!0,t),e.isReady=!0}async function $(e,a,t,n=v){const o=e.getEngine(),r=new C(o,5),s=new O(o,r);e._irradianceTexture=s,r.isCube=!0,r.format=5,r.type=0,r.generateMipMaps=!0,r._cachedAnisotropicFilteringLevel=null,r.generateMipMaps=!0,r.width=t,r.height=t,o.updateTextureSamplingMode(3,r),await B(r,[a],!1,n),o.generateMipMapsForCubemap(r),r.isReady=!0}async function B(e,a,t,n=v){if(!G.IsExponentOfTwo(e.width))throw new Error("Texture size must be a power of two");const o=z(e.width)+1,r=e.getEngine();let s=!1,l=!1,h=null,f=null,w=null;const m=r.getCaps();m.textureLOD?r._features.supportRenderAndCopyToLodForFloatTextures?m.textureHalfFloatRender&&m.textureHalfFloatLinearFiltering?(s=!0,e.type=2):m.textureFloatRender&&m.textureFloatLinearFiltering&&(s=!0,e.type=1):s=!1:(s=!1,l=t);let I=0;if(s)r.isWebGPU?(I=1,await D(()=>import("./rgbdDecode.fragment-BdCjlJKZ.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url)):await D(()=>import("./rgbdDecode.fragment-CEbhyVHR.js"),__vite__mapDeps([5,1,2,3,6]),import.meta.url),h=new V("rgbdDecode","rgbdDecode",null,null,1,null,3,r,!1,void 0,e.type,void 0,null,!1,void 0,I),e._isRGBD=!1,e.invertY=!1,f=r.createRenderTargetCubeTexture(e.width,{generateDepthBuffer:!1,generateMipMaps:!0,generateStencilBuffer:!1,samplingMode:3,type:e.type,format:5});else if(e._isRGBD=!0,e.invertY=!0,l){w={};const c=e._lodGenerationScale,g=e._lodGenerationOffset;for(let p=0;p<3;p++){const b=1-p/2,u=g,M=(o-1)*c+g,L=u+(M-u)*b,A=Math.round(Math.min(Math.max(L,0),M)),R=new C(r,2);R.isCube=!0,R.invertY=!0,R.generateMipMaps=!1,r.updateTextureSamplingMode(2,R);const T=new O(null);switch(T._isCube=!0,T._texture=R,w[A]=T,p){case 0:e._lodTextureLow=T;break;case 1:e._lodTextureMid=T;break;case 2:e._lodTextureHigh=T;break}}}const y=[];for(let i=0;i<a.length;i++)for(let c=0;c<6;c++){const g=a[i][c],p=new Blob([g],{type:n}),_=URL.createObjectURL(p);let b;if(r._features.forceBitmapOverHTMLImageElement)b=r.createImageBitmap(p,{premultiplyAlpha:"none"}).then(u=>U(u,r,s,h,_,c,i,l,w,f,e));else{const u=new Image;u.src=_,b=new Promise((M,L)=>{u.onload=()=>{U(u,r,s,h,_,c,i,l,w,f,e).then(()=>M()).catch(A=>{L(A)})},u.onerror=A=>{L(A)}})}y.push(b)}if(await Promise.all(y),a.length<o){let i;const c=Math.pow(2,o-1-a.length),g=c*c*4;switch(e.type){case 0:{i=new Uint8Array(g);break}case 2:{i=new Uint16Array(g);break}case 1:{i=new Float32Array(g);break}}for(let p=a.length;p<o;p++)for(let _=0;_<6;_++)r._uploadArrayBufferViewToTexture(f?.texture||e,i,_,p)}if(f){const i=e._irradianceTexture;e._irradianceTexture=null,r._releaseTexture(e),f._swapAndDie(e),e._irradianceTexture=i}h&&h.dispose(),l&&(e._lodTextureHigh&&e._lodTextureHigh._texture&&(e._lodTextureHigh._texture.isReady=!0),e._lodTextureMid&&e._lodTextureMid._texture&&(e._lodTextureMid._texture.isReady=!0),e._lodTextureLow&&e._lodTextureLow._texture&&(e._lodTextureLow._texture.isReady=!0))}function K(e,a){a=x(a);const t=a.irradiance;if(!t)return;const n=new k;d.FromArrayToRef(t.x,0,n.x),d.FromArrayToRef(t.y,0,n.y),d.FromArrayToRef(t.z,0,n.z),d.FromArrayToRef(t.xx,0,n.xx),d.FromArrayToRef(t.yy,0,n.yy),d.FromArrayToRef(t.zz,0,n.zz),d.FromArrayToRef(t.yz,0,n.yz),d.FromArrayToRef(t.zx,0,n.zx),d.FromArrayToRef(t.xy,0,n.xy),e._sphericalPolynomial=n}function Q(e,a,t,n,o){const r=e.getEngine().createRawCubeTexture(null,e.width,e.format,e.type,e.generateMipMaps,e.invertY,e.samplingMode,e._compression),s=F(r,a).then(()=>e);return e.onRebuildCallback=l=>({proxy:s,isReady:!0,isAsync:!0}),e._source=13,e._bufferViewArrayArray=a,e._lodGenerationScale=n,e._lodGenerationOffset=o,e._sphericalPolynomial=t,F(e,a).then(()=>(e.isReady=!0,e))}export{J as G,K as U,Q as _,q as a};
//# sourceMappingURL=environmentTextureTools-B2gws8HC.js.map
