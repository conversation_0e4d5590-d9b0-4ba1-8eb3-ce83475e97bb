{"version": 3, "file": "flowGraphPointerOverEventBlock-DxP_c5QG.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOverEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { RichTypeAny, RichTypeNumber } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { _isADescendantOf } from \"../../utils.js\";\n/**\n * A pointer over event block.\n * This block can be used as an entry pointer to when a pointer is over a specific target mesh.\n */\nexport class FlowGraphPointerOverEventBlock extends FlowGraphEventBlock {\n    constructor(config) {\n        super(config);\n        this.type = \"PointerOver\" /* FlowGraphEventType.PointerOver */;\n        this.pointerId = this.registerDataOutput(\"pointerId\", RichTypeNumber);\n        this.targetMesh = this.registerDataInput(\"targetMesh\", RichTypeAny, config?.targetMesh);\n        this.meshUnderPointer = this.registerDataOutput(\"meshUnderPointer\", RichTypeAny);\n    }\n    _executeEvent(context, payload) {\n        const mesh = this.targetMesh.getValue(context);\n        this.meshUnderPointer.setValue(payload.mesh, context);\n        // skip if we moved from a mesh that is under the hierarchy of the target mesh\n        const skipEvent = payload.out && _isADescendantOf(payload.out, mesh);\n        this.pointerId.setValue(payload.pointerId, context);\n        if (!skipEvent && (payload.mesh === mesh || _isADescendantOf(payload.mesh, mesh))) {\n            this._execute(context);\n            return !this.config?.stopPropagation;\n        }\n        return true;\n    }\n    _preparePendingTasks(_context) {\n        // no-op\n    }\n    _cancelPendingTasks(_context) {\n        // no-op\n    }\n    getClassName() {\n        return \"FlowGraphPointerOverEventBlock\" /* FlowGraphBlockNames.PointerOverEvent */;\n    }\n}\nRegisterClass(\"FlowGraphPointerOverEventBlock\" /* FlowGraphBlockNames.PointerOverEvent */, FlowGraphPointerOverEventBlock);\n//# sourceMappingURL=flowGraphPointerOverEventBlock.js.map"], "names": ["FlowGraphPointerOverEventBlock", "FlowGraphEventBlock", "config", "RichTypeNumber", "RichTypeAny", "context", "payload", "mesh", "skip<PERSON><PERSON>", "_isADescendantOf", "_context", "RegisterClass"], "mappings": "8PAQO,MAAMA,UAAuCC,CAAoB,CACpE,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,KAAO,cACZ,KAAK,UAAY,KAAK,mBAAmB,YAAaC,CAAc,EACpE,KAAK,WAAa,KAAK,kBAAkB,aAAcC,EAAaF,GAAQ,UAAU,EACtF,KAAK,iBAAmB,KAAK,mBAAmB,mBAAoBE,CAAW,CAClF,CACD,cAAcC,EAASC,EAAS,CAC5B,MAAMC,EAAO,KAAK,WAAW,SAASF,CAAO,EAC7C,KAAK,iBAAiB,SAASC,EAAQ,KAAMD,CAAO,EAEpD,MAAMG,EAAYF,EAAQ,KAAOG,EAAiBH,EAAQ,IAAKC,CAAI,EAEnE,OADA,KAAK,UAAU,SAASD,EAAQ,UAAWD,CAAO,EAC9C,CAACG,IAAcF,EAAQ,OAASC,GAAQE,EAAiBH,EAAQ,KAAMC,CAAI,IAC3E,KAAK,SAASF,CAAO,EACd,CAAC,KAAK,QAAQ,iBAElB,EACV,CACD,qBAAqBK,EAAU,CAE9B,CACD,oBAAoBA,EAAU,CAE7B,CACD,cAAe,CACX,MAAO,gCACV,CACL,CACAC,EAAc,iCAA6EX,CAA8B", "x_google_ignoreList": [0]}