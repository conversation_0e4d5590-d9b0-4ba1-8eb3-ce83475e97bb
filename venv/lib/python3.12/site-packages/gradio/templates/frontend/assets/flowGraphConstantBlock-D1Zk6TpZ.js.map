{"version": 3, "file": "flowGraphConstantBlock-D1Zk6TpZ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConstantBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { getRichTypeFromValue } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { defaultValueSerializationFunction } from \"../../serialization.js\";\n/**\n * Block that returns a constant value.\n */\nexport class FlowGraphConstantBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this.output = this.registerDataOutput(\"output\", getRichTypeFromValue(config.value));\n    }\n    _updateOutputs(context) {\n        this.output.setValue(this.config.value, context);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphConstantBlock\" /* FlowGraphBlockNames.Constant */;\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize to\n     * @param valueSerializeFunction the function to use to serialize the value\n     */\n    serialize(serializationObject = {}, valueSerializeFunction = defaultValueSerializationFunction) {\n        super.serialize(serializationObject);\n        valueSerializeFunction(\"value\", this.config.value, serializationObject.config);\n    }\n}\nRegisterClass(\"FlowGraphConstantBlock\" /* FlowGraphBlockNames.Constant */, FlowGraphConstantBlock);\n//# sourceMappingURL=flowGraphConstantBlock.js.map"], "names": ["FlowGraphConstantBlock", "FlowGraphBlock", "config", "getRichTypeFromValue", "context", "serializationObject", "valueSerializeFunction", "defaultValueSerializationFunction", "RegisterClass"], "mappings": "uPAOO,MAAMA,UAA+BC,CAAe,CACvD,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,OAAS,KAAK,mBAAmB,SAAUC,EAAqBD,EAAO,KAAK,CAAC,CACrF,CACD,eAAeE,EAAS,CACpB,KAAK,OAAO,SAAS,KAAK,OAAO,MAAOA,CAAO,CAClD,CAKD,cAAe,CACX,MAAO,wBACV,CAMD,UAAUC,EAAsB,GAAIC,EAAyBC,EAAmC,CAC5F,MAAM,UAAUF,CAAmB,EACnCC,EAAuB,QAAS,KAAK,OAAO,MAAOD,EAAoB,MAAM,CAChF,CACL,CACAG,EAAc,yBAA6DR,CAAsB", "x_google_ignoreList": [0]}