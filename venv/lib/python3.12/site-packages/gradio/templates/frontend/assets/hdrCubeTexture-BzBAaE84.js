const __vite__fileDeps=["./hdrFiltering.vertex-BW3R2Hbo.js","./index-Cb4A4-Xi.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./hdrFiltering.fragment-jLbuPl1K.js","./helperFunctions-DzxrWFCN.js","./hdrFilteringFunctions-D0veL7GQ.js","./hdrFiltering.vertex-d7uF6ZlR.js","./hdrFiltering.fragment-B6g1aE73.js","./helperFunctions-DvZkArRr.js","./hdrFilteringFunctions-CAuUWR2X.js","./procedural.vertex-mclaqJ5u.js","./procedural.vertex-C_FPjJe2.js","./iblCdfx.fragment-dBdQZZlO.js","./iblCdfy.fragment-Cj_sNOIK.js","./iblScaledLuminance.fragment-DrVO-MiV.js","./iblCdfx.fragment-DUDPgkuv.js","./iblCdfy.fragment-DyAF1Cy5.js","./iblScaledLuminance.fragment-BEWM-ZCL.js","./iblIcdf.fragment-BWcCMNTp.js","./iblIcdf.fragment-Bm_EccLA.js","./iblCdfDebug.fragment-DgUEi6ld.js","./iblCdfDebug.fragment-CagQ33XN.js","./hdrIrradianceFiltering.vertex-CX1cfqEK.js","./hdrIrradianceFiltering.fragment-stWAfLe2.js","./hdrIrradianceFiltering.vertex-QLOFD1PE.js","./hdrIrradianceFiltering.fragment-CtQGnmVz.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{ag as C,am as F,al as U,V as a,b2 as L,b as v,_ as b,s as x,R as z,T,O as P,ak as G,b3 as k,aa as p,as as A,t as B,az as N,b4 as H,ah as Y,b5 as q,B as V,M as D,aw as K,b6 as I,av as M}from"./index-Cb4A4-Xi.js";import{G as X}from"./hdr-LsP_yQgA.js";import{_ as c}from"./index-Ccc2t4AG.js";import{R as O}from"./rawTexture-PjZ4PTsN.js";import"./svelte/svelte.js";class j{constructor(e,t={}){this._lodGenerationOffset=0,this._lodGenerationScale=.8,this.quality=4096,this.hdrScale=1,this._engine=e,this.hdrScale=t.hdrScale||this.hdrScale,this.quality=t.quality||this.quality}_createRenderTarget(e){let t=0;this._engine.getCaps().textureHalfFloatRender?t=2:this._engine.getCaps().textureFloatRender&&(t=1);const i=this._engine.createRenderTargetCubeTexture(e,{format:5,type:t,createMipMaps:!0,generateMipMaps:!1,generateDepthBuffer:!1,generateStencilBuffer:!1,samplingMode:1,label:"HDR_Radiance_Filtering_Target"});return this._engine.updateTextureWrappingMode(i.texture,0,0,0),this._engine.updateTextureSamplingMode(3,i.texture,!0),i}_prefilterInternal(e){const t=e.getSize().width,i=C(t)+1,s=this._effectWrapper.effect,r=this._createRenderTarget(t);this._effectRenderer.saveStates(),this._effectRenderer.setViewport();const h=e.getInternalTexture();h&&this._engine.updateTextureSamplingMode(3,h,!0),this._effectRenderer.applyEffectWrapper(this._effectWrapper);const l=[[new a(0,0,-1),new a(0,-1,0),new a(1,0,0)],[new a(0,0,1),new a(0,-1,0),new a(-1,0,0)],[new a(1,0,0),new a(0,0,1),new a(0,1,0)],[new a(1,0,0),new a(0,0,-1),new a(0,-1,0)],[new a(1,0,0),new a(0,-1,0),new a(0,0,1)],[new a(-1,0,0),new a(0,-1,0),new a(0,0,-1)]];s.setFloat("hdrScale",this.hdrScale),s.setFloat2("vFilteringInfo",e.getSize().width,i),s.setTexture("inputTexture",e);for(let d=0;d<6;d++){s.setVector3("up",l[d][0]),s.setVector3("right",l[d][1]),s.setVector3("front",l[d][2]);for(let n=0;n<i;n++){this._engine.bindFramebuffer(r,d,void 0,void 0,!0,n),this._effectRenderer.applyEffectWrapper(this._effectWrapper);let f=Math.pow(2,(n-this._lodGenerationOffset)/this._lodGenerationScale)/t;n===0&&(f=0),s.setFloat("alphaG",f),this._effectRenderer.draw()}}this._effectRenderer.restoreStates(),this._engine.restoreDefaultFramebuffer(),this._engine._releaseTexture(e._texture);const _=r.texture.type,o=r.texture.format;return r._swapAndDie(e._texture),e._texture.type=_,e._texture.format=o,e.gammaSpace=!1,e.lodGenerationOffset=this._lodGenerationOffset,e.lodGenerationScale=this._lodGenerationScale,e._prefiltered=!0,e}_createEffect(e,t){const i=[];e.gammaSpace&&i.push("#define GAMMA_INPUT"),i.push("#define NUM_SAMPLES "+this.quality+"u");const s=this._engine.isWebGPU;return new F({engine:this._engine,name:"hdrFiltering",vertexShader:"hdrFiltering",fragmentShader:"hdrFiltering",samplerNames:["inputTexture"],uniformNames:["vSampleDirections","vWeights","up","right","front","vFilteringInfo","hdrScale","alphaG"],useShaderStore:!0,defines:i,onCompiled:t,shaderLanguage:s?1:0,extraInitializationsAsync:async()=>{s?await Promise.all([c(()=>import("./hdrFiltering.vertex-BW3R2Hbo.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),c(()=>import("./hdrFiltering.fragment-jLbuPl1K.js"),__vite__mapDeps([4,1,2,3,5,6]),import.meta.url)]):await Promise.all([c(()=>import("./hdrFiltering.vertex-d7uF6ZlR.js"),__vite__mapDeps([7,1,2,3]),import.meta.url),c(()=>import("./hdrFiltering.fragment-B6g1aE73.js"),__vite__mapDeps([8,1,2,3,9,10]),import.meta.url)])}})}isReady(e){return e.isReady()&&this._effectWrapper.effect.isReady()}async prefilter(e){if(!this._engine._features.allowTexturePrefiltering)throw new Error("HDR prefiltering is not available in WebGL 1., you can use real time filtering instead.");this._effectRenderer=new U(this._engine),this._effectWrapper=this._createEffect(e),await this._effectWrapper.effect.whenCompiledAsync(),this._prefilterInternal(e),this._effectRenderer.dispose(),this._effectWrapper.dispose()}}class Z{constructor(e){this.name=L.NAME_PROCEDURALTEXTURE,this.scene=e}register(){this.scene._beforeClearStage.registerStep(L.STEP_BEFORECLEAR_PROCEDURALTEXTURE,this,this._beforeClear)}rebuild(){}dispose(){}_beforeClear(){if(this.scene.proceduralTexturesEnabled){v.StartPerformanceCounter("Procedural textures",this.scene.proceduralTextures.length>0);for(let e=0;e<this.scene.proceduralTextures.length;e++){const t=this.scene.proceduralTextures[e];t._shouldRender()&&t.render()}v.EndPerformanceCounter("Procedural textures",this.scene.proceduralTextures.length>0)}}}class u extends T{get shaderLanguage(){return this._shaderLanguage}constructor(e,t,i,s,r=null,h=!0,l=!1,_=0){super(null,s,!h),this.isEnabled=!0,this.autoClear=!0,this.onGeneratedObservable=new P,this.onBeforeGenerationObservable=new P,this.nodeMaterialSource=null,this.defines="",this._textures={},this._currentRefreshId=-1,this._frameId=-1,this._refreshRate=1,this._vertexBuffers={},this._uniforms=new Array,this._samplers=new Array,this._floats={},this._ints={},this._floatsArrays={},this._colors3={},this._colors4={},this._vectors2={},this._vectors3={},this._vectors4={},this._matrices={},this._fallbackTextureUsed=!1,this._cachedDefines=null,this._contentUpdateId=-1,this._rtWrapper=null,r!==null&&!(r instanceof T)?(this._options=r,this._fallbackTexture=r.fallbackTexture??null):(this._options={},this._fallbackTexture=r),this._shaderLanguage=this._options.shaderLanguage??0,s=this.getScene()||G.LastCreatedScene;let o=s._getComponent(L.NAME_PROCEDURALTEXTURE);o||(o=new Z(s),s._addComponent(o)),s.proceduralTextures.push(this),this._fullEngine=s.getEngine(),this.name=e,this.isRenderTarget=!0,this._size=t,this._textureType=_,this._generateMipMaps=h,this._drawWrapper=new k(this._fullEngine),this.setFragment(i);const d=this._createRtWrapper(l,t,h,_);this._texture=d.texture;const n=[];n.push(1,1),n.push(-1,1),n.push(-1,-1),n.push(1,-1),this._vertexBuffers[p.PositionKind]=new p(this._fullEngine,n,p.PositionKind,!1,!1,2),this._createIndexBuffer()}_createRtWrapper(e,t,i,s){return e?(this._rtWrapper=this._fullEngine.createRenderTargetCubeTexture(t,{generateMipMaps:i,generateDepthBuffer:!1,generateStencilBuffer:!1,type:s,...this._options}),this.setFloat("face",0)):(this._rtWrapper=this._fullEngine.createRenderTargetTexture(t,{generateMipMaps:i,generateDepthBuffer:!1,generateStencilBuffer:!1,type:s,...this._options}),this._rtWrapper.is3D&&(this.setFloat("layer",0),this.setInt("layerNum",0))),this._rtWrapper}getEffect(){return this._drawWrapper.effect}_setEffect(e){this._drawWrapper.effect=e}getContent(){return this._contentData&&this._frameId===this._contentUpdateId?this._contentData:(this._contentData?this._contentData.then(e=>{this._contentData=this.readPixels(0,0,e),this._contentUpdateId=this._frameId}):(this._contentData=this.readPixels(0,0),this._contentUpdateId=this._frameId),this._contentData)}_createIndexBuffer(){const e=this._fullEngine,t=[];t.push(0),t.push(1),t.push(2),t.push(0),t.push(2),t.push(3),this._indexBuffer=e.createIndexBuffer(t)}_rebuild(){const e=this._vertexBuffers[p.PositionKind];e&&e._rebuild(),this._createIndexBuffer(),this.refreshRate===A.REFRESHRATE_RENDER_ONCE&&(this.refreshRate=A.REFRESHRATE_RENDER_ONCE)}reset(){this._drawWrapper.effect?.dispose(),this._drawWrapper.effect=null,this._cachedDefines=null}_getDefines(){return this.defines}executeWhenReady(e){if(this.isReady()){e(this);return}const t=this.getEffect();t&&t.executeWhenCompiled(()=>{e(this)})}isReady(){const e=this._fullEngine;if(this.nodeMaterialSource)return this._drawWrapper.effect.isReady();if(!this._fragment)return!1;if(this._fallbackTextureUsed)return!0;if(!this._texture)return!1;const t=this._getDefines();if(this._drawWrapper.effect&&t===this._cachedDefines&&this._drawWrapper.effect.isReady())return!0;const i={vertex:"procedural",fragmentElement:this._fragment.fragmentElement,fragmentSource:this._fragment.fragmentSource,fragment:typeof this._fragment=="string"?this._fragment:void 0};return this._cachedDefines!==t&&(this._cachedDefines=t,this._drawWrapper.effect=e.createEffect(i,[p.PositionKind],this._uniforms,this._samplers,t,void 0,void 0,()=>{this._rtWrapper?.dispose(),this._rtWrapper=this._texture=null,this._fallbackTexture&&(this._texture=this._fallbackTexture._texture,this._texture&&this._texture.incrementReferences()),this._fallbackTextureUsed=!0},void 0,this._shaderLanguage,async()=>{this._options.extraInitializationsAsync?this.shaderLanguage===1?await Promise.all([c(()=>import("./procedural.vertex-mclaqJ5u.js"),__vite__mapDeps([11,1,2,3]),import.meta.url),this._options.extraInitializationsAsync()]):await Promise.all([c(()=>import("./procedural.vertex-C_FPjJe2.js"),__vite__mapDeps([12,1,2,3]),import.meta.url),this._options.extraInitializationsAsync()]):this.shaderLanguage===1?await c(()=>import("./procedural.vertex-mclaqJ5u.js"),__vite__mapDeps([11,1,2,3]),import.meta.url):await c(()=>import("./procedural.vertex-C_FPjJe2.js"),__vite__mapDeps([12,1,2,3]),import.meta.url)})),this._drawWrapper.effect.isReady()}resetRefreshCounter(){this._currentRefreshId=-1}setFragment(e){this._fragment=e}get refreshRate(){return this._refreshRate}set refreshRate(e){this._refreshRate=e,this.resetRefreshCounter()}_shouldRender(){return!this.isEnabled||!this.isReady()||!this._texture?(this._texture&&(this._texture.isReady=!1),!1):this._fallbackTextureUsed?!1:this._currentRefreshId===-1?(this._currentRefreshId=1,this._frameId++,!0):this.refreshRate===this._currentRefreshId?(this._currentRefreshId=1,this._frameId++,!0):(this._currentRefreshId++,!1)}getRenderSize(){return this._size}resize(e,t){if(this._fallbackTextureUsed||!this._rtWrapper||!this._texture)return;const i=this._texture.isCube;this._rtWrapper.dispose();const s=this._createRtWrapper(i,e,t,this._textureType);this._texture=s.texture,this._size=e,this._generateMipMaps=t}_checkUniform(e){this._uniforms.indexOf(e)===-1&&this._uniforms.push(e)}setTexture(e,t){return this._samplers.indexOf(e)===-1&&this._samplers.push(e),this._textures[e]=t,this}setFloat(e,t){return this._checkUniform(e),this._floats[e]=t,this}setInt(e,t){return this._checkUniform(e),this._ints[e]=t,this}setFloats(e,t){return this._checkUniform(e),this._floatsArrays[e]=t,this}setColor3(e,t){return this._checkUniform(e),this._colors3[e]=t,this}setColor4(e,t){return this._checkUniform(e),this._colors4[e]=t,this}setVector2(e,t){return this._checkUniform(e),this._vectors2[e]=t,this}setVector3(e,t){return this._checkUniform(e),this._vectors3[e]=t,this}setVector4(e,t){return this._checkUniform(e),this._vectors4[e]=t,this}setMatrix(e,t){return this._checkUniform(e),this._matrices[e]=t,this}render(e){const t=this.getScene();if(!t)return;const i=this._fullEngine;if(i.enableEffect(this._drawWrapper),this.onBeforeGenerationObservable.notifyObservers(this),i.setState(!1),!this.nodeMaterialSource){for(const r in this._textures)this._drawWrapper.effect.setTexture(r,this._textures[r]);for(const r in this._ints)this._drawWrapper.effect.setInt(r,this._ints[r]);for(const r in this._floats)this._drawWrapper.effect.setFloat(r,this._floats[r]);for(const r in this._floatsArrays)this._drawWrapper.effect.setArray(r,this._floatsArrays[r]);for(const r in this._colors3)this._drawWrapper.effect.setColor3(r,this._colors3[r]);for(const r in this._colors4){const h=this._colors4[r];this._drawWrapper.effect.setFloat4(r,h.r,h.g,h.b,h.a)}for(const r in this._vectors2)this._drawWrapper.effect.setVector2(r,this._vectors2[r]);for(const r in this._vectors3)this._drawWrapper.effect.setVector3(r,this._vectors3[r]);for(const r in this._vectors4)this._drawWrapper.effect.setVector4(r,this._vectors4[r]);for(const r in this._matrices)this._drawWrapper.effect.setMatrix(r,this._matrices[r])}if(!this._texture||!this._rtWrapper)return;i._debugPushGroup?.(`procedural texture generation for ${this.name}`,1);const s=i.currentViewport;if(this.isCube)for(let r=0;r<6;r++)i.bindFramebuffer(this._rtWrapper,r,void 0,void 0,!0),i.bindBuffers(this._vertexBuffers,this._indexBuffer,this._drawWrapper.effect),this._drawWrapper.effect.setFloat("face",r),this.autoClear&&i.clear(t.clearColor,!0,!1,!1),i.drawElementsType(B.TriangleFillMode,0,6),i.unBindFramebuffer(this._rtWrapper,!0);else{let r=1;this._rtWrapper.is3D?r=this._rtWrapper.depth:this._rtWrapper.is2DArray&&(r=this._rtWrapper.layers);for(let h=0;h<r;h++){if(i.bindFramebuffer(this._rtWrapper,0,void 0,void 0,!0,0,h),i.bindBuffers(this._vertexBuffers,this._indexBuffer,this._drawWrapper.effect),this._rtWrapper.is3D||this._rtWrapper.is2DArray){this._drawWrapper.effect?.setFloat("layer",r!==1?h/(r-1):0),this._drawWrapper.effect?.setInt("layerNum",h);for(const l in this._textures)this._drawWrapper.effect.setTexture(l,this._textures[l])}this.autoClear&&i.clear(t.clearColor,!0,!1,!1),i.drawElementsType(B.TriangleFillMode,0,6),i.unBindFramebuffer(this._rtWrapper,!this._generateMipMaps)}}s&&i.setViewport(s),this.isCube&&i.generateMipMapsForCubemap(this._texture,!0),i._debugPopGroup?.(1),this.onGenerated&&this.onGenerated(),this.onGeneratedObservable.notifyObservers(this)}clone(){const e=this.getSize(),t=new u(this.name,e.width,this._fragment,this.getScene(),this._fallbackTexture,this._generateMipMaps);return t.hasAlpha=this.hasAlpha,t.level=this.level,t.coordinatesMode=this.coordinatesMode,t}dispose(){const e=this.getScene();if(!e)return;const t=e.proceduralTextures.indexOf(this);t>=0&&e.proceduralTextures.splice(t,1);const i=this._vertexBuffers[p.PositionKind];i&&(i.dispose(),this._vertexBuffers[p.PositionKind]=null),this._indexBuffer&&this._fullEngine._releaseBuffer(this._indexBuffer)&&(this._indexBuffer=null),this.onGeneratedObservable.clear(),this.onBeforeGenerationObservable.clear(),super.dispose()}}b([x()],u.prototype,"isEnabled",void 0);b([x()],u.prototype,"autoClear",void 0);b([x()],u.prototype,"_generateMipMaps",void 0);b([x()],u.prototype,"_size",void 0);b([x()],u.prototype,"refreshRate",null);z("BABYLON.ProceduralTexture",u);class m{get iblSource(){return this._iblSource}set iblSource(e){this._iblSource!==e&&(this._disposeTextures(),this._iblSource=e,e&&(e.isCube?e.isReadyOrNotBlocking()?this._recreateAssetsFromNewIbl():e.onLoadObservable.addOnce(this._recreateAssetsFromNewIbl.bind(this,e)):e.isReadyOrNotBlocking()?this._recreateAssetsFromNewIbl():e.onLoadObservable.addOnce(this._recreateAssetsFromNewIbl.bind(this,e))))}_recreateAssetsFromNewIbl(){this._debugPass&&this._debugPass.dispose(),this._createTextures(),this._debugPass&&this._createDebugPass()}getIcdfTexture(){return this._icdfPT?this._icdfPT:this._dummyTexture}setDebugDisplayParams(e,t,i,s){this._debugSizeParams.set(e,t,i,s)}get debugPassName(){return this._debugPassName}getDebugPassPP(){return this._debugPass||this._createDebugPass(),this._debugPass}constructor(e){this.debugEnabled=!1,this._debugSizeParams=new N(0,0,1,1),this._debugPassName="CDF Debug",this.onGeneratedObservable=new P,e?m._IsScene(e)?this._scene=e:this._engine=e:this._scene=G.LastCreatedScene,this._scene&&(this._engine=this._scene.getEngine());const t=new Uint16Array([0,0,0,255]);this._dummyTexture=new O(t,1,1,H.TEXTUREFORMAT_RGBA,e,!1,!1,void 0,2),this._scene&&m._SceneComponentInitialization(this._scene)}_createTextures(){const e=this._iblSource?{width:this._iblSource.getSize().width,height:this._iblSource.getSize().height}:{width:1,height:1};this._iblSource||(this._iblSource=O.CreateRTexture(new Uint8Array([255]),1,1,this._engine,!1,!1,1,0),this._iblSource.name="Placeholder IBL Source"),this._iblSource.isCube&&(e.width*=4,e.height*=2,e.width=1<<Math.floor(Math.log2(e.width)),e.height=1<<Math.floor(Math.log2(e.height)));const t=this._engine.isWebGPU,i={generateDepthBuffer:!1,generateMipMaps:!1,format:6,type:1,samplingMode:1,shaderLanguage:t?1:0,gammaSpace:!1,extraInitializationsAsync:async()=>{t?await Promise.all([c(()=>import("./iblCdfx.fragment-dBdQZZlO.js"),__vite__mapDeps([13,1,2,3]),import.meta.url),c(()=>import("./iblCdfy.fragment-Cj_sNOIK.js"),__vite__mapDeps([14,1,2,3]),import.meta.url),c(()=>import("./iblScaledLuminance.fragment-DrVO-MiV.js"),__vite__mapDeps([15,1,2,3,5]),import.meta.url)]):await Promise.all([c(()=>import("./iblCdfx.fragment-DUDPgkuv.js"),__vite__mapDeps([16,1,2,3]),import.meta.url),c(()=>import("./iblCdfy.fragment-DyAF1Cy5.js"),__vite__mapDeps([17,1,2,3,9]),import.meta.url),c(()=>import("./iblScaledLuminance.fragment-BEWM-ZCL.js"),__vite__mapDeps([18,1,2,3,9]),import.meta.url)])}},s={generateDepthBuffer:!1,generateMipMaps:!1,format:5,type:2,samplingMode:1,shaderLanguage:t?1:0,gammaSpace:!1,extraInitializationsAsync:async()=>{t?await Promise.all([c(()=>import("./iblIcdf.fragment-BWcCMNTp.js"),__vite__mapDeps([19,1,2,3,5]),import.meta.url)]):await Promise.all([c(()=>import("./iblIcdf.fragment-Bm_EccLA.js"),__vite__mapDeps([20,1,2,3,9]),import.meta.url)])}};this._cdfyPT=new u("cdfyTexture",{width:e.width,height:e.height+1},"iblCdfy",this._scene,i,!1,!1),this._cdfyPT.autoClear=!1,this._cdfyPT.setTexture("iblSource",this._iblSource),this._cdfyPT.setInt("iblHeight",e.height),this._cdfyPT.wrapV=0,this._cdfyPT.refreshRate=0,this._iblSource.isCube&&(this._cdfyPT.defines=`#define IBL_USE_CUBE_MAP
`),this._cdfxPT=new u("cdfxTexture",{width:e.width+1,height:1},"iblCdfx",this._scene,i,!1,!1),this._cdfxPT.autoClear=!1,this._cdfxPT.setTexture("cdfy",this._cdfyPT),this._cdfxPT.refreshRate=0,this._cdfxPT.wrapU=0,this._scaledLuminancePT=new u("iblScaledLuminance",{width:e.width,height:e.height},"iblScaledLuminance",this._scene,{...i,samplingMode:3,generateMipMaps:!0},!0,!1),this._scaledLuminancePT.autoClear=!1,this._scaledLuminancePT.setTexture("iblSource",this._iblSource),this._scaledLuminancePT.setInt("iblHeight",e.height),this._scaledLuminancePT.setInt("iblWidth",e.width),this._scaledLuminancePT.refreshRate=0,this._iblSource.isCube&&(this._scaledLuminancePT.defines=`#define IBL_USE_CUBE_MAP
`),this._icdfPT=new u("icdfTexture",{width:e.width,height:e.height},"iblIcdf",this._scene,s,!1,!1),this._icdfPT.autoClear=!1,this._icdfPT.setTexture("cdfy",this._cdfyPT),this._icdfPT.setTexture("cdfx",this._cdfxPT),this._icdfPT.setTexture("iblSource",this._iblSource),this._icdfPT.setTexture("scaledLuminanceSampler",this._scaledLuminancePT),this._icdfPT.refreshRate=0,this._icdfPT.wrapV=0,this._icdfPT.wrapU=0,this._iblSource.isCube&&(this._icdfPT.defines=`#define IBL_USE_CUBE_MAP
`),this._icdfPT.onGeneratedObservable.addOnce(()=>{this.onGeneratedObservable.notifyObservers()})}_disposeTextures(){this._cdfyPT?.dispose(),this._cdfxPT?.dispose(),this._icdfPT?.dispose(),this._scaledLuminancePT?.dispose()}_createDebugPass(){this._debugPass&&this._debugPass.dispose();const e=this._engine.isWebGPU,t={width:this._engine.getRenderWidth(),height:this._engine.getRenderHeight(),samplingMode:T.BILINEAR_SAMPLINGMODE,engine:this._engine,textureType:0,uniforms:["sizeParams"],samplers:["cdfy","icdf","cdfx","iblSource"],defines:this._iblSource?.isCube?`#define IBL_USE_CUBE_MAP
`:"",shaderLanguage:e?1:0,extraInitializations:(s,r)=>{s?r.push(c(()=>import("./iblCdfDebug.fragment-DgUEi6ld.js"),__vite__mapDeps([21,1,2,3]),import.meta.url)):r.push(c(()=>import("./iblCdfDebug.fragment-CagQ33XN.js"),__vite__mapDeps([22,1,2,3]),import.meta.url))}};this._debugPass=new Y(this._debugPassName,"iblCdfDebug",t);const i=this._debugPass.getEffect();i&&(i.defines=this._iblSource?.isCube?`#define IBL_USE_CUBE_MAP
`:""),this._iblSource?.isCube&&this._debugPass.updateEffect(`#define IBL_USE_CUBE_MAP
`),this._debugPass.onApplyObservable.add(s=>{s.setTexture("cdfy",this._cdfyPT),s.setTexture("icdf",this._icdfPT),s.setTexture("cdfx",this._cdfxPT),s.setTexture("iblSource",this._iblSource),s.setFloat4("sizeParams",this._debugSizeParams.x,this._debugSizeParams.y,this._debugSizeParams.z,this._debugSizeParams.w)})}isReady(){return this._iblSource&&this._iblSource.name!=="Placeholder IBL Source"&&this._iblSource.isReady()&&this._cdfyPT&&this._cdfyPT.isReady()&&this._icdfPT&&this._icdfPT.isReady()&&this._cdfxPT&&this._cdfxPT.isReady()&&this._scaledLuminancePT&&this._scaledLuminancePT.isReady()}renderWhenReady(){this._icdfPT.onGeneratedObservable.addOnce(()=>{this.onGeneratedObservable.notifyObservers()});const e=[],t=[this._cdfyPT,this._cdfxPT,this._scaledLuminancePT,this._icdfPT];return t.forEach(i=>{e.push(new Promise(s=>{i.isReady()?s():i.getEffect().executeWhenCompiled(()=>{s()})}))}),Promise.all(e).then(()=>{t.forEach(i=>{i.render()})})}dispose(){this._disposeTextures(),this._dummyTexture.dispose(),this._debugPass&&this._debugPass.dispose(),this.onGeneratedObservable.clear()}static _IsScene(e){return e.getClassName()==="Scene"}}m._SceneComponentInitialization=w=>{throw q("IblCdfGeneratorSceneComponentSceneComponent")};class ${constructor(e,t={}){this.quality=4096,this.hdrScale=1,this.useCdf=!1,this._engine=e,this.hdrScale=t.hdrScale||this.hdrScale,this.quality=t.quality||this.quality,this.useCdf=t.useCdf||this.useCdf}_createRenderTarget(e){let t=0;this._engine.getCaps().textureHalfFloatRender?t=2:this._engine.getCaps().textureFloatRender&&(t=1);const i=this._engine.createRenderTargetCubeTexture(e,{format:5,type:t,createMipMaps:!1,generateMipMaps:!1,generateDepthBuffer:!1,generateStencilBuffer:!1,samplingMode:2,label:"HDR_Irradiance_Filtering_Target"});return this._engine.updateTextureWrappingMode(i.texture,0,0,0),i}_prefilterInternal(e){const t=e.getSize().width,i=C(t),s=this._effectWrapper.effect,r=Math.max(32,1<<C(t>>3)),h=this._createRenderTarget(r);this._effectRenderer.saveStates(),this._effectRenderer.setViewport(),this._effectRenderer.applyEffectWrapper(this._effectWrapper);const l=[[new a(0,0,-1),new a(0,-1,0),new a(1,0,0)],[new a(0,0,1),new a(0,-1,0),new a(-1,0,0)],[new a(1,0,0),new a(0,0,1),new a(0,1,0)],[new a(1,0,0),new a(0,0,-1),new a(0,-1,0)],[new a(1,0,0),new a(0,-1,0),new a(0,0,1)],[new a(-1,0,0),new a(0,-1,0),new a(0,0,-1)]];s.setFloat("hdrScale",this.hdrScale),s.setFloat2("vFilteringInfo",e.getSize().width,i),s.setTexture("inputTexture",e),this._cdfGenerator&&s.setTexture("icdfTexture",this._cdfGenerator.getIcdfTexture());for(let o=0;o<6;o++)s.setVector3("up",l[o][0]),s.setVector3("right",l[o][1]),s.setVector3("front",l[o][2]),this._engine.bindFramebuffer(h,o,void 0,void 0,!0),this._effectRenderer.applyEffectWrapper(this._effectWrapper),this._effectRenderer.draw();this._effectRenderer.restoreStates(),this._engine.restoreDefaultFramebuffer(),s.setTexture("inputTexture",null),s.setTexture("icdfTexture",null);const _=new V(e.getScene(),h.texture);return _.name=e.name+"_irradiance",_.displayName=e.name+"_irradiance",_.gammaSpace=!1,_}_createEffect(e,t){const i=[];e.gammaSpace&&i.push("#define GAMMA_INPUT"),i.push("#define NUM_SAMPLES "+this.quality+"u");const s=this._engine.isWebGPU,r=["inputTexture"];return this._cdfGenerator&&(r.push("icdfTexture"),i.push("#define IBL_CDF_FILTERING")),new F({engine:this._engine,name:"HDRIrradianceFiltering",vertexShader:"hdrIrradianceFiltering",fragmentShader:"hdrIrradianceFiltering",samplerNames:r,uniformNames:["vSampleDirections","vWeights","up","right","front","vFilteringInfo","hdrScale"],useShaderStore:!0,defines:i,onCompiled:t,shaderLanguage:s?1:0,extraInitializationsAsync:async()=>{s?await Promise.all([c(()=>import("./hdrIrradianceFiltering.vertex-CX1cfqEK.js"),__vite__mapDeps([23,1,2,3]),import.meta.url),c(()=>import("./hdrIrradianceFiltering.fragment-stWAfLe2.js"),__vite__mapDeps([24,1,2,3,5,6]),import.meta.url)]):await Promise.all([c(()=>import("./hdrIrradianceFiltering.vertex-QLOFD1PE.js"),__vite__mapDeps([25,1,2,3]),import.meta.url),c(()=>import("./hdrIrradianceFiltering.fragment-CtQGnmVz.js"),__vite__mapDeps([26,1,2,3,9,10]),import.meta.url)])}})}isReady(e){return e.isReady()&&this._effectWrapper.effect.isReady()}async prefilter(e){if(!this._engine._features.allowTexturePrefiltering)throw new Error("HDR prefiltering is not available in WebGL 1., you can use real time filtering instead.");this.useCdf&&(this._cdfGenerator=new m(this._engine),this._cdfGenerator.iblSource=e,await this._cdfGenerator.renderWhenReady()),this._effectRenderer=new U(this._engine),this._effectWrapper=this._createEffect(e),await this._effectWrapper.effect.whenCompiledAsync();const t=this._prefilterInternal(e);return this._effectRenderer.dispose(),this._effectWrapper.dispose(),this._cdfGenerator?.dispose(),t}}class g extends V{set isBlocking(e){this._isBlocking=e}get isBlocking(){return this._isBlocking}set rotationY(e){this._rotationY=e,this.setReflectionTextureMatrix(D.RotationY(this._rotationY))}get rotationY(){return this._rotationY}set boundingBoxSize(e){if(this._boundingBoxSize&&this._boundingBoxSize.equals(e))return;this._boundingBoxSize=e;const t=this.getScene();t&&t.markAllMaterialsAsDirty(1)}get boundingBoxSize(){return this._boundingBoxSize}constructor(e,t,i,s=!1,r=!0,h=!1,l=!1,_=null,o=null,d=!1,n=!1,f=!1){super(t),this._generateHarmonics=!0,this._onError=null,this._isBlocking=!0,this._rotationY=0,this.boundingBoxPosition=a.Zero(),this.onLoadObservable=new P,e&&(this._coordinatesMode=T.CUBIC_MODE,this.name=e,this.url=e,this.hasAlpha=!1,this.isCube=!0,this._textureMatrix=D.Identity(),this._prefilterOnLoad=l,this._prefilterIrradianceOnLoad=n,this._prefilterUsingCdf=f,this._onLoad=()=>{this.onLoadObservable.notifyObservers(this),_&&_()},this._onError=o,this.gammaSpace=h,this._noMipmap=s,this._size=i,this._supersample=d||f,this._generateHarmonics=r,this._texture=this._getFromCache(e,this._noMipmap,void 0,void 0,void 0,this.isCube),this._texture?this._texture.isReady?v.SetImmediate(()=>this._onLoad()):this._texture.onLoadedObservable.add(this._onLoad):this.getScene()?.useDelayedTextureLoading?this.delayLoadState=4:this._loadTexture())}getClassName(){return"HDRCubeTexture"}_loadTexture(){const e=this._getEngine(),t=e.getCaps();let i=0;t.textureFloat&&t.textureFloatLinearFiltering?i=1:t.textureHalfFloat&&t.textureHalfFloatLinearFiltering&&(i=2);const s=r=>{this.lodGenerationOffset=0,this.lodGenerationScale=.8;const h=X(r,this._size,this._supersample);if(this._generateHarmonics){const d=K.ConvertCubeMapToSphericalPolynomial(h);this.sphericalPolynomial=d}const l=[];let _=null,o=null;for(let d=0;d<6;d++){i===2?o=new Uint16Array(this._size*this._size*3):i===0&&(_=new Uint8Array(this._size*this._size*3));const n=h[g._FacesMapping[d]];if(this.gammaSpace||o||_){for(let f=0;f<this._size*this._size;f++)if(this.gammaSpace&&(n[f*3+0]=Math.pow(n[f*3+0],I),n[f*3+1]=Math.pow(n[f*3+1],I),n[f*3+2]=Math.pow(n[f*3+2],I)),o&&(o[f*3+0]=M(n[f*3+0]),o[f*3+1]=M(n[f*3+1]),o[f*3+2]=M(n[f*3+2])),_){let S=Math.max(n[f*3+0]*255,0),R=Math.max(n[f*3+1]*255,0),y=Math.max(n[f*3+2]*255,0);const W=Math.max(Math.max(S,R),y);if(W>255){const E=255/W;S*=E,R*=E,y*=E}_[f*3+0]=S,_[f*3+1]=R,_[f*3+2]=y}}o?l.push(o):_?l.push(_):l.push(n)}return l};if(e._features.allowTexturePrefiltering&&(this._prefilterOnLoad||this._prefilterIrradianceOnLoad)){const r=this._onLoad,h=new j(e);this._onLoad=()=>{let l=Promise.resolve(null),_=Promise.resolve();this._prefilterIrradianceOnLoad&&(l=new $(e,{useCdf:this._prefilterUsingCdf}).prefilter(this)),this._prefilterOnLoad&&(_=h.prefilter(this)),Promise.all([l,_]).then(o=>{const d=o[0];if(this._prefilterIrradianceOnLoad&&d){this.irradianceTexture=d;const n=this.getScene();n&&n.markAllMaterialsAsDirty(1)}r&&r()})}}this._texture=e.createRawCubeTextureFromUrl(this.url,this.getScene(),this._size,4,i,this._noMipmap,s,null,this._onLoad,this._onError)}clone(){const e=new g(this.url,this.getScene()||this._getEngine(),this._size,this._noMipmap,this._generateHarmonics,this.gammaSpace);return e.level=this.level,e.wrapU=this.wrapU,e.wrapV=this.wrapV,e.coordinatesIndex=this.coordinatesIndex,e.coordinatesMode=this.coordinatesMode,e}delayLoad(){this.delayLoadState===4&&(this.delayLoadState=1,this._texture=this._getFromCache(this.url,this._noMipmap),this._texture||this._loadTexture())}getReflectionTextureMatrix(){return this._textureMatrix}setReflectionTextureMatrix(e){this._textureMatrix=e,e.updateFlag!==this._textureMatrix.updateFlag&&e.isIdentity()!==this._textureMatrix.isIdentity()&&this.getScene()?.markAllMaterialsAsDirty(1,t=>t.getActiveTextures().indexOf(this)!==-1)}dispose(){this.onLoadObservable.clear(),super.dispose()}static Parse(e,t,i){let s=null;return e.name&&!e.isRenderTarget&&(s=new g(i+e.name,t,e.size,e.noMipmap,e.generateHarmonics,e.useInGammaSpace),s.name=e.name,s.hasAlpha=e.hasAlpha,s.level=e.level,s.coordinatesMode=e.coordinatesMode,s.isBlocking=e.isBlocking),s&&(e.boundingBoxPosition&&(s.boundingBoxPosition=a.FromArray(e.boundingBoxPosition)),e.boundingBoxSize&&(s.boundingBoxSize=a.FromArray(e.boundingBoxSize)),e.rotationY&&(s.rotationY=e.rotationY)),s}serialize(){if(!this.name)return null;const e={};return e.name=this.name,e.hasAlpha=this.hasAlpha,e.isCube=!0,e.level=this.level,e.size=this._size,e.coordinatesMode=this.coordinatesMode,e.useInGammaSpace=this.gammaSpace,e.generateHarmonics=this._generateHarmonics,e.customType="BABYLON.HDRCubeTexture",e.noMipmap=this._noMipmap,e.isBlocking=this._isBlocking,e.rotationY=this._rotationY,e}}g._FacesMapping=["right","left","up","down","front","back"];z("BABYLON.HDRCubeTexture",g);export{g as HDRCubeTexture};
//# sourceMappingURL=hdrCubeTexture-BzBAaE84.js.map
