import{c as i}from"./KHR_interactivity-DVSiPm30.js";import{R as s}from"./index-Cb4A4-Xi.js";import{b as r}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class a extends i{constructor(){super(),this.type="SceneBeforeRender",this.timeSinceStart=this.registerDataOutput("timeSinceStart",r),this.deltaTime=this.registerDataOutput("deltaTime",r)}_preparePendingTasks(e){}_executeEvent(e,t){return this.timeSinceStart.setValue(t.timeSinceStart,e),this.deltaTime.setValue(t.deltaTime,e),this._execute(e),!0}_cancelPendingTasks(e){}getClassName(){return"FlowGraphSceneTickEventBlock"}}s("FlowGraphSceneTickEventBlock",a);export{a as FlowGraphSceneTickEventBlock};
//# sourceMappingURL=flowGraphSceneTickEventBlock-C0G_eXum.js.map
