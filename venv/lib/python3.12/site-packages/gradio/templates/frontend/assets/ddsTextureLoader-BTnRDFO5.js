import{ai as h}from"./index-Cb4A4-Xi.js";import{DDSTools as n}from"./dds-CCaj91Rw.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./abstractEngine.cubeTexture-CYPYm3ce.js";class g{constructor(){this.supportCascades=!0}loadCubeData(e,i,m,o){const p=i.getEngine();let a,l=!1,d=1e3;if(Array.isArray(e))for(let s=0;s<e.length;s++){const t=e[s];a=n.GetDDSInfo(t),i.width=a.width,i.height=a.height,l=(a.isRGB||a.isLuminance||a.mipmapCount>1)&&i.generateMipMaps,p._unpackFlipY(a.isCompressed),n.UploadDDSLevels(p,i,t,a,l,6,-1,s),!a.isFourCC&&a.mipmapCount===1?p.generateMipMapsForCubemap(i):d=a.mipmapCount-1}else{const s=e;a=n.GetDDSInfo(s),i.width=a.width,i.height=a.height,m&&(a.sphericalPolynomial=new h),l=(a.isRGB||a.isLuminance||a.mipmapCount>1)&&i.generateMipMaps,p._unpackFlipY(a.isCompressed),n.UploadDDSLevels(p,i,s,a,l,6),!a.isFourCC&&a.mipmapCount===1?p.generateMipMapsForCubemap(i,!1):d=a.mipmapCount-1}p._setCubeMapTextureParams(i,l,d),i.isReady=!0,i.onLoadedObservable.notifyObservers(i),i.onLoadedObservable.clear(),o&&o({isDDS:!0,width:i.width,info:a,data:e,texture:i})}loadData(e,i,m){const o=n.GetDDSInfo(e),p=(o.isRGB||o.isLuminance||o.mipmapCount>1)&&i.generateMipMaps&&Math.max(o.width,o.height)>>o.mipmapCount-1===1;m(o.width,o.height,p,o.isFourCC,()=>{n.UploadDDSLevels(i.getEngine(),i,e,o,p,1)})}}export{g as _DDSTextureLoader};
//# sourceMappingURL=ddsTextureLoader-BTnRDFO5.js.map
