{"version": 3, "file": "tgaTextureLoader-BFcHp3Ba.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/tga.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/tgaTextureLoader.js"], "sourcesContent": ["import { Logger } from \"../Misc/logger.js\";\n//private static _TYPE_NO_DATA = 0;\nconst _TYPE_INDEXED = 1;\nconst _TYPE_RGB = 2;\nconst _TYPE_GREY = 3;\nconst _TYPE_RLE_INDEXED = 9;\nconst _TYPE_RLE_RGB = 10;\nconst _TYPE_RLE_GREY = 11;\nconst _ORIGIN_MASK = 0x30;\nconst _ORIGIN_SHIFT = 0x04;\nconst _ORIGIN_BL = 0x00;\nconst _ORIGIN_BR = 0x01;\nconst _ORIGIN_UL = 0x02;\nconst _ORIGIN_UR = 0x03;\n/**\n * Gets the header of a TGA file\n * @param data defines the TGA data\n * @returns the header\n */\nexport function GetTGAHeader(data) {\n    let offset = 0;\n    const header = {\n        id_length: data[offset++],\n        colormap_type: data[offset++],\n        image_type: data[offset++],\n        colormap_index: data[offset++] | (data[offset++] << 8),\n        colormap_length: data[offset++] | (data[offset++] << 8),\n        colormap_size: data[offset++],\n        origin: [data[offset++] | (data[offset++] << 8), data[offset++] | (data[offset++] << 8)],\n        width: data[offset++] | (data[offset++] << 8),\n        height: data[offset++] | (data[offset++] << 8),\n        pixel_size: data[offset++],\n        flags: data[offset++],\n    };\n    return header;\n}\n/**\n * Uploads TGA content to a Babylon Texture\n * @internal\n */\nexport function UploadContent(texture, data) {\n    // Not enough data to contain header ?\n    if (data.length < 19) {\n        Logger.Error(\"Unable to load TGA file - Not enough data to contain header\");\n        return;\n    }\n    // Read Header\n    let offset = 18;\n    const header = GetTGAHeader(data);\n    // Assume it's a valid Targa file.\n    if (header.id_length + offset > data.length) {\n        Logger.Error(\"Unable to load TGA file - Not enough data\");\n        return;\n    }\n    // Skip not needed data\n    offset += header.id_length;\n    let use_rle = false;\n    let use_pal = false;\n    let use_grey = false;\n    // Get some informations.\n    switch (header.image_type) {\n        case _TYPE_RLE_INDEXED:\n            use_rle = true;\n        // eslint-disable-next-line no-fallthrough\n        case _TYPE_INDEXED:\n            use_pal = true;\n            break;\n        case _TYPE_RLE_RGB:\n            use_rle = true;\n        // eslint-disable-next-line no-fallthrough\n        case _TYPE_RGB:\n            // use_rgb = true;\n            break;\n        case _TYPE_RLE_GREY:\n            use_rle = true;\n        // eslint-disable-next-line no-fallthrough\n        case _TYPE_GREY:\n            use_grey = true;\n            break;\n    }\n    let pixel_data;\n    // var numAlphaBits = header.flags & 0xf;\n    const pixel_size = header.pixel_size >> 3;\n    const pixel_total = header.width * header.height * pixel_size;\n    // Read palettes\n    let palettes;\n    if (use_pal) {\n        palettes = data.subarray(offset, (offset += header.colormap_length * (header.colormap_size >> 3)));\n    }\n    // Read LRE\n    if (use_rle) {\n        pixel_data = new Uint8Array(pixel_total);\n        let c, count, i;\n        let localOffset = 0;\n        const pixels = new Uint8Array(pixel_size);\n        while (offset < pixel_total && localOffset < pixel_total) {\n            c = data[offset++];\n            count = (c & 0x7f) + 1;\n            // RLE pixels\n            if (c & 0x80) {\n                // Bind pixel tmp array\n                for (i = 0; i < pixel_size; ++i) {\n                    pixels[i] = data[offset++];\n                }\n                // Copy pixel array\n                for (i = 0; i < count; ++i) {\n                    pixel_data.set(pixels, localOffset + i * pixel_size);\n                }\n                localOffset += pixel_size * count;\n            }\n            // Raw pixels\n            else {\n                count *= pixel_size;\n                for (i = 0; i < count; ++i) {\n                    pixel_data[localOffset + i] = data[offset++];\n                }\n                localOffset += count;\n            }\n        }\n    }\n    // RAW Pixels\n    else {\n        pixel_data = data.subarray(offset, (offset += use_pal ? header.width * header.height : pixel_total));\n    }\n    // Load to texture\n    let x_start, y_start, x_step, y_step, y_end, x_end;\n    switch ((header.flags & _ORIGIN_MASK) >> _ORIGIN_SHIFT) {\n        default:\n        case _ORIGIN_UL:\n            x_start = 0;\n            x_step = 1;\n            x_end = header.width;\n            y_start = 0;\n            y_step = 1;\n            y_end = header.height;\n            break;\n        case _ORIGIN_BL:\n            x_start = 0;\n            x_step = 1;\n            x_end = header.width;\n            y_start = header.height - 1;\n            y_step = -1;\n            y_end = -1;\n            break;\n        case _ORIGIN_UR:\n            x_start = header.width - 1;\n            x_step = -1;\n            x_end = -1;\n            y_start = 0;\n            y_step = 1;\n            y_end = header.height;\n            break;\n        case _ORIGIN_BR:\n            x_start = header.width - 1;\n            x_step = -1;\n            x_end = -1;\n            y_start = header.height - 1;\n            y_step = -1;\n            y_end = -1;\n            break;\n    }\n    // Load the specify method\n    const func = \"_getImageData\" + (use_grey ? \"Grey\" : \"\") + header.pixel_size + \"bits\";\n    const imageData = TGATools[func](header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end);\n    const engine = texture.getEngine();\n    engine._uploadDataToTextureDirectly(texture, imageData);\n}\n/**\n * @internal\n */\nfunction _getImageData8bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data, colormap = palettes;\n    const width = header.width, height = header.height;\n    let color, i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n            color = image[i];\n            imageData[(x + width * y) * 4 + 3] = 255;\n            imageData[(x + width * y) * 4 + 2] = colormap[color * 3 + 0];\n            imageData[(x + width * y) * 4 + 1] = colormap[color * 3 + 1];\n            imageData[(x + width * y) * 4 + 0] = colormap[color * 3 + 2];\n        }\n    }\n    return imageData;\n}\n/**\n * @internal\n */\nfunction _getImageData16bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data;\n    const width = header.width, height = header.height;\n    let color, i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n            color = image[i + 0] + (image[i + 1] << 8); // Inversed ?\n            const r = ((((color & 0x7c00) >> 10) * 255) / 0x1f) | 0;\n            const g = ((((color & 0x03e0) >> 5) * 255) / 0x1f) | 0;\n            const b = (((color & 0x001f) * 255) / 0x1f) | 0;\n            imageData[(x + width * y) * 4 + 0] = r;\n            imageData[(x + width * y) * 4 + 1] = g;\n            imageData[(x + width * y) * 4 + 2] = b;\n            imageData[(x + width * y) * 4 + 3] = color & 0x8000 ? 0 : 255;\n        }\n    }\n    return imageData;\n}\n/**\n * @internal\n */\nfunction _getImageData24bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data;\n    const width = header.width, height = header.height;\n    let i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 3) {\n            imageData[(x + width * y) * 4 + 3] = 255;\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\n            imageData[(x + width * y) * 4 + 1] = image[i + 1];\n            imageData[(x + width * y) * 4 + 0] = image[i + 2];\n        }\n    }\n    return imageData;\n}\n/**\n * @internal\n */\nfunction _getImageData32bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data;\n    const width = header.width, height = header.height;\n    let i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 4) {\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\n            imageData[(x + width * y) * 4 + 1] = image[i + 1];\n            imageData[(x + width * y) * 4 + 0] = image[i + 2];\n            imageData[(x + width * y) * 4 + 3] = image[i + 3];\n        }\n    }\n    return imageData;\n}\n/**\n * @internal\n */\nfunction _getImageDataGrey8bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data;\n    const width = header.width, height = header.height;\n    let color, i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n            color = image[i];\n            imageData[(x + width * y) * 4 + 0] = color;\n            imageData[(x + width * y) * 4 + 1] = color;\n            imageData[(x + width * y) * 4 + 2] = color;\n            imageData[(x + width * y) * 4 + 3] = 255;\n        }\n    }\n    return imageData;\n}\n/**\n * @internal\n */\nfunction _getImageDataGrey16bits(header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end) {\n    const image = pixel_data;\n    const width = header.width, height = header.height;\n    let i = 0, x, y;\n    const imageData = new Uint8Array(width * height * 4);\n    for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n            imageData[(x + width * y) * 4 + 0] = image[i + 0];\n            imageData[(x + width * y) * 4 + 1] = image[i + 0];\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\n            imageData[(x + width * y) * 4 + 3] = image[i + 1];\n        }\n    }\n    return imageData;\n}\n/**\n * Based on jsTGALoader - Javascript loader for TGA file\n * By Vincent Thibault\n * @see http://blog.robrowser.com/javascript-tga-loader.html\n */\nexport const TGATools = {\n    /**\n     * Gets the header of a TGA file\n     * @param data defines the TGA data\n     * @returns the header\n     */\n    GetTGAHeader,\n    /**\n     * Uploads TGA content to a Babylon Texture\n     * @internal\n     */\n    UploadContent,\n    /** @internal */\n    _getImageData8bits,\n    /** @internal */\n    _getImageData16bits,\n    /** @internal */\n    _getImageData24bits,\n    /** @internal */\n    _getImageData32bits,\n    /** @internal */\n    _getImageDataGrey8bits,\n    /** @internal */\n    _getImageDataGrey16bits,\n};\n//# sourceMappingURL=tga.js.map", "import { GetTGAHeader, UploadContent } from \"../../../Misc/tga.js\";\n/**\n * Implementation of the TGA Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _TGATextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     */\n    loadCubeData() {\n        // eslint-disable-next-line no-throw-literal\n        throw \".env not supported in Cube.\";\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    loadData(data, texture, callback) {\n        const bytes = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n        const header = GetTGAHeader(bytes);\n        callback(header.width, header.height, texture.generateMipMaps, false, () => {\n            UploadContent(texture, bytes);\n        });\n    }\n}\n//# sourceMappingURL=tgaTextureLoader.js.map"], "names": ["_TYPE_INDEXED", "_TYPE_RGB", "_TYPE_GREY", "_TYPE_RLE_INDEXED", "_TYPE_RLE_RGB", "_TYPE_RLE_GREY", "_ORIGIN_MASK", "_ORIGIN_SHIFT", "_ORIGIN_BL", "_ORIGIN_BR", "_ORIGIN_UL", "_ORIGIN_UR", "GetTGAHeader", "data", "offset", "UploadContent", "texture", "<PERSON><PERSON>", "header", "use_rle", "use_pal", "use_grey", "pixel_data", "pixel_size", "pixel_total", "palettes", "c", "count", "i", "localOffset", "pixels", "x_start", "y_start", "x_step", "y_step", "y_end", "x_end", "func", "imageData", "TGATools", "_getImageData8bits", "image", "colormap", "width", "height", "color", "x", "y", "_getImageData16bits", "r", "g", "b", "_getImageData24bits", "_getImageData32bits", "_getImageDataGrey8bits", "_getImageDataGrey16bits", "_TGATextureLoader", "callback", "bytes"], "mappings": "+FAEA,MAAMA,EAAgB,EAChBC,EAAY,EACZC,EAAa,EACbC,EAAoB,EACpBC,EAAgB,GAChBC,EAAiB,GACjBC,EAAe,GACfC,EAAgB,EAChBC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EAMZ,SAASC,EAAaC,EAAM,CAC/B,IAAIC,EAAS,EAcb,MAbe,CACX,UAAWD,EAAKC,GAAQ,EACxB,cAAeD,EAAKC,GAAQ,EAC5B,WAAYD,EAAKC,GAAQ,EACzB,eAAgBD,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,EACpD,gBAAiBD,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,EACrD,cAAeD,EAAKC,GAAQ,EAC5B,OAAQ,CAACD,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,EAAID,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,CAAE,EACvF,MAAOD,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,EAC3C,OAAQD,EAAKC,GAAQ,EAAKD,EAAKC,GAAQ,GAAK,EAC5C,WAAYD,EAAKC,GAAQ,EACzB,MAAOD,EAAKC,GAAQ,CAC5B,CAEA,CAKO,SAASC,EAAcC,EAASH,EAAM,CAEzC,GAAIA,EAAK,OAAS,GAAI,CAClBI,EAAO,MAAM,6DAA6D,EAC1E,MACH,CAED,IAAIH,EAAS,GACb,MAAMI,EAASN,EAAaC,CAAI,EAEhC,GAAIK,EAAO,UAAYJ,EAASD,EAAK,OAAQ,CACzCI,EAAO,MAAM,2CAA2C,EACxD,MACH,CAEDH,GAAUI,EAAO,UACjB,IAAIC,EAAU,GACVC,EAAU,GACVC,EAAW,GAEf,OAAQH,EAAO,WAAU,CACrB,KAAKf,EACDgB,EAAU,GAEd,KAAKnB,EACDoB,EAAU,GACV,MACJ,KAAKhB,EACDe,EAAU,GAEd,KAAKlB,EAED,MACJ,KAAKI,EACDc,EAAU,GAEd,KAAKjB,EACDmB,EAAW,GACX,KACP,CACD,IAAIC,EAEJ,MAAMC,EAAaL,EAAO,YAAc,EAClCM,EAAcN,EAAO,MAAQA,EAAO,OAASK,EAEnD,IAAIE,EAKJ,GAJIL,IACAK,EAAWZ,EAAK,SAASC,EAASA,GAAUI,EAAO,iBAAmBA,EAAO,eAAiB,EAAE,GAGhGC,EAAS,CACTG,EAAa,IAAI,WAAWE,CAAW,EACvC,IAAIE,EAAGC,EAAOC,EACVC,EAAc,EAClB,MAAMC,EAAS,IAAI,WAAWP,CAAU,EACxC,KAAOT,EAASU,GAAeK,EAAcL,GAIzC,GAHAE,EAAIb,EAAKC,GAAQ,EACjBa,GAASD,EAAI,KAAQ,EAEjBA,EAAI,IAAM,CAEV,IAAKE,EAAI,EAAGA,EAAIL,EAAY,EAAEK,EAC1BE,EAAOF,CAAC,EAAIf,EAAKC,GAAQ,EAG7B,IAAKc,EAAI,EAAGA,EAAID,EAAO,EAAEC,EACrBN,EAAW,IAAIQ,EAAQD,EAAcD,EAAIL,CAAU,EAEvDM,GAAeN,EAAaI,CAC/B,KAEI,CAED,IADAA,GAASJ,EACJK,EAAI,EAAGA,EAAID,EAAO,EAAEC,EACrBN,EAAWO,EAAcD,CAAC,EAAIf,EAAKC,GAAQ,EAE/Ce,GAAeF,CAClB,CAER,MAGGL,EAAaT,EAAK,SAASC,EAASA,GAAUM,EAAUF,EAAO,MAAQA,EAAO,OAASM,CAAW,EAGtG,IAAIO,EAASC,EAASC,EAAQC,EAAQC,EAAOC,EAC7C,QAASlB,EAAO,MAAQZ,IAAiBC,EAAa,CAClD,QACA,KAAKG,EACDqB,EAAU,EACVE,EAAS,EACTG,EAAQlB,EAAO,MACfc,EAAU,EACVE,EAAS,EACTC,EAAQjB,EAAO,OACf,MACJ,KAAKV,EACDuB,EAAU,EACVE,EAAS,EACTG,EAAQlB,EAAO,MACfc,EAAUd,EAAO,OAAS,EAC1BgB,EAAS,GACTC,EAAQ,GACR,MACJ,KAAKxB,EACDoB,EAAUb,EAAO,MAAQ,EACzBe,EAAS,GACTG,EAAQ,GACRJ,EAAU,EACVE,EAAS,EACTC,EAAQjB,EAAO,OACf,MACJ,KAAKT,EACDsB,EAAUb,EAAO,MAAQ,EACzBe,EAAS,GACTG,EAAQ,GACRJ,EAAUd,EAAO,OAAS,EAC1BgB,EAAS,GACTC,EAAQ,GACR,KACP,CAED,MAAME,EAAO,iBAAmBhB,EAAW,OAAS,IAAMH,EAAO,WAAa,OACxEoB,EAAYC,EAASF,CAAI,EAAEnB,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,CAAK,EAC9FpB,EAAQ,YAChB,6BAA6BA,EAASsB,CAAS,CAC1D,CAIA,SAASE,EAAmBtB,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CACtG,MAAMK,EAAQnB,EAAYoB,EAAWjB,EAC/BkB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAI2B,EAAOjB,EAAI,EAAGkB,EAAGC,EACrB,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQL,IACxCiB,EAAQJ,EAAMb,CAAC,EACfU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAI,IACrCT,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIL,EAASG,EAAQ,EAAI,CAAC,EAC3DP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIL,EAASG,EAAQ,EAAI,CAAC,EAC3DP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIL,EAASG,EAAQ,EAAI,CAAC,EAGnE,OAAOP,CACX,CAIA,SAASU,EAAoB9B,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CACvG,MAAMK,EAAQnB,EACRqB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAI2B,EAAO,EAAI,EAAGC,EAAGC,EACrB,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQ,GAAK,EAAG,CAChDY,EAAQJ,EAAM,EAAI,CAAC,GAAKA,EAAM,EAAI,CAAC,GAAK,GACxC,MAAMQ,IAAQJ,EAAQ,QAAW,IAAM,IAAO,GAAQ,EAChDK,IAAQL,EAAQ,MAAW,GAAK,IAAO,GAAQ,EAC/CM,GAAON,EAAQ,IAAU,IAAO,GAAQ,EAC9CP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIE,EACrCX,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIG,EACrCZ,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAII,EACrCb,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIF,EAAQ,MAAS,EAAI,GAC7D,CAEL,OAAOP,CACX,CAIA,SAASc,EAAoBlC,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CACvG,MAAMK,EAAQnB,EACRqB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAIU,EAAI,EAAGkB,EAAGC,EACd,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQL,GAAK,EAC7CU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAI,IACrCT,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAGxD,OAAOU,CACX,CAIA,SAASe,EAAoBnC,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CACvG,MAAMK,EAAQnB,EACRqB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAIU,EAAI,EAAGkB,EAAGC,EACd,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQL,GAAK,EAC7CU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAGxD,OAAOU,CACX,CAIA,SAASgB,EAAuBpC,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CAC1G,MAAMK,EAAQnB,EACRqB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAI2B,EAAO,EAAI,EAAGC,EAAGC,EACrB,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQ,IACxCY,EAAQJ,EAAM,CAAC,EACfH,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIF,EACrCP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIF,EACrCP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIF,EACrCP,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAI,IAG7C,OAAOT,CACX,CAIA,SAASiB,EAAwBrC,EAAQO,EAAUH,EAAYU,EAASE,EAAQC,EAAOJ,EAASE,EAAQG,EAAO,CAC3G,MAAMK,EAAQnB,EACRqB,EAAQzB,EAAO,MAAO0B,EAAS1B,EAAO,OAC5C,IAAIU,EAAI,EAAGkB,EAAGC,EACd,MAAMT,EAAY,IAAI,WAAWK,EAAQC,EAAS,CAAC,EACnD,IAAKG,EAAIf,EAASe,IAAMZ,EAAOY,GAAKb,EAChC,IAAKY,EAAIf,EAASe,IAAMV,EAAOU,GAAKb,EAAQL,GAAK,EAC7CU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAChDU,GAAWQ,EAAIH,EAAQI,GAAK,EAAI,CAAC,EAAIN,EAAMb,EAAI,CAAC,EAGxD,OAAOU,CACX,CAMO,MAAMC,EAAW,CAMpB,aAAA3B,EAKA,cAAAG,EAEA,mBAAAyB,EAEA,oBAAAQ,EAEA,oBAAAI,EAEA,oBAAAC,EAEA,uBAAAC,EAEA,wBAAAC,CACJ,EChTO,MAAMC,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAID,cAAe,CAEX,KAAM,6BACT,CAOD,SAAS3C,EAAMG,EAASyC,EAAU,CAC9B,MAAMC,EAAQ,IAAI,WAAW7C,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EACpEK,EAASN,EAAa8C,CAAK,EACjCD,EAASvC,EAAO,MAAOA,EAAO,OAAQF,EAAQ,gBAAiB,GAAO,IAAM,CACxED,EAAcC,EAAS0C,CAAK,CACxC,CAAS,CACJ,CACL", "x_google_ignoreList": [0, 1]}