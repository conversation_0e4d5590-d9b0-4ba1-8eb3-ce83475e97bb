{"version": 3, "file": "flowGraphThrottleBlock-D2tWef1Y.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphThrottleBlock.js"], "sourcesContent": ["import { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * A block that throttles the execution of its output flow.\n */\nexport class FlowGraphThrottleBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.reset = this._registerSignalInput(\"reset\");\n        this.duration = this.registerDataInput(\"duration\", RichTypeNumber);\n        this.lastRemainingTime = this.registerDataOutput(\"lastRemainingTime\", RichTypeNumber, NaN);\n    }\n    _execute(context, callingSignal) {\n        if (callingSignal === this.reset) {\n            this.lastRemainingTime.setValue(NaN, context);\n            context._setExecutionVariable(this, \"lastRemainingTime\", NaN);\n            context._setExecutionVariable(this, \"timestamp\", 0);\n            return;\n        }\n        // in seconds\n        const durationValue = this.duration.getValue(context);\n        if (durationValue <= 0 || isNaN(durationValue) || !isFinite(durationValue)) {\n            return this._reportError(context, \"Invalid duration in Throttle block\");\n        }\n        const lastRemainingTime = context._getExecutionVariable(this, \"lastRemainingTime\", NaN);\n        // Using Date.now() to get ms since epoch. not using performance.now() because its precision is not needed here\n        const currentTime = Date.now();\n        if (isNaN(lastRemainingTime)) {\n            this.lastRemainingTime.setValue(0, context);\n            context._setExecutionVariable(this, \"lastRemainingTime\", 0);\n            context._setExecutionVariable(this, \"timestamp\", currentTime);\n            // according to glTF interactivity specs\n            return this.out._activateSignal(context);\n        }\n        else {\n            const elapsedTime = currentTime - context._getExecutionVariable(this, \"timestamp\", 0);\n            // duration is in seconds, so we need to multiply by 1000\n            const durationInMs = durationValue * 1000;\n            if (durationInMs <= elapsedTime) {\n                this.lastRemainingTime.setValue(0, context);\n                context._setExecutionVariable(this, \"lastRemainingTime\", 0);\n                context._setExecutionVariable(this, \"timestamp\", currentTime);\n                return this.out._activateSignal(context);\n            }\n            else {\n                const remainingTime = durationInMs - elapsedTime;\n                // output is in seconds\n                this.lastRemainingTime.setValue(remainingTime / 1000, context);\n                context._setExecutionVariable(this, \"lastRemainingTime\", remainingTime);\n            }\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphThrottleBlock\" /* FlowGraphBlockNames.Throttle */;\n    }\n}\nRegisterClass(\"FlowGraphThrottleBlock\" /* FlowGraphBlockNames.Throttle */, FlowGraphThrottleBlock);\n//# sourceMappingURL=flowGraphThrottleBlock.js.map"], "names": ["FlowGraphThrottleBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeNumber", "context", "callingSignal", "durationValue", "lastRemainingTime", "currentTime", "elapsedTime", "durationInMs", "remainingTime", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAA+BC,CAAqC,CAC7E,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,MAAQ,KAAK,qBAAqB,OAAO,EAC9C,KAAK,SAAW,KAAK,kBAAkB,WAAYC,CAAc,EACjE,KAAK,kBAAoB,KAAK,mBAAmB,oBAAqBA,EAAgB,GAAG,CAC5F,CACD,SAASC,EAASC,EAAe,CAC7B,GAAIA,IAAkB,KAAK,MAAO,CAC9B,KAAK,kBAAkB,SAAS,IAAKD,CAAO,EAC5CA,EAAQ,sBAAsB,KAAM,oBAAqB,GAAG,EAC5DA,EAAQ,sBAAsB,KAAM,YAAa,CAAC,EAClD,MACH,CAED,MAAME,EAAgB,KAAK,SAAS,SAASF,CAAO,EACpD,GAAIE,GAAiB,GAAK,MAAMA,CAAa,GAAK,CAAC,SAASA,CAAa,EACrE,OAAO,KAAK,aAAaF,EAAS,oCAAoC,EAE1E,MAAMG,EAAoBH,EAAQ,sBAAsB,KAAM,oBAAqB,GAAG,EAEhFI,EAAc,KAAK,MACzB,GAAI,MAAMD,CAAiB,EACvB,YAAK,kBAAkB,SAAS,EAAGH,CAAO,EAC1CA,EAAQ,sBAAsB,KAAM,oBAAqB,CAAC,EAC1DA,EAAQ,sBAAsB,KAAM,YAAaI,CAAW,EAErD,KAAK,IAAI,gBAAgBJ,CAAO,EAEtC,CACD,MAAMK,EAAcD,EAAcJ,EAAQ,sBAAsB,KAAM,YAAa,CAAC,EAE9EM,EAAeJ,EAAgB,IACrC,GAAII,GAAgBD,EAChB,YAAK,kBAAkB,SAAS,EAAGL,CAAO,EAC1CA,EAAQ,sBAAsB,KAAM,oBAAqB,CAAC,EAC1DA,EAAQ,sBAAsB,KAAM,YAAaI,CAAW,EACrD,KAAK,IAAI,gBAAgBJ,CAAO,EAEtC,CACD,MAAMO,EAAgBD,EAAeD,EAErC,KAAK,kBAAkB,SAASE,EAAgB,IAAMP,CAAO,EAC7DA,EAAQ,sBAAsB,KAAM,oBAAqBO,CAAa,CACzE,CACJ,CACJ,CAID,cAAe,CACX,MAAO,wBACV,CACL,CACAC,EAAc,yBAA6DZ,CAAsB", "x_google_ignoreList": [0]}