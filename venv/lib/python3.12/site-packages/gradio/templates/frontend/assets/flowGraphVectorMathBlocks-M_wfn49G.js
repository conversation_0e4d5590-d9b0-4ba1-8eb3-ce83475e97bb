import{R as c,b as n,e as s,f as w,g as m,h as f}from"./declarationMapper-r-RREw_K.js";import{R as a,V as h,ay as B,M as u,az as G}from"./index-Cb4A4-Xi.js";import{F as l}from"./flowGraphBinaryOperationBlock-DaBPP43z.js";import{F}from"./flowGraphUnaryOperationBlock-BsZVallq.js";import{F as k}from"./flowGraphTernaryOperationBlock-DqI69tK1.js";import{e as p}from"./KHR_interactivity-DVSiPm30.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./flowGraphCachedOperationBlock-D--7yusk.js";import"./objectModelMapping-D3Nr8hfO.js";class y extends F{constructor(o){super(c,n,t=>this._polymorphicLength(t),"FlowGraphLengthBlock",o)}_polymorphicLength(o){switch(p(o)){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return o.length();default:throw new Error(`Cannot compute length of value ${o}`)}}}a("FlowGraphLengthBlock",y);class V extends F{constructor(o){super(c,c,t=>this._polymorphicNormalize(t),"FlowGraphNormalizeBlock",o)}_polymorphicNormalize(o){const t=p(o);let e;switch(t){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return e=o.normalizeToNew(),this.config?.nanOnZeroLength&&o.length()===0&&e.setAll(NaN),e;default:throw new Error(`Cannot normalize value ${o}`)}}}a("FlowGraphNormalizeBlock",V);class d extends l{constructor(o){super(c,c,n,(t,e)=>this._polymorphicDot(t,e),"FlowGraphDotBlock",o)}_polymorphicDot(o,t){switch(p(o)){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return o.dot(t);default:throw new Error(`Cannot get dot product of ${o} and ${t}`)}}}a("FlowGraphDotBlock",d);class T extends l{constructor(o){super(s,s,s,(t,e)=>h.Cross(t,e),"FlowGraphCrossBlock",o)}}a("FlowGraphCrossBlock",T);class x extends l{constructor(o){super(w,n,w,(t,e)=>B.Transform(t,u.RotationZ(e)),"FlowGraphRotate2DBlock",o)}}a("FlowGraphRotate2DBlock",x);class C extends k{constructor(o){super(s,s,n,s,(t,e,i)=>h.TransformCoordinates(t,u.RotationAxis(e,i)),"FlowGraphRotate3DBlock",o)}}a("FlowGraphRotate3DBlock",C);function N(r,o){switch(p(r)){case"Vector2":return o.transformVector(r);case"Vector3":return o.transformVector(r);case"Vector4":return r=r,new G(r.x*o.m[0]+r.y*o.m[1]+r.z*o.m[2]+r.w*o.m[3],r.x*o.m[4]+r.y*o.m[5]+r.z*o.m[6]+r.w*o.m[7],r.x*o.m[8]+r.y*o.m[9]+r.z*o.m[10]+r.w*o.m[11],r.x*o.m[12]+r.y*o.m[13]+r.z*o.m[14]+r.w*o.m[15]);default:throw new Error(`Cannot transform value ${r}`)}}class R extends l{constructor(o){const t=o?.vectorType||"Vector3",e=t==="Vector2"?"Matrix2D":t==="Vector3"?"Matrix3D":"Matrix";super(m(t),m(e),m(t),N,"FlowGraphTransformVectorBlock",o)}}a("FlowGraphTransformVectorBlock",R);class g extends l{constructor(o){super(s,f,s,(t,e)=>h.TransformCoordinates(t,e),"FlowGraphTransformCoordinatesBlock",o)}}a("FlowGraphTransformCoordinatesBlock",g);export{T as FlowGraphCrossBlock,d as FlowGraphDotBlock,y as FlowGraphLengthBlock,V as FlowGraphNormalizeBlock,x as FlowGraphRotate2DBlock,C as FlowGraphRotate3DBlock,R as FlowGraphTransformBlock,g as FlowGraphTransformCoordinatesBlock};
//# sourceMappingURL=flowGraphVectorMathBlocks-M_wfn49G.js.map
