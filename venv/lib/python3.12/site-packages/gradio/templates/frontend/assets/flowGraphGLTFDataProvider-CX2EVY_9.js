import{F as n}from"./KHR_interactivity-DVSiPm30.js";import{R as r}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./index-Cb4A4-Xi.js";import"./objectModelMapping-D3Nr8hfO.js";class d extends n{constructor(a){super();const t=a.glTF,s=t.animations?.map(o=>o._babylonAnimationGroup)||[];this.animationGroups=this.registerDataOutput("animationGroups",r,s);const i=t.nodes?.map(o=>o._babylonTransformNode)||[];this.nodes=this.registerDataOutput("nodes",r,i)}getClassName(){return"FlowGraphGLTFDataProvider"}}export{d as FlowGraphGLTFDataProvider};
//# sourceMappingURL=flowGraphGLTFDataProvider-CX2EVY_9.js.map
