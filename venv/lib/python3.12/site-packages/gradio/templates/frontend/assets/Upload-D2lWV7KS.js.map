{"version": 3, "file": "Upload-D2lWV7KS.js", "sources": ["../../../../js/icons/src/ImagePaste.svelte", "../../../../js/icons/src/Upload.svelte", "../../../../js/upload/src/UploadProgress.svelte", "../../../../js/upload/src/utils.ts", "../../../../js/upload/src/Upload.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 256 256\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M200 32h-36.26a47.92 47.92 0 0 0-71.48 0H56a16 16 0 0 0-16 16v168a16 16 0 0 0 16 16h144a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16m-72 0a32 32 0 0 1 32 32H96a32 32 0 0 1 32-32m72 184H56V48h26.75A47.9 47.9 0 0 0 80 64v8a8 8 0 0 0 8 8h80a8 8 0 0 0 8-8v-8a47.9 47.9 0 0 0-2.75-16H200Z\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"90%\"\n\theight=\"90%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-upload\"\n\t><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" /><polyline\n\t\tpoints=\"17 8 12 3 7 8\"\n\t/><line x1=\"12\" y1=\"3\" x2=\"12\" y2=\"15\" /></svg\n>\n", "<script lang=\"ts\">\n\timport { FileData, type Client } from \"@gradio/client\";\n\timport { onMount, createEventDispatcher, onDestroy } from \"svelte\";\n\n\ttype FileDataWithProgress = FileData & { progress: number };\n\n\texport let upload_id: string;\n\texport let root: string;\n\texport let files: FileData[];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet stream: Awaited<ReturnType<Client[\"stream\"]>>;\n\tlet progress = false;\n\tlet current_file_upload: FileDataWithProgress;\n\tlet file_to_display: FileDataWithProgress;\n\n\tlet files_with_progress: FileDataWithProgress[] = files.map((file) => {\n\t\treturn {\n\t\t\t...file,\n\t\t\tprogress: 0\n\t\t};\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction handleProgress(filename: string, chunk_size: number): void {\n\t\t// Find the corresponding file in the array and update its progress\n\t\tfiles_with_progress = files_with_progress.map((file) => {\n\t\t\tif (file.orig_name === filename) {\n\t\t\t\tfile.progress += chunk_size;\n\t\t\t}\n\t\t\treturn file;\n\t\t});\n\t}\n\n\tfunction getProgress(file: FileDataWithProgress): number {\n\t\treturn (file.progress * 100) / (file.size || 0) || 0;\n\t}\n\n\tonMount(async () => {\n\t\tstream = await stream_handler(\n\t\t\tnew URL(`${root}/gradio_api/upload_progress?upload_id=${upload_id}`)\n\t\t);\n\n\t\tif (stream == null) {\n\t\t\tthrow new Error(\"Event source is not defined\");\n\t\t}\n\t\t// Event listener for progress updates\n\t\tstream.onmessage = async function (event) {\n\t\t\tconst _data = JSON.parse(event.data);\n\t\t\tif (!progress) progress = true;\n\t\t\tif (_data.msg === \"done\") {\n\t\t\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\t\t\tstream?.close();\n\t\t\t\tdispatch(\"done\");\n\t\t\t} else {\n\t\t\t\tcurrent_file_upload = _data;\n\t\t\t\thandleProgress(_data.orig_name, _data.chunk_size);\n\t\t\t}\n\t\t};\n\t});\n\tonDestroy(() => {\n\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\tif (stream != null || stream != undefined) stream.close();\n\t});\n\n\tfunction calculateTotalProgress(files: FileData[]): number {\n\t\tlet totalProgress = 0;\n\t\tfiles.forEach((file) => {\n\t\t\ttotalProgress += getProgress(file as FileDataWithProgress);\n\t\t});\n\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--upload-progress-width\",\n\t\t\t(totalProgress / files.length).toFixed(2) + \"%\"\n\t\t);\n\n\t\treturn totalProgress / files.length;\n\t}\n\n\t$: calculateTotalProgress(files_with_progress);\n\n\t$: file_to_display = current_file_upload || files_with_progress[0];\n</script>\n\n<div class=\"wrap\" class:progress>\n\t<span class=\"uploading\"\n\t\t>Uploading {files_with_progress.length}\n\t\t{files_with_progress.length > 1 ? \"files\" : \"file\"}...</span\n\t>\n\n\t{#if file_to_display}\n\t\t<div class=\"file\">\n\t\t\t<span>\n\t\t\t\t<div class=\"progress-bar\">\n\t\t\t\t\t<progress\n\t\t\t\t\t\tstyle=\"visibility:hidden;height:0;width:0;\"\n\t\t\t\t\t\tvalue={getProgress(file_to_display)}\n\t\t\t\t\t\tmax=\"100\">{getProgress(file_to_display)}</progress\n\t\t\t\t\t>\n\t\t\t\t</div>\n\t\t\t</span>\n\t\t\t<span class=\"file-name\">\n\t\t\t\t{file_to_display.orig_name}\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\toverflow-y: auto;\n\t\ttransition: opacity 0.5s ease-in-out;\n\t\tbackground: var(--block-background-fill);\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmin-height: var(--size-40);\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: var(--upload-progress-width);\n\t\theight: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tz-index: 1;\n\t}\n\n\t.uploading {\n\t\tfont-size: var(--text-lg);\n\t\tfont-family: var(--font);\n\t\tz-index: 2;\n\t}\n\n\t.file-name {\n\t\tmargin: var(--spacing-md);\n\t\tfont-size: var(--text-lg);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.file {\n\t\tfont-size: var(--text-md);\n\t\tz-index: 2;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.file progress {\n\t\tdisplay: inline;\n\t\theight: var(--size-1);\n\t\twidth: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tcolor: var(--color-accent);\n\t\tborder: none;\n\t}\n\n\t.file progress[value]::-webkit-progress-value {\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.file progress[value]::-webkit-progress-bar {\n\t\tbackground-color: var(--border-color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.progress-bar {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t\tborder-radius: 50%;\n\t\tbackground: radial-gradient(\n\t\t\t\tclosest-side,\n\t\t\t\tvar(--block-background-fill) 64%,\n\t\t\t\ttransparent 53% 100%\n\t\t\t),\n\t\t\tconic-gradient(\n\t\t\t\tvar(--color-accent) var(--upload-progress-width),\n\t\t\t\tvar(--border-color-accent) 0\n\t\t\t);\n\t\ttransition: all 0.5s ease-in-out;\n\t}\n</style>\n", "interface DragActionOptions {\n\tdisable_click?: boolean;\n\taccepted_types?: string | string[] | null;\n\tmode?: \"single\" | \"multiple\" | \"directory\";\n\ton_drag_change?: (dragging: boolean) => void;\n\ton_files?: (files: File[]) => void;\n}\n\ntype ActionReturn = {\n\tupdate: (new_options: DragActionOptions) => void;\n\tdestroy: () => void;\n};\n\nexport function create_drag(): {\n\tdrag: (node: HTMLElement, options: DragActionOptions) => ActionReturn;\n\topen_file_upload: () => void;\n} {\n\tlet hidden_input: HTMLInputElement;\n\tlet _options: DragActionOptions;\n\treturn {\n\t\tdrag(\n\t\t\tnode: HTMLElement,\n\t\t\toptions: DragActionOptions = {}\n\t\t): {\n\t\t\tupdate: (new_options: DragActionOptions) => void;\n\t\t\tdestroy: () => void;\n\t\t} {\n\t\t\t_options = options;\n\n\t\t\t// Create and configure hidden file input\n\t\t\tfunction setup_hidden_input(): void {\n\t\t\t\thidden_input = document.createElement(\"input\");\n\t\t\t\thidden_input.type = \"file\";\n\t\t\t\thidden_input.style.display = \"none\";\n\t\t\t\thidden_input.setAttribute(\"aria-label\", \"File upload\");\n\t\t\t\thidden_input.setAttribute(\"data-testid\", \"file-upload\");\n\t\t\t\tconst accept_options = Array.isArray(_options.accepted_types)\n\t\t\t\t\t? _options.accepted_types.join(\",\")\n\t\t\t\t\t: _options.accepted_types || undefined;\n\n\t\t\t\tif (accept_options) {\n\t\t\t\t\thidden_input.accept = accept_options;\n\t\t\t\t}\n\n\t\t\t\thidden_input.multiple = _options.mode === \"multiple\" || false;\n\t\t\t\tif (_options.mode === \"directory\") {\n\t\t\t\t\thidden_input.webkitdirectory = true;\n\t\t\t\t\thidden_input.setAttribute(\"directory\", \"\");\n\t\t\t\t\thidden_input.setAttribute(\"mozdirectory\", \"\");\n\t\t\t\t}\n\t\t\t\tnode.appendChild(hidden_input);\n\t\t\t}\n\n\t\t\tsetup_hidden_input();\n\n\t\t\tfunction handle_drag(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\n\t\t\tfunction handle_drag_enter(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(true);\n\t\t\t}\n\n\t\t\tfunction handle_drag_leave(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(false);\n\t\t\t}\n\n\t\t\tfunction handle_drop(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(false);\n\n\t\t\t\tif (!e.dataTransfer?.files) return;\n\t\t\t\tconst files = Array.from(e.dataTransfer.files);\n\t\t\t\tif (files.length > 0) {\n\t\t\t\t\t_options.on_files?.(files);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_click(): void {\n\t\t\t\tif (!_options.disable_click) {\n\t\t\t\t\thidden_input.value = \"\";\n\t\t\t\t\thidden_input.click();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_file_input_change(): void {\n\t\t\t\tif (hidden_input.files) {\n\t\t\t\t\tconst files = Array.from(hidden_input.files);\n\t\t\t\t\tif (files.length > 0) {\n\t\t\t\t\t\t_options.on_files?.(files);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add all event listeners\n\t\t\tnode.addEventListener(\"drag\", handle_drag);\n\t\t\tnode.addEventListener(\"dragstart\", handle_drag);\n\t\t\tnode.addEventListener(\"dragend\", handle_drag);\n\t\t\tnode.addEventListener(\"dragover\", handle_drag);\n\t\t\tnode.addEventListener(\"dragenter\", handle_drag_enter);\n\t\t\tnode.addEventListener(\"dragleave\", handle_drag_leave);\n\t\t\tnode.addEventListener(\"drop\", handle_drop);\n\t\t\tnode.addEventListener(\"click\", handle_click);\n\t\t\thidden_input!.addEventListener(\"change\", handle_file_input_change);\n\n\t\t\treturn {\n\t\t\t\tupdate(new_options: DragActionOptions) {\n\t\t\t\t\t_options = new_options;\n\t\t\t\t\t// Recreate hidden input with new options\n\t\t\t\t\thidden_input.remove();\n\t\t\t\t\tsetup_hidden_input();\n\t\t\t\t\thidden_input.addEventListener(\"change\", handle_file_input_change);\n\t\t\t\t},\n\t\t\t\tdestroy() {\n\t\t\t\t\tnode.removeEventListener(\"drag\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragstart\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragend\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragover\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragenter\", handle_drag_enter);\n\t\t\t\t\tnode.removeEventListener(\"dragleave\", handle_drag_leave);\n\t\t\t\t\tnode.removeEventListener(\"drop\", handle_drop);\n\t\t\t\t\tnode.removeEventListener(\"click\", handle_click);\n\t\t\t\t\thidden_input.removeEventListener(\"change\", handle_file_input_change);\n\t\t\t\t\thidden_input.remove();\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\topen_file_upload(): void {\n\t\t\tif (hidden_input) {\n\t\t\t\thidden_input.value = \"\";\n\t\t\t\thidden_input.click();\n\t\t\t}\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, getContext } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { prepare_files, type Client } from \"@gradio/client\";\n\timport { _ } from \"svelte-i18n\";\n\timport UploadProgress from \"./UploadProgress.svelte\";\n\timport { create_drag } from \"./utils\";\n\n\tconst { drag, open_file_upload: _open_file_upload } = create_drag();\n\n\texport let filetype: string | string[] | null = null;\n\texport let dragging = false;\n\texport let boundedheight = true;\n\texport let center = true;\n\texport let flex = true;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\" = \"single\";\n\texport let disable_click = false;\n\texport let root: string;\n\texport let hidden = false;\n\texport let format: \"blob\" | \"file\" = \"file\";\n\texport let uploading = false;\n\texport let show_progress = true;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let icon_upload = false;\n\texport let height: number | string | undefined = undefined;\n\texport let aria_label: string | undefined = undefined;\n\texport function open_upload(): void {\n\t\t_open_file_upload();\n\t}\n\tlet upload_id: string;\n\tlet file_data: FileData[];\n\tlet accept_file_types: string | null;\n\tlet use_post_upload_validation: boolean | null = null;\n\n\tconst get_ios = (): boolean => {\n\t\tif (typeof navigator !== \"undefined\") {\n\t\t\tconst userAgent = navigator.userAgent.toLowerCase();\n\t\t\treturn userAgent.indexOf(\"iphone\") > -1 || userAgent.indexOf(\"ipad\") > -1;\n\t\t}\n\t\treturn false;\n\t};\n\n\t$: ios = get_ios();\n\n\tconst dispatch = createEventDispatcher();\n\tconst validFileTypes = [\"image\", \"video\", \"audio\", \"text\", \"file\"];\n\tconst process_file_type = (type: string): string => {\n\t\tif (ios && type.startsWith(\".\")) {\n\t\t\tuse_post_upload_validation = true;\n\t\t\treturn type;\n\t\t}\n\t\tif (ios && type.includes(\"file/*\")) {\n\t\t\treturn \"*\";\n\t\t}\n\t\tif (type.startsWith(\".\") || type.endsWith(\"/*\")) {\n\t\t\treturn type;\n\t\t}\n\t\tif (validFileTypes.includes(type)) {\n\t\t\treturn type + \"/*\";\n\t\t}\n\t\treturn \".\" + type;\n\t};\n\n\t$: if (filetype == null) {\n\t\taccept_file_types = null;\n\t} else if (typeof filetype === \"string\") {\n\t\taccept_file_types = process_file_type(filetype);\n\t} else if (ios && filetype.includes(\"file/*\")) {\n\t\taccept_file_types = \"*\";\n\t} else {\n\t\tfiletype = filetype.map(process_file_type);\n\t\taccept_file_types = filetype.join(\", \");\n\t}\n\n\texport function paste_clipboard(): void {\n\t\tnavigator.clipboard.read().then(async (items) => {\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst type = items[i].types.find((t) => t.startsWith(\"image/\"));\n\t\t\t\tif (type) {\n\t\t\t\t\titems[i].getType(type).then(async (blob) => {\n\t\t\t\t\t\tconst file = new File(\n\t\t\t\t\t\t\t[blob],\n\t\t\t\t\t\t\t`clipboard.${type.replace(\"image/\", \"\")}`\n\t\t\t\t\t\t);\n\t\t\t\t\t\tawait load_files([file]);\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\texport function open_file_upload(): void {\n\t\t_open_file_upload();\n\t}\n\n\tasync function handle_upload(\n\t\tfile_data: FileData[]\n\t): Promise<(FileData | null)[]> {\n\t\tawait tick();\n\t\tupload_id = Math.random().toString(36).substring(2, 15);\n\t\tuploading = true;\n\t\ttry {\n\t\t\tconst _file_data = await upload(\n\t\t\t\tfile_data,\n\t\t\t\troot,\n\t\t\t\tupload_id,\n\t\t\t\tmax_file_size ?? Infinity\n\t\t\t);\n\t\t\tdispatch(\"load\", file_count === \"single\" ? _file_data?.[0] : _file_data);\n\t\t\tuploading = false;\n\t\t\treturn _file_data || [];\n\t\t} catch (e) {\n\t\t\tdispatch(\"error\", (e as Error).message);\n\t\t\tuploading = false;\n\t\t\treturn [];\n\t\t}\n\t}\n\n\tfunction is_valid_mimetype(\n\t\tfile_accept: string | string[] | null,\n\t\tuploaded_file_extension: string,\n\t\tuploaded_file_type: string\n\t): boolean {\n\t\tif (\n\t\t\t!file_accept ||\n\t\t\tfile_accept === \"*\" ||\n\t\t\tfile_accept === \"file/*\" ||\n\t\t\t(Array.isArray(file_accept) &&\n\t\t\t\tfile_accept.some((accept) => accept === \"*\" || accept === \"file/*\"))\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t\tlet acceptArray: string[];\n\t\tif (typeof file_accept === \"string\") {\n\t\t\tacceptArray = file_accept.split(\",\").map((s) => s.trim());\n\t\t} else if (Array.isArray(file_accept)) {\n\t\t\tacceptArray = file_accept;\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn (\n\t\t\tacceptArray.includes(uploaded_file_extension) ||\n\t\t\tacceptArray.some((type) => {\n\t\t\t\tconst [category] = type.split(\"/\").map((s) => s.trim());\n\t\t\t\treturn (\n\t\t\t\t\ttype.endsWith(\"/*\") && uploaded_file_type.startsWith(category + \"/\")\n\t\t\t\t);\n\t\t\t})\n\t\t);\n\t}\n\n\texport async function load_files(\n\t\tfiles: File[] | Blob[]\n\t): Promise<(FileData | null)[] | void> {\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tlet _files: File[] = files.map(\n\t\t\t(f) =>\n\t\t\t\tnew File([f], f instanceof File ? f.name : \"file\", { type: f.type })\n\t\t);\n\n\t\tif (ios && use_post_upload_validation) {\n\t\t\t_files = _files.filter((file) => {\n\t\t\t\tif (is_valid_file(file)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tdispatch(\n\t\t\t\t\t\"error\",\n\t\t\t\t\t`Invalid file type: ${file.name}. Only ${filetype} allowed.`\n\t\t\t\t);\n\t\t\t\treturn false;\n\t\t\t});\n\n\t\t\tif (_files.length === 0) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t}\n\n\t\tfile_data = await prepare_files(_files);\n\t\treturn await handle_upload(file_data);\n\t}\n\n\tfunction is_valid_file(file: File): boolean {\n\t\tif (!filetype) return true;\n\n\t\tconst allowed_types = Array.isArray(filetype) ? filetype : [filetype];\n\n\t\treturn allowed_types.some((type) => {\n\t\t\tconst processed_type = process_file_type(type);\n\n\t\t\tif (processed_type.startsWith(\".\")) {\n\t\t\t\treturn file.name.toLowerCase().endsWith(processed_type.toLowerCase());\n\t\t\t}\n\n\t\t\tif (processed_type === \"*\") {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (processed_type.endsWith(\"/*\")) {\n\t\t\t\tconst [category] = processed_type.split(\"/\");\n\t\t\t\treturn file.type.startsWith(category + \"/\");\n\t\t\t}\n\n\t\t\treturn file.type === processed_type;\n\t\t});\n\t}\n\n\tasync function load_files_from_upload(files: File[]): Promise<void> {\n\t\tconst files_to_load = files.filter((file) => {\n\t\t\tconst file_extension = \".\" + file.name.split(\".\").pop();\n\t\t\tif (\n\t\t\t\tfile_extension &&\n\t\t\t\tis_valid_mimetype(accept_file_types, file_extension, file.type)\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tfile_extension && Array.isArray(filetype)\n\t\t\t\t\t? filetype.includes(file_extension)\n\t\t\t\t\t: file_extension === filetype\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tdispatch(\"error\", `Invalid file type only ${filetype} allowed.`);\n\t\t\treturn false;\n\t\t});\n\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(files_to_load);\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", files_to_load[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", files_to_load);\n\t\t}\n\t}\n\n\texport async function load_files_from_drop(e: DragEvent): Promise<void> {\n\t\tdragging = false;\n\t\tif (!e.dataTransfer?.files) return;\n\t\tconst files_to_load = Array.from(e.dataTransfer.files).filter(\n\t\t\tis_valid_file\n\t\t);\n\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(files_to_load);\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", files_to_load[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", files_to_load);\n\t\t}\n\t}\n</script>\n\n{#if filetype === \"clipboard\"}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tclass:icon-mode={icon_upload}\n\t\tstyle:height={icon_upload\n\t\t\t? \"\"\n\t\t\t: height\n\t\t\t\t? typeof height === \"number\"\n\t\t\t\t\t? height + \"px\"\n\t\t\t\t\t: height\n\t\t\t\t: \"100%\"}\n\t\ttabindex={hidden ? -1 : 0}\n\t\ton:click={paste_clipboard}\n\t\taria-label={aria_label || \"Paste from clipboard\"}\n\t>\n\t\t<slot />\n\t</button>\n{:else if uploading && show_progress}\n\t{#if !hidden}\n\t\t<UploadProgress {root} {upload_id} files={file_data} {stream_handler} />\n\t{/if}\n{:else}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tclass:disable_click\n\t\tclass:icon-mode={icon_upload}\n\t\tstyle:height={icon_upload\n\t\t\t? \"\"\n\t\t\t: height\n\t\t\t\t? typeof height === \"number\"\n\t\t\t\t\t? height + \"px\"\n\t\t\t\t\t: height\n\t\t\t\t: \"100%\"}\n\t\ttabindex={hidden ? -1 : 0}\n\t\tuse:drag={{\n\t\t\ton_drag_change: (dragging) => (dragging = dragging),\n\t\t\ton_files: (files) => load_files_from_upload(files),\n\t\t\taccepted_types: accept_file_types,\n\t\t\tmode: file_count,\n\t\t\tdisable_click\n\t\t}}\n\t\taria-label={aria_label || \"Click to upload or drop files\"}\n\t\taria-dropeffect=\"copy\"\n\t>\n\t\t<slot />\n\t</button>\n{/if}\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.center {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t.flex {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.hidden {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tflex-grow: 0;\n\t}\n\n\t.hidden :global(svg) {\n\t\tdisplay: none;\n\t}\n\n\t.disable_click {\n\t\tcursor: default;\n\t}\n\n\t.icon-mode {\n\t\tposition: absolute !important;\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tpadding: 0;\n\t\tmin-height: 0;\n\t\tborder-radius: var(--radius-circle);\n\t}\n\n\t.icon-mode :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "polyline", "line", "onMount", "createEventDispatcher", "t0_value", "getProgress", "ctx", "t2_value", "progress_1", "progress_1_value_value", "div1", "span0", "div0", "span1", "dirty", "set_data", "t0", "t2", "t1_value", "create_if_block", "div", "span", "t1", "t3", "t3_value", "file", "calculateTotalProgress", "files", "totalProgress", "upload_id", "$$props", "root", "stream_handler", "stream", "progress", "current_file_upload", "file_to_display", "files_with_progress", "dispatch", "handleProgress", "filename", "chunk_size", "$$invalidate", "event", "_data", "onDestroy", "create_drag", "hidden_input", "_options", "node", "options", "setup_hidden_input", "accept_options", "handle_drag", "e", "handle_drag_enter", "handle_drag_leave", "handle_drop", "handle_click", "handle_file_input_change", "new_options", "tick", "attr", "button", "button_aria_label_value", "set_style", "drag_function", "current", "create_if_block_2", "is_valid_mimetype", "file_accept", "uploaded_file_extension", "uploaded_file_type", "accept", "acceptArray", "s", "type", "category", "dragging", "drag", "_open_file_upload", "filetype", "boundedheight", "center", "flex", "file_count", "disable_click", "hidden", "format", "uploading", "show_progress", "max_file_size", "upload", "icon_upload", "height", "aria_label", "open_upload", "file_data", "accept_file_types", "use_post_upload_validation", "get_ios", "userAgent", "validFileTypes", "process_file_type", "ios", "paste_clipboard", "items", "i", "t", "blob", "load_files", "open_file_upload", "handle_upload", "_file_data", "_files", "f", "is_valid_file", "prepare_files", "processed_type", "load_files_from_upload", "files_to_load", "file_extension", "load_files_from_drop"], "mappings": "ouBAAAA,GASAC,EAAAC,EAAAC,CAAA,EAJEC,GAGCF,EAAAG,CAAA,yvBCRHL,GAcAC,EAAAC,EAAAC,CAAA,EAHEC,EAAsDF,EAAAG,CAAA,EAAAD,EAErDF,EAAAI,CAAA,EAAAF,EAAuCF,EAAAK,CAAA,oTCXhC,CAAA,QAAAC,GAAAC,sBAAAA,eAAiD,EAAA,OAAA,sDAgG1CC,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,WAKvCC,EAAAD,KAAgB,UAAS,iKANjBE,EAAA,MAAAC,EAAAJ,EAAYC,EAAe,CAAA,CAAA,+KALtCZ,GAaKC,EAAAe,EAAAb,CAAA,EAZJC,EAQMY,EAAAC,CAAA,EAPLb,EAMKa,EAAAC,CAAA,EALJd,EAIAc,EAAAJ,CAAA,gBAGFV,EAEMY,EAAAG,CAAA,iBANQC,EAAA,GAAAV,KAAAA,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,KAAAS,EAAAC,EAAAZ,CAAA,EAD/BU,EAAA,GAAAL,KAAAA,EAAAJ,EAAYC,EAAe,CAAA,CAAA,gBAMnCQ,EAAA,GAAAP,KAAAA,EAAAD,KAAgB,UAAS,KAAAS,EAAAE,EAAAV,CAAA,4CAhBhBW,EAAAZ,KAAoB,OAAM,SACrCA,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,eAGxCA,EAAe,CAAA,GAAAa,GAAAb,CAAA,wCAJlB,YAAU,2BACwC,KAAG,2HAHxDZ,GAsBKC,EAAAyB,EAAAvB,CAAA,EArBJC,EAGAsB,EAAAC,CAAA,qEAFaP,EAAA,GAAAI,KAAAA,EAAAZ,KAAoB,OAAM,KAAAS,EAAAO,EAAAJ,CAAA,cACrCZ,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,SAAMS,EAAAQ,EAAAC,CAAA,EAG9ClB,EAAe,CAAA,oIAxDXD,EAAYoB,EAAA,CACZ,OAAAA,EAAK,SAAW,KAAQA,EAAK,MAAQ,IAAM,WA8B3CC,GAAuBC,EAAAA,KAC3BC,EAAgB,EACpBD,OAAAA,EAAM,QAASF,GAAA,CACdG,GAAiBvB,EAAYoB,CAA4B,IAG1D,SAAS,gBAAgB,MAAM,YAC9B,2BACCG,EAAgBD,EAAM,QAAQ,QAAQ,CAAC,EAAI,GAAA,EAGtCC,EAAgBD,EAAM,0BAvEnB,GAAA,CAAA,UAAAE,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,EACA,CAAA,MAAAH,CAAA,EAAAG,EACA,CAAA,eAAAE,CAAA,EAAAF,EAEPG,EACAC,EAAW,GACXC,EACAC,EAEAC,EAA8CV,EAAM,IAAKF,IAExD,CAAA,GAAAA,EACH,SAAU,CAAA,UAINa,EAAWnC,KAER,SAAAoC,EAAeC,EAAkBC,EAAA,CAEzCC,EAAA,EAAAL,EAAsBA,EAAoB,IAAKZ,IAC1CA,EAAK,YAAce,IACtBf,EAAK,UAAYgB,GAEXhB,KAQT,OAAAvB,GAAA,SAAA,IACC+B,EAAA,MAAeD,EACV,IAAA,IAAA,GAAOD,CAAI,yCAAyCF,CAAS,EAAA,CAAA,EAG9DI,GAAU,KACH,MAAA,IAAA,MAAM,6BAA6B,EAG9CA,EAAO,UAA4B,eAAAU,EAAA,OAC5BC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EAC9BT,GAAAQ,EAAA,EAAUR,EAAW,EAAA,EACtBU,EAAM,MAAQ,QAEjBX,GAAQ,MAAA,EACRK,EAAS,MAAM,QAEfH,EAAsBS,CAAA,EACtBL,EAAeK,EAAM,UAAWA,EAAM,UAAU,MAInDC,GAAA,IAAA,EAEKZ,GAAU,MAAQA,GAAU,OAAWA,EAAO,8LAiBhDP,GAAuBW,CAAmB,kBAE1CK,EAAA,EAAAN,EAAkBD,GAAuBE,EAAoB,CAAC,CAAA,2cCrE3D,SAASS,IAGd,CACG,IAAAC,EACAC,EACG,MAAA,CACN,KACCC,EACAC,EAA6B,GAI5B,CACUF,EAAAE,EAGX,SAASC,GAA2B,CACpBJ,EAAA,SAAS,cAAc,OAAO,EAC7CA,EAAa,KAAO,OACpBA,EAAa,MAAM,QAAU,OAChBA,EAAA,aAAa,aAAc,aAAa,EACxCA,EAAA,aAAa,cAAe,aAAa,EACtD,MAAMK,EAAiB,MAAM,QAAQJ,EAAS,cAAc,EACzDA,EAAS,eAAe,KAAK,GAAG,EAChCA,EAAS,gBAAkB,OAE1BI,IACHL,EAAa,OAASK,GAGVL,EAAA,SAAWC,EAAS,OAAS,YAAc,GACpDA,EAAS,OAAS,cACrBD,EAAa,gBAAkB,GAClBA,EAAA,aAAa,YAAa,EAAE,EAC5BA,EAAA,aAAa,eAAgB,EAAE,GAE7CE,EAAK,YAAYF,CAAY,CAC9B,CAEmBI,IAEnB,SAASE,EAAYC,EAAoB,CACxCA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,CACnB,CAEA,SAASC,EAAkBD,EAAoB,CAC9CA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAClBN,EAAS,iBAAiB,EAAI,CAC/B,CAEA,SAASQ,EAAkBF,EAAoB,CAC9CA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAClBN,EAAS,iBAAiB,EAAK,CAChC,CAEA,SAASS,EAAYH,EAAoB,CAKpC,GAJJA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAClBN,EAAS,iBAAiB,EAAK,EAE3B,CAACM,EAAE,cAAc,MAAO,OAC5B,MAAM3B,EAAQ,MAAM,KAAK2B,EAAE,aAAa,KAAK,EACzC3B,EAAM,OAAS,GAClBqB,EAAS,WAAWrB,CAAK,CAE3B,CAEA,SAAS+B,GAAqB,CACxBV,EAAS,gBACbD,EAAa,MAAQ,GACrBA,EAAa,MAAM,EAErB,CAEA,SAASY,GAAiC,CACzC,GAAIZ,EAAa,MAAO,CACvB,MAAMpB,EAAQ,MAAM,KAAKoB,EAAa,KAAK,EACvCpB,EAAM,OAAS,GAClBqB,EAAS,WAAWrB,CAAK,CAE3B,CACD,CAGK,OAAAsB,EAAA,iBAAiB,OAAQI,CAAW,EACpCJ,EAAA,iBAAiB,YAAaI,CAAW,EACzCJ,EAAA,iBAAiB,UAAWI,CAAW,EACvCJ,EAAA,iBAAiB,WAAYI,CAAW,EACxCJ,EAAA,iBAAiB,YAAaM,CAAiB,EAC/CN,EAAA,iBAAiB,YAAaO,CAAiB,EAC/CP,EAAA,iBAAiB,OAAQQ,CAAW,EACpCR,EAAA,iBAAiB,QAASS,CAAY,EAC7BX,EAAA,iBAAiB,SAAUY,CAAwB,EAE1D,CACN,OAAOC,EAAgC,CAC3BZ,EAAAY,EAEXb,EAAa,OAAO,EACDI,IACNJ,EAAA,iBAAiB,SAAUY,CAAwB,CACjE,EACA,SAAU,CACJV,EAAA,oBAAoB,OAAQI,CAAW,EACvCJ,EAAA,oBAAoB,YAAaI,CAAW,EAC5CJ,EAAA,oBAAoB,UAAWI,CAAW,EAC1CJ,EAAA,oBAAoB,WAAYI,CAAW,EAC3CJ,EAAA,oBAAoB,YAAaM,CAAiB,EAClDN,EAAA,oBAAoB,YAAaO,CAAiB,EAClDP,EAAA,oBAAoB,OAAQQ,CAAW,EACvCR,EAAA,oBAAoB,QAASS,CAAY,EACjCX,EAAA,oBAAoB,SAAUY,CAAwB,EACnEZ,EAAa,OAAO,CACrB,CAAA,CAEF,EACA,kBAAyB,CACpBA,IACHA,EAAa,MAAQ,GACrBA,EAAa,MAAM,EAErB,CAAA,CAEF,oaC3IU,CAAA,sBAAA5C,GAAA,KAAA0D,gBAA+C,EAAA,OAAA,2JA4S7CvD,EAAM,CAAA,EAAA,GAAQ,CAAC,EAQbwD,EAAAC,EAAA,aAAAC,EAAA1D,OAAc,+BAA+B,wLAhBxCA,EAAW,EAAA,CAAA,EACd2D,EAAAF,EAAA,SAAAzD,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,UAbXZ,EA0BQC,EAAAoE,EAAAlE,CAAA,oDAVN,eAAcqE,GACd,SAAQ5D,EAAA,EAAA,EACR,eAAgBA,EAAiB,EAAA,EACjC,KAAMA,EAAU,CAAA,EAChB,cAAAA,EAAA,CAAA,2HANSA,EAAM,CAAA,EAAA,GAAQ,wBAQZ,CAAA6D,GAAArD,EAAA,CAAA,EAAA,OAAAkD,KAAAA,EAAA1D,OAAc,yGANzB,eAAc4D,GACd,SAAQ5D,EAAA,EAAA,EACR,eAAgBA,EAAiB,EAAA,EACjC,KAAMA,EAAU,CAAA,EAChB,cAAAA,EAAA,CAAA,4NAdgBA,EAAW,EAAA,CAAA,cACd2D,EAAAF,EAAA,SAAAzD,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,wGAjBLA,EAAM,CAAA,GAAA8D,GAAA9D,CAAA,uEAANA,EAAM,CAAA,6SAPDA,EAAM,CAAA,EAAA,GAAQ,CAAC,EAEbwD,EAAAC,EAAA,aAAAC,EAAA1D,OAAc,sBAAsB,gIAV/BA,EAAW,EAAA,CAAA,EACd2D,EAAAF,EAAA,SAAAzD,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,UAZXZ,EAkBQC,EAAAoE,EAAAlE,CAAA,yCAJGS,EAAe,EAAA,CAAA,wHADfA,EAAM,CAAA,EAAA,GAAQ,wBAEZ,CAAA6D,GAAArD,EAAA,CAAA,EAAA,OAAAkD,KAAAA,EAAA1D,OAAc,4NAVTA,EAAW,EAAA,CAAA,cACd2D,EAAAF,EAAA,SAAAzD,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,6JASgCA,EAAS,EAAA,yKAATA,EAAS,EAAA,qMAtBhD,OAAAA,OAAa,YAAW,EAoBnBA,MAAaA,EAAa,EAAA,EAAA,ySAjK1B,SAAA+D,GACRC,EACAC,EACAC,EAAA,CAGE,GAAA,CAAAF,GACDA,IAAgB,KAChBA,IAAgB,UACf,MAAM,QAAQA,CAAW,GACzBA,EAAY,KAAMG,GAAWA,IAAW,KAAOA,IAAW,QAAQ,EAE5D,MAAA,GAEJ,IAAAC,YACOJ,GAAgB,SAC1BI,EAAcJ,EAAY,MAAM,GAAG,EAAE,IAAKK,GAAMA,EAAE,KAAA,CAAA,UACxC,MAAM,QAAQL,CAAW,EACnCI,EAAcJ,MAEP,OAAA,GAIP,OAAAI,EAAY,SAASH,CAAuB,GAC5CG,EAAY,KAAME,GAAA,CACV,KAAA,CAAAC,CAAQ,EAAID,EAAK,MAAM,GAAG,EAAE,IAAKD,GAAMA,EAAE,KAAA,CAAA,EAE/C,OAAAC,EAAK,SAAS,IAAI,GAAKJ,EAAmB,WAAWK,EAAW,GAAG,aA0JpDC,GAAcA,EAAWA,sDAvSpC,KAAA,CAAA,KAAAC,EAAM,iBAAkBC,CAAsB,EAAAlC,GAAA,MAE3C,SAAAmC,EAAqC,IAAA,EAAAnD,GACrC,SAAAgD,EAAW,EAAA,EAAAhD,GACX,cAAAoD,EAAgB,EAAA,EAAApD,GAChB,OAAAqD,EAAS,EAAA,EAAArD,GACT,KAAAsD,EAAO,EAAA,EAAAtD,GACP,WAAAuD,EAAkD,QAAA,EAAAvD,GAClD,cAAAwD,EAAgB,EAAA,EAAAxD,EAChB,CAAA,KAAAC,CAAA,EAAAD,GACA,OAAAyD,EAAS,EAAA,EAAAzD,GACT,OAAA0D,EAA0B,MAAA,EAAA1D,GAC1B,UAAA2D,EAAY,EAAA,EAAA3D,GACZ,cAAA4D,GAAgB,EAAA,EAAA5D,GAChB,cAAA6D,EAA+B,IAAA,EAAA7D,EAC/B,CAAA,OAAA8D,CAAA,EAAA9D,EACA,CAAA,eAAAE,EAAA,EAAAF,GACA,YAAA+D,GAAc,EAAA,EAAA/D,GACd,OAAAgE,GAAsC,MAAA,EAAAhE,GACtC,WAAAiE,GAAiC,MAAA,EAAAjE,EAC5B,SAAAkE,IAAA,CACfhB,IAEG,IAAAnD,EACAoE,EACAC,EACAC,GAA6C,KAE3C,MAAAC,GAAA,IAAA,WACM,UAAc,IAAA,CAClB,MAAAC,EAAY,UAAU,UAAU,YAAA,EAC/B,OAAAA,EAAU,QAAQ,QAAQ,EAAA,IAAUA,EAAU,QAAQ,MAAM,EAAI,GAEjE,MAAA,IAKF/D,EAAWnC,KACXmG,GAAA,CAAkB,QAAS,QAAS,QAAS,OAAQ,MAAM,EAC3DC,EAAqB3B,GACtB4B,GAAO5B,EAAK,WAAW,GAAG,GAC7BuB,GAA6B,GACtBvB,GAEJ4B,GAAO5B,EAAK,SAAS,QAAQ,EACzB,IAEJA,EAAK,WAAW,GAAG,GAAKA,EAAK,SAAS,IAAI,EACtCA,EAEJ0B,GAAe,SAAS1B,CAAI,EACxBA,EAAO,KAER,IAAMA,EAcE,SAAA6B,IAAA,CACf,UAAU,UAAU,OAAO,KAAY,MAAAC,GAAA,SAC7BC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAA,OAC3B/B,EAAO8B,EAAMC,CAAC,EAAE,MAAM,KAAMC,GAAMA,EAAE,WAAW,QAAQ,CAAA,EACzD,GAAAhC,EAAA,CACH8B,EAAMC,CAAC,EAAE,QAAQ/B,CAAI,EAAE,KAAY,MAAAiC,GAAA,CAC5B,MAAApF,EAAA,IAAW,MACfoF,CAAI,EAAA,aACQjC,EAAK,QAAQ,SAAU,EAAE,CAAA,EAAA,EAEjC,MAAAkC,EAAA,CAAYrF,CAAI,CAAA,cAQX,SAAAsF,IAAA,CACf/B,mBAGcgC,GACdf,EAAAA,CAEM,MAAApC,GAAA,EACNnB,EAAA,GAAAb,EAAY,KAAK,OAAS,EAAA,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,CAAA,MACtD4D,EAAY,EAAA,MAEL,MAAAwB,EAAA,MAAmBrB,EACxBK,EACAlE,EACAF,EACA8D,GAAiB,GAAA,EAElB,OAAArD,EAAS,OAAQ+C,IAAe,SAAW4B,IAAa,CAAC,EAAIA,CAAU,MACvExB,EAAY,EAAA,EACLwB,GAAA,CAAA,CACC,OAAA3D,EAAA,CACR,OAAAhB,EAAS,QAAUgB,EAAY,OAAO,MACtCmC,EAAY,EAAA,qBAuCQqB,EACrBnF,EAAA,KAEKA,EAAM,cAGP,IAAAuF,EAAiBvF,EAAM,IACzBwF,GACI,IAAA,KAAA,CAAMA,CAAC,EAAGA,aAAa,KAAOA,EAAE,KAAO,OAAU,CAAA,KAAMA,EAAE,IAAA,CAAA,CAAA,SAG3DX,GAAOL,KACVe,EAASA,EAAO,OAAQzF,GACnB2F,GAAc3F,CAAI,EACd,IAERa,EACC,QACsB,sBAAAb,EAAK,IAAI,UAAUwD,CAAQ,WAAA,EAE3C,KAGJiC,EAAO,SAAW,OAKvBxE,EAAA,GAAAuD,EAAA,MAAkBoB,GAAcH,CAAM,CAAA,EACzB,MAAAF,GAAcf,CAAS,YAG5BmB,GAAc3F,EAAA,QACjBwD,GAEiB,MAAM,QAAQA,CAAQ,EAAIA,GAAYA,CAAQ,GAE/C,KAAML,GAAA,CACpB,MAAA0C,EAAiBf,EAAkB3B,CAAI,EAEzC,GAAA0C,EAAe,WAAW,GAAG,SACzB7F,EAAK,KAAK,YAAc,EAAA,SAAS6F,EAAe,YAAA,CAAA,KAGpDA,IAAmB,IACf,MAAA,GAGJ,GAAAA,EAAe,SAAS,IAAI,EAAA,OACxBzC,CAAQ,EAAIyC,EAAe,MAAM,GAAG,SACpC7F,EAAK,KAAK,WAAWoD,EAAW,GAAG,EAGpC,OAAApD,EAAK,OAAS6F,IApBA,kBAwBRC,GAAuB5F,EAAA,CAC/B,MAAA6F,EAAgB7F,EAAM,OAAQF,GAAA,CAC7B,MAAAgG,EAAiB,IAAMhG,EAAK,KAAK,MAAM,GAAG,EAAE,aAEjDgG,GACApD,GAAkB6B,EAAmBuB,EAAgBhG,EAAK,IAAI,IAK9DgG,GAAkB,MAAM,QAAQxC,CAAQ,EACrCA,EAAS,SAASwC,CAAc,EAChCA,IAAmBxC,GAEf,IAER3C,EAAS,kCAAmC2C,CAAQ,WAAA,EAC7C,SAGJO,GAAU,OACP,MAAAsB,EAAWU,CAAa,UAE1BnC,IAAe,SAAA,CAClB/C,EAAS,OAAQkF,EAAc,CAAC,CAAA,SAGjClF,EAAS,OAAQkF,CAAa,kBAIVE,GAAqBpE,EAAA,CAErC,OADLwB,EAAW,EAAA,EACN,CAAAxB,EAAE,cAAc,MAAA,OACf,MAAAkE,EAAgB,MAAM,KAAKlE,EAAE,aAAa,KAAK,EAAE,OACtD8D,EAAA,KAGG5B,GAAU,OACP,MAAAsB,EAAWU,CAAa,UAE1BnC,IAAe,SAAA,CAClB/C,EAAS,OAAQkF,EAAc,CAAC,CAAA,SAGjClF,EAAS,OAAQkF,CAAa,YA+CnB7F,GAAU4F,GAAuB5F,CAAK,mvBA/O5CsD,GAAY,UAClBiB,EAAoB,IAAA,SACHjB,GAAa,SAC9BvC,EAAA,GAAAwD,EAAoBK,EAAkBtB,CAAQ,CAAA,EACpCuB,GAAOvB,EAAS,SAAS,QAAQ,OAC3CiB,EAAoB,GAAA,OAEpBjB,EAAWA,EAAS,IAAIsB,CAAiB,CAAA,OACzCL,EAAoBjB,EAAS,KAAK,IAAI,CAAA,KA7BvCvC,EAAA,GAAG8D,EAAMJ,GAAA,CAAA"}