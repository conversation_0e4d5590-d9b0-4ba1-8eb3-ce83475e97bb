import{aj as yn,h as Sn,b as G}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";const ln=4,X=4,an=1,C=2,xn=8,H=65536,j=H>>3,En=16,T=14,F=(1<<En)+1,V=1<<T,d=V-1,K=59,sn=63,vn=2+sn-K;var U;(function(n){n[n.NO_COMPRESSION=0]="NO_COMPRESSION",n[n.RLE_COMPRESSION=1]="RLE_COMPRESSION",n[n.ZIPS_COMPRESSION=2]="ZIPS_COMPRESSION",n[n.ZIP_COMPRESSION=3]="ZIP_COMPRESSION",n[n.PIZ_COMPRESSION=4]="PIZ_COMPRESSION",n[n.PXR24_COMPRESSION=5]="PXR24_COMPRESSION"})(U||(U={}));var q;(function(n){n[n.INCREASING_Y=0]="INCREASING_Y",n[n.DECREASING_Y=1]="DECREASING_Y"})(q||(q={}));const z=bn();function bn(){const n=new ArrayBuffer(4),t=new Float32Array(n),e=new Uint32Array(n),l=new Uint32Array(512),r=new Uint32Array(512);for(let i=0;i<256;++i){const a=i-127;a<-27?(l[i]=0,l[i|256]=32768,r[i]=24,r[i|256]=24):a<-14?(l[i]=1024>>-a-14,l[i|256]=1024>>-a-14|32768,r[i]=-a-1,r[i|256]=-a-1):a<=15?(l[i]=a+15<<10,l[i|256]=a+15<<10|32768,r[i]=13,r[i|256]=13):a<128?(l[i]=31744,l[i|256]=64512,r[i]=24,r[i|256]=24):(l[i]=31744,l[i|256]=64512,r[i]=13,r[i|256]=13)}const c=new Uint32Array(2048),s=new Uint32Array(64),o=new Uint32Array(64);for(let i=1;i<1024;++i){let a=i<<13,u=0;for(;!(a&8388608);)a<<=1,u-=8388608;a&=-8388609,u+=947912704,c[i]=a|u}for(let i=1024;i<2048;++i)c[i]=939524096+(i-1024<<13);for(let i=1;i<31;++i)s[i]=i<<23;s[31]=1199570944,s[32]=2147483648;for(let i=33;i<63;++i)s[i]=2147483648+(i-32<<23);s[63]=3347054592;for(let i=1;i<64;++i)i!==32&&(o[i]=1024);return{floatView:t,uint32View:e,baseTable:l,shiftTable:r,mantissaTable:c,exponentTable:s,offsetTable:o}}function $(n,t){const e=new Uint8Array(n);let l=0;for(;e[t.value+l]!=0;)l+=1;const r=new TextDecoder().decode(e.slice(t.value,t.value+l));return t.value=t.value+l+1,r}function P(n,t){const e=n.getInt32(t.value,!0);return t.value+=ln,e}function m(n,t){const e=n.getUint32(t.value,!0);return t.value+=ln,e}function Z(n,t){const e=n.getUint8(t.value);return t.value+=an,e}function B(n,t){const e=n.getUint16(t.value,!0);return t.value+=C,e}function cn(n,t){const e=n[t.value];return t.value+=an,e}function gn(n,t){let e;return"getBigInt64"in DataView.prototype?e=Number(n.getBigInt64(t.value,!0)):e=n.getUint32(t.value+4,!0)+Number(n.getUint32(t.value,!0)<<32),t.value+=xn,e}function b(n,t){const e=n.getFloat32(t.value,!0);return t.value+=X,e}function In(n,t){return On(B(n,t))}function On(n){const t=(n&31744)>>10,e=n&1023;return(n>>15?-1:1)*(t?t===31?e?NaN:1/0:Math.pow(2,t-15)*(1+e/1024):6103515625e-14*(e/1024))}function An(n){if(Math.abs(n)>65504)throw new Error("Value out of range.Consider using float instead of half-float.");n=yn(n,-65504,65504),z.floatView[0]=n;const t=z.uint32View[0],e=t>>23&511;return z.baseTable[e]+((t&8388607)>>z.shiftTable[e])}function Pn(n,t){return An(b(n,t))}function Un(n,t,e){const l=new TextDecoder().decode(new Uint8Array(n).slice(t.value,t.value+e));return t.value=t.value+e,l}function _n(n,t){const e=P(n,t),l=m(n,t);return[e,l]}function mn(n,t){const e=m(n,t),l=m(n,t);return[e,l]}function Tn(n,t){const e=b(n,t),l=b(n,t);return[e,l]}function Nn(n,t){const e=b(n,t),l=b(n,t),r=b(n,t);return[e,l,r]}function Rn(n,t,e){const l=t.value,r=[];for(;t.value<l+e-1;){const c=$(n.buffer,t),s=P(n,t),o=Z(n,t);t.value+=3;const i=P(n,t),a=P(n,t);r.push({name:c,pixelType:s,pLinear:o,xSampling:i,ySampling:a})}return t.value+=1,r}function Cn(n,t){const e=b(n,t),l=b(n,t),r=b(n,t),c=b(n,t),s=b(n,t),o=b(n,t),i=b(n,t),a=b(n,t);return{redX:e,redY:l,greenX:r,greenY:c,blueX:s,blueY:o,whiteX:i,whiteY:a}}function Mn(n,t){return Z(n,t)}function Dn(n,t){const e=P(n,t),l=P(n,t),r=P(n,t),c=P(n,t);return{xMin:e,yMin:l,xMax:r,yMax:c}}function Fn(n,t){const e=Z(n,t);return q[e]}function Ln(n,t,e,l){switch(e){case"string":case"stringvector":case"iccProfile":return Un(n.buffer,t,l);case"chlist":return Rn(n,t,l);case"chromaticities":return Cn(n,t);case"compression":return Mn(n,t);case"box2i":return Dn(n,t);case"lineOrder":return Fn(n,t);case"float":return b(n,t);case"v2f":return Tn(n,t);case"v3f":return Nn(n,t);case"int":return P(n,t);case"rational":return _n(n,t);case"timecode":return mn(n,t);case"preview":return t.value+=l,"skipped";default:t.value+=l;return}}function on(n){for(let t=1;t<n.length;t++){const e=n[t-1]+n[t]-128;n[t]=e}}function un(n,t){let e=0,l=Math.floor((n.length+1)/2),r=0;const c=n.length-1;for(;!(r>c||(t[r++]=n[e++],r>c));)t[r++]=n[l++]}const kn=20000630;function zn(n,t){if(n.getUint32(0,!0)!=kn)throw new Error("Incorrect OpenEXR format");const e=n.getUint8(4),l=n.getUint8(5),r={singleTile:!!(l&2),longName:!!(l&4),deepFormat:!!(l&8),multiPart:!!(l&16)};t.value=8;const c={};let s=!0;for(;s;){const o=$(n.buffer,t);if(!o)s=!1;else{const i=$(n.buffer,t),a=m(n,t),u=Ln(n,t,i,a);u===void 0?Sn.Warn(`Unknown header attribute type ${i}'.`):c[o]=u}}if(l&-5)throw new Error("Unsupported file format");return{version:e,spec:r,...c}}const pn=16,Hn=1<<pn-1,nn=(1<<pn)-1;function Bn(n,t){let e=0;for(let r=0;r<H;++r)(r==0||n[r>>3]&1<<(r&7))&&(t[e++]=r);const l=e-1;for(;e<H;)t[e++]=0;return l}function Zn(n){for(let t=0;t<V;t++)n[t]={},n[t].len=0,n[t].lit=0,n[t].p=null}function tn(n,t,e,l,r){for(;e<n;)t=t<<8|cn(l,r),e+=8;return e-=n,{l:t>>e&(1<<n)-1,c:t,lc:e}}function J(n,t,e,l){return n=n<<8|cn(e,l),t+=8,{c:n,lc:t}}function Y(n,t,e,l,r,c,s,o,i){if(n==t){if(l<8){const f=J(e,l,r,c);e=f.c,l=f.lc}l-=8;let a=e>>l;if(a=new Uint8Array([a])[0],o.value+a>i)return null;const u=s[o.value-1];for(;a-- >0;)s[o.value++]=u}else if(o.value<i)s[o.value++]=n;else return null;return{c:e,lc:l}}const D=new Array(59);function Wn(n){for(let e=0;e<=58;++e)D[e]=0;for(let e=0;e<F;++e)D[n[e]]+=1;let t=0;for(let e=58;e>0;--e){const l=t+D[e]>>1;D[e]=t,t=l}for(let e=0;e<F;++e){const l=n[e];l>0&&(n[e]=l|D[l]++<<6)}}function Gn(n,t,e,l,r,c){const s=t;let o=0,i=0;for(;l<=r;l++){if(s.value-t.value>e)return;let a=tn(6,o,i,n,s);const u=a.l;if(o=a.c,i=a.lc,c[l]=u,u==sn){if(s.value-t.value>e)throw new Error("Error in HufUnpackEncTable");a=tn(8,o,i,n,s);let f=a.l+vn;if(o=a.c,i=a.lc,l+f>r+1)throw new Error("Error in HufUnpackEncTable");for(;f--;)c[l++]=0;l--}else if(u>=K){let f=u-K+2;if(l+f>r+1)throw new Error("Error in HufUnpackEncTable");for(;f--;)c[l++]=0;l--}}Wn(c)}function fn(n){return n&63}function hn(n){return n>>6}function Yn(n,t,e,l){for(;t<=e;t++){const r=hn(n[t]),c=fn(n[t]);if(r>>c)throw new Error("Invalid table entry");if(c>T){const s=l[r>>c-T];if(s.len)throw new Error("Invalid table entry");if(s.lit++,s.p){const o=s.p;s.p=new Array(s.lit);for(let i=0;i<s.lit-1;++i)s.p[i]=o[i]}else s.p=new Array(1);s.p[s.lit-1]=t}else if(c){let s=0;for(let o=1<<T-c;o>0;o--){const i=l[(r<<T-c)+s];if(i.len||i.p)throw new Error("Invalid table entry");i.len=c,i.lit=t,s++}}}return!0}function Xn(n,t,e,l,r,c,s,o,i){let a=0,u=0;const f=s,E=Math.trunc(l.value+(r+7)/8);for(;l.value<E;){let p=J(a,u,e,l);for(a=p.c,u=p.lc;u>=T;){const w=a>>u-T&d,y=t[w];if(y.len){u-=y.len;const h=Y(y.lit,c,a,u,e,l,o,i,f);h&&(a=h.c,u=h.lc)}else{if(!y.p)throw new Error("hufDecode issues");let h;for(h=0;h<y.lit;h++){const v=fn(n[y.p[h]]);for(;u<v&&l.value<E;)p=J(a,u,e,l),a=p.c,u=p.lc;if(u>=v&&hn(n[y.p[h]])==(a>>u-v&(1<<v)-1)){u-=v;const A=Y(y.p[h],c,a,u,e,l,o,i,f);A&&(a=A.c,u=A.lc);break}}if(h==y.lit)throw new Error("HufDecode issues")}}}const I=8-r&7;for(a>>=I,u-=I;u>0;){const p=t[a<<T-u&d];if(p.len){u-=p.len;const w=Y(p.lit,c,a,u,e,l,o,i,f);w&&(a=w.c,u=w.lc)}else throw new Error("HufDecode issues")}return!0}function Kn(n,t,e,l,r,c){const s={value:0},o=e.value,i=m(t,e),a=m(t,e);e.value+=4;const u=m(t,e);if(e.value+=4,i<0||i>=F||a<0||a>=F)throw new Error("Wrong HUF_ENCSIZE");const f=new Array(F),E=new Array(V);Zn(E);const I=l-(e.value-o);if(Gn(n,e,I,i,a,f),u>8*(l-(e.value-o)))throw new Error("Wrong hufUncompress");Yn(f,i,a,E),Xn(f,E,n,e,u,a,c,r,s)}function Q(n){return n&65535}function en(n){const t=Q(n);return t>32767?t-65536:t}function N(n,t){const e=en(n),r=en(t),c=e+(r&1)+(r>>1),s=c,o=c-r;return{a:s,b:o}}function R(n,t){const e=Q(n),l=Q(t),r=e-(l>>1)&nn;return{a:l+r-Hn&nn,b:r}}function qn(n,t,e,l,r,c,s){const o=s<16384,i=e>r?r:e;let a=1,u,f;for(;a<=i;)a<<=1;for(a>>=1,u=a,a>>=1;a>=1;){f=0;const E=f+c*(r-u),I=c*a,p=c*u,w=l*a,y=l*u;let h,v,A,L;for(;f<=E;f+=p){let x=f;const W=f+l*(e-u);for(;x<=W;x+=y){const O=x+w,g=x+I,k=g+w;if(o){let S=N(n[x+t],n[g+t]);h=S.a,A=S.b,S=N(n[O+t],n[k+t]),v=S.a,L=S.b,S=N(h,v),n[x+t]=S.a,n[O+t]=S.b,S=N(A,L),n[g+t]=S.a,n[k+t]=S.b}else{let S=R(n[x+t],n[g+t]);h=S.a,A=S.b,S=R(n[O+t],n[k+t]),v=S.a,L=S.b,S=R(h,v),n[x+t]=S.a,n[O+t]=S.b,S=R(A,L),n[g+t]=S.a,n[k+t]=S.b}}if(e&a){const O=x+I;let g;o?g=N(n[x+t],n[O+t]):g=R(n[x+t],n[O+t]),h=g.a,n[O+t]=g.b,n[x+t]=h}}if(r&a){let x=f;const W=f+l*(e-u);for(;x<=W;x+=y){const O=x+w;let g;o?g=N(n[x+t],n[O+t]):g=R(n[x+t],n[O+t]),h=g.a,n[O+t]=g.b,n[x+t]=h}}u=a,a>>=1}return f}function $n(n,t,e){for(let l=0;l<e;++l)t[l]=n[t[l]]}function Jn(n){let t=n.byteLength;const e=new Array;let l=0;const r=new DataView(n);for(;t>0;){const c=r.getInt8(l++);if(c<0){const s=-c;t-=s+1;for(let o=0;o<s;o++)e.push(r.getUint8(l++))}else{const s=c;t-=2;const o=r.getUint8(l++);for(let i=0;i<s+1;i++)e.push(o)}}return e}function wn(n){return new DataView(n.array.buffer,n.offset.value,n.size)}function Qn(n){const t=n.viewer.buffer.slice(n.offset.value,n.offset.value+n.size),e=new Uint8Array(Jn(t)),l=new Uint8Array(e.length);return on(e),un(e,l),new DataView(l.buffer)}function rn(n){const t=n.array.slice(n.offset.value,n.offset.value+n.size),e=fflate.unzlibSync(t),l=new Uint8Array(e.length);return on(e),un(e,l),new DataView(l.buffer)}function Vn(n){const t=n.array.slice(n.offset.value,n.offset.value+n.size),e=fflate.unzlibSync(t),l=n.lines*n.channels*n.width,r=n.type==1?new Uint16Array(l):new Uint32Array(l);let c=0,s=0;const o=new Array(4);for(let i=0;i<n.lines;i++)for(let a=0;a<n.channels;a++){let u=0;switch(n.type){case 1:o[0]=c,o[1]=o[0]+n.width,c=o[1]+n.width;for(let f=0;f<n.width;++f){const E=e[o[0]++]<<8|e[o[1]++];u+=E,r[s]=u,s++}break;case 2:o[0]=c,o[1]=o[0]+n.width,o[2]=o[1]+n.width,c=o[2]+n.width;for(let f=0;f<n.width;++f){const E=e[o[0]++]<<24|e[o[1]++]<<16|e[o[2]++]<<8;u+=E,r[s]=u,s++}break}}return new DataView(r.buffer)}function jn(n){const t=n.viewer,e={value:n.offset.value},l=new Uint16Array(n.width*n.scanlineBlockSize*(n.channels*n.type)),r=new Uint8Array(j);let c=0;const s=new Array(n.channels);for(let p=0;p<n.channels;p++)s[p]={},s[p].start=c,s[p].end=s[p].start,s[p].nx=n.width,s[p].ny=n.lines,s[p].size=n.type,c+=s[p].nx*s[p].ny*s[p].size;const o=B(t,e),i=B(t,e);if(i>=j)throw new Error("Wrong PIZ_COMPRESSION BITMAP_SIZE");if(o<=i)for(let p=0;p<i-o+1;p++)r[p+o]=Z(t,e);const a=new Uint16Array(H),u=Bn(r,a),f=m(t,e);Kn(n.array,t,e,f,l,c);for(let p=0;p<n.channels;++p){const w=s[p];for(let y=0;y<s[p].size;++y)qn(l,w.start+y,w.nx,w.size,w.ny,w.nx*w.size,u)}$n(a,l,c);let E=0;const I=new Uint8Array(l.buffer.byteLength);for(let p=0;p<n.lines;p++)for(let w=0;w<n.channels;w++){const y=s[w],h=y.nx*y.size,v=new Uint8Array(l.buffer,y.end*C,h*C);I.set(v,E),E+=h*C,y.end+=h}return new DataView(I.buffer)}var _;(function(n){n[n.Float=0]="Float",n[n.HalfFloat=1]="HalfFloat"})(_||(_={}));class M{}M.DefaultOutputType=_.HalfFloat;M.FFLATEUrl="https://unpkg.com/fflate@0.8.2";async function dn(n,t,e,l){const r={size:0,viewer:t,array:new Uint8Array(t.buffer),offset:e,width:n.dataWindow.xMax-n.dataWindow.xMin+1,height:n.dataWindow.yMax-n.dataWindow.yMin+1,channels:n.channels.length,channelLineOffsets:{},scanOrder:()=>0,bytesPerLine:0,outLineWidth:0,lines:0,scanlineBlockSize:0,inputSize:null,type:0,uncompress:null,getter:()=>0,format:5,outputChannels:0,decodeChannels:{},blockCount:null,byteArray:null,linearSpace:!1,textureType:0};switch(n.compression){case U.NO_COMPRESSION:r.lines=1,r.uncompress=wn;break;case U.RLE_COMPRESSION:r.lines=1,r.uncompress=Qn;break;case U.ZIPS_COMPRESSION:r.lines=1,r.uncompress=rn,await G.LoadScriptAsync(M.FFLATEUrl);break;case U.ZIP_COMPRESSION:r.lines=16,r.uncompress=rn,await G.LoadScriptAsync(M.FFLATEUrl);break;case U.PIZ_COMPRESSION:r.lines=32,r.uncompress=jn;break;case U.PXR24_COMPRESSION:r.lines=16,r.uncompress=Vn,await G.LoadScriptAsync(M.FFLATEUrl);break;default:throw new Error(U[n.compression]+" is unsupported")}r.scanlineBlockSize=r.lines;const c={};for(const a of n.channels)switch(a.name){case"Y":case"R":case"G":case"B":case"A":c[a.name]=!0,r.type=a.pixelType}let s=!1;if(c.R&&c.G&&c.B)s=!c.A,r.outputChannels=4,r.decodeChannels={R:0,G:1,B:2,A:3};else if(c.Y)r.outputChannels=1,r.decodeChannels={Y:0};else throw new Error("EXRLoader.parse: file contains unsupported data channels.");if(r.type===1)switch(l){case _.Float:r.getter=In,r.inputSize=C;break;case _.HalfFloat:r.getter=B,r.inputSize=C;break}else if(r.type===2)switch(l){case _.Float:r.getter=b,r.inputSize=X;break;case _.HalfFloat:r.getter=Pn,r.inputSize=X}else throw new Error("Unsupported pixelType "+r.type+" for "+n.compression);r.blockCount=r.height/r.scanlineBlockSize;for(let a=0;a<r.blockCount;a++)gn(t,e);const o=r.width*r.height*r.outputChannels;switch(l){case _.Float:r.byteArray=new Float32Array(o),r.textureType=1,s&&r.byteArray.fill(1,0,o);break;case _.HalfFloat:r.byteArray=new Uint16Array(o),r.textureType=2,s&&r.byteArray.fill(15360,0,o);break;default:throw new Error("Unsupported type: "+l)}let i=0;for(const a of n.channels)r.decodeChannels[a.name]!==void 0&&(r.channelLineOffsets[a.name]=i*r.width),i+=a.pixelType*2;return r.bytesPerLine=r.width*i,r.outLineWidth=r.width*r.outputChannels,n.lineOrder==="INCREASING_Y"?r.scanOrder=a=>a:r.scanOrder=a=>r.height-1-a,r.outputChannels==4?(r.format=5,r.linearSpace=!0):(r.format=6,r.linearSpace=!1),r}function nt(n,t,e,l){const r={value:0};for(let c=0;c<n.height/n.scanlineBlockSize;c++){const s=P(e,l)-t.dataWindow.yMin;n.size=m(e,l),n.lines=s+n.scanlineBlockSize>n.height?n.height-s:n.scanlineBlockSize;const i=n.size<n.lines*n.bytesPerLine&&n.uncompress?n.uncompress(n):wn(n);l.value+=n.size;for(let a=0;a<n.scanlineBlockSize;a++){const u=c*n.scanlineBlockSize,f=a+n.scanOrder(u);if(f>=n.height)continue;const E=a*n.bytesPerLine,I=(n.height-1-f)*n.outLineWidth;for(let p=0;p<n.channels;p++){const w=t.channels[p].name,y=n.channelLineOffsets[w],h=n.decodeChannels[w];if(h!==void 0){r.value=E+y;for(let v=0;v<n.width;v++){const A=I+v*n.outputChannels+h;n.byteArray&&(n.byteArray[A]=n.getter(i,r))}}}}}}class lt{constructor(){this.supportCascades=!1}loadCubeData(t,e,l,r,c){throw".exr not supported in Cube."}async loadData(t,e,l){const r=new DataView(t.buffer),c={value:0},s=zn(r,c),o=await dn(s,r,c,M.DefaultOutputType);nt(o,s,r,c);const i=s.dataWindow.xMax-s.dataWindow.xMin+1,a=s.dataWindow.yMax-s.dataWindow.yMin+1;l(i,a,e.generateMipMaps,!1,()=>{const u=e.getEngine();e.format=s.format,e.type=o.textureType,e.invertY=!1,e._gammaSpace=!s.linearSpace,o.byteArray&&u._uploadDataToTextureDirectly(e,o.byteArray,0,0,void 0,!0)})}}export{lt as _ExrTextureLoader};
//# sourceMappingURL=exrTextureLoader-BsLFnGKb.js.map
