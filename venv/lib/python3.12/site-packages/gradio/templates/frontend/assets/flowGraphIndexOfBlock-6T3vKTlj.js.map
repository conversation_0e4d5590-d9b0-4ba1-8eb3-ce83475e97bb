{"version": 3, "file": "flowGraphIndexOfBlock-6T3vKTlj.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphIndexOfBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeFlowGraphInteger } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * This block takes an object as input and an array and returns the index of the object in the array.\n */\nexport class FlowGraphIndexOfBlock extends FlowGraphBlock {\n    /**\n     * Construct a FlowGraphIndexOfBlock.\n     * @param config construction parameters\n     */\n    constructor(config) {\n        super(config);\n        this.config = config;\n        this.object = this.registerDataInput(\"object\", RichTypeAny);\n        this.array = this.registerDataInput(\"array\", RichTypeAny);\n        this.index = this.registerDataOutput(\"index\", RichTypeFlowGraphInteger, new FlowGraphInteger(-1));\n    }\n    /**\n     * @internal\n     */\n    _updateOutputs(context) {\n        const object = this.object.getValue(context);\n        const array = this.array.getValue(context);\n        if (array) {\n            this.index.setValue(new FlowGraphInteger(array.indexOf(object)), context);\n        }\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n    }\n    getClassName() {\n        return \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */;\n    }\n}\nRegisterClass(\"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */, FlowGraphIndexOfBlock);\n//# sourceMappingURL=flowGraphIndexOfBlock.js.map"], "names": ["FlowGraphIndexOfBlock", "FlowGraphBlock", "config", "RichTypeAny", "RichTypeFlowGraphInteger", "FlowGraphInteger", "context", "object", "array", "serializationObject", "RegisterClass"], "mappings": "8PAOO,MAAMA,UAA8BC,CAAe,CAKtD,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,OAAS,KAAK,kBAAkB,SAAUC,CAAW,EAC1D,KAAK,MAAQ,KAAK,kBAAkB,QAASA,CAAW,EACxD,KAAK,MAAQ,KAAK,mBAAmB,QAASC,EAA0B,IAAIC,EAAiB,EAAE,CAAC,CACnG,CAID,eAAeC,EAAS,CACpB,MAAMC,EAAS,KAAK,OAAO,SAASD,CAAO,EACrCE,EAAQ,KAAK,MAAM,SAASF,CAAO,EACrCE,GACA,KAAK,MAAM,SAAS,IAAIH,EAAiBG,EAAM,QAAQD,CAAM,CAAC,EAAGD,CAAO,CAE/E,CAKD,UAAUG,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,CACtC,CACD,cAAe,CACX,MAAO,uBACV,CACL,CACAC,EAAc,wBAA2DV,CAAqB", "x_google_ignoreList": [0]}