import{c as n,_ as r}from"./KHR_interactivity-DVSiPm30.js";import{b as o,R as i}from"./declarationMapper-r-RREw_K.js";import{R as h}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class u extends n{constructor(t){super(t),this.type="PointerOut",this.pointerId=this.registerDataOutput("pointerId",o),this.targetMesh=this.registerDataInput("targetMesh",i,t?.targetMesh),this.meshOutOfPointer=this.registerDataOutput("meshOutOfPointer",i)}_executeEvent(t,e){const s=this.targetMesh.getValue(t);return this.meshOutOfPointer.setValue(e.mesh,t),this.pointerId.setValue(e.pointerId,t),!(e.over&&r(e.mesh,s))&&(e.mesh===s||r(e.mesh,s))?(this._execute(t),!this.config?.stopPropagation):!0}_preparePendingTasks(t){}_cancelPendingTasks(t){}getClassName(){return"FlowGraphPointerOutEventBlock"}}h("FlowGraphPointerOutEventBlock",u);export{u as FlowGraphPointerOutEventBlock};
//# sourceMappingURL=flowGraphPointerOutEventBlock-BWnyltcF.js.map
