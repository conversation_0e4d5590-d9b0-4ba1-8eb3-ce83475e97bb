import{b as o}from"./KHR_interactivity-DVSiPm30.js";import{R as t}from"./declarationMapper-r-RREw_K.js";import{R as i}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class e extends o{constructor(a){super(a),this.animationToPause=this.registerDataInput("animationToPause",t)}_execute(a){this.animationToPause.getValue(a).pause(),this.out._activateSignal(a)}getClassName(){return"FlowGraphPauseAnimationBlock"}}i("FlowGraphPauseAnimationBlock",e);export{e as FlowGraphPauseAnimationBlock};
//# sourceMappingURL=flowGraphPauseAnimationBlock-DEDB__5r.js.map
