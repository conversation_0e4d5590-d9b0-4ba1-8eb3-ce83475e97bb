{"version": 3, "file": "flowGraphBinaryOperationBlock-DaBPP43z.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphBinaryOperationBlock.js"], "sourcesContent": ["import { FlowGraphCachedOperationBlock } from \"./flowGraphCachedOperationBlock.js\";\n/**\n * The base block for all binary operation blocks. Receives an input of type\n * LeftT, one of type RightT, and outputs a value of type ResultT.\n */\nexport class FlowGraphBinaryOperationBlock extends FlowGraphCachedOperationBlock {\n    constructor(leftRichType, rightRichType, resultRichType, _operation, _className, config) {\n        super(resultRichType, config);\n        this._operation = _operation;\n        this._className = _className;\n        this.a = this.registerDataInput(\"a\", leftRichType);\n        this.b = this.registerDataInput(\"b\", rightRichType);\n    }\n    /**\n     * the operation performed by this block\n     * @param context the graph context\n     * @returns the result of the operation\n     */\n    _doOperation(context) {\n        const a = this.a.getValue(context);\n        const b = this.b.getValue(context);\n        return this._operation(a, b);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return this._className;\n    }\n}\n//# sourceMappingURL=flowGraphBinaryOperationBlock.js.map"], "names": ["FlowGraphBinaryOperationBlock", "FlowGraphCachedOperationBlock", "leftRichType", "rightRichType", "resultRichType", "_operation", "_className", "config", "context", "b"], "mappings": "gEAKO,MAAMA,UAAsCC,CAA8B,CAC7E,YAAYC,EAAcC,EAAeC,EAAgBC,EAAYC,EAAYC,EAAQ,CACrF,MAAMH,EAAgBG,CAAM,EAC5B,KAAK,WAAaF,EAClB,KAAK,WAAaC,EAClB,KAAK,EAAI,KAAK,kBAAkB,IAAKJ,CAAY,EACjD,KAAK,EAAI,KAAK,kBAAkB,IAAKC,CAAa,CACrD,CAMD,aAAaK,EAAS,CAClB,MAAM,EAAI,KAAK,EAAE,SAASA,CAAO,EAC3BC,EAAI,KAAK,EAAE,SAASD,CAAO,EACjC,OAAO,KAAK,WAAW,EAAGC,CAAC,CAC9B,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CACL", "x_google_ignoreList": [0]}