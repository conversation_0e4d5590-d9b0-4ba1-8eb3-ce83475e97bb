{"version": 3, "file": "flowGraphMultiGateBlock-jaDVjzzS.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphMultiGateBlock.js"], "sourcesContent": ["import { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphExecutionBlock } from \"../../../flowGraphExecutionBlock.js\";\nimport { RichTypeFlowGraphInteger } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * A block that has an input flow and routes it to any potential output flows, randomly or sequentially\n */\nexport class FlowGraphMultiGateBlock extends FlowGraphExecutionBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * Output connections: The output signals.\n         */\n        this.outputSignals = [];\n        this.reset = this._registerSignalInput(\"reset\");\n        this.lastIndex = this.registerDataOutput(\"lastIndex\", RichTypeFlowGraphInteger, new FlowGraphInteger(-1));\n        this.setNumberOfOutputSignals(config?.outputSignalCount);\n    }\n    _getNextIndex(indexesUsed) {\n        // find the next index available from the indexes used array\n        // if all outputs were used, reset the indexes used array if we are in a loop multi gate\n        if (!indexesUsed.includes(false)) {\n            if (this.config.isLoop) {\n                indexesUsed.fill(false);\n            }\n        }\n        if (!this.config.isRandom) {\n            return indexesUsed.indexOf(false);\n        }\n        else {\n            const unusedIndexes = indexesUsed.map((used, index) => (used ? -1 : index)).filter((index) => index !== -1);\n            return unusedIndexes.length ? unusedIndexes[Math.floor(Math.random() * unusedIndexes.length)] : -1;\n        }\n    }\n    /**\n     * Sets the block's output signals. Would usually be passed from the constructor but can be changed afterwards.\n     * @param numberOutputSignals the number of output flows\n     */\n    setNumberOfOutputSignals(numberOutputSignals = 1) {\n        // check the size of the outFlow Array, see if it is not larger than needed\n        while (this.outputSignals.length > numberOutputSignals) {\n            const flow = this.outputSignals.pop();\n            if (flow) {\n                flow.disconnectFromAll();\n                this._unregisterSignalOutput(flow.name);\n            }\n        }\n        while (this.outputSignals.length < numberOutputSignals) {\n            this.outputSignals.push(this._registerSignalOutput(`out_${this.outputSignals.length}`));\n        }\n    }\n    _execute(context, callingSignal) {\n        // set the state(s) of the block\n        if (!context._hasExecutionVariable(this, \"indexesUsed\")) {\n            context._setExecutionVariable(this, \"indexesUsed\", this.outputSignals.map(() => false));\n        }\n        if (callingSignal === this.reset) {\n            context._deleteExecutionVariable(this, \"indexesUsed\");\n            this.lastIndex.setValue(new FlowGraphInteger(-1), context);\n            return;\n        }\n        const indexesUsed = context._getExecutionVariable(this, \"indexesUsed\", []);\n        const nextIndex = this._getNextIndex(indexesUsed);\n        if (nextIndex > -1) {\n            this.lastIndex.setValue(new FlowGraphInteger(nextIndex), context);\n            indexesUsed[nextIndex] = true;\n            context._setExecutionVariable(this, \"indexesUsed\", indexesUsed);\n            this.outputSignals[nextIndex]._activateSignal(context);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphMultiGateBlock\" /* FlowGraphBlockNames.MultiGate */;\n    }\n    /**\n     * Serializes the block.\n     * @param serializationObject the object to serialize to.\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n        serializationObject.config.outputSignalCount = this.config.outputSignalCount;\n        serializationObject.config.isRandom = this.config.isRandom;\n        serializationObject.config.loop = this.config.isLoop;\n        serializationObject.config.startIndex = this.config.startIndex;\n    }\n}\nRegisterClass(\"FlowGraphMultiGateBlock\" /* FlowGraphBlockNames.MultiGate */, FlowGraphMultiGateBlock);\n//# sourceMappingURL=flowGraphMultiGateBlock.js.map"], "names": ["FlowGraphMultiGateBlock", "FlowGraphExecutionBlock", "config", "RichTypeFlowGraphInteger", "FlowGraphInteger", "indexesUsed", "unusedIndexes", "used", "index", "numberOutputSignals", "flow", "context", "callingSignal", "nextIndex", "serializationObject", "RegisterClass"], "mappings": "uPAOO,MAAMA,UAAgCC,CAAwB,CACjE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAId,KAAK,cAAgB,GACrB,KAAK,MAAQ,KAAK,qBAAqB,OAAO,EAC9C,KAAK,UAAY,KAAK,mBAAmB,YAAaC,EAA0B,IAAIC,EAAiB,EAAE,CAAC,EACxG,KAAK,yBAAyBF,GAAQ,iBAAiB,CAC1D,CACD,cAAcG,EAAa,CAQvB,GALKA,EAAY,SAAS,EAAK,GACvB,KAAK,OAAO,QACZA,EAAY,KAAK,EAAK,EAGzB,KAAK,OAAO,SAGZ,CACD,MAAMC,EAAgBD,EAAY,IAAI,CAACE,EAAMC,IAAWD,EAAO,GAAKC,CAAM,EAAE,OAAQA,GAAUA,IAAU,EAAE,EAC1G,OAAOF,EAAc,OAASA,EAAc,KAAK,MAAM,KAAK,OAAM,EAAKA,EAAc,MAAM,CAAC,EAAI,EACnG,KALG,QAAOD,EAAY,QAAQ,EAAK,CAMvC,CAKD,yBAAyBI,EAAsB,EAAG,CAE9C,KAAO,KAAK,cAAc,OAASA,GAAqB,CACpD,MAAMC,EAAO,KAAK,cAAc,IAAG,EAC/BA,IACAA,EAAK,kBAAiB,EACtB,KAAK,wBAAwBA,EAAK,IAAI,EAE7C,CACD,KAAO,KAAK,cAAc,OAASD,GAC/B,KAAK,cAAc,KAAK,KAAK,sBAAsB,OAAO,KAAK,cAAc,MAAM,EAAE,CAAC,CAE7F,CACD,SAASE,EAASC,EAAe,CAK7B,GAHKD,EAAQ,sBAAsB,KAAM,aAAa,GAClDA,EAAQ,sBAAsB,KAAM,cAAe,KAAK,cAAc,IAAI,IAAM,EAAK,CAAC,EAEtFC,IAAkB,KAAK,MAAO,CAC9BD,EAAQ,yBAAyB,KAAM,aAAa,EACpD,KAAK,UAAU,SAAS,IAAIP,EAAiB,EAAE,EAAGO,CAAO,EACzD,MACH,CACD,MAAMN,EAAcM,EAAQ,sBAAsB,KAAM,cAAe,CAAA,CAAE,EACnEE,EAAY,KAAK,cAAcR,CAAW,EAC5CQ,EAAY,KACZ,KAAK,UAAU,SAAS,IAAIT,EAAiBS,CAAS,EAAGF,CAAO,EAChEN,EAAYQ,CAAS,EAAI,GACzBF,EAAQ,sBAAsB,KAAM,cAAeN,CAAW,EAC9D,KAAK,cAAcQ,CAAS,EAAE,gBAAgBF,CAAO,EAE5D,CAID,cAAe,CACX,MAAO,yBACV,CAKD,UAAUG,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,OAAO,kBAAoB,KAAK,OAAO,kBAC3DA,EAAoB,OAAO,SAAW,KAAK,OAAO,SAClDA,EAAoB,OAAO,KAAO,KAAK,OAAO,OAC9CA,EAAoB,OAAO,WAAa,KAAK,OAAO,UACvD,CACL,CACAC,EAAc,0BAA+Df,CAAuB", "x_google_ignoreList": [0]}