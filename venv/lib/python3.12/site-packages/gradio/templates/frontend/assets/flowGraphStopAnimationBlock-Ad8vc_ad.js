import{R as a,b as e}from"./declarationMapper-r-RREw_K.js";import{h as s,R as r}from"./index-Cb4A4-Xi.js";import{a as p}from"./KHR_interactivity-DVSiPm30.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class l extends p{constructor(i){super(i),this.animationGroup=this.registerDataInput("animationGroup",a),this.stopAtFrame=this.registerDataInput("stopAtFrame",e,-1)}_preparePendingTasks(i){const o=this.animationGroup.getValue(i),t=this.stopAtFrame.getValue(i)??-1,n=i._getGlobalContextVariable("pendingStopAnimations",[]);n.push({uniqueId:o.uniqueId,stopAtFrame:t}),i._setGlobalContextVariable("pendingStopAnimations",n)}_cancelPendingTasks(i){const o=this.animationGroup.getValue(i),t=i._getGlobalContextVariable("pendingStopAnimations",[]);for(let n=0;n<t.length;n++)if(t[n].uniqueId===o.uniqueId){t.splice(n,1),i._setGlobalContextVariable("pendingStopAnimations",t);break}}_execute(i){const o=this.animationGroup.getValue(i),t=this.stopAtFrame.getValue(i)??-1;if(!o)return s.Warn("No animation group provided to stop."),this._reportError(i,"No animation group provided to stop.");if(isNaN(t))return this._reportError(i,"Invalid stop time.");t>0?this._startPendingTasks(i):this._stopAnimation(o,i),this.out._activateSignal(i)}_executeOnTick(i){const o=this.animationGroup.getValue(i),t=i._getGlobalContextVariable("pendingStopAnimations",[]);for(let n=0;n<t.length;n++)if(t[n].uniqueId===o.uniqueId&&o.getCurrentFrame()>=t[n].stopAtFrame){this._stopAnimation(o,i),t.splice(n,1),i._setGlobalContextVariable("pendingStopAnimations",t),this.done._activateSignal(i),i._removePendingBlock(this);break}}getClassName(){return"FlowGraphStopAnimationBlock"}_stopAnimation(i,o){const t=o._getGlobalContextVariable("currentlyRunningAnimationGroups",[]),n=t.indexOf(i.uniqueId);n!==-1&&(i.stop(),t.splice(n,1),o._setGlobalContextVariable("currentlyRunningAnimationGroups",t))}}r("FlowGraphStopAnimationBlock",l);export{l as FlowGraphStopAnimationBlock};
//# sourceMappingURL=flowGraphStopAnimationBlock-Ad8vc_ad.js.map
