const __vite__fileDeps=["./Index-Bq_jvq4F.js","./Block-CJdXVpa7.js","./IconButtonWrapper-BqcF4N5S.css","./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js","./prism-python-D8O99YiR.js","./MarkdownCode-Dw1-H2nG.css","./index-BFBcOI-E.js","./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js","./StreamingBar-DOagx4HU.css","./IconButton-C_HS7fTi.js","./Clear-By3xiIwg.js","./Index-C_pBLFQL.js","./Index-CptIZeFZ.css","./Index-C5NYahSl.css","./Index-DTxhE6Ms.js","./BlockLabel-3KxTaaiM.js","./Empty-ZqppqzTN.js","./Image-Bsh8Umrh.js","./IconButtonWrapper--EIOWuEM.js","./FullscreenButton-C1XLRmAs.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Index-E3yBBMTH.css","./StaticAudio-C_goMxpb.js","./utils-BsGrhMNe.js","./ShareButton-q6iG5u0X.js","./Community-Dw1micSV.js","./Download-DVtk-Jv3.js","./Music-CDm0RGMk.js","./AudioPlayer-UyFUW52_.js","./Trim-JQYgj7Jd.js","./Play-B0Q0U1Qz.js","./Undo-DCjBnnSO.js","./hls-CnVhpNcu.js","./AudioPlayer-BAKhejK8.css","./DownloadLink-QIttOhoR.js","./Example-BQyGztrG.js","./Example-D7K5RtQ2.css","./index-CmY2SGP5.js","./InteractiveAudio-C1oZjHXI.js","./Upload-D2lWV7KS.js","./Upload-L7mprsyN.css","./ModifyUpload-CkvPtjlQ.js","./Edit-BpRIf5rU.js","./SelectSource-DC1-vFVA.js","./StreamingBar-BU9S4hA7.js","./InteractiveAudio-B76TQFG-.css","./UploadText-DsNsRaib.js","./Index-P7JUfB5x.js","./Index-BEfWDEie.js","./__vite-browser-external-D7Ct-6yo.js","./Index-BGqHGXa7.js","./Button-Dy8yxofg.js","./Image-CnqB5dbD.js","./Image-B8dFOee4.css","./Button-DTh9AgeE.css","./ImagePreview-C_qhEOxI.css","./Index-DAN2YSmp.js","./Check-CEkiXcyC.js","./Copy-CxQ9EyK2.js","./File-BQ_9P3Ye.js","./MarkdownCode-BRQ4PUpt.js","./index-CRyThWY1.js","./index-CnqicUFC.js","./Trash-RbZEwH-j.js","./Index-Ru4sKdXi.css","./Example-CZ-iEz1g.js","./Index-3kkZfHe6.js","./Info-BpelqhYn.js","./Checkbox-1fK2t2Mh.js","./Checkbox-WIAKB-_s.css","./Example-DccrJI--.js","./Index-CeRGpzhg.js","./BlockTitle-D2VH9_Na.js","./Index-DMKGW8pW.css","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./Index-BBBT_7z0.js","./Code-DGNrTu_I.js","./Index-DloLYeAi.css","./Example-BaLyJYAe.js","./Example-Bw8Q_3wB.css","./Index-DZafFVon.js","./tinycolor-DhRrpXkc.js","./Index-DwWu86Nh.css","./index-C7pqxX8U.js","./Embed-yYAfXBkj.js","./Example-CqPGqNav.js","./Example-1kVNej19.css","./Index-B-oL2Ctq.js","./index-tFQomdd2.js","./dsv-DB8NKgIY.js","./Index-BJtN0ikg.js","./ImagePreview-2kHKEtmm.js","./utils-Gtzs_Zla.js","./ImageUploader-BPlr11mZ.js","./DropdownArrow-DYWFcSFn.js","./Square-oAGqOwsh.js","./ImageUploader-DMdYP1a9.css","./Example-CC8yxxGn.js","./Example-DikqVAPo.css","./Index-BCdKUwkT.css","./Index-BGzJCeJV.js","./Example-Cx2SdskM.js","./Example-ClKJOMGh.css","./Index-D3f6Hf9S.css","./Textbox-jWD3sCxr.css","./Index-gTS0BfGg.js","./Example-BBLMS951.js","./Index-GDHg_u1o.css","./Index-Cggxnaw9.js","./Index-tcNSQSor.css","./Example-BgQNfMWT.js","./Index-5MfacqWU.js","./Dropdown-Ib2nGkdD.js","./Dropdown-CWxB-qJp.css","./Example-DrmWnoSo.js","./Example-DpWs9cEC.css","./Index-CAlEGANp.js","./FileUpload-DuAyaBMf.js","./FileUpload-CQVu-hjH.css","./Example-CIFMxn5c.js","./Example-DfhEULNF.css","./Index-uwGTfAfz.js","./Index-BKaa_GXG.css","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Gallery-CQraGjdH.js","./Video-CW9C2EgQ.js","./Video-DJw86Ppo.css","./Gallery-BqrYX9d2.css","./Index-DGJYDoi5.js","./Index-WEzAIkMk.js","./Index-Cgj6KPvj.css","./Index-BUdzJf_d.js","./color-BEUIZb6Y.js","./Index-Dwy3Ni24.css","./Index-Cqy2Fmu2.js","./Index-Csm0OGa9.css","./Example-C2a4WxRl.js","./Example-CSw4pLi5.css","./Example-CF5q22w7.js","./Example-6rv12T44.css","./Index-CQdjniUO.js","./Index-ClP1ItfE.css","./Example-j_9MW44b.js","./Example-fMB4cHw6.css","./Index-jm4-egAD.js","./select-BigU4G0v.js","./dispatch-kxCwF96_.js","./Index-CTOeqf6n.css","./Example-DY_fp6t_.js","./JSON-n0se6vu7.js","./JSON-DItMwpQq.css","./Example-CG7uBGLE.css","./Index-BiGxUF7w.js","./Index-CmeEScIW.js","./LineChart-CKh1Fdep.js","./Index-D3BKJl5I.css","./Example-s6q-Imk8.js","./Index-AokCAbrK.js","./Index-BTXaMQgd.css","./Example-uQ8MuYg6.js","./Index-B4M_FgsQ.js","./Index-Be3F7oKw.css","./Example-CXcdMjGN.js","./Example-CCTTJ5R1.css","./Index-BrxIuZVJ.js","./Send-DyoOovnk.js","./Video-fsmLZWjA.js","./Index-1zeyvqK2.css","./Index-Bbrpr1kE.js","./Index-WdTVQ0oj.css","./Example-CqL1e7EB.js","./Index-CGhgRfEX.js","./Index-Dclo02rM.css","./Example-C9__vDgN.js","./Index-xFwj5roU.js","./Index-C02erx_8.css","./Plot-DHdxFihW.js","./Index-C7BEHOrj.js","./Example-BoMLuz1A.js","./Index-CjxJjfsR.js","./Index--UpFQsHg.css","./Index-CxbCa_Hu.js","./Index-CfowPFmo.css","./Index-DWQ43kEh.js","./Index-CNbhkFly.css","./Index-BlWK1-fD.js","./Index-B0hFno2n.css","./Example-BrizabXh.js","./Index-DqtpUcMZ.js","./Index-Bd9hBDCH.css","./index-D15TB0sU.js","./Toast-DTlCeY7K.js","./Index-Dcd9NrWD.js","./Tabs-D8sppewm.js","./Tabs-C0qLuAtA.css","./Index-Gmwqb-vD.css","./Index-CNb_gXq7.js","./Index-DAKBwkte.js","./Textbox-DI9Q41tU.js","./Index-DaZWCdNS.js","./Index-DYDmCduo.css","./VideoPreview-DYkibY1b.js","./VideoPreview-wQufNXbv.css","./Example-B_ghNU9h.js","./Example-B5CSTz0f.css","./index-DLMU5ww8.js","./index-CFBZQE_H.css","./Example-DxdiEFS_.js","./Index-xQEydEg1.js","./Index-CgDrEMlk.css","./Index-BzI0zy4H.js","./Index-BJ_RfjVB.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import*as Je from"./svelte/svelte.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const n of i.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&r(n)}).observe(document,{childList:!0,subtree:!0});function t(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(a){if(a.ep)return;a.ep=!0;const i=t(a);fetch(a.href,i)}})();const pg="modulepreload",mg=function(e,o){return new URL(e,o).href},Oo={},p=function(o,t,r){let a=Promise.resolve();if(t&&t.length>0){const i=document.getElementsByTagName("link"),n=document.querySelector("meta[property=csp-nonce]"),s=n?.nonce||n?.getAttribute("nonce");a=Promise.all(t.map(d=>{if(d=mg(d,r),d in Oo)return;Oo[d]=!0;const l=d.endsWith(".css"),c=l?'[rel="stylesheet"]':"";if(!!r)for(let g=i.length-1;g>=0;g--){const h=i[g];if(h.href===d&&(!l||h.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${d}"]${c}`))return;const _=document.createElement("link");if(_.rel=l?"stylesheet":pg,l||(_.as="script",_.crossOrigin=""),_.href=d,s&&_.setAttribute("nonce",s),document.head.appendChild(_),l)return new Promise((g,h)=>{_.addEventListener("load",g),_.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${d}`)))})}))}return a.then(()=>o()).catch(i=>{const n=new Event("vite:preloadError",{cancelable:!0});if(n.payload=i,window.dispatchEvent(n),!n.defaultPrevented)throw i})};var Ye=new Intl.Collator(0,{numeric:1}).compare;function lt(e,o,t){return e=e.split("."),o=o.split("."),Ye(e[0],o[0])||Ye(e[1],o[1])||(o[2]=o.slice(2).join("."),t=/[.-]/.test(e[2]=e.slice(2).join(".")),t==/[.-]/.test(o[2])?Ye(e[2],o[2]):t?-1:1)}const hg="host",ct="queue/data",gg="queue/join",jo="upload",fg="login",Co="config",bg="info",vg="runtime",wg="sleeptime",yg="heartbeat",kg="component_server",Sg="reset",xg="cancel",Eg="https://gradio-space-api-fetcher-v2.hf.space/api",dt="This application is currently busy. Please try again. ",pe="Connection errored out. ",le="Could not resolve app config. ",$g="Could not get space status. ",Pg="Could not get API info. ",wo="Space metadata could not be loaded. ",Ag="Invalid URL. A full URL path is required.",Tg="Not authorized to access this space. ",ut="Invalid credentials. Could not login. ",Og="Login credentials are required to access this space.",jg="File system access is only available in Node.js environments",_t="Root URL not found in client config",Cg="Error uploading file";function pt(e,o,t){return o.startsWith("http://")||o.startsWith("https://")?t?e:o:e+o}async function Lo(e,o,t){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${o}`,...t?{Cookie:t}:{}}})).json()).token||!1}catch{return!1}}function Lg(e){let o={};return e.forEach(({api_name:t,id:r})=>{t&&(o[t]=r)}),o}async function Dg(e){const o=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(o["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const t=window.gradio_config.root,r=window.gradio_config;let a=pt(e,r.root,!1);return r.root=a,{...r,path:t}}else if(e){let t=gt(e,this.deep_link?Co+"?deep_link="+this.deep_link:Co);const r=await this.fetch(t,{headers:o,credentials:"include"});if(r?.status===401&&!this.options.auth)throw new Error(Og);if(r?.status===401&&this.options.auth)throw new Error(ut);if(r?.status===200){let a=await r.json();return a.path=a.path??"",a.root=e,a.dependencies?.forEach((i,n)=>{i.id===void 0&&(i.id=n)}),a}else if(r?.status===401)throw new Error(Tg);throw new Error(le)}throw new Error(le)}async function Ig(){const{http_protocol:e,host:o}=await qe(this.app_reference,this.options.hf_token);try{if(this.options.auth){const t=await mt(e,o,this.options.auth,this.fetch,this.options.hf_token);t&&this.set_cookies(t)}}catch(t){throw Error(t.message)}}async function mt(e,o,t,r,a){const i=new FormData;i.append("username",t?.[0]),i.append("password",t?.[1]);let n={};a&&(n.Authorization=`Bearer ${a}`);const s=await r(`${e}//${o}/${fg}`,{headers:n,method:"POST",body:i,credentials:"include"});if(s.status===200)return s.headers.get("set-cookie");throw s.status===401?new Error(ut):new Error(wo)}function Qe(e){if(e.startsWith("http")){const{protocol:o,host:t,pathname:r}=new URL(e);return{ws_protocol:o==="https:"?"wss":"ws",http_protocol:o,host:t+(r!=="/"?r:"")}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:new URL(e).host}}const ht=e=>{let o=[];return e.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(r=>{const[a,i]=r.split(";")[0].split("=");a&&i&&o.push(`${a.trim()}=${i.trim()}`)}),o},yo=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,zg=/.*hf\.space\/{0,1}.*$/;async function qe(e,o){const t={};o&&(t.Authorization=`Bearer ${o}`);const r=e.trim().replace(/\/$/,"");if(yo.test(r))try{const i=(await(await fetch(`https://huggingface.co/api/spaces/${r}/${hg}`,{headers:t})).json()).host;return{space_id:e,...Qe(i)}}catch{throw new Error(wo)}if(zg.test(r)){const{ws_protocol:a,http_protocol:i,host:n}=Qe(r);return{space_id:n.split("/")[0].replace(".hf.space",""),ws_protocol:a,http_protocol:i,host:n}}return{space_id:!1,...Qe(r)}}const gt=(...e)=>{try{return e.reduce((o,t)=>(o=o.replace(/\/+$/,""),t=t.replace(/^\/+/,""),new URL(t,o+"/").toString()))}catch{throw new Error(Ag)}};function Rg(e,o,t){const r={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(e).forEach(a=>{(a==="named_endpoints"||a==="unnamed_endpoints")&&(r[a]={},Object.entries(e[a]).forEach(([i,{parameters:n,returns:s}])=>{const d=o.dependencies.find(u=>u.api_name===i||u.api_name===i.replace("/",""))?.id||t[i.replace("/","")]||-1,l=d!==-1?o.dependencies.find(u=>u.id==d)?.types:{generator:!1,cancel:!1};if(d!==-1&&o.dependencies.find(u=>u.id==d)?.inputs?.length!==n.length){const u=o.dependencies.find(_=>_.id==d).inputs.map(_=>o.components.find(g=>g.id===_)?.type);try{u.forEach((_,g)=>{if(_==="state"){const h={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};n.splice(g,0,h)}})}catch(_){console.error(_)}}const c=(u,_,g,h)=>({...u,description:Hg(u?.type,g),type:Ng(u?.type,_,g,h)||""});r[a][i]={parameters:n.map(u=>c(u,u?.component,u?.serializer,"parameter")),returns:s.map(u=>c(u,u?.component,u?.serializer,"return")),type:l}}))}),r}function Ng(e,o,t,r){if(o==="Api")return e.type;switch(e?.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(t==="JSONSerializable"||t==="StringSerializable")return"any";if(t==="ListStringSerializable")return"string[]";if(o==="Image")return r==="parameter"?"Blob | File | Buffer":"string";if(t==="FileSerializable")return e?.type==="array"?r==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":r==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(t==="GallerySerializable")return r==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function Hg(e,o){return o==="GallerySerializable"?"array of [file, label] tuples":o==="ListStringSerializable"?"array of strings":o==="FileSerializable"?"array of files or single file":e?.description}function eo(e,o){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:dt,stage:"error",code:e.code,success:e.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:e.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:o||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_streaming":return{type:"streaming",status:{queue:!0,message:e.output.error,stage:"streaming",time_limit:e.time_limit,code:e.code,progress_data:e.progress_data,eta:e.eta},data:e.output};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,title:e.output.title,message:e.output.error,visible:e.output.visible,duration:e.output.duration,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success,eta:e.eta},original_msg:"process_starts"}}return{type:"none",status:{stage:"error",queue:!0}}}const Bg=(e=[],o)=>{const t=o?o.parameters:[];if(Array.isArray(e))return e.length>t.length&&console.warn("Too many arguments provided for the endpoint."),e;const r=[],a=Object.keys(e);return t.forEach((i,n)=>{if(e.hasOwnProperty(i.parameter_name))r[n]=e[i.parameter_name];else if(i.parameter_has_default)r[n]=i.parameter_default;else throw new Error(`No value provided for required parameter: ${i.parameter_name}`)}),a.forEach(i=>{if(!t.some(n=>n.parameter_name===i))throw new Error(`Parameter \`${i}\` is not a valid keyword argument. Please refer to the API for usage.`)}),r.forEach((i,n)=>{if(i===void 0&&!t[n].parameter_has_default)throw new Error(`No value provided for required parameter: ${t[n].parameter_name}`)}),r};async function Mg(){if(this.api_info)return this.api_info;const{hf_token:e}=this.options,{config:o}=this,t={"Content-Type":"application/json"};if(e&&(t.Authorization=`Bearer ${e}`),!!o)try{let r,a;if(typeof window<"u"&&window.gradio_api_info)a=window.gradio_api_info;else{if(lt(o?.version||"2.0.0","3.30")<0)r=await this.fetch(Eg,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(o)}),headers:t,credentials:"include"});else{const i=gt(o.root,this.api_prefix,bg);r=await this.fetch(i,{headers:t,credentials:"include"})}if(!r.ok)throw new Error(pe);a=await r.json()}return"api"in a&&(a=a.api),a.named_endpoints["/predict"]&&!a.unnamed_endpoints[0]&&(a.unnamed_endpoints[0]=a.named_endpoints["/predict"]),Rg(a,o,this.api_map)}catch(r){throw new Error("Could not get API info. "+r.message)}}async function Vg(e,o,t){const r={};this?.options?.hf_token&&(r.Authorization=`Bearer ${this.options.hf_token}`);const a=1e3,i=[];let n;for(let s=0;s<o.length;s+=a){const d=o.slice(s,s+a),l=new FormData;d.forEach(u=>{l.append("files",u)});try{const u=t?`${e}${this.api_prefix}/${jo}?upload_id=${t}`:`${e}${this.api_prefix}/${jo}`;n=await this.fetch(u,{method:"POST",body:l,headers:r,credentials:"include"})}catch(u){throw new Error(pe+u.message)}if(!n.ok){const u=await n.text();return{error:`HTTP ${n.status}: ${u}`}}const c=await n.json();c&&i.push(...c)}return{files:i}}async function qg(e,o,t,r){let a=(Array.isArray(e)?e:[e]).map(n=>n.blob);const i=a.filter(n=>n.size>(r??1/0));if(i.length)throw new Error(`File size exceeds the maximum allowed size of ${r} bytes: ${i.map(n=>n.name).join(", ")}`);return await Promise.all(await this.upload_files(o,a,t).then(async n=>{if(n.error)throw new Error(n.error);return n.files?n.files.map((s,d)=>new Ge({...e[d],path:s,url:`${o}${this.api_prefix}/file=${s}`})):[]}))}async function zv(e,o){return e.map(t=>new Ge({path:t.name,orig_name:t.name,blob:t,size:t.size,mime_type:t.type,is_stream:o}))}class Ge{path;url;orig_name;size;blob;is_stream;mime_type;alt_text;b64;meta={_type:"gradio.FileData"};constructor({path:o,url:t,orig_name:r,size:a,blob:i,is_stream:n,mime_type:s,alt_text:d,b64:l}){this.path=o,this.url=t,this.orig_name=r,this.size=a,this.blob=t?void 0:i,this.is_stream=n,this.mime_type=s,this.alt_text=d,this.b64=l}}class Gg{type;command;meta;fileData;constructor(o,t){this.type="command",this.command=o,this.meta=t}}typeof process<"u"&&process.versions&&process.versions.node;function Do(e,o,t){for(;t.length>1;){const a=t.shift();if(typeof a=="string"||typeof a=="number")e=e[a];else throw new Error("Invalid key type")}const r=t.shift();if(typeof r=="string"||typeof r=="number")e[r]=o;else throw new Error("Invalid key type")}async function lo(e,o=void 0,t=[],r=!1,a=void 0){if(Array.isArray(e)){let i=[];return await Promise.all(e.map(async(n,s)=>{let d=t.slice();d.push(String(s));const l=await lo(e[s],r?a?.parameters[s]?.component||void 0:o,d,!1,a);i=i.concat(l)})),i}else{if(globalThis.Buffer&&e instanceof globalThis.Buffer||e instanceof Blob)return[{path:t,blob:new Blob([e]),type:o}];if(typeof e=="object"&&e!==null){let i=[];for(const n of Object.keys(e)){const s=[...t,n],d=e[n];i=i.concat(await lo(d,void 0,s,!1,a))}return i}}return[]}function Ug(e,o){let t=o?.dependencies?.find(r=>r.id==e)?.queue;return t!=null?!t:!o.enable_queue}function Fg(e,o){return new Promise((t,r)=>{const a=new MessageChannel;a.port1.onmessage=({data:i})=>{a.port1.close(),t(i)},window.parent.postMessage(e,o,[a.port2])})}function Pe(e,o,t,r,a=!1){if(r==="input"&&!a)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(r==="output"&&a)return e;let i=[],n=0;const s=r==="input"?o.inputs:o.outputs;for(let d=0;d<s.length;d++){const l=s[d];if(t.find(u=>u.id===l)?.type==="state"){if(a)if(e.length===s.length){const u=e[n];i.push(u),n++}else i.push(null);else{n++;continue}continue}else{const u=e[n];i.push(u),n++}}return i}async function Wg(e,o,t){const r=this;await Kg(r,o);const a=await lo(o,void 0,[],!0,t);return(await Promise.all(a.map(async({path:n,blob:s,type:d})=>{if(!s)return{path:n,type:d};const l=await r.upload_files(e,[s]),c=l.files&&l.files[0];return{path:n,file_url:c,type:d,name:typeof File<"u"&&s instanceof File?s?.name:void 0}}))).forEach(({path:n,file_url:s,type:d,name:l})=>{if(d==="Gallery")Do(o,s,n);else if(s){const c=new Ge({path:s,orig_name:l});Do(o,c,n)}}),o}async function Kg(e,o){if(!(e.config?.root||e.config?.root_url))throw new Error(_t);await ft(e,o)}async function ft(e,o,t=[]){for(const r in o)o[r]instanceof Gg?await Xg(e,o,r):typeof o[r]=="object"&&o[r]!==null&&await ft(e,o[r],[...t,r])}async function Xg(e,o,t){let r=o[t];const a=e.config?.root||e.config?.root_url;if(!a)throw new Error(_t);try{let i,n;if(typeof process<"u"&&process.versions&&process.versions.node){const c=await p(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(_=>_._),[],import.meta.url);n=(await p(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(_=>_._),[],import.meta.url)).resolve(process.cwd(),r.meta.path),i=await c.readFile(n)}else throw new Error(jg);const s=new Blob([i],{type:"application/octet-stream"}),d=await e.upload_files(a,[s]),l=d.files&&d.files[0];if(l){const c=new Ge({path:l,orig_name:r.meta.name||""});o[t]=c}}catch(i){console.error(Cg,i)}}async function Zg(e,o,t){const r={"Content-Type":"application/json"};this.options.hf_token&&(r.Authorization=`Bearer ${this.options.hf_token}`);try{var a=await this.fetch(e,{method:"POST",body:JSON.stringify(o),headers:{...r,...t},credentials:"include"})}catch{return[{error:pe},500]}let i,n;try{i=await a.json(),n=a.status}catch(s){i={error:`Could not parse server response: ${s}`},n=500}return[i,n]}async function Jg(e,o={}){let t=!1,r=!1;if(!this.config)throw new Error("Could not resolve app config");if(typeof e=="number")this.config.dependencies.find(a=>a.id==e);else{const a=e.replace(/^\//,"");this.config.dependencies.find(i=>i.id==this.api_map[a])}return new Promise(async(a,i)=>{const n=this.submit(e,o,null,null,!0);let s;for await(const d of n)d.type==="data"&&(r&&a(s),t=!0,s=d),d.type==="status"&&(d.stage==="error"&&i(d),d.stage==="complete"&&(r=!0,t&&a(s)))})}async function Oe(e,o,t){let r=o==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,a,i;try{if(a=await fetch(r),i=a.status,i!==200)throw new Error;a=await a.json()}catch{t({status:"error",load_status:"error",message:$g,detail:"NOT_FOUND"});return}if(!a||i!==200)return;const{runtime:{stage:n},id:s}=a;switch(n){case"STOPPED":case"SLEEPING":t({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:n}),setTimeout(()=>{Oe(e,o,t)},1e3);break;case"PAUSED":t({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:n,discussions_enabled:await Io(s)});break;case"RUNNING":case"RUNNING_BUILDING":t({status:"running",load_status:"complete",message:"Space is running.",detail:n});break;case"BUILDING":t({status:"building",load_status:"pending",message:"Space is building...",detail:n}),setTimeout(()=>{Oe(e,o,t)},1e3);break;case"APP_STARTING":t({status:"starting",load_status:"pending",message:"Space is starting...",detail:n}),setTimeout(()=>{Oe(e,o,t)},1e3);break;default:t({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:n,discussions_enabled:await Io(s)});break}}const bt=async(e,o)=>{let t=0;const r=12,a=5e3;return new Promise(i=>{Oe(e,yo.test(e)?"space_name":"subdomain",n=>{o(n),n.status==="running"||n.status==="error"||n.status==="paused"||n.status==="space_error"?i():(n.status==="sleeping"||n.status==="building")&&(t<r?(t++,setTimeout(()=>{bt(e,o).then(i)},a)):i())})})},Yg=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function Io(e){try{const o=await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"}),t=o.headers.get("x-error-message");return!(!o.ok||t&&Yg.test(t))}catch{return!1}}async function Qg(e,o){const t={};o&&(t.Authorization=`Bearer ${o}`);try{const r=await fetch(`https://huggingface.co/api/spaces/${e}/${vg}`,{headers:t});if(r.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:a}=await r.json();return a.current}catch(r){throw new Error(r.message)}}async function ef(e,o,t){const r={};t&&(r.Authorization=`Bearer ${t}`);const a={seconds:o};try{const i=await fetch(`https://huggingface.co/api/spaces/${e}/${wg}`,{method:"POST",headers:{"Content-Type":"application/json",...r},body:JSON.stringify(a)});if(i.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await i.json()}catch(i){throw new Error(i.message)}}const zo=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function of(e,o){const{hf_token:t,private:r,hardware:a,timeout:i,auth:n}=o;if(a&&!zo.includes(a))throw new Error(`Invalid hardware type provided. Valid types are: ${zo.map($=>`"${$}"`).join(",")}.`);const{http_protocol:s,host:d}=await qe(e,t);let l=null;if(n){const $=await mt(s,d,n,fetch);$&&(l=ht($))}const c={Authorization:`Bearer ${t}`,"Content-Type":"application/json",...l?{Cookie:l.join("; ")}:{}},u=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:c})).json()).name,_=e.split("/")[1],g={repository:`${u}/${_}`};r&&(g.private=!0);let h;try{a||(h=await Qg(e,t))}catch($){throw Error(wo+$.message)}const x=a||h||"cpu-basic";g.hardware=x;try{const $=await fetch(`https://huggingface.co/api/spaces/${e}/duplicate`,{method:"POST",headers:c,body:JSON.stringify(g)});if($.status===409)try{return await He.connect(`${u}/${_}`,o)}catch(D){throw console.error("Failed to connect Client instance:",D),D}else if($.status!==200)throw new Error($.statusText);const M=await $.json();return await ef(`${u}/${_}`,i||300,t),await He.connect(tf(M.url),o)}catch($){throw new Error($)}}function tf(e){const o=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,t=e.match(o);if(t)return t[1]}class rf extends TransformStream{#e="";constructor(o={allowCR:!1}){super({transform:(t,r)=>{for(t=this.#e+t;;){const a=t.indexOf(`
`),i=o.allowCR?t.indexOf("\r"):-1;if(i!==-1&&i!==t.length-1&&(a===-1||a-1>i)){r.enqueue(t.slice(0,i)),t=t.slice(i+1);continue}if(a===-1)break;const n=t[a-1]==="\r"?a-1:a;r.enqueue(t.slice(0,n)),t=t.slice(a+1)}this.#e=t},flush:t=>{if(this.#e==="")return;const r=o.allowCR&&this.#e.endsWith("\r")?this.#e.slice(0,-1):this.#e;t.enqueue(r)}})}}function af(e){let o=new TextDecoderStream,t=new rf({allowCR:!0});return e.pipeThrough(o).pipeThrough(t)}function nf(e){let t=/[:]\s*/.exec(e),r=t&&t.index;if(r)return[e.substring(0,r),e.substring(r+t[0].length)]}function Ro(e,o,t){e.get(o)||e.set(o,t)}async function*sf(e,o){if(!e.body)return;let t=af(e.body),r,a=t.getReader(),i;for(;;){if(o&&o.aborted)return a.cancel();if(r=await a.read(),r.done)return;if(!r.value){i&&(yield i),i=void 0;continue}let[n,s]=nf(r.value)||[];n&&(n==="data"?(i||={},i[n]=i[n]?i[n]+`
`+s:s):n==="event"?(i||={},i[n]=s):n==="id"?(i||={},i[n]=+s||s):n==="retry"&&(i||={},i[n]=+s||void 0))}}async function lf(e,o){let t=new Request(e,o);Ro(t.headers,"Accept","text/event-stream"),Ro(t.headers,"Content-Type","application/json");let r=await fetch(t);if(!r.ok)throw r;return sf(r,t.signal)}async function cf(){let{event_callbacks:e,unclosed_events:o,pending_stream_messages:t,stream_status:r,config:a,jwt:i}=this;const n=this;if(!a)throw new Error("Could not resolve app config");r.open=!0;let s=null,d=new URLSearchParams({session_hash:this.session_hash}).toString(),l=new URL(`${a.root}${this.api_prefix}/${ct}?${d}`);if(i&&l.searchParams.set("__sign",i),s=this.stream(l),!s){console.warn("Cannot connect to SSE endpoint: "+l.toString());return}s.onmessage=async function(c){let u=JSON.parse(c.data);if(u.msg==="close_stream"){Ne(r,n.abort_controller);return}const _=u.event_id;if(!_)await Promise.all(Object.keys(e).map(g=>e[g](u)));else if(e[_]&&a){u.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(a.protocol)&&o.delete(_);let g=e[_];typeof window<"u"&&typeof document<"u"?setTimeout(g,0,u):g(u)}else t[_]||(t[_]=[]),t[_].push(u)},s.onerror=async function(){await Promise.all(Object.keys(e).map(c=>e[c]({msg:"unexpected_error",message:pe})))}}function Ne(e,o){e&&(e.open=!1,o?.abort())}function df(e,o,t){!e[o]?(e[o]=[],t.data.forEach((a,i)=>{e[o][i]=a})):t.data.forEach((a,i)=>{let n=uf(e[o][i],a);e[o][i]=n,t.data[i]=n})}function uf(e,o){return o.forEach(([t,r,a])=>{e=_f(e,r,t,a)}),e}function _f(e,o,t,r){if(o.length===0){if(t==="replace")return r;if(t==="append")return e+r;throw new Error(`Unsupported action: ${t}`)}let a=e;for(let n=0;n<o.length-1;n++)a=a[o[n]];const i=o[o.length-1];switch(t){case"replace":a[i]=r;break;case"append":a[i]+=r;break;case"add":Array.isArray(a)?a.splice(Number(i),0,r):a[i]=r;break;case"delete":Array.isArray(a)?a.splice(Number(i),1):delete a[i];break;default:throw new Error(`Unknown action: ${t}`)}return e}function pf(e,o={}){const t={close:()=>{console.warn("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:e.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return lf(e,o).then(async r=>{t.readyState=t.OPEN;try{for await(const a of r)t.onmessage&&t.onmessage(a);t.readyState=t.CLOSED}catch(a){t.onerror&&t.onerror(a),t.readyState=t.CLOSED}}).catch(r=>{console.error(r),t.onerror&&t.onerror(r),t.readyState=t.CLOSED}),t}function mf(e,o={},t,r,a){try{let i=function(T){(a||Ee[T.type])&&l(T)},n=function(){for(Ke=!0;$e.length>0;)$e.shift()({value:void 0,done:!0})},s=function(T){Ke||($e.length>0?$e.shift()(T):Xe.push(T))},d=function(T){s(hf(T)),n()},l=function(T){s({value:T,done:!1})},c=function(){return Xe.length>0?Promise.resolve(Xe.shift()):Ke?Promise.resolve({value:void 0,done:!0}):new Promise(T=>$e.push(T))};const{hf_token:u}=this.options,{fetch:_,app_reference:g,config:h,session_hash:x,api_info:$,api_map:M,stream_status:D,pending_stream_messages:I,pending_diff_streams:H,event_callbacks:V,unclosed_events:L,post_data:z,options:q,api_prefix:F}=this,ge=this;if(!$)throw new Error("No API found");if(!h)throw new Error("Could not resolve app config");let{fn_index:m,endpoint_info:b,dependency:y}=gf($,e,M,h),A=Bg(o,b),f,j,E=h.protocol??"ws",N="",Q=()=>N;const v=typeof e=="number"?"/predict":e;let w,O=null,B=!1,ne={},Z=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const Ee=q?.events?.reduce((T,re)=>(T[re]=!0,T),{})||{};async function dg(){const T={stage:"complete",queue:!1,time:new Date};B=T,i({...T,type:"status",endpoint:v,fn_index:m});let re={},_e={};E==="ws"?(f&&f.readyState===0?f.addEventListener("open",()=>{f.close()}):f.close(),re={fn_index:m,session_hash:x}):(Ne(D,ge.abort_controller),n(),re={event_id:O},_e={event_id:O,session_hash:x,fn_index:m});try{if(!h)throw new Error("Could not resolve app config");"event_id"in _e&&await _(`${h.root}${F}/${xg}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(_e)}),await _(`${h.root}${F}/${Sg}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(re)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const ug=async T=>{await this._resolve_hearbeat(T)};async function Po(T){if(!h)return;let re=T.render_id;h.components=[...h.components.filter(W=>W.props.rendered_in!==re),...T.components],h.dependencies=[...h.dependencies.filter(W=>W.rendered_in!==re),...T.dependencies];const _e=h.components.some(W=>W.type==="state"),R=h.dependencies.some(W=>W.targets.some(ee=>ee[1]==="unload"));h.connect_heartbeat=_e||R,await ug(h),i({type:"render",data:T,endpoint:v,fn_index:m})}this.handle_blob(h.root,A,b).then(async T=>{if(w={data:Pe(T,y,h.components,"input",!0)||[],event_data:t,fn_index:m,trigger_id:r},Ug(m,h))i({type:"status",endpoint:v,stage:"pending",queue:!1,fn_index:m,time:new Date}),z(`${h.root}${F}/run${v.startsWith("/")?v:`/${v}`}${Z?"?"+Z:""}`,{...w,session_hash:x}).then(([R,W])=>{const ee=R.data;W==200?(i({type:"data",endpoint:v,fn_index:m,data:Pe(ee,y,h.components,"output",q.with_null_state),time:new Date,event_data:t,trigger_id:r}),R.render_config&&Po(R.render_config),i({type:"status",endpoint:v,fn_index:m,stage:"complete",eta:R.average_duration,queue:!1,time:new Date})):i({type:"status",stage:"error",endpoint:v,fn_index:m,message:R.error,queue:!1,time:new Date})}).catch(R=>{i({type:"status",stage:"error",message:R.message,endpoint:v,fn_index:m,queue:!1,time:new Date})});else if(E=="ws"){const{ws_protocol:R,host:W}=await qe(g,u);i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:m,time:new Date});let ee=new URL(`${R}://${pt(W,h.path,!0)}/queue/join${Z?"?"+Z:""}`);this.jwt&&ee.searchParams.set("__sign",this.jwt),f=new WebSocket(ee),f.onclose=J=>{J.wasClean||i({type:"status",stage:"error",broken:!0,message:pe,queue:!0,endpoint:v,fn_index:m,time:new Date})},f.onmessage=function(J){const oe=JSON.parse(J.data),{type:G,status:K,data:X}=eo(oe,ne[m]);if(G==="update"&&K&&!B)i({type:"status",endpoint:v,fn_index:m,time:new Date,...K}),K.stage==="error"&&f.close();else if(G==="hash"){f.send(JSON.stringify({fn_index:m,session_hash:x}));return}else G==="data"?f.send(JSON.stringify({...w,session_hash:x})):G==="complete"?B=K:G==="log"?i({type:"log",title:X.title,log:X.log,level:X.level,endpoint:v,duration:X.duration,visible:X.visible,fn_index:m}):G==="generating"&&i({type:"status",time:new Date,...K,stage:K?.stage,queue:!0,endpoint:v,fn_index:m});X&&(i({type:"data",time:new Date,data:Pe(X.data,y,h.components,"output",q.with_null_state),endpoint:v,fn_index:m,event_data:t,trigger_id:r}),B&&(i({type:"status",time:new Date,...B,stage:K?.stage,queue:!0,endpoint:v,fn_index:m}),f.close()))},lt(h.version||"2.0.0","3.6")<0&&addEventListener("open",()=>f.send(JSON.stringify({hash:x})))}else if(E=="sse"){i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:m,time:new Date});var _e=new URLSearchParams({fn_index:m.toString(),session_hash:x}).toString();let R=new URL(`${h.root}${F}/${ct}?${Z?Z+"&":""}${_e}`);if(this.jwt&&R.searchParams.set("__sign",this.jwt),j=this.stream(R),!j)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+R.toString()));j.onmessage=async function(W){const ee=JSON.parse(W.data),{type:J,status:oe,data:G}=eo(ee,ne[m]);if(J==="update"&&oe&&!B)i({type:"status",endpoint:v,fn_index:m,time:new Date,...oe}),oe.stage==="error"&&(j?.close(),n());else if(J==="data"){let[K,X]=await z(`${h.root}${F}/queue/data`,{...w,session_hash:x,event_id:O});X!==200&&(i({type:"status",stage:"error",message:pe,queue:!0,endpoint:v,fn_index:m,time:new Date}),j?.close(),n())}else J==="complete"?B=oe:J==="log"?i({type:"log",title:G.title,log:G.log,level:G.level,endpoint:v,duration:G.duration,visible:G.visible,fn_index:m}):(J==="generating"||J==="streaming")&&i({type:"status",time:new Date,...oe,stage:oe?.stage,queue:!0,endpoint:v,fn_index:m});G&&(i({type:"data",time:new Date,data:Pe(G.data,y,h.components,"output",q.with_null_state),endpoint:v,fn_index:m,event_data:t,trigger_id:r}),B&&(i({type:"status",time:new Date,...B,stage:oe?.stage,queue:!0,endpoint:v,fn_index:m}),j?.close(),n()))}}else if(E=="sse_v1"||E=="sse_v2"||E=="sse_v2.1"||E=="sse_v3"){i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:m,time:new Date});let R="";typeof window<"u"&&typeof document<"u"&&(R=window?.location?.hostname);const ee=R.includes(".dev.")?`https://moon-${R.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";(typeof window<"u"&&typeof document<"u"&&window.parent!=window&&window.supports_zerogpu_headers?Fg("zerogpu-headers",ee):Promise.resolve(null)).then(K=>z(`${h.root}${F}/${gg}?${Z}`,{...w,session_hash:x},K)).then(async([K,X])=>{if(X===503)i({type:"status",stage:"error",message:dt,queue:!0,endpoint:v,fn_index:m,time:new Date});else if(X!==200)i({type:"status",stage:"error",message:pe,queue:!0,endpoint:v,fn_index:m,time:new Date});else{O=K.event_id,N=O;let To=async function(Ze){try{const{type:ae,status:te,data:Y,original_msg:_g}=eo(Ze,ne[m]);if(ae=="heartbeat")return;if(ae==="update"&&te&&!B)i({type:"status",endpoint:v,fn_index:m,time:new Date,original_msg:_g,...te});else if(ae==="complete")B=te;else if(ae=="unexpected_error")console.error("Unexpected error",te?.message),i({type:"status",stage:"error",message:te?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:v,fn_index:m,time:new Date});else if(ae==="log"){i({type:"log",title:Y.title,log:Y.log,level:Y.level,endpoint:v,duration:Y.duration,visible:Y.visible,fn_index:m});return}else(ae==="generating"||ae==="streaming")&&(i({type:"status",time:new Date,...te,stage:te?.stage,queue:!0,endpoint:v,fn_index:m}),Y&&y.connection!=="stream"&&["sse_v2","sse_v2.1","sse_v3"].includes(E)&&df(H,O,Y));Y&&(i({type:"data",time:new Date,data:Pe(Y.data,y,h.components,"output",q.with_null_state),endpoint:v,fn_index:m}),Y.render_config&&await Po(Y.render_config),B&&(i({type:"status",time:new Date,...B,stage:te?.stage,queue:!0,endpoint:v,fn_index:m}),n())),(te?.stage==="complete"||te?.stage==="error")&&(V[O]&&delete V[O],O in H&&delete H[O])}catch(ae){console.error("Unexpected client exception",ae),i({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:v,fn_index:m,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(E)&&(Ne(D,ge.abort_controller),D.open=!1,n())}};O in I&&(I[O].forEach(Ze=>To(Ze)),delete I[O]),V[O]=To,L.add(O),D.open||await this.open_stream()}})}});let Ke=!1;const Xe=[],$e=[],Ao={[Symbol.asyncIterator]:()=>Ao,next:c,throw:async T=>(d(T),c()),return:async()=>(n(),c()),cancel:dg,event_id:Q};return Ao}catch(i){throw console.error("Submit function encountered an error:",i),i}}function hf(e){return{then:(o,t)=>t(e)}}function gf(e,o,t,r){let a,i,n;if(typeof o=="number")a=o,i=e.unnamed_endpoints[a],n=r.dependencies.find(s=>s.id==o);else{const s=o.replace(/^\//,"");a=t[s],i=e.named_endpoints[o.trim()],n=r.dependencies.find(d=>d.id==t[s])}if(typeof a!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:a,endpoint_info:i,dependency:n}}class He{app_reference;options;deep_link=null;config;api_prefix="";api_info;api_map={};session_hash=Math.random().toString(36).substring(2);jwt=!1;last_status={};cookies=null;stream_status={open:!1};closed=!1;pending_stream_messages={};pending_diff_streams={};event_callbacks={};unclosed_events=new Set;heartbeat_event=null;abort_controller=null;stream_instance=null;current_payload;ws_map={};get_url_config(o=null){if(!this.config)throw new Error(le);o===null&&(o=window.location.href);const t=n=>n.replace(/^\/+|\/+$/g,"");let r=t(new URL(this.config.root).pathname),a=t(new URL(o).pathname),i;return a.startsWith(r)?i=t(a.substring(r.length)):i="",this.get_page_config(i)}get_page_config(o){if(!this.config)throw new Error(le);let t=this.config;return o in t.page||(o=""),{...t,current_page:o,layout:t.page[o].layout,components:t.components.filter(r=>t.page[o].components.includes(r.id)),dependencies:this.config.dependencies.filter(r=>t.page[o].dependencies.includes(r.id))}}fetch(o,t){const r=new Headers(t?.headers||{});if(this&&this.cookies&&r.append("Cookie",this.cookies),this&&this.options.headers)for(const a in this.options.headers)r.append(a,this.options.headers[a]);return fetch(o,{...t,headers:r})}stream(o){const t=new Headers;if(this&&this.cookies&&t.append("Cookie",this.cookies),this&&this.options.headers)for(const r in this.options.headers)t.append(r,this.options.headers[r]);return this&&this.options.hf_token&&t.append("Authorization",`Bearer ${this.options.hf_token}`),this.abort_controller=new AbortController,this.stream_instance=pf(o.toString(),{credentials:"include",headers:t,signal:this.abort_controller.signal}),this.stream_instance}view_api;upload_files;upload;handle_blob;post_data;submit;predict;open_stream;resolve_config;resolve_cookies;constructor(o,t={events:["data"]}){this.app_reference=o,this.deep_link=t.query_params?.deep_link||null,t.events||(t.events=["data"]),this.options=t,this.current_payload={},this.view_api=Mg.bind(this),this.upload_files=Vg.bind(this),this.handle_blob=Wg.bind(this),this.post_data=Zg.bind(this),this.submit=mf.bind(this),this.predict=Jg.bind(this),this.open_stream=cf.bind(this),this.resolve_config=Dg.bind(this),this.resolve_cookies=Ig.bind(this),this.upload=qg.bind(this),this.fetch=this.fetch.bind(this),this.handle_space_success=this.handle_space_success.bind(this),this.stream=this.stream.bind(this)}async init(){if((typeof window>"u"||!("WebSocket"in window))&&!global.WebSocket){const o=await p(()=>import("./browser-DEIVKsaa.js").then(t=>t.b),[],import.meta.url);global.WebSocket=o.WebSocket}this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:o})=>this._resolve_hearbeat(o)),this.api_info=await this.view_api(),this.api_map=Lg(this.config?.dependencies||[])}async _resolve_hearbeat(o){if(o&&(this.config=o,this.api_prefix=o.api_prefix||"",this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await Lo(this.config.space_id,this.options.hf_token,this.cookies))),o.space_id&&this.options.hf_token&&(this.jwt=await Lo(o.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const t=new URL(`${this.config.root}${this.api_prefix}/${yg}/${this.session_hash}`);this.jwt&&t.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(t))}}static async connect(o,t={events:["data"]}){const r=new this(o,t);return await r.init(),r}close(){this.closed=!0,Ne(this.stream_status,this.abort_controller)}set_current_payload(o){this.current_payload=o}static async duplicate(o,t={events:["data"]}){return of(o,t)}async _resolve_config(){const{http_protocol:o,host:t,space_id:r}=await qe(this.app_reference,this.options.hf_token),{status_callback:a}=this.options;r&&a&&await bt(r,a);let i;try{let n=`${o}//${t}`;if(i=await this.resolve_config(n),!i)throw new Error(le);return this.config_success(i)}catch(n){if(r&&a)Oe(r,yo.test(r)?"space_name":"subdomain",this.handle_space_success);else throw a&&a({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(n)}}async config_success(o){if(this.config=o,this.api_prefix=o.api_prefix||"",typeof window<"u"&&typeof document<"u"&&window.location.protocol==="https:"&&(this.config.root=this.config.root.replace("http://","https://")),this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(t){console.error(Pg+t.message)}return this.prepare_return_obj()}async handle_space_success(o){if(!this)throw new Error(le);const{status_callback:t}=this.options;if(t&&t(o),o.status==="running")try{if(this.config=await this._resolve_config(),this.api_prefix=this?.config?.api_prefix||"",!this.config)throw new Error(le);return await this.config_success(this.config)}catch(r){throw t&&t({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),r}}async component_server(o,t,r){if(!this.config)throw new Error(le);const a={},{hf_token:i}=this.options,{session_hash:n}=this;i&&(a.Authorization=`Bearer ${this.options.hf_token}`);let s,d=this.config.components.find(c=>c.id===o);d?.props?.root_url?s=d.props.root_url:s=this.config.root;let l;if("binary"in r){l=new FormData;for(const c in r.data)c!=="binary"&&l.append(c,r.data[c]);l.set("component_id",o.toString()),l.set("fn_name",t),l.set("session_hash",n)}else l=JSON.stringify({data:r,component_id:o,fn_name:t,session_hash:n}),a["Content-Type"]="application/json";i&&(a.Authorization=`Bearer ${i}`);try{const c=await this.fetch(`${s}${this.api_prefix}/${kg}/`,{method:"POST",body:l,headers:a,credentials:"include"});if(!c.ok)throw new Error("Could not connect to component server: "+c.statusText);return await c.json()}catch(c){console.warn(c)}}set_cookies(o){this.cookies=ht(o).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}async connect_ws(o){return new Promise((t,r)=>{let a;try{a=new WebSocket(o)}catch{this.ws_map[o]="failed";return}a.onopen=()=>{t()},a.onerror=i=>{console.error("WebSocket error:",i),this.close_ws(o),this.ws_map[o]="failed",t()},a.onclose=()=>{delete this.ws_map[o],this.ws_map[o]="failed"},a.onmessage=i=>{},this.ws_map[o]=a})}async send_ws_message(o,t){o in this.ws_map||await this.connect_ws(o);const r=this.ws_map[o];r instanceof WebSocket?r.send(JSON.stringify(t)):this.post_data(o,t)}async close_ws(o){if(o in this.ws_map){const t=this.ws_map[o];t instanceof WebSocket&&(t.close(),delete this.ws_map[o])}}}function ve(){}const Rv=e=>e;function Nv(e,o){for(const t in o)e[t]=o[t];return e}function ff(e){return e()}function bf(e){e.forEach(ff)}function vf(e){return typeof e=="function"}function wf(e,o){return e!=e?o==o:e!==o||e&&typeof e=="object"||typeof e=="function"}function vt(e,...o){if(e==null){for(const r of o)r(void 0);return ve}const t=e.subscribe(...o);return t.unsubscribe?()=>t.unsubscribe():t}function wt(e){let o;return vt(e,t=>o=t)(),o}function Hv(e){const o=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return o?[parseFloat(o[1]),o[2]||"px"]:[e,"px"]}const fe=[];function yf(e,o){return{subscribe:de(e,o).subscribe}}function de(e,o=ve){let t;const r=new Set;function a(s){if(wf(e,s)&&(e=s,t)){const d=!fe.length;for(const l of r)l[1](),fe.push(l,e);if(d){for(let l=0;l<fe.length;l+=2)fe[l][0](fe[l+1]);fe.length=0}}}function i(s){a(s(e))}function n(s,d=ve){const l=[s,d];return r.add(l),r.size===1&&(t=o(a,i)||ve),s(e),()=>{r.delete(l),r.size===0&&t&&(t(),t=null)}}return{set:a,update:i,subscribe:n}}function me(e,o,t){const r=!Array.isArray(e),a=r?[e]:e;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=o.length<2;return yf(t,(n,s)=>{let d=!1;const l=[];let c=0,u=ve;const _=()=>{if(c)return;u();const h=o(r?l[0]:l,n,s);i?n(h):u=vf(h)?h:ve},g=a.map((h,x)=>vt(h,$=>{l[x]=$,c&=~(1<<x),d&&_()},()=>{c|=1<<x}));return d=!0,_(),function(){bf(g),u(),d=!1}})}var Bv=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function kf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Mv(e){if(e.__esModule)return e;var o=e.default;if(typeof o=="function"){var t=function r(){return this instanceof r?Reflect.construct(o,arguments,this.constructor):o.apply(this,arguments)};t.prototype=o.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}),t}var Sf=function(o){return xf(o)&&!Ef(o)};function xf(e){return!!e&&typeof e=="object"}function Ef(e){var o=Object.prototype.toString.call(e);return o==="[object RegExp]"||o==="[object Date]"||Af(e)}var $f=typeof Symbol=="function"&&Symbol.for,Pf=$f?Symbol.for("react.element"):60103;function Af(e){return e.$$typeof===Pf}function Tf(e){return Array.isArray(e)?[]:{}}function je(e,o){return o.clone!==!1&&o.isMergeableObject(e)?we(Tf(e),e,o):e}function Of(e,o,t){return e.concat(o).map(function(r){return je(r,t)})}function jf(e,o){if(!o.customMerge)return we;var t=o.customMerge(e);return typeof t=="function"?t:we}function Cf(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(o){return Object.propertyIsEnumerable.call(e,o)}):[]}function No(e){return Object.keys(e).concat(Cf(e))}function yt(e,o){try{return o in e}catch{return!1}}function Lf(e,o){return yt(e,o)&&!(Object.hasOwnProperty.call(e,o)&&Object.propertyIsEnumerable.call(e,o))}function Df(e,o,t){var r={};return t.isMergeableObject(e)&&No(e).forEach(function(a){r[a]=je(e[a],t)}),No(o).forEach(function(a){Lf(e,a)||(yt(e,a)&&t.isMergeableObject(o[a])?r[a]=jf(a,t)(e[a],o[a],t):r[a]=je(o[a],t))}),r}function we(e,o,t){t=t||{},t.arrayMerge=t.arrayMerge||Of,t.isMergeableObject=t.isMergeableObject||Sf,t.cloneUnlessOtherwiseSpecified=je;var r=Array.isArray(o),a=Array.isArray(e),i=r===a;return i?r?t.arrayMerge(e,o,t):Df(e,o,t):je(o,t)}we.all=function(o,t){if(!Array.isArray(o))throw new Error("first argument should be an array");return o.reduce(function(r,a){return we(r,a,t)},{})};var If=we,zf=If;const Rf=kf(zf);var co=function(e,o){return co=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])},co(e,o)};function Ue(e,o){if(typeof o!="function"&&o!==null)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");co(e,o);function t(){this.constructor=e}e.prototype=o===null?Object.create(o):(t.prototype=o.prototype,new t)}var P=function(){return P=Object.assign||function(o){for(var t,r=1,a=arguments.length;r<a;r++){t=arguments[r];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(o[i]=t[i])}return o},P.apply(this,arguments)};function Nf(e,o){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)o.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t}function oo(e,o,t){if(t||arguments.length===2)for(var r=0,a=o.length,i;r<a;r++)(i||!(r in o))&&(i||(i=Array.prototype.slice.call(o,0,r)),i[r]=o[r]);return e.concat(i||Array.prototype.slice.call(o))}var k;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(k||(k={}));var C;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(C||(C={}));var ye;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(ye||(ye={}));function Ho(e){return e.type===C.literal}function Hf(e){return e.type===C.argument}function kt(e){return e.type===C.number}function St(e){return e.type===C.date}function xt(e){return e.type===C.time}function Et(e){return e.type===C.select}function $t(e){return e.type===C.plural}function Bf(e){return e.type===C.pound}function Pt(e){return e.type===C.tag}function At(e){return!!(e&&typeof e=="object"&&e.type===ye.number)}function uo(e){return!!(e&&typeof e=="object"&&e.type===ye.dateTime)}var Tt=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,Mf=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function Vf(e){var o={};return e.replace(Mf,function(t){var r=t.length;switch(t[0]){case"G":o.era=r===4?"long":r===5?"narrow":"short";break;case"y":o.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":o.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":o.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":o.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");o.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");o.weekday=["short","long","narrow","short"][r-4];break;case"a":o.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":o.hourCycle="h12",o.hour=["numeric","2-digit"][r-1];break;case"H":o.hourCycle="h23",o.hour=["numeric","2-digit"][r-1];break;case"K":o.hourCycle="h11",o.hour=["numeric","2-digit"][r-1];break;case"k":o.hourCycle="h24",o.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":o.minute=["numeric","2-digit"][r-1];break;case"s":o.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":o.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),o}var qf=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Gf(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var o=e.split(qf).filter(function(_){return _.length>0}),t=[],r=0,a=o;r<a.length;r++){var i=a[r],n=i.split("/");if(n.length===0)throw new Error("Invalid number skeleton");for(var s=n[0],d=n.slice(1),l=0,c=d;l<c.length;l++){var u=c[l];if(u.length===0)throw new Error("Invalid number skeleton")}t.push({stem:s,options:d})}return t}function Uf(e){return e.replace(/^(.*?)-/,"")}var Bo=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,Ot=/^(@+)?(\+|#+)?[rs]?$/g,Ff=/(\*)(0+)|(#+)(0+)|(0+)/g,jt=/^(0+)$/;function Mo(e){var o={};return e[e.length-1]==="r"?o.roundingPriority="morePrecision":e[e.length-1]==="s"&&(o.roundingPriority="lessPrecision"),e.replace(Ot,function(t,r,a){return typeof a!="string"?(o.minimumSignificantDigits=r.length,o.maximumSignificantDigits=r.length):a==="+"?o.minimumSignificantDigits=r.length:r[0]==="#"?o.maximumSignificantDigits=r.length:(o.minimumSignificantDigits=r.length,o.maximumSignificantDigits=r.length+(typeof a=="string"?a.length:0)),""}),o}function Ct(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Wf(e){var o;if(e[0]==="E"&&e[1]==="E"?(o={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(o={notation:"scientific"},e=e.slice(1)),o){var t=e.slice(0,2);if(t==="+!"?(o.signDisplay="always",e=e.slice(2)):t==="+?"&&(o.signDisplay="exceptZero",e=e.slice(2)),!jt.test(e))throw new Error("Malformed concise eng/scientific notation");o.minimumIntegerDigits=e.length}return o}function Vo(e){var o={},t=Ct(e);return t||o}function Kf(e){for(var o={},t=0,r=e;t<r.length;t++){var a=r[t];switch(a.stem){case"percent":case"%":o.style="percent";continue;case"%x100":o.style="percent",o.scale=100;continue;case"currency":o.style="currency",o.currency=a.options[0];continue;case"group-off":case",_":o.useGrouping=!1;continue;case"precision-integer":case".":o.maximumFractionDigits=0;continue;case"measure-unit":case"unit":o.style="unit",o.unit=Uf(a.options[0]);continue;case"compact-short":case"K":o.notation="compact",o.compactDisplay="short";continue;case"compact-long":case"KK":o.notation="compact",o.compactDisplay="long";continue;case"scientific":o=P(P(P({},o),{notation:"scientific"}),a.options.reduce(function(d,l){return P(P({},d),Vo(l))},{}));continue;case"engineering":o=P(P(P({},o),{notation:"engineering"}),a.options.reduce(function(d,l){return P(P({},d),Vo(l))},{}));continue;case"notation-simple":o.notation="standard";continue;case"unit-width-narrow":o.currencyDisplay="narrowSymbol",o.unitDisplay="narrow";continue;case"unit-width-short":o.currencyDisplay="code",o.unitDisplay="short";continue;case"unit-width-full-name":o.currencyDisplay="name",o.unitDisplay="long";continue;case"unit-width-iso-code":o.currencyDisplay="symbol";continue;case"scale":o.scale=parseFloat(a.options[0]);continue;case"rounding-mode-floor":o.roundingMode="floor";continue;case"rounding-mode-ceiling":o.roundingMode="ceil";continue;case"rounding-mode-down":o.roundingMode="trunc";continue;case"rounding-mode-up":o.roundingMode="expand";continue;case"rounding-mode-half-even":o.roundingMode="halfEven";continue;case"rounding-mode-half-down":o.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":o.roundingMode="halfExpand";continue;case"integer-width":if(a.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");a.options[0].replace(Ff,function(d,l,c,u,_,g){if(l)o.minimumIntegerDigits=c.length;else{if(u&&_)throw new Error("We currently do not support maximum integer digits");if(g)throw new Error("We currently do not support exact integer digits")}return""});continue}if(jt.test(a.stem)){o.minimumIntegerDigits=a.stem.length;continue}if(Bo.test(a.stem)){if(a.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");a.stem.replace(Bo,function(d,l,c,u,_,g){return c==="*"?o.minimumFractionDigits=l.length:u&&u[0]==="#"?o.maximumFractionDigits=u.length:_&&g?(o.minimumFractionDigits=_.length,o.maximumFractionDigits=_.length+g.length):(o.minimumFractionDigits=l.length,o.maximumFractionDigits=l.length),""});var i=a.options[0];i==="w"?o=P(P({},o),{trailingZeroDisplay:"stripIfInteger"}):i&&(o=P(P({},o),Mo(i)));continue}if(Ot.test(a.stem)){o=P(P({},o),Mo(a.stem));continue}var n=Ct(a.stem);n&&(o=P(P({},o),n));var s=Wf(a.stem);s&&(o=P(P({},o),s))}return o}var Ie={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Xf(e,o){for(var t="",r=0;r<e.length;r++){var a=e.charAt(r);if(a==="j"){for(var i=0;r+1<e.length&&e.charAt(r+1)===a;)i++,r++;var n=1+(i&1),s=i<2?1:3+(i>>1),d="a",l=Zf(o);for((l=="H"||l=="k")&&(s=0);s-- >0;)t+=d;for(;n-- >0;)t=l+t}else a==="J"?t+="H":t+=a}return t}function Zf(e){var o=e.hourCycle;if(o===void 0&&e.hourCycles&&e.hourCycles.length&&(o=e.hourCycles[0]),o)switch(o){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var t=e.language,r;t!=="root"&&(r=e.maximize().region);var a=Ie[r||""]||Ie[t||""]||Ie["".concat(t,"-001")]||Ie["001"];return a[0]}var to,Jf=new RegExp("^".concat(Tt.source,"*")),Yf=new RegExp("".concat(Tt.source,"*$"));function S(e,o){return{start:e,end:o}}var Qf=!!String.prototype.startsWith&&"_a".startsWith("a",1),eb=!!String.fromCodePoint,ob=!!Object.fromEntries,tb=!!String.prototype.codePointAt,rb=!!String.prototype.trimStart,ab=!!String.prototype.trimEnd,ib=!!Number.isSafeInteger,nb=ib?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},_o=!0;try{var sb=Dt("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");_o=((to=sb.exec("a"))===null||to===void 0?void 0:to[0])==="a"}catch{_o=!1}var qo=Qf?function(o,t,r){return o.startsWith(t,r)}:function(o,t,r){return o.slice(r,r+t.length)===t},po=eb?String.fromCodePoint:function(){for(var o=[],t=0;t<arguments.length;t++)o[t]=arguments[t];for(var r="",a=o.length,i=0,n;a>i;){if(n=o[i++],n>1114111)throw RangeError(n+" is not a valid code point");r+=n<65536?String.fromCharCode(n):String.fromCharCode(((n-=65536)>>10)+55296,n%1024+56320)}return r},Go=ob?Object.fromEntries:function(o){for(var t={},r=0,a=o;r<a.length;r++){var i=a[r],n=i[0],s=i[1];t[n]=s}return t},Lt=tb?function(o,t){return o.codePointAt(t)}:function(o,t){var r=o.length;if(!(t<0||t>=r)){var a=o.charCodeAt(t),i;return a<55296||a>56319||t+1===r||(i=o.charCodeAt(t+1))<56320||i>57343?a:(a-55296<<10)+(i-56320)+65536}},lb=rb?function(o){return o.trimStart()}:function(o){return o.replace(Jf,"")},cb=ab?function(o){return o.trimEnd()}:function(o){return o.replace(Yf,"")};function Dt(e,o){return new RegExp(e,o)}var mo;if(_o){var Uo=Dt("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");mo=function(o,t){var r;Uo.lastIndex=t;var a=Uo.exec(o);return(r=a[1])!==null&&r!==void 0?r:""}}else mo=function(o,t){for(var r=[];;){var a=Lt(o,t);if(a===void 0||It(a)||pb(a))break;r.push(a),t+=a>=65536?2:1}return po.apply(void 0,r)};var db=function(){function e(o,t){t===void 0&&(t={}),this.message=o,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(o,t,r){for(var a=[];!this.isEOF();){var i=this.char();if(i===123){var n=this.parseArgument(o,r);if(n.err)return n;a.push(n.val)}else{if(i===125&&o>0)break;if(i===35&&(t==="plural"||t==="selectordinal")){var s=this.clonePosition();this.bump(),a.push({type:C.pound,location:S(s,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(k.UNMATCHED_CLOSING_TAG,S(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&ho(this.peek()||0)){var n=this.parseTag(o,t);if(n.err)return n;a.push(n.val)}else{var n=this.parseLiteral(o,t);if(n.err)return n;a.push(n.val)}}}return{val:a,err:null}},e.prototype.parseTag=function(o,t){var r=this.clonePosition();this.bump();var a=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:C.literal,value:"<".concat(a,"/>"),location:S(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(o+1,t,!0);if(i.err)return i;var n=i.val,s=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!ho(this.char()))return this.error(k.INVALID_TAG,S(s,this.clonePosition()));var d=this.clonePosition(),l=this.parseTagName();return a!==l?this.error(k.UNMATCHED_CLOSING_TAG,S(d,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:C.tag,value:a,children:n,location:S(r,this.clonePosition())},err:null}:this.error(k.INVALID_TAG,S(s,this.clonePosition())))}else return this.error(k.UNCLOSED_TAG,S(r,this.clonePosition()))}else return this.error(k.INVALID_TAG,S(r,this.clonePosition()))},e.prototype.parseTagName=function(){var o=this.offset();for(this.bump();!this.isEOF()&&_b(this.char());)this.bump();return this.message.slice(o,this.offset())},e.prototype.parseLiteral=function(o,t){for(var r=this.clonePosition(),a="";;){var i=this.tryParseQuote(t);if(i){a+=i;continue}var n=this.tryParseUnquoted(o,t);if(n){a+=n;continue}var s=this.tryParseLeftAngleBracket();if(s){a+=s;continue}break}var d=S(r,this.clonePosition());return{val:{type:C.literal,value:a,location:d},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!ub(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(o){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(o==="plural"||o==="selectordinal")break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return po.apply(void 0,t)},e.prototype.tryParseUnquoted=function(o,t){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(t==="plural"||t==="selectordinal")||r===125&&o>0?null:(this.bump(),po(r))},e.prototype.parseArgument=function(o,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(k.EXPECT_ARGUMENT_CLOSING_BRACE,S(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(k.EMPTY_ARGUMENT,S(r,this.clonePosition()));var a=this.parseIdentifierIfPossible().value;if(!a)return this.error(k.MALFORMED_ARGUMENT,S(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(k.EXPECT_ARGUMENT_CLOSING_BRACE,S(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:C.argument,value:a,location:S(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(k.EXPECT_ARGUMENT_CLOSING_BRACE,S(r,this.clonePosition())):this.parseArgumentOptions(o,t,a,r);default:return this.error(k.MALFORMED_ARGUMENT,S(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var o=this.clonePosition(),t=this.offset(),r=mo(this.message,t),a=t+r.length;this.bumpTo(a);var i=this.clonePosition(),n=S(o,i);return{value:r,location:n}},e.prototype.parseArgumentOptions=function(o,t,r,a){var i,n=this.clonePosition(),s=this.parseIdentifierIfPossible().value,d=this.clonePosition();switch(s){case"":return this.error(k.EXPECT_ARGUMENT_TYPE,S(n,d));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),u=this.parseSimpleArgStyleIfPossible();if(u.err)return u;var _=cb(u.val);if(_.length===0)return this.error(k.EXPECT_ARGUMENT_STYLE,S(this.clonePosition(),this.clonePosition()));var g=S(c,this.clonePosition());l={style:_,styleLocation:g}}var h=this.tryParseArgumentClose(a);if(h.err)return h;var x=S(a,this.clonePosition());if(l&&qo(l?.style,"::",0)){var $=lb(l.style.slice(2));if(s==="number"){var u=this.parseNumberSkeletonFromString($,l.styleLocation);return u.err?u:{val:{type:C.number,value:r,location:x,style:u.val},err:null}}else{if($.length===0)return this.error(k.EXPECT_DATE_TIME_SKELETON,x);var M=$;this.locale&&(M=Xf($,this.locale));var _={type:ye.dateTime,pattern:M,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?Vf(M):{}},D=s==="date"?C.date:C.time;return{val:{type:D,value:r,location:x,style:_},err:null}}}return{val:{type:s==="number"?C.number:s==="date"?C.date:C.time,value:r,location:x,style:(i=l?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var I=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(k.EXPECT_SELECT_ARGUMENT_OPTIONS,S(I,P({},I)));this.bumpSpace();var H=this.parseIdentifierIfPossible(),V=0;if(s!=="select"&&H.value==="offset"){if(!this.bumpIf(":"))return this.error(k.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,S(this.clonePosition(),this.clonePosition()));this.bumpSpace();var u=this.tryParseDecimalInteger(k.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,k.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(u.err)return u;this.bumpSpace(),H=this.parseIdentifierIfPossible(),V=u.val}var L=this.tryParsePluralOrSelectOptions(o,s,t,H);if(L.err)return L;var h=this.tryParseArgumentClose(a);if(h.err)return h;var z=S(a,this.clonePosition());return s==="select"?{val:{type:C.select,value:r,options:Go(L.val),location:z},err:null}:{val:{type:C.plural,value:r,options:Go(L.val),offset:V,pluralType:s==="plural"?"cardinal":"ordinal",location:z},err:null}}default:return this.error(k.INVALID_ARGUMENT_TYPE,S(n,d))}},e.prototype.tryParseArgumentClose=function(o){return this.isEOF()||this.char()!==125?this.error(k.EXPECT_ARGUMENT_CLOSING_BRACE,S(o,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var o=0,t=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var a=this.clonePosition();if(!this.bumpUntil("'"))return this.error(k.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,S(a,this.clonePosition()));this.bump();break}case 123:{o+=1,this.bump();break}case 125:{if(o>0)o-=1;else return{val:this.message.slice(t.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(o,t){var r=[];try{r=Gf(o)}catch{return this.error(k.INVALID_NUMBER_SKELETON,t)}return{val:{type:ye.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?Kf(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(o,t,r,a){for(var i,n=!1,s=[],d=new Set,l=a.value,c=a.location;;){if(l.length===0){var u=this.clonePosition();if(t!=="select"&&this.bumpIf("=")){var _=this.tryParseDecimalInteger(k.EXPECT_PLURAL_ARGUMENT_SELECTOR,k.INVALID_PLURAL_ARGUMENT_SELECTOR);if(_.err)return _;c=S(u,this.clonePosition()),l=this.message.slice(u.offset,this.offset())}else break}if(d.has(l))return this.error(t==="select"?k.DUPLICATE_SELECT_ARGUMENT_SELECTOR:k.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);l==="other"&&(n=!0),this.bumpSpace();var g=this.clonePosition();if(!this.bumpIf("{"))return this.error(t==="select"?k.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:k.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,S(this.clonePosition(),this.clonePosition()));var h=this.parseMessage(o+1,t,r);if(h.err)return h;var x=this.tryParseArgumentClose(g);if(x.err)return x;s.push([l,{value:h.val,location:S(g,this.clonePosition())}]),d.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,c=i.location}return s.length===0?this.error(t==="select"?k.EXPECT_SELECT_ARGUMENT_SELECTOR:k.EXPECT_PLURAL_ARGUMENT_SELECTOR,S(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!n?this.error(k.MISSING_OTHER_CLAUSE,S(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(o,t){var r=1,a=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,n=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,n=n*10+(s-48),this.bump();else break}var d=S(a,this.clonePosition());return i?(n*=r,nb(n)?{val:n,err:null}:this.error(t,d)):this.error(o,d)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var o=this.position.offset;if(o>=this.message.length)throw Error("out of bound");var t=Lt(this.message,o);if(t===void 0)throw Error("Offset ".concat(o," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(o,t){return{val:null,err:{kind:o,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var o=this.char();o===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=o<65536?1:2)}},e.prototype.bumpIf=function(o){if(qo(this.message,o,this.offset())){for(var t=0;t<o.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(o){var t=this.offset(),r=this.message.indexOf(o,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(o){if(this.offset()>o)throw Error("targetOffset ".concat(o," must be greater than or equal to the current offset ").concat(this.offset()));for(o=Math.min(o,this.message.length);;){var t=this.offset();if(t===o)break;if(t>o)throw Error("targetOffset ".concat(o," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&It(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var o=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(o>=65536?2:1));return r??null},e}();function ho(e){return e>=97&&e<=122||e>=65&&e<=90}function ub(e){return ho(e)||e===47}function _b(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function It(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function pb(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function go(e){e.forEach(function(o){if(delete o.location,Et(o)||$t(o))for(var t in o.options)delete o.options[t].location,go(o.options[t].value);else kt(o)&&At(o.style)||(St(o)||xt(o))&&uo(o.style)?delete o.style.location:Pt(o)&&go(o.children)})}function mb(e,o){o===void 0&&(o={}),o=P({shouldParseSkeletons:!0,requiresOtherClause:!0},o);var t=new db(e,o).parse();if(t.err){var r=SyntaxError(k[t.err.kind]);throw r.location=t.err.location,r.originalMessage=t.err.message,r}return o?.captureLocation||go(t.val),t.val}function ro(e,o){var t=o&&o.cache?o.cache:wb,r=o&&o.serializer?o.serializer:vb,a=o&&o.strategy?o.strategy:gb;return a(e,{cache:t,serializer:r})}function hb(e){return e==null||typeof e=="number"||typeof e=="boolean"}function zt(e,o,t,r){var a=hb(r)?r:t(r),i=o.get(a);return typeof i>"u"&&(i=e.call(this,r),o.set(a,i)),i}function Rt(e,o,t){var r=Array.prototype.slice.call(arguments,3),a=t(r),i=o.get(a);return typeof i>"u"&&(i=e.apply(this,r),o.set(a,i)),i}function ko(e,o,t,r,a){return t.bind(o,e,r,a)}function gb(e,o){var t=e.length===1?zt:Rt;return ko(e,this,t,o.cache.create(),o.serializer)}function fb(e,o){return ko(e,this,Rt,o.cache.create(),o.serializer)}function bb(e,o){return ko(e,this,zt,o.cache.create(),o.serializer)}var vb=function(){return JSON.stringify(arguments)};function So(){this.cache=Object.create(null)}So.prototype.get=function(e){return this.cache[e]};So.prototype.set=function(e,o){this.cache[e]=o};var wb={create:function(){return new So}},ao={variadic:fb,monadic:bb},ke;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(ke||(ke={}));var Fe=function(e){Ue(o,e);function o(t,r,a){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=a,i}return o.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},o}(Error),Fo=function(e){Ue(o,e);function o(t,r,a,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(a).join('", "'),'"'),ke.INVALID_VALUE,i)||this}return o}(Fe),yb=function(e){Ue(o,e);function o(t,r,a){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),ke.INVALID_VALUE,a)||this}return o}(Fe),kb=function(e){Ue(o,e);function o(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),ke.MISSING_VALUE,r)||this}return o}(Fe),U;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(U||(U={}));function Sb(e){return e.length<2?e:e.reduce(function(o,t){var r=o[o.length-1];return!r||r.type!==U.literal||t.type!==U.literal?o.push(t):r.value+=t.value,o},[])}function xb(e){return typeof e=="function"}function ze(e,o,t,r,a,i,n){if(e.length===1&&Ho(e[0]))return[{type:U.literal,value:e[0].value}];for(var s=[],d=0,l=e;d<l.length;d++){var c=l[d];if(Ho(c)){s.push({type:U.literal,value:c.value});continue}if(Bf(c)){typeof i=="number"&&s.push({type:U.literal,value:t.getNumberFormat(o).format(i)});continue}var u=c.value;if(!(a&&u in a))throw new kb(u,n);var _=a[u];if(Hf(c)){(!_||typeof _=="string"||typeof _=="number")&&(_=typeof _=="string"||typeof _=="number"?String(_):""),s.push({type:typeof _=="string"?U.literal:U.object,value:_});continue}if(St(c)){var g=typeof c.style=="string"?r.date[c.style]:uo(c.style)?c.style.parsedOptions:void 0;s.push({type:U.literal,value:t.getDateTimeFormat(o,g).format(_)});continue}if(xt(c)){var g=typeof c.style=="string"?r.time[c.style]:uo(c.style)?c.style.parsedOptions:r.time.medium;s.push({type:U.literal,value:t.getDateTimeFormat(o,g).format(_)});continue}if(kt(c)){var g=typeof c.style=="string"?r.number[c.style]:At(c.style)?c.style.parsedOptions:void 0;g&&g.scale&&(_=_*(g.scale||1)),s.push({type:U.literal,value:t.getNumberFormat(o,g).format(_)});continue}if(Pt(c)){var h=c.children,x=c.value,$=a[x];if(!xb($))throw new yb(x,"function",n);var M=ze(h,o,t,r,a,i),D=$(M.map(function(V){return V.value}));Array.isArray(D)||(D=[D]),s.push.apply(s,D.map(function(V){return{type:typeof V=="string"?U.literal:U.object,value:V}}))}if(Et(c)){var I=c.options[_]||c.options.other;if(!I)throw new Fo(c.value,_,Object.keys(c.options),n);s.push.apply(s,ze(I.value,o,t,r,a));continue}if($t(c)){var I=c.options["=".concat(_)];if(!I){if(!Intl.PluralRules)throw new Fe(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,ke.MISSING_INTL_API,n);var H=t.getPluralRules(o,{type:c.pluralType}).select(_-(c.offset||0));I=c.options[H]||c.options.other}if(!I)throw new Fo(c.value,_,Object.keys(c.options),n);s.push.apply(s,ze(I.value,o,t,r,a,_-(c.offset||0)));continue}}return Sb(s)}function Eb(e,o){return o?P(P(P({},e||{}),o||{}),Object.keys(e).reduce(function(t,r){return t[r]=P(P({},e[r]),o[r]||{}),t},{})):e}function $b(e,o){return o?Object.keys(e).reduce(function(t,r){return t[r]=Eb(e[r],o[r]),t},P({},e)):e}function io(e){return{create:function(){return{get:function(o){return e[o]},set:function(o,t){e[o]=t}}}}}function Pb(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:ro(function(){for(var o,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((o=Intl.NumberFormat).bind.apply(o,oo([void 0],t,!1)))},{cache:io(e.number),strategy:ao.variadic}),getDateTimeFormat:ro(function(){for(var o,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((o=Intl.DateTimeFormat).bind.apply(o,oo([void 0],t,!1)))},{cache:io(e.dateTime),strategy:ao.variadic}),getPluralRules:ro(function(){for(var o,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((o=Intl.PluralRules).bind.apply(o,oo([void 0],t,!1)))},{cache:io(e.pluralRules),strategy:ao.variadic})}}var Nt=function(){function e(o,t,r,a){var i=this;if(t===void 0&&(t=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(d){var l=i.formatToParts(d);if(l.length===1)return l[0].value;var c=l.reduce(function(u,_){return!u.length||_.type!==U.literal||typeof u[u.length-1]!="string"?u.push(_.value):u[u.length-1]+=_.value,u},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(d){return ze(i.ast,i.locales,i.formatters,i.formats,d,void 0,i.message)},this.resolvedOptions=function(){var d;return{locale:((d=i.resolvedLocale)===null||d===void 0?void 0:d.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=t,this.resolvedLocale=e.resolveLocale(t),typeof o=="string"){if(this.message=o,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var n=a||{};n.formatters;var s=Nf(n,["formatters"]);this.ast=e.__parse(o,P(P({},s),{locale:this.resolvedLocale}))}else this.ast=o;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=$b(e.formats,r),this.formatters=a&&a.formatters||Pb(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(o){if(!(typeof Intl.Locale>"u")){var t=Intl.NumberFormat.supportedLocalesOf(o);return t.length>0?new Intl.Locale(t[0]):new Intl.Locale(typeof o=="string"?o:o[0])}},e.__parse=mb,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function Ab(e,o){if(o==null)return;if(o in e)return e[o];const t=o.split(".");let r=e;for(let a=0;a<t.length;a++)if(typeof r=="object"){if(a>0){const i=t.slice(a,t.length).join(".");if(i in r){r=r[i];break}}r=r[t[a]]}else r=void 0;return r}const ce={},Tb=(e,o,t)=>t&&(o in ce||(ce[o]={}),e in ce[o]||(ce[o][e]=t),t),Ht=(e,o)=>{if(o==null)return;if(o in ce&&e in ce[o])return ce[o][e];const t=De(o);for(let r=0;r<t.length;r++){const a=t[r],i=jb(a,e);if(i)return Tb(e,o,i)}};let xo;const Le=de({});function Ob(e){return xo[e]||null}function Bt(e){return e in xo}function jb(e,o){if(!Bt(e))return null;const t=Ob(e);return Ab(t,o)}function Cb(e){if(e==null)return;const o=De(e);for(let t=0;t<o.length;t++){const r=o[t];if(Bt(r))return r}}function Mt(e,...o){delete ce[e],Le.update(t=>(t[e]=Rf.all([t[e]||{},...o]),t))}me([Le],([e])=>Object.keys(e));Le.subscribe(e=>xo=e);const Re={};function Lb(e,o){Re[e].delete(o),Re[e].size===0&&delete Re[e]}function Vt(e){return Re[e]}function Db(e){return De(e).map(o=>{const t=Vt(o);return[o,t?[...t]:[]]}).filter(([,o])=>o.length>0)}function Be(e){return e==null?!1:De(e).some(o=>{var t;return(t=Vt(o))==null?void 0:t.size})}function Ib(e,o){return Promise.all(o.map(r=>(Lb(e,r),r().then(a=>a.default||a)))).then(r=>Mt(e,...r))}const Ae={};function qt(e){if(!Be(e))return e in Ae?Ae[e]:Promise.resolve();const o=Db(e);return Ae[e]=Promise.all(o.map(([t,r])=>Ib(t,r))).then(()=>{if(Be(e))return qt(e);delete Ae[e]}),Ae[e]}var Wo=Object.getOwnPropertySymbols,zb=Object.prototype.hasOwnProperty,Rb=Object.prototype.propertyIsEnumerable,Nb=(e,o)=>{var t={};for(var r in e)zb.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&Wo)for(var r of Wo(e))o.indexOf(r)<0&&Rb.call(e,r)&&(t[r]=e[r]);return t};const Hb={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function Bb({locale:e,id:o}){console.warn(`[svelte-i18n] The message "${o}" was not found in "${De(e).join('", "')}".${Be(ue())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const Mb={fallbackLocale:null,loadingDelay:200,formats:Hb,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},Te=Mb;function Se(){return Te}function Vb(e){const o=e,{formats:t}=o,r=Nb(o,["formats"]);let a=e.fallbackLocale;if(e.initialLocale)try{Nt.resolveLocale(e.initialLocale)&&(a=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return r.warnOnMissingMessages&&(delete r.warnOnMissingMessages,r.handleMissingMessage==null?r.handleMissingMessage=Bb:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(Te,r,{initialLocale:a}),t&&("number"in t&&Object.assign(Te.formats.number,t.number),"date"in t&&Object.assign(Te.formats.date,t.date),"time"in t&&Object.assign(Te.formats.time,t.time)),he.set(a)}const no=de(!1);var qb=Object.defineProperty,Gb=Object.defineProperties,Ub=Object.getOwnPropertyDescriptors,Ko=Object.getOwnPropertySymbols,Fb=Object.prototype.hasOwnProperty,Wb=Object.prototype.propertyIsEnumerable,Xo=(e,o,t)=>o in e?qb(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,Kb=(e,o)=>{for(var t in o||(o={}))Fb.call(o,t)&&Xo(e,t,o[t]);if(Ko)for(var t of Ko(o))Wb.call(o,t)&&Xo(e,t,o[t]);return e},Xb=(e,o)=>Gb(e,Ub(o));let fo;const Me=de(null);function Zo(e){return e.split("-").map((o,t,r)=>r.slice(0,t+1).join("-")).reverse()}function De(e,o=Se().fallbackLocale){const t=Zo(e);return o?[...new Set([...t,...Zo(o)])]:t}function ue(){return fo??void 0}Me.subscribe(e=>{fo=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Zb=e=>{if(e&&Cb(e)&&Be(e)){const{loadingDelay:o}=Se();let t;return typeof window<"u"&&ue()!=null&&o?t=window.setTimeout(()=>no.set(!0),o):no.set(!0),qt(e).then(()=>{Me.set(e)}).finally(()=>{clearTimeout(t),no.set(!1)})}return Me.set(e)},he=Xb(Kb({},Me),{set:Zb}),Jb=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],We=e=>{const o=Object.create(null);return r=>{const a=JSON.stringify(r);return a in o?o[a]:o[a]=e(r)}};var Yb=Object.defineProperty,Ve=Object.getOwnPropertySymbols,Gt=Object.prototype.hasOwnProperty,Ut=Object.prototype.propertyIsEnumerable,Jo=(e,o,t)=>o in e?Yb(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,Eo=(e,o)=>{for(var t in o||(o={}))Gt.call(o,t)&&Jo(e,t,o[t]);if(Ve)for(var t of Ve(o))Ut.call(o,t)&&Jo(e,t,o[t]);return e},xe=(e,o)=>{var t={};for(var r in e)Gt.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&Ve)for(var r of Ve(e))o.indexOf(r)<0&&Ut.call(e,r)&&(t[r]=e[r]);return t};const Ce=(e,o)=>{const{formats:t}=Se();if(e in t&&o in t[e])return t[e][o];throw new Error(`[svelte-i18n] Unknown "${o}" ${e} format.`)},Qb=We(e=>{var o=e,{locale:t,format:r}=o,a=xe(o,["locale","format"]);if(t==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return r&&(a=Ce("number",r)),new Intl.NumberFormat(t,a)}),e0=We(e=>{var o=e,{locale:t,format:r}=o,a=xe(o,["locale","format"]);if(t==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return r?a=Ce("date",r):Object.keys(a).length===0&&(a=Ce("date","short")),new Intl.DateTimeFormat(t,a)}),o0=We(e=>{var o=e,{locale:t,format:r}=o,a=xe(o,["locale","format"]);if(t==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return r?a=Ce("time",r):Object.keys(a).length===0&&(a=Ce("time","short")),new Intl.DateTimeFormat(t,a)}),t0=(e={})=>{var o=e,{locale:t=ue()}=o,r=xe(o,["locale"]);return Qb(Eo({locale:t},r))},r0=(e={})=>{var o=e,{locale:t=ue()}=o,r=xe(o,["locale"]);return e0(Eo({locale:t},r))},a0=(e={})=>{var o=e,{locale:t=ue()}=o,r=xe(o,["locale"]);return o0(Eo({locale:t},r))},i0=We((e,o=ue())=>new Nt(e,o,Se().formats,{ignoreTag:Se().ignoreTag})),n0=(e,o={})=>{var t,r,a,i;let n=o;typeof e=="object"&&(n=e,e=n.id);const{values:s,locale:d=ue(),default:l}=n;if(d==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=Ht(e,d);if(!c)c=(i=(a=(r=(t=Se()).handleMissingMessage)==null?void 0:r.call(t,{locale:d,id:e,defaultValue:l}))!=null?a:l)!=null?i:e;else if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c;if(!s)return c;let u=c;try{u=i0(c,d).format(s)}catch(_){_ instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,_.message)}return u},s0=(e,o)=>a0(o).format(e),l0=(e,o)=>r0(o).format(e),c0=(e,o)=>t0(o).format(e),d0=(e,o=ue())=>Ht(e,o),Ft=me([he,Le],()=>n0);me([he],()=>s0);me([he],()=>l0);me([he],()=>c0);me([he,Le],()=>d0);let Wt=!1;typeof window<"u"&&"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Wt="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function Yo(e,o){const t=new URL(import.meta.url).origin;var r=e;if(window.location.origin!==t&&(r=new URL(e,t).href),document.querySelector(`link[href='${r}']`))return Promise.resolve();const i=document.createElement("link");return i.rel="stylesheet",i.href=r,new Promise((n,s)=>{i.addEventListener("load",()=>n()),i.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${r}`),n()}),o.appendChild(i)})}function Vv(e,o,t){if(!Wt)return e;t||(t=document.createElement("style")),t.remove();const r=new CSSStyleSheet;r.replaceSync(e);let a="";e=e.replace(/@import\s+url\((.*?)\);\s*/g,(d,l)=>(a+=`@import url(${l});
`,""));const i=r.cssRules;let n="",s=`.gradio-container.gradio-container-${o} .contain `;for(let d=0;d<i.length;d++){const l=i[d];let c=l.cssText.includes(".dark");if(l instanceof CSSStyleRule){const u=l.selectorText;if(u){const _=u.replace(".dark","").split(",").map(g=>`${c?".dark":""} ${s} ${g.trim()} `).join(",");n+=l.cssText,n+=l.cssText.replace(u,_)}}else if(l instanceof CSSMediaRule){let u=`@media ${l.media.mediaText} {`;for(let _=0;_<l.cssRules.length;_++){const g=l.cssRules[_];if(g instanceof CSSStyleRule){let h=g.cssText.includes(".dark ");const x=g.selectorText,$=x.replace(".dark","").split(",").map(M=>`${h?".dark":""} ${s} ${M.trim()} `).join(",");u+=g.cssText.replace(x,$)}}u+="}",n+=u}else if(l instanceof CSSKeyframesRule){n+=`@keyframes ${l.name} {`;for(let u=0;u<l.cssRules.length;u++){const _=l.cssRules[u];_ instanceof CSSKeyframeRule&&(n+=`${_.keyText} { ${_.style.cssText} }`)}n+="}"}else l instanceof CSSFontFaceRule&&(n+=`@font-face { ${l.style.cssText} }`)}return a+n}const u0={accordion:{component:()=>p(()=>import("./Index-Bq_jvq4F.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)},annotatedimage:{component:()=>p(()=>import("./Index-DTxhE6Ms.js"),__vite__mapDeps([14,1,2,3,4,5,15,16,17,18,19,9,6,7,8,10,20,21,22,23]),import.meta.url)},audio:{base:()=>p(()=>import("./StaticAudio-C_goMxpb.js"),__vite__mapDeps([24,25,3,4,5,15,2,9,16,26,27,28,29,18,30,31,32,33,20,21,22,34,35,36]),import.meta.url),example:()=>p(()=>import("./Example-BQyGztrG.js"),__vite__mapDeps([37,38]),import.meta.url),component:()=>p(()=>import("./index-CmY2SGP5.js"),__vite__mapDeps([39,24,25,3,4,5,15,2,9,16,26,27,28,29,18,30,31,32,33,20,21,22,34,35,36,40,41,42,43,10,44,45,7,8,46,47,6,1,48,37,38]),import.meta.url)},box:{component:()=>p(()=>import("./Index-P7JUfB5x.js"),__vite__mapDeps([49,1,2,3,4,5]),import.meta.url)},browserstate:{component:()=>p(()=>import("./Index-BEfWDEie.js"),__vite__mapDeps([50,51]),import.meta.url)},button:{component:()=>p(()=>import("./Index-BGqHGXa7.js"),__vite__mapDeps([52,53,54,20,21,22,55,3,4,5,56,2,57]),import.meta.url)},chatbot:{component:()=>p(()=>import("./Index-DAN2YSmp.js"),__vite__mapDeps([58,25,54,20,21,22,55,3,4,5,9,2,59,60,10,44,33,18,61,62,63,64,27,65,29,1,15,6,7,8,66,57]),import.meta.url)},checkbox:{example:()=>p(()=>import("./Example-CZ-iEz1g.js"),__vite__mapDeps([67,38]),import.meta.url),component:()=>p(()=>import("./Index-3kkZfHe6.js"),__vite__mapDeps([68,1,2,69,62,3,4,5,6,7,8,9,10,70,71]),import.meta.url)},checkboxgroup:{example:()=>p(()=>import("./Example-DccrJI--.js"),__vite__mapDeps([72,38]),import.meta.url),component:()=>p(()=>import("./Index-CeRGpzhg.js"),__vite__mapDeps([73,1,2,74,69,62,3,4,5,6,7,8,9,10,75]),import.meta.url)},code:{example:()=>p(()=>import("./Example-Wp-_4AVX.js"),__vite__mapDeps([76,77]),import.meta.url),component:()=>p(()=>import("./Index-BBBT_7z0.js").then(e=>e.K),__vite__mapDeps([78,21,22,59,60,3,4,5,9,2,28,36,20,18,6,7,8,10,79,1,15,16,76,77,80]),import.meta.url)},colorpicker:{example:()=>p(()=>import("./Example-BaLyJYAe.js"),__vite__mapDeps([81,82]),import.meta.url),component:()=>p(()=>import("./Index-DZafFVon.js"),__vite__mapDeps([83,84,74,69,62,3,4,5,2,1,6,7,8,9,10,81,82,85]),import.meta.url)},column:{component:()=>p(()=>import("./Index-C_pBLFQL.js"),__vite__mapDeps([11,6,7,8,3,4,5,9,2,10,12]),import.meta.url)},core:{component:()=>p(()=>import("./index-C7pqxX8U.js"),__vite__mapDeps([86,87]),import.meta.url)},dataframe:{example:()=>p(()=>import("./Example-CqPGqNav.js"),__vite__mapDeps([88,89]),import.meta.url),component:()=>p(()=>import("./Index-B-oL2Ctq.js"),__vite__mapDeps([90,1,2,3,4,5,91,92,64,41,42,21,22,62,59,60,19,9,6,7,8,10,93,94,25,15,16,26,27,28,17,18,95,54,20,55,36,57,96,45,97,98,63,46,99,48,100,101,88,89,102]),import.meta.url)},dataset:{component:()=>p(()=>import("./Index-BGzJCeJV.js"),__vite__mapDeps([103,1,2,3,4,5,7,8,104,105,106,107]),import.meta.url)},datetime:{example:()=>p(()=>import("./Example-BBLMS951.js"),[],import.meta.url),component:()=>p(()=>import("./Index-gTS0BfGg.js"),__vite__mapDeps([108,1,2,74,69,62,3,4,5,109,110]),import.meta.url)},downloadbutton:{component:()=>p(()=>import("./Index-Cggxnaw9.js"),__vite__mapDeps([111,53,54,20,21,22,55,3,4,5,56,2,57,112]),import.meta.url)},dropdown:{example:()=>p(()=>import("./Example-BgQNfMWT.js"),__vite__mapDeps([113,38]),import.meta.url),component:()=>p(()=>import("./Index-5MfacqWU.js"),__vite__mapDeps([114,74,69,62,3,4,5,2,97,115,63,116,1,6,7,8,9,10,113,38]),import.meta.url)},file:{example:()=>p(()=>import("./Example-DrmWnoSo.js"),__vite__mapDeps([117,118]),import.meta.url),component:()=>p(()=>import("./Index-CAlEGANp.js"),__vite__mapDeps([119,120,3,4,5,15,2,16,61,41,42,9,10,18,21,22,36,20,121,1,48,6,7,8,117,118]),import.meta.url)},fileexplorer:{example:()=>p(()=>import("./Example-CIFMxn5c.js"),__vite__mapDeps([122,123]),import.meta.url),component:()=>p(()=>import("./Index-uwGTfAfz.js"),__vite__mapDeps([124,61,1,2,3,4,5,15,6,7,8,9,10,125]),import.meta.url)},form:{component:()=>p(()=>import("./Index-DE1Sah7F.js"),__vite__mapDeps([126,127]),import.meta.url)},gallery:{base:()=>p(()=>import("./Gallery-CQraGjdH.js"),__vite__mapDeps([128,3,4,5,15,2,9,16,26,27,25,10,28,17,32,18,19,43,44,33,36,21,22,20,54,55,129,34,130,91,131,42,57]),import.meta.url),component:()=>p(()=>import("./Index-DGJYDoi5.js"),__vite__mapDeps([132,1,2,3,4,5,48,41,42,128,15,9,16,26,27,25,10,28,17,32,18,19,43,44,33,36,21,22,20,54,55,129,34,130,91,131,57,6,7,8,120,61,121,118]),import.meta.url)},group:{component:()=>p(()=>import("./Index-WEzAIkMk.js"),__vite__mapDeps([133,134]),import.meta.url)},highlightedtext:{component:()=>p(()=>import("./Index-BUdzJf_d.js"),__vite__mapDeps([135,136,1,2,3,4,5,15,16,6,7,8,9,10,137]),import.meta.url)},html:{base:()=>p(()=>import("./Index-Cqy2Fmu2.js"),__vite__mapDeps([138,6,7,8,3,4,5,9,2,10,79,1,15,25,139]),import.meta.url),example:()=>p(()=>import("./Example-C2a4WxRl.js"),__vite__mapDeps([140,141]),import.meta.url),component:()=>p(()=>import("./Index-Cqy2Fmu2.js"),__vite__mapDeps([138,6,7,8,3,4,5,9,2,10,79,1,15,25,139]),import.meta.url)},image:{base:()=>p(()=>import("./ImagePreview-2kHKEtmm.js"),__vite__mapDeps([94,25,3,4,5,15,2,9,16,26,27,28,17,18,19,95,54,20,21,22,55,36,57]),import.meta.url),example:()=>p(()=>import("./Example-CC8yxxGn.js"),__vite__mapDeps([100,54,20,21,22,55,101]),import.meta.url),component:()=>p(()=>import("./Index-BJtN0ikg.js"),__vite__mapDeps([93,94,25,3,4,5,15,2,9,16,26,27,28,17,18,19,95,54,20,21,22,55,36,57,96,10,45,41,42,97,98,7,8,63,46,99,1,48,6,100,101]),import.meta.url)},imageeditor:{example:()=>p(()=>import("./Example-CF5q22w7.js"),__vite__mapDeps([142,3,4,5,54,20,21,22,55,96,15,2,9,10,17,45,41,42,18,19,95,97,98,7,8,63,46,99,143,57,101]),import.meta.url),component:()=>p(()=>import("./Index-CQdjniUO.js").then(e=>e.as),__vite__mapDeps([144,94,25,3,4,5,15,2,9,16,26,27,28,17,18,19,95,54,20,21,22,55,36,57,96,10,45,41,42,97,98,7,8,63,46,99,1,6,84,59,65,33,145,101]),import.meta.url)},imageslider:{example:()=>p(()=>import("./Example-j_9MW44b.js"),__vite__mapDeps([146,147]),import.meta.url),component:()=>p(()=>import("./Index-jm4-egAD.js"),__vite__mapDeps([148,149,150,20,21,22,3,4,5,15,2,9,16,10,28,17,33,18,19,36,6,7,8,41,42,1,48,151]),import.meta.url)},json:{example:()=>p(()=>import("./Example-DY_fp6t_.js"),__vite__mapDeps([152,153,59,60,3,4,5,9,2,16,18,154,155]),import.meta.url),component:()=>p(()=>import("./Index-BiGxUF7w.js"),__vite__mapDeps([156,153,59,60,3,4,5,9,2,16,18,154,1,15,6,7,8,10]),import.meta.url)},label:{component:()=>p(()=>import("./Index-CmeEScIW.js"),__vite__mapDeps([157,158,1,2,3,4,5,15,16,6,7,8,9,10,159]),import.meta.url)},markdown:{example:()=>p(()=>import("./Example-s6q-Imk8.js"),__vite__mapDeps([160,62,3,4,5,38]),import.meta.url),component:()=>p(()=>import("./Index-AokCAbrK.js"),__vite__mapDeps([161,25,59,60,62,3,4,5,9,2,18,6,7,8,10,1,160,38,162]),import.meta.url)},model3d:{example:()=>p(()=>import("./Example-uQ8MuYg6.js"),__vite__mapDeps([163,38]),import.meta.url),component:()=>p(()=>import("./Index-B4M_FgsQ.js"),__vite__mapDeps([164,3,4,5,15,2,9,28,61,33,18,41,42,43,10,44,36,21,22,20,1,16,48,6,7,8,163,38,165]),import.meta.url)},multimodaltextbox:{example:()=>p(()=>import("./Example-CXcdMjGN.js"),__vite__mapDeps([166,54,20,21,22,55,3,4,5,129,34,130,167,2,57]),import.meta.url),component:()=>p(()=>import("./Index-BrxIuZVJ.js"),__vite__mapDeps([168,74,69,62,3,4,5,2,10,61,45,41,42,29,169,98,170,21,22,54,20,55,40,43,9,28,44,33,18,36,15,7,8,46,30,25,31,32,16,34,35,47,1,6,166,129,130,167,57,171]),import.meta.url)},nativeplot:{example:()=>p(()=>import("./Example-Creifpe8.js"),[],import.meta.url),component:()=>p(()=>import("./Index-Bbrpr1kE.js"),__vite__mapDeps([172,1,2,74,69,62,3,4,5,16,158,18,19,9,6,7,8,10,173]),import.meta.url)},number:{example:()=>p(()=>import("./Example-CqL1e7EB.js"),__vite__mapDeps([174,38]),import.meta.url),component:()=>p(()=>import("./Index-CGhgRfEX.js"),__vite__mapDeps([175,1,2,74,69,62,3,4,5,6,7,8,9,10,176]),import.meta.url)},paramviewer:{example:()=>p(()=>import("./Example-C9__vDgN.js"),__vite__mapDeps([177,38]),import.meta.url),component:()=>p(()=>import("./Index-xFwj5roU.js"),__vite__mapDeps([178,4,179]),import.meta.url)},plot:{base:()=>p(()=>import("./Plot-DHdxFihW.js").then(e=>e.b),__vite__mapDeps([180,3,4,5,16,2]),import.meta.url),component:()=>p(()=>import("./Index-C7BEHOrj.js"),__vite__mapDeps([181,180,3,4,5,16,2,1,15,18,19,9,6,7,8,10]),import.meta.url)},radio:{example:()=>p(()=>import("./Example-BoMLuz1A.js"),__vite__mapDeps([182,38]),import.meta.url),component:()=>p(()=>import("./Index-CjxJjfsR.js"),__vite__mapDeps([183,1,2,74,69,62,3,4,5,6,7,8,9,10,182,38,184]),import.meta.url)},row:{component:()=>p(()=>import("./Index-CxbCa_Hu.js"),__vite__mapDeps([185,6,7,8,3,4,5,9,2,10,186]),import.meta.url)},sidebar:{component:()=>p(()=>import("./Index-DWQ43kEh.js"),__vite__mapDeps([187,6,7,8,3,4,5,9,2,10,11,12,188]),import.meta.url)},sketchbox:{component:()=>p(()=>import("./Index-BlWK1-fD.js"),__vite__mapDeps([189,190]),import.meta.url)},slider:{example:()=>p(()=>import("./Example-BrizabXh.js"),__vite__mapDeps([191,38]),import.meta.url),component:()=>p(()=>import("./Index-DqtpUcMZ.js"),__vite__mapDeps([192,1,2,74,69,62,3,4,5,6,7,8,9,10,193]),import.meta.url)},state:{component:()=>p(()=>import("./Index-uRgjJb4U.js"),[],import.meta.url)},statustracker:{component:()=>p(()=>import("./index-D15TB0sU.js"),__vite__mapDeps([194,6,7,8,3,4,5,9,2,10,195,63,46]),import.meta.url)},tabitem:{component:()=>p(()=>import("./Index-Dcd9NrWD.js"),__vite__mapDeps([196,197,198,11,6,7,8,3,4,5,9,2,10,12,199]),import.meta.url)},tabs:{component:()=>p(()=>import("./Index-CNb_gXq7.js"),__vite__mapDeps([200,197,198]),import.meta.url)},textbox:{example:()=>p(()=>import("./Example-Cx2SdskM.js"),__vite__mapDeps([104,105]),import.meta.url),component:()=>p(()=>import("./Index-DAKBwkte.js"),__vite__mapDeps([201,202,74,69,62,3,4,5,2,59,60,169,98,63,107,1,6,7,8,9,10,104,105]),import.meta.url)},timer:{component:()=>p(()=>import("./Index-BMLc4VxK.js"),[],import.meta.url)},uploadbutton:{component:()=>p(()=>import("./Index-DaZWCdNS.js"),__vite__mapDeps([203,53,54,20,21,22,55,3,4,5,56,2,57,204]),import.meta.url)},video:{base:()=>p(()=>import("./VideoPreview-DYkibY1b.js").then(e=>e.a),__vite__mapDeps([205,3,4,5,15,2,9,16,26,27,25,28,170,18,36,21,22,20,31,32,33,129,34,130,43,10,44,206,42]),import.meta.url),example:()=>p(()=>import("./Example-B_ghNU9h.js"),__vite__mapDeps([207,129,20,21,22,34,130,208]),import.meta.url),component:()=>p(()=>import("./index-DLMU5ww8.js"),__vite__mapDeps([209,41,42,3,4,5,15,2,170,45,21,22,54,20,55,96,9,10,17,18,19,95,97,98,7,8,63,46,99,129,34,130,205,16,26,27,25,28,36,31,32,33,43,44,206,207,208,1,48,6,210,57,101]),import.meta.url)}},se={},$o=typeof window<"u";function Qo({api_url:e,name:o,id:t,variant:r}){const a=$o&&window.__GRADIO__CC__,i={...u0,...a||{}};let n=t||o;if(se[`${n}-${r}`])return{component:se[`${n}-${r}`],name:o};try{if(!i?.[n]?.[r]&&!i?.[o]?.[r])throw new Error;return se[`${n}-${r}`]=(i?.[n]?.[r]||i?.[o]?.[r])(),{name:o,component:se[`${n}-${r}`]}}catch{if(!n)throw new Error(`Component not found: ${o}`);try{return se[`${n}-${r}`]=_0(e,n,r),{name:o,component:se[`${n}-${r}`]}}catch(d){if(r==="example")return se[`${n}-${r}`]=p(()=>import("./Example-DxdiEFS_.js"),__vite__mapDeps([211,38]),import.meta.url),{name:o,component:se[`${n}-${r}`]};throw console.error(`failed to load: ${o}`),console.error(d),d}}}function et(e){return $o?new Promise((o,t)=>{const r=document.createElement("link");r.rel="stylesheet",r.href=e,document.head.appendChild(r),r.onload=()=>o(),r.onerror=()=>t()}):Promise.resolve()}function _0(e,o,t){const r=$o?"client":"server";let a;return r==="server"?Promise.all([et(`${e}/custom_component/${o}/${t}/style.css`),p(()=>import("./Index-xQEydEg1.js"),__vite__mapDeps([212,1,2,3,4,5,6,7,8,9,10,213]),import.meta.url)]).then(([i,n])=>n):(a=`${e}/custom_component/${o}/${r}/${t}/index.js`,Promise.all([et(`${e}/custom_component/${o}/${r}/${t}/style.css`),import(a)]).then(([i,n])=>n))}function p0(){const e=de({}),o={},t={},r=new Map,a=new Map,i=new Map,n={};function s({fn_index:l,status:c,queue:u=!0,size:_,position:g=null,eta:h=null,message:x=null,progress:$,time_limit:M=null}){const D=t[l],I=o[l],H=n[l],V=D.map(L=>{let z;const q=r.get(L)||0;if(H==="pending"&&c!=="pending"){let F=q-1;r.set(L,F<0?0:F),z=F>0?"pending":c}else H==="pending"&&c==="pending"?z="pending":H!=="pending"&&c==="pending"?(z="pending",r.set(L,q+1)):z=c;return{id:L,queue_position:g,queue_size:_,eta:h,status:z,message:x,progress:$}});I.forEach(L=>{const z=a.get(L)||0;if(H==="pending"&&c!=="pending"){let q=z-1;a.set(L,q<0?0:q),i.set(L,c)}else H!=="pending"&&c==="pending"?(a.set(L,z+1),i.set(L,c)):i.delete(L)}),e.update(L=>(V.forEach(({id:z,queue_position:q,queue_size:F,eta:ge,status:m,message:b,progress:y})=>{L[z]={queue:u,queue_size:F,queue_position:q,eta:ge,message:b,progress:y,status:m,fn_index:l}}),L)),n[l]=c}function d(l,c,u){o[l]=c,t[l]=u}return{update:s,register:d,subscribe:e.subscribe,get_status_for_fn(l){return n[l]},get_inputs_to_update(){return i}}}let be=[];const m0=typeof window<"u",h0=m0?requestAnimationFrame:async e=>await e();function qv(e){let o,t=de({}),r={},a,i,n,s,d=p0();const l=de(e);let c=[],u,_={},g;function h(m){m.forEach(b=>{b.targets.forEach(y=>{const A=s[y[0]];A&&b.event_specific_args?.length>0&&b.event_specific_args?.forEach(f=>{A.props[f]=b[f]})})})}async function x({app:m,components:b,layout:y,dependencies:A,root:f,options:j}){H(),u=m,c=b,a=new Set,i=new Set,be=[],n=new Map,o=new Map,s={},g={id:y.id,type:"column",props:{interactive:!1,scale:j.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},b.push(g),A.forEach(E=>{d.register(E.id,E.inputs,E.show_progress_on||E.outputs),E.frontend_fn=ot(E.js,!!E.backend_fn,E.inputs.length,E.outputs.length),tt(E.targets,E.id,r),rt(E,a,i)}),t.set(r),n=at(b,f),s=b.reduce((E,N)=>(E[N.id]=N,E),{}),await M(y,f,c),l.set(g),h(A)}function $({render_id:m,components:b,layout:y,root:A,dependencies:f}){b.forEach(w=>{for(const O in w.props)w.props[O]===null&&(w.props[O]=void 0)});let j=[],E=[];b.forEach(w=>{w.key==null||!_[m]?.includes(w.key)?E.push(w):j.push(w)}),at(E,A).forEach((w,O)=>{n.set(O,w)}),r={},f.forEach(w=>{d.register(w.id,w.inputs,w.outputs),w.frontend_fn=ot(w.js,!!w.backend_fn,w.inputs.length,w.outputs.length),tt(w.targets,w.id,r),rt(w,a,i)}),t.set(r);let Q=s[y.id];const v=w=>{w.children&&w.children.forEach(O=>{v(O)})};v(Q),Object.entries(s).forEach(([w,O])=>{let B=Number(w);if(O.rendered_in===m){let ne=j.find(Z=>Z.key===O.key);if(O.key!=null&&ne!==void 0){const Z=s[O.id];for(const Ee in ne.props)ne.props.preserved_by_key?.includes(Ee)||(Z.props[Ee]=ne.props[Ee])}else delete s[B],o.has(B)&&o.delete(B)}}),E.forEach(w=>{s[w.id]=w,o.set(w.id,w)}),Q.parent&&(Q.parent.children[Q.parent.children.indexOf(Q)]=s[y.id]),M(y,A,c.concat(b),Q.parent).then(()=>{l.set(g),_[m]=b.map(w=>w.key).filter(w=>w!=null)}),h(f)}async function M(m,b,y,A){const f=s[m.id];if(f.component||(f.component=(await n.get(f.component_class_id||f.type))?.default),f.parent=A,f.type==="dataset"&&(f.props.component_map=Kt(f.type,f.component_class_id,b,y,f.props.components).example_components),r[f.id]&&(f.props.attached_events=Object.keys(r[f.id])),f.props.interactive=b0(f.id,f.props.interactive,f.props.value,a,i),f.props.server=v0(f.id,f.props.server_fns,u),o.set(f.id,f),m.children&&(f.children=await Promise.all(m.children.map(j=>M(j,b,y,f)))),f.type==="tabs"&&!f.props.initial_tabs){const E=(m.children?.map((N,Q)=>{const v=s[N.id];return v.props.id??=N.id,{type:v.type,props:{...v.props,id:v.props.id,order:Q}}})||[]).filter(N=>N.type==="tabitem");f.props.initial_tabs=E?.map(N=>({label:N.props.label,id:N.props.id,visible:N.props.visible,interactive:N.props.interactive,order:N.props.order}))}return f.type==="tabs"&&m.children?.forEach((j,E)=>{const N=s[j.id];N.props.order=E}),f}let D=!1,I=de(!1);function H(){l.update(m=>{for(let b=0;b<be.length;b++)for(let y=0;y<be[b].length;y++){const A=be[b][y];if(!A)continue;const f=s[A.id];if(!f)continue;let j;A.value instanceof Map?j=new Map(A.value):A.value instanceof Set?j=new Set(A.value):Array.isArray(A.value)?j=[...A.value]:A.value==null?j=null:typeof A.value=="object"?j={...A.value}:j=A.value,f.props[A.prop]=j}return m}),be=[],D=!1,I.set(!1)}function V(m){m&&(be.push(m),D||(D=!0,I.set(!0),h0(H)))}function L(m){let b=o.get(m);if(!b){const y=wt(l);b=z(y,m)}return b?b.instance?.get_value?b.instance.get_value():b.props.value:null}function z(m,b){if(m.id===b)return m;if(m.children)for(const y of m.children){const A=z(y,b);if(A)return A}}function q(m,b){const y=o.get(m);y&&y.instance?.modify_stream_state&&y.instance.modify_stream_state(b)}function F(m){const b=o.get(m);return b?.instance?.get_stream_state?b.instance.get_stream_state():"not_set"}function ge(m,b){const y=o.get(m);y?.instance?.set_time_limit&&y.instance.set_time_limit(b)}return{layout:l,targets:t,update_value:V,get_data:L,modify_stream:q,get_stream_state:F,set_time_limit:ge,loading_status:d,scheduled_updates:I,create_layout:x,rerender_layout:$}}const g0=Object.getPrototypeOf(async function(){}).constructor;function ot(e,o,t,r){if(!e||e===!0)return null;const a=o?t===1:r===1;try{return new g0("__fn_args",`  let result = await (${e})(...__fn_args);
  if (typeof result === "undefined") return [];
  return (${a} && !Array.isArray(result)) ? [result] : result;`)}catch(i){return console.error("Could not parse custom js method."),console.error(i),null}}function tt(e,o,t){return e.forEach(([r,a])=>{t[r]||(t[r]={}),t[r]?.[a]&&!t[r]?.[a].includes(o)?t[r][a].push(o):t[r][a]=[o]}),t}function rt(e,o,t){return e.inputs.forEach(r=>o.add(r)),e.outputs.forEach(r=>t.add(r)),[o,t]}function f0(e){return Array.isArray(e)&&e.length===0||e===""||e===0||!e}function b0(e,o,t,r,a){return o===!1?!1:o===!0?!0:!!(r.has(e)||!a.has(e)&&f0(t))}function v0(e,o,t){return o?o.reduce((r,a)=>(r[a]=async(...i)=>(i.length===1&&(i=i[0]),await t.component_server(e,a,i)),r),{}):{}}function Kt(e,o,t,r,a){let i=new Map;e==="api"&&(e="state"),e==="dataset"&&a&&a.forEach(s=>{if(i.has(s))return;let d;const l=r.find(c=>c.type===s);l&&(d=Qo({api_url:t,name:s,id:l.component_class_id,variant:"example"}),i.set(s,d.component))});const n=Qo({api_url:t,name:e,id:o,variant:"component"});return{component:n.component,name:n.name,example_components:i.size>0?i:void 0}}function at(e,o){let t=new Map;return e.forEach(r=>{const{component:a,example_components:i}=Kt(r.type,r.component_class_id,o,e);if(t.set(r.component_class_id||r.type,a),i)for(const[n,s]of i)t.set(n,s)}),t}const Xt="العربية",Zt={annotated_image:"صورة مشروحة"},Jt={allow_recording_access:"يرجى السماح بالوصول إلى الميكروفون للتسجيل",audio:"صوتي",record_from_microphone:"تسجيل من الميكروفون",stop_recording:"إيقاف التسجيل",no_device_support:"لا يمكن الوصول إلى أجهزة الوسائط. تأكد من أنك تعمل على مصدر آمن (https) أو localhost (أو قمت بتمرير شهادة SSL صالحة إلى ssl_verify)، وأنك سمحت للمتصفح بالوصول إلى جهازك.",stop:"إيقاف",resume:"استئناف",record:"تسجيل",no_microphone:"لم يتم العثور على ميكروفون",pause:"إيقاف مؤقت",play:"تشغيل",waiting:"جاري الانتظار",drop_to_upload:"اسقط ملف صوت هنا للتحميل"},Yt={connection_can_break:"على الهاتف المحمول، يمكن أن ينقطع الاتصال إذا تم تغيير التبويب أو دخل الجهاز في وضع السكون، مما يؤدي إلى فقدان موقعك في قائمة الانتظار.",long_requests_queue:"هناك قائمة انتظار طويلة من الطلبات المعلقة. قم بتكرار هذا الفضاء للتخطي.",lost_connection:"فقد الاتصال بسبب مغادرة الصفحة. إعادة الانضمام إلى قائمة الانتظار...",waiting_for_inputs:"انتظار انتهاء تحميل الملف(ات)، يرجى إعادة المحاولة."},Qt={checkbox:"خانة اختيار",checkbox_group:"مجموعة خانات الاختيار"},er={code:"الكود"},or={color_picker:"منتقي الألوان"},tr={built_with:"بُني باستخدام",built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",download:"تحميل",edit:"تعديل",empty:"فارغ",error:"خطأ",hosted_on:"مستضاف على",loading:"جاري التحميل",logo:"شعار",or:"أو",remove:"إزالة",settings:"الإعدادات",share:"مشاركة",submit:"أرسل",undo:"تراجع",no_devices:"لم يتم العثور على أجهزة",language:"اللغة",display_theme:"مظهر العرض",pwa:"تطبيق ويب تقدمي"},rr={incorrect_format:"تنسيق غير صحيح، يتم دعم ملفات CSV و TSV فقط",new_column:"إضافة عمود",new_row:"صف جديد",add_row_above:"إضافة صف فوق",add_row_below:"إضافة صف تحت",add_column_left:"إضافة عمود لليسار",add_column_right:"إضافة عمود لليمين",delete_row:"حذف الصف",delete_column:"حذف العمود",sort_column:"فرز العمود",sort_ascending:"فرز تصاعدي",sort_descending:"فرز تنازلي",drop_to_upload:"اسقط ملفات CSV أو TSV هنا لاستيراد البيانات إلى الإطار الجدولية",clear_sort:"مسح الترتيب"},ar={dropdown:"قائمة منسدلة"},ir={build_error:"هناك خطأ في البناء",config_error:"هناك خطأ في التكوين",contact_page_author:"يرجى الاتصال بمؤلف الصفحة",no_app_file:"لا يوجد ملف تطبيق",runtime_error:"هناك خطأ في وقت التشغيل",space_not_working:"المساحة لا تعمل لأن {0}",space_paused:"المساحة متوقفة مؤقتًا",use_via_api:"استخدم عبر API"},nr={uploading:"جاري الرفع..."},sr={highlighted_text:"نص مميز"},lr={allow_webcam_access:"يرجى السماح بالوصول إلى كاميرا الويب للتسجيل.",brush_color:"لون الفرشاة",brush_radius:"حجم الفرشاة",image:"صورة",remove_image:"إزالة الصورة",select_brush_color:"اختر لون الفرشاة",start_drawing:"ابدأ الرسم",use_brush:"استخدم الفرشاة",drop_to_upload:"اسقط ملف الصورة هنا لتحميله"},cr={label:"ملصق"},dr={enable_cookies:"إذا كنت تزور مساحة HuggingFace في وضع التصفح المتخفي، يجب عليك تمكين ملفات تعريف الارتباط من الجهات الخارجية.",incorrect_credentials:"بيانات الاعتماد غير صحيحة",username:"اسم المستخدم",password:"كلمة المرور",login:"تسجيل الدخول"},ur={number:"رقم"},_r={plot:"رسم بياني"},pr={radio:"زر راديو"},mr={slider:"شريط التمرير"},hr={click_to_upload:"انقر للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا",drop_gallery:"أسقط الوسائط هنا",paste_clipboard:"لصق من الحافظة"},gr={drop_to_upload:"اسقط ملف الفيديو هنا للتحميل"},fr={edit:"تعديل",retry:"إعادة المحاولة",undo:"تراجع",submit:"إرسال",cancel:"إلغاء",like:"إعجاب",dislike:"عدم الإعجاب",clear:"مسح المحادثة"},w0={_name:Xt,"3D_model":{"3d_model":"نموذج ثلاثي الأبعاد",drop_to_upload:"اسقط ملف نموذج 3D (.obj، .glb، .stl، .gltf، .splat، أو .ply) هنا للتحميل"},annotated_image:Zt,audio:Jt,blocks:Yt,checkbox:Qt,code:er,color_picker:or,common:tr,dataframe:rr,dropdown:ar,errors:ir,file:nr,highlighted_text:sr,image:lr,label:cr,login:dr,number:ur,plot:_r,radio:pr,slider:mr,upload_text:hr,video:gr,chatbot:fr},y0=Object.freeze(Object.defineProperty({__proto__:null,_name:Xt,annotated_image:Zt,audio:Jt,blocks:Yt,chatbot:fr,checkbox:Qt,code:er,color_picker:or,common:tr,dataframe:rr,default:w0,dropdown:ar,errors:ir,file:nr,highlighted_text:sr,image:lr,label:cr,login:dr,number:ur,plot:_r,radio:pr,slider:mr,upload_text:hr,video:gr},Symbol.toStringTag,{value:"Module"})),br="Català",vr={annotated_image:"Imatge Anotada"},wr={allow_recording_access:"Si us plau, permeteu l'accés al micròfon per gravar.",audio:"Àudio",record_from_microphone:"Gravar des del micròfon",stop_recording:"Aturar la gravació",no_device_support:"No s'ha pogut accedir als dispositius multimèdia. Comproveu que esteu executant en un origen segur (https) o localhost (o heu passat un certificat SSL vàlid a ssl_verify), i que heu permès l'accés del navegador al vostre dispositiu.",stop:"Aturar",resume:"Reprendre",record:"Gravar",no_microphone:"No s'ha trobat cap micròfon",pause:"Pausa",play:"Reproduir",waiting:"Esperant",drop_to_upload:"Arrossega un fitxer d'àudio aquí per pujar-lo"},yr={connection_can_break:"En dispositius mòbils, la connexió es pot trencar si aquesta pestanya perd el focus o el dispositiu s'adorm, perdent la vostra posició a la cua.",long_requests_queue:"Hi ha una llarga cua de sol·licituds pendents. Dupliqueu aquest Space per saltar-la.",lost_connection:"S'ha perdut la connexió en deixar la pàgina. Tornant a la cua...",waiting_for_inputs:"Esperant la finalització de la pujada dels fitxers, si us plau, torna a provar."},kr={checkbox:"Casella de selecció",checkbox_group:"Grup de caselles"},Sr={code:"Codi"},xr={color_picker:"Selector de color"},Er={built_with:"construït amb",built_with_gradio:"Construït amb Gradio",clear:"Neteja",download:"Descarregar",edit:"Editar",empty:"Buit",error:"Error",hosted_on:"Allotjat a",loading:"S'està carregant",logo:"logotip",or:"o",remove:"Eliminar",settings:"Configuració",share:"Compartir",submit:"Envia",undo:"Desfer",no_devices:"No s'han trobat dispositius",language:"Idioma",display_theme:"Tema de visualització",pwa:"Aplicació web progressiva"},$r={incorrect_format:"Format incorrecte, només s'admeten fitxers CSV i TSV",new_column:"Afegir columna",new_row:"Nova fila",add_row_above:"Afegir fila a dalt",add_row_below:"Afegir fila a baix",add_column_left:"Afegir columna a l'esquerra",add_column_right:"Afegir columna a la dreta",delete_row:"Esborrar fila",delete_column:"Suprimeix la columna",sort_column:"Ordena la columna",sort_ascending:"Ordena ascendent",sort_descending:"Ordena descendent",drop_to_upload:"Arrossega fitxers CSV o TSV aquí per importar dades al dataframe",clear_sort:"Neteja la classificació"},Pr={dropdown:"Desplegable"},Ar={build_error:"hi ha un error de compilació",config_error:"hi ha un error de configuració",contact_page_author:"Si us plau, contacteu amb l'autor de la pàgina per informar-lo.",no_app_file:"no hi ha fitxer d'aplicació",runtime_error:"hi ha un error d'execució",space_not_working:`"L'Space no funciona perquè" {0}`,space_paused:"l'space està en pausa",use_via_api:"Utilitzar via API"},Tr={uploading:"Pujant..."},Or={highlighted_text:"Text ressaltat"},jr={allow_webcam_access:"Si us plau, permeteu l'accés a la càmera web per gravar.",brush_color:"Color del pinzell",brush_radius:"Radi del pinzell",image:"Imatge",remove_image:"Eliminar imatge",select_brush_color:"Seleccionar color del pinzell",start_drawing:"Començar a dibuixar",use_brush:"Utilitzar pinzell",drop_to_upload:"Arrossega un fitxer d'imatge aquí per pujar-lo"},Cr={label:"Etiqueta"},Lr={enable_cookies:"Si esteu visitant un Space de HuggingFace en mode Incògnit, heu d'habilitar les cookies de tercers.",incorrect_credentials:"Credencials incorrectes",username:"nom d'usuari",password:"contrasenya",login:"Iniciar sessió"},Dr={number:"Número"},Ir={plot:"Gràfic"},zr={radio:"Ràdio"},Rr={slider:"Control lliscant"},Nr={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí",drop_gallery:"Deixeu anar els mitjans aquí",paste_clipboard:"Enganxar del porta-retalls"},Hr={drop_to_upload:"Arrossega un fitxer de vídeo aquí per pujar-lo"},Br={edit:"Editar",retry:"Tornar a provar",undo:"Desfer",submit:"Enviar",cancel:"Cancel·lar",like:"M'agrada",dislike:"No m'agrada",clear:"Netejar xat"},k0={_name:br,"3D_model":{"3d_model":"Model 3D",drop_to_upload:"Arrossega un model 3D (.obj, .glb, .stl, .gltf, .splat o .ply) aquí per pujar-lo."},annotated_image:vr,audio:wr,blocks:yr,checkbox:kr,code:Sr,color_picker:xr,common:Er,dataframe:$r,dropdown:Pr,errors:Ar,file:Tr,highlighted_text:Or,image:jr,label:Cr,login:Lr,number:Dr,plot:Ir,radio:zr,slider:Rr,upload_text:Nr,video:Hr,chatbot:Br},S0=Object.freeze(Object.defineProperty({__proto__:null,_name:br,annotated_image:vr,audio:wr,blocks:yr,chatbot:Br,checkbox:kr,code:Sr,color_picker:xr,common:Er,dataframe:$r,default:k0,dropdown:Pr,errors:Ar,file:Tr,highlighted_text:Or,image:jr,label:Cr,login:Lr,number:Dr,plot:Ir,radio:zr,slider:Rr,upload_text:Nr,video:Hr},Symbol.toStringTag,{value:"Module"})),Mr="کوردی",Vr={annotated_image:"وێنەی نیشانە کراو"},qr={allow_recording_access:"تکایە ڕێگە بدە بە دەستگەیشتن بە مایکرۆفۆن بۆ تۆمارکردن",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکرۆفۆنەوە",stop_recording:"تۆمارکردن بوەستێنە",no_device_support:"ناتوانرێت دەستبگات بە ئامێرەکانی میدیا. دڵنیابە لەوەی کە لەسەر سەرچاوەیەکی پارێزراو (https) یان localhost کاردەکەیت (یان بڕوانامەیەکی SSL دروستت داوە)، و ڕێگەت داوە بە وێبگەڕەکە دەستی بگات بە ئامێرەکەت.",stop:"وەستان",resume:"بەردەوامبوون",record:"تۆمارکردن",no_microphone:"هیچ مایکرۆفۆنێک نەدۆزرایەوە",pause:"وەستاندنی کاتی",play:"لێدان",waiting:"چاوەڕوانی",drop_to_upload:"فایلی دەنگی لێرە دابنێ بۆ بارکردن"},Gr={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. گەڕانەوە بۆ ڕیز...",waiting_for_inputs:"بەخێربێی بۆ فایل(ەکان) دەستپێکردن، هەڵبژارە دووبارە هەوڵ بدە."},Ur={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},Fr={code:"کۆد"},Wr={color_picker:"هەڵبژاردنی ڕەنگ"},Kr={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",settings:"ڕێکخستنەکان",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە",no_devices:"هیچ ئامێرێک نەدۆزرایەوە",language:"زمان",display_theme:"ڕووکاری نیشاندان",pwa:"بەرنامەی وێبی پێشکەوتوو"},Xr={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"زیادکردنی ستوون",new_row:"ڕیزی نوێ",add_row_above:"زیادکردنی ڕیز لە سەرەوە",add_row_below:"زیادکردنی ڕیز لە خوارەوە",add_column_left:"زیادکردنی ستوون لە چەپەوە",add_column_right:"زیادکردنی ستوون لە ڕاستەوە",delete_row:"ڕووی ڕاکردن",delete_column:"کۆلۆنی ڕاکردن",sort_column:"کۆلۆنی ڕێکەوە کردن",sort_ascending:"بەرچاوەی ڕوونی",sort_descending:"سەرچاوەکە بەپاشی بەرگرێڕەوە بەڕێوە بکە",drop_to_upload:"فایلەکانی CSV یان TSV لەگەڵ ئەمە بکەوە تا داتا بەرچوون بێت لەوەی دەیتە فرەیمەکەدا",clear_sort:"ڕاژکردنی ڕوونکردن"},Zr={dropdown:"لیستی داکەوتوو"},Jr={build_error:"هەڵەیەک هەیە لە دروستکردندا",config_error:"هەڵەیەک هەیە لە ڕێکخستندا",contact_page_author:"تکایە پەیوەندی بکە بە نووسەری لاپەڕەکە بۆ ئاگادارکردنەوە.",no_app_file:"فایلی بەرنامە نییە",runtime_error:"هەڵەیەک هەیە لە کاتی جێبەجێکردندا",space_not_working:'"سپەیسەکە کار ناکات چونکە" {0}',space_paused:"سپەیسەکە وەستێنراوە",use_via_api:"بەکارهێنان لە ڕێگەی API"},Yr={uploading:"بارکردن..."},Qr={highlighted_text:"دەقی دیاریکراو"},ea={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی کامێرای وێب بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە",drop_to_upload:"فایل وێنەیەک هەروەها لەمەکە دەڕوونە بۆ بارکردنەوە"},oa={label:"لەیبڵ"},ta={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"زانیاری چوونەژوورەوە هەڵەیە",username:"ناوی بەکارهێنەر",password:"وشەی نهێنی",login:"چوونە ژوورەوە"},ra={number:"ژمارە"},aa={plot:"هێڵکاری"},ia={radio:"ڕادیۆ"},na={slider:"سلایدەر"},sa={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ",drop_gallery:"میدیا لێرە دابنێ",paste_clipboard:"لکاندن لە کلیپبۆردەوە"},la={drop_to_upload:"فایل ویدیۆییەکەت هەنە بکەرەوە بۆ بارکردنەوە"},ca={edit:"دەستکاری",retry:"هەوڵدانەوە",undo:"گەڕانەوە",submit:"ناردن",cancel:"هەڵوەشاندنەوە",like:"پەسەند",dislike:"ناپەسەند",clear:"پاککردنەوەی گفتوگۆ"},x0={_name:Mr,"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی",drop_to_upload:"فایل مۆدێلی ٣دی (.obj، .glb، .stl، .gltf، .splat یان .ply) لەمە هێرە بکە لەبەر بارکردنی."},annotated_image:Vr,audio:qr,blocks:Gr,checkbox:Ur,code:Fr,color_picker:Wr,common:Kr,dataframe:Xr,dropdown:Zr,errors:Jr,file:Yr,highlighted_text:Qr,image:ea,label:oa,login:ta,number:ra,plot:aa,radio:ia,slider:na,upload_text:sa,video:la,chatbot:ca},E0=Object.freeze(Object.defineProperty({__proto__:null,_name:Mr,annotated_image:Vr,audio:qr,blocks:Gr,chatbot:ca,checkbox:Ur,code:Fr,color_picker:Wr,common:Kr,dataframe:Xr,default:x0,dropdown:Zr,errors:Jr,file:Yr,highlighted_text:Qr,image:ea,label:oa,login:ta,number:ra,plot:aa,radio:ia,slider:na,upload_text:sa,video:la},Symbol.toStringTag,{value:"Module"})),da="Deutsch",ua={annotated_image:"Annotiertes Bild"},_a={allow_recording_access:"Bitte erlauben Sie den Zugriff auf das Mikrofon für die Aufnahme.",audio:"Audio",record_from_microphone:"Vom Mikrofon aufnehmen",stop_recording:"Aufnahme stoppen",no_device_support:"Auf Mediengeräte konnte nicht zugegriffen werden. Stellen Sie sicher, dass Sie sich auf einer sicheren Quelle (https) oder localhost befinden (oder ein gültiges SSL-Zertifikat an ssl_verify übergeben haben) und Sie dem Browser den Zugriff auf Ihr Gerät erlaubt haben.",stop:"Stopp",resume:"Fortsetzen",record:"Aufnehmen",no_microphone:"Kein Mikrofon gefunden",pause:"Pause",play:"Abspielen",waiting:"Warten",drop_to_upload:"Laden Sie eine Audiodatei hier ab, um sie hochzuladen."},pa={connection_can_break:"Auf Mobilgeräten kann die Verbindung unterbrochen werden, wenn dieser Tab den Fokus verliert oder das Gerät in den Ruhezustand geht, wodurch Ihre Position in der Warteschlange verloren geht.",long_requests_queue:"Es gibt eine lange Warteschlange ausstehender Anfragen. Duplizieren Sie diesen Space zum Überspringen.",lost_connection:"Verbindung durch Verlassen der Seite verloren. Kehre zur Warteschlange zurück...",waiting_for_inputs:"Warten auf das Hochladen der Datei(en), bitte versuchen Sie es erneut."},ma={checkbox:"Kontrollkästchen",checkbox_group:"Kontrollkästchengruppe"},ha={code:"Code"},ga={color_picker:"Farbwähler"},fa={built_with:"erstellt mit",built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",download:"Herunterladen",edit:"Bearbeiten",empty:"Leer",error:"Fehler",hosted_on:"Gehostet auf",loading:"Laden",logo:"Logo",or:"oder",remove:"Entfernen",settings:"Einstellungen",share:"Teilen",submit:"Absenden",undo:"Rückgängig",no_devices:"Keine Geräte gefunden",language:"Sprache",display_theme:"Anzeigedesign",pwa:"Progressive Web App"},ba={incorrect_format:"Falsches Format, nur CSV- und TSV-Dateien werden unterstützt",new_column:"Spalte hinzufügen",new_row:"Neue Zeile",add_row_above:"Zeile oben hinzufügen",add_row_below:"Zeile unten hinzufügen",add_column_left:"Spalte links hinzufügen",add_column_right:"Spalte rechts hinzufügen",delete_row:"Zeile löschen",delete_column:"Spalte löschen",sort_column:"Spalte sortieren",sort_ascending:"Aufsteigend sortieren",sort_descending:"Absteigend sortieren",drop_to_upload:"CSV- oder TSV-Dateien hier ablegen, um Daten in den DataFrame zu importieren.",clear_sort:"Sortierung aufheben"},va={dropdown:"Dropdown-Menü"},wa={build_error:"Es gibt einen Build-Fehler",config_error:"Es gibt einen Konfigurationsfehler",contact_page_author:"Bitte kontaktieren Sie den Autor der Seite.",no_app_file:"Es gibt keine App-Datei",runtime_error:"Es gibt einen Laufzeitfehler",space_not_working:'"Space funktioniert nicht, weil" {0}',space_paused:"Der Space ist pausiert",use_via_api:"Über API verwenden"},ya={uploading:"Hochladen..."},ka={highlighted_text:"Hervorgehobener Text"},Sa={allow_webcam_access:"Bitte erlauben Sie den Zugriff auf die Webcam für die Aufnahme.",brush_color:"Pinselfarbe",brush_radius:"Pinselgröße",image:"Bild",remove_image:"Bild entfernen",select_brush_color:"Pinselfarbe auswählen",start_drawing:"Zeichnen beginnen",use_brush:"Pinsel verwenden",drop_to_upload:"Laden Sie ein Bild hier ab, um es hochzuladen."},xa={label:"Beschriftung"},Ea={enable_cookies:"Wenn Sie einen HuggingFace Space im Inkognito-Modus besuchen, müssen Sie Cookies von Drittanbietern aktivieren.",incorrect_credentials:"Falsche Anmeldedaten",username:"Benutzername",password:"Passwort",login:"Anmelden"},$a={number:"Zahl"},Pa={plot:"Diagramm"},Aa={radio:"Optionsfeld"},Ta={slider:"Schieberegler"},Oa={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen",drop_gallery:"Medien hier ablegen",paste_clipboard:"Aus Zwischenablage einfügen"},ja={drop_to_upload:"Laden Sie eine Video-Datei hier ab, um sie hochzuladen."},Ca={edit:"Bearbeiten",retry:"Wiederholen",undo:"Rückgängig",submit:"Senden",cancel:"Abbrechen",like:"Gefällt mir",dislike:"Gefällt mir nicht",clear:"Chat leeren"},$0={_name:da,"3D_model":{"3d_model":"3D-Modell",drop_to_upload:"Laden Sie eine 3D-Modell-Datei (.obj, .glb, .stl, .gltf, .splat oder .ply) hier ab, um sie hochzuladen."},annotated_image:ua,audio:_a,blocks:pa,checkbox:ma,code:ha,color_picker:ga,common:fa,dataframe:ba,dropdown:va,errors:wa,file:ya,highlighted_text:ka,image:Sa,label:xa,login:Ea,number:$a,plot:Pa,radio:Aa,slider:Ta,upload_text:Oa,video:ja,chatbot:Ca},P0=Object.freeze(Object.defineProperty({__proto__:null,_name:da,annotated_image:ua,audio:_a,blocks:pa,chatbot:Ca,checkbox:ma,code:ha,color_picker:ga,common:fa,dataframe:ba,default:$0,dropdown:va,errors:wa,file:ya,highlighted_text:ka,image:Sa,label:xa,login:Ea,number:$a,plot:Pa,radio:Aa,slider:Ta,upload_text:Oa,video:ja},Symbol.toStringTag,{value:"Module"})),La="English",Da={annotated_image:"Annotated Image"},Ia={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",drop_to_upload:"Drop an audio file here to upload",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play",waiting:"Waiting"},za={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue...",waiting_for_inputs:"Waiting for file(s) to finish uploading, please retry."},Ra={edit:"Edit",retry:"Retry",undo:"Undo",submit:"Submit",cancel:"Cancel",like:"Like",dislike:"Dislike",clear:"Clear"},Na={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},Ha={code:"Code"},Ba={color_picker:"Color Picker"},Ma={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",settings:"Settings",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found",language:"Language",display_theme:"Display Theme",pwa:"Progressive Web App",record:"Record",stop_recording:"Stop Recording",screen_studio:"Screen Studio",share_gradio_tab:"[Sharing] Gradio Tab",run:"Run"},Va={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"Add column",new_row:"New row",add_row_above:"Add row above",add_row_below:"Add row below",delete_row:"Delete row",delete_column:"Delete column",add_column_left:"Add column to the left",add_column_right:"Add column to the right",sort_column:"Sort column",sort_ascending:"Sort ascending",sort_descending:"Sort descending",drop_to_upload:"Drop CSV or TSV files here to import data into dataframe",clear_sort:"Clear sort"},qa={dropdown:"Dropdown"},Ga={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API",use_via_api_or_mcp:"Use via API or MCP"},Ua={uploading:"Uploading..."},Fa={highlighted_text:"Highlighted Text"},Wa={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush",drop_to_upload:"Drop an image file here to upload"},Ka={label:"Label"},Xa={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",username:"username",password:"password",login:"Login"},Za={number:"Number"},Ja={plot:"Plot"},Ya={radio:"Radio"},Qa={slider:"Slider"},ei={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Media Here",paste_clipboard:"Paste from Clipboard"},oi={drop_to_upload:"Drop a video file here to upload"},A0={_name:La,"3D_model":{"3d_model":"3D Model",drop_to_upload:"Drop a 3D model (.obj, .glb, .stl, .gltf, .splat, or .ply) file here to upload"},annotated_image:Da,audio:Ia,blocks:za,chatbot:Ra,checkbox:Na,code:Ha,color_picker:Ba,common:Ma,dataframe:Va,dropdown:qa,errors:Ga,file:Ua,highlighted_text:Fa,image:Wa,label:Ka,login:Xa,number:Za,plot:Ja,radio:Ya,slider:Qa,upload_text:ei,video:oi},T0=Object.freeze(Object.defineProperty({__proto__:null,_name:La,annotated_image:Da,audio:Ia,blocks:za,chatbot:Ra,checkbox:Na,code:Ha,color_picker:Ba,common:Ma,dataframe:Va,default:A0,dropdown:qa,errors:Ga,file:Ua,highlighted_text:Fa,image:Wa,label:Ka,login:Xa,number:Za,plot:Ja,radio:Ya,slider:Qa,upload_text:ei,video:oi},Symbol.toStringTag,{value:"Module"})),ti="Español",ri={annotated_image:"Imagen Anotada"},ai={allow_recording_access:"Por favor, permita el acceso al micrófono para grabar.",audio:"Audio",record_from_microphone:"Grabar desde el micrófono",stop_recording:"Detener grabación",no_device_support:"No se pudo acceder a los dispositivos multimedia. Compruebe que está ejecutando en un origen seguro (https) o localhost (o que ha pasado un certificado SSL válido a ssl_verify), y que ha permitido el acceso del navegador a su dispositivo.",stop:"Detener",resume:"Reanudar",record:"Grabar",no_microphone:"No se encontró micrófono",pause:"Pausar",play:"Reproducir",waiting:"Esperando",drop_to_upload:"Arrastra un archivo de audio aquí para subirlo"},ii={connection_can_break:"En dispositivos móviles, la conexión puede interrumpirse si esta pestaña pierde el foco o el dispositivo entra en reposo, perdiendo su posición en la cola.",long_requests_queue:"Hay una larga cola de solicitudes pendientes. Duplique este Space para saltarse la cola.",lost_connection:"Se perdió la conexión al abandonar la página. Volviendo a la cola...",waiting_for_inputs:"Esperando a que se complete(n) la(s) carga(s) de archivo(s), por favor inténtelo de nuevo."},ni={checkbox:"Casilla de verificación",checkbox_group:"Grupo de casillas"},si={code:"Código"},li={color_picker:"Selector de color"},ci={built_with:"construido con",built_with_gradio:"Construido con Gradio",clear:"Limpiar",download:"Descargar",edit:"Editar",empty:"Vacío",error:"Error",flag:"Marcar",hosted_on:"Alojado en",loading:"Cargando",logo:"logo",or:"o",remove:"Eliminar",settings:"Configuración",share:"Compartir",submit:"Enviar",undo:"Deshacer",no_devices:"No se encontraron dispositivos",language:"Idioma",display_theme:"Tema de visualización",pwa:"Aplicación web progresiva"},di={incorrect_format:"Formato incorrecto, solo se admiten archivos CSV y TSV",new_column:"Agregar columna",new_row:"Nueva fila",add_row_above:"Agregar fila arriba",add_row_below:"Agregar fila abajo",add_column_left:"Agregar columna a la izquierda",add_column_right:"Agregar columna a la derecha",delete_row:"Eliminar fila",delete_column:"Eliminar columna",sort_column:"Ordenar columna",sort_ascending:"Ordenar ascendente",sort_descending:"Ordenar descendente",drop_to_upload:"Arrastra y suelta los archivos CSV o TSV aquí para importar datos al dataframe",clear_sort:"Limpiar ordenamiento"},ui={dropdown:"Menú desplegable"},_i={build_error:"hay un error de compilación",config_error:"hay un error de configuración",contact_page_author:"Por favor, contacte al autor de la página para informarle.",no_app_file:"no hay archivo de aplicación",runtime_error:"hay un error de ejecución",space_not_working:'"El Space no funciona porque" {0}',space_paused:"el space está pausado",use_via_api:"Usar vía API",use_via_api_or_mcp:"Usar vía API o MCP"},pi={uploading:"Subiendo..."},mi={highlighted_text:"Texto resaltado"},hi={allow_webcam_access:"Por favor, permita el acceso a la cámara web para grabar.",brush_color:"Color del pincel",brush_radius:"Radio del pincel",image:"Imagen",remove_image:"Eliminar imagen",select_brush_color:"Seleccionar color del pincel",start_drawing:"Empezar a dibujar",use_brush:"Usar pincel",drop_to_upload:"Arrastra un archivo de imagen aquí para subirlo"},gi={label:"Etiqueta"},fi={enable_cookies:"Si está visitando un Space de HuggingFace en modo Incógnito, debe habilitar las cookies de terceros.",incorrect_credentials:"Credenciales incorrectas",username:"usuario",password:"contraseña",login:"Iniciar sesión"},bi={number:"Número"},vi={plot:"Gráfico"},wi={radio:"Radio"},yi={slider:"Deslizador"},ki={click_to_upload:"Haga clic para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí",drop_gallery:"Coloque las imágenes aquí",paste_clipboard:"Pegar desde el portapapeles"},Si={drop_to_upload:"Arrastra un archivo de video aquí para subirlo"},xi={edit:"Editar",retry:"Reintentar",undo:"Deshacer",submit:"Enviar",cancel:"Cancelar",like:"Me gusta",dislike:"No me gusta",clear:"Limpiar chat"},O0={_name:ti,"3D_model":{"3d_model":"Modelo 3D",drop_to_upload:"Arrastra y suelta un archivo de modelo 3D (.obj, .glb, .stl, .gltf, .splat o .ply) aquí para cargarlo."},annotated_image:ri,audio:ai,blocks:ii,checkbox:ni,code:si,color_picker:li,common:ci,dataframe:di,dropdown:ui,errors:_i,file:pi,highlighted_text:mi,image:hi,label:gi,login:fi,number:bi,plot:vi,radio:wi,slider:yi,upload_text:ki,video:Si,chatbot:xi},j0=Object.freeze(Object.defineProperty({__proto__:null,_name:ti,annotated_image:ri,audio:ai,blocks:ii,chatbot:xi,checkbox:ni,code:si,color_picker:li,common:ci,dataframe:di,default:O0,dropdown:ui,errors:_i,file:pi,highlighted_text:mi,image:hi,label:gi,login:fi,number:bi,plot:vi,radio:wi,slider:yi,upload_text:ki,video:Si},Symbol.toStringTag,{value:"Module"})),Ei="Euskara",$i={annotated_image:"Irudi Erantsia"},Pi={allow_recording_access:"Mesedez, baimendu mikrofonoaren sarbidea grabatzeko.",audio:"Audioa",record_from_microphone:"Mikrofonotik grabatu",stop_recording:"Grabaketa gelditu",no_device_support:"Ezin izan da multimedia gailuetara sartu. Ziurtatu jatorri seguru batean (https) edo localhost-en exekutatzen ari zarela (edo SSL ziurtagiri baliozkoa eman diozula ssl_verify-ri), eta nabigatzaileari zure gailura sarbidea eman diozula.",stop:"Gelditu",resume:"Jarraitu",record:"Grabatu",no_microphone:"Ez da mikrofonorik aurkitu",pause:"Pausatu",play:"Erreproduzitu",waiting:"Itxaroten",drop_to_upload:"Arrastatu audio fitxategia hemen igotzeko"},Ai={connection_can_break:"Mugikorretan, konexioa eten daiteke fitxa honek fokua galtzen badu edo gailua lokartu egiten bada, ilaran duzun posizioa galduz.",long_requests_queue:"Eskaera ilara luzea dago zain. Space hau bikoiztu saltatzeko.",lost_connection:"Konexioa galdu da orria uzteagatik. Ilarara itzultzen...",waiting_for_inputs:"Itxaron fitxategiak igo arte, saiatu berriro mesedez"},Ti={checkbox:"Kontrol-laukia",checkbox_group:"Kontrol-laukien taldea"},Oi={code:"Kodea"},ji={color_picker:"Kolore hautatzailea"},Ci={built_with:"honekin eraikia",built_with_gradio:"Gradio-rekin eraikia",clear:"Garbitu",download:"Deskargatu",edit:"Editatu",empty:"Hutsik",error:"Errorea",hosted_on:"Ostatatuta",loading:"Kargatzen",logo:"logoa",or:"edo",remove:"Kendu",settings:"Ezarpenak",share:"Partekatu",submit:"Bidali",undo:"Desegin",no_devices:"Ez da gailurik aurkitu",language:"Hizkuntza",display_theme:"Bistaratze gaia",pwa:"Web Aplikazio Aurreratua"},Li={incorrect_format:"Formatu okerra, CSV eta TSV fitxategiak soilik onartzen dira",new_column:"Zutabea gehitu",new_row:"Errenkada berria",add_row_above:"Errenkada gehitu gainean",add_row_below:"Errenkada gehitu azpian",add_column_left:"Zutabea gehitu ezkerrean",add_column_right:"Zutabea gehitu eskuinean",delete_row:"Ezabatu errenkila",delete_column:"Ezabatu zutabea",sort_column:"Ordenatu zutabea",sort_ascending:"Behetik gorako ordena",sort_descending:"Goitik beherako ordena",drop_to_upload:"Arrastatu CSV edo TSV fitxategiak hemen datuak dataframe-an inportatzeko",clear_sort:"Garbitu ordena"},Di={dropdown:"Goitibeherako menua"},Ii={build_error:"eraikitze errore bat dago",config_error:"konfigurazio errore bat dago",contact_page_author:"Mesedez, jarri harremanetan orriaren egilearekin jakinarazteko.",no_app_file:"ez dago aplikazio fitxategirik",runtime_error:"exekuzio errore bat dago",space_not_working:'"Space-a ez dabil honako arrazoi honengatik:" {0}',space_paused:"space-a pausatuta dago",use_via_api:"API bidez erabili"},zi={uploading:"Igotzen..."},Ri={highlighted_text:"Nabarmendutako testua"},Ni={allow_webcam_access:"Mesedez, baimendu web-kameraren sarbidea grabatzeko.",brush_color:"Pintzelaren kolorea",brush_radius:"Pintzelaren tamaina",image:"Irudia",remove_image:"Irudia kendu",select_brush_color:"Aukeratu pintzelaren kolorea",start_drawing:"Hasi marrazten",use_brush:"Pintzela erabili",drop_to_upload:"Arrastatu irudi fitxategia hemen igotzeko"},Hi={label:"Etiketa"},Bi={enable_cookies:"HuggingFace Space bat Inkognito moduan bisitatzen ari bazara, hirugarrenen cookieak gaitu behar dituzu.",incorrect_credentials:"Kredentzial okerrak",username:"erabiltzaile izena",password:"pasahitza",login:"Saioa hasi"},Mi={number:"Zenbakia"},Vi={plot:"Grafikoa"},qi={radio:"Aukera botoia"},Gi={slider:"Graduatzailea"},Ui={click_to_upload:"Klikatu igotzeko",drop_audio:"Utzi audioa hemen",drop_csv:"Utzi CSV-a hemen",drop_file:"Utzi fitxategia hemen",drop_image:"Utzi irudia hemen",drop_video:"Utzi bideoa hemen",drop_gallery:"Utzi multimedia hemen",paste_clipboard:"Itsatsi arbeleko edukia"},Fi={drop_to_upload:"Arrastatu bideo fitxategia hemen igotzeko"},Wi={edit:"Editatu",retry:"Saiatu berriro",undo:"Desegin",submit:"Bidali",cancel:"Utzi",like:"Gustuko dut",dislike:"Ez dut gustuko",clear:"Garbitu txata"},C0={_name:Ei,"3D_model":{"3d_model":"3D Modeloa",drop_to_upload:"Arrastatu 3D modelo bat (.obj, .glb, .stl, .gltf, .splat edo .ply) igotzeko."},annotated_image:$i,audio:Pi,blocks:Ai,checkbox:Ti,code:Oi,color_picker:ji,common:Ci,dataframe:Li,dropdown:Di,errors:Ii,file:zi,highlighted_text:Ri,image:Ni,label:Hi,login:Bi,number:Mi,plot:Vi,radio:qi,slider:Gi,upload_text:Ui,video:Fi,chatbot:Wi},L0=Object.freeze(Object.defineProperty({__proto__:null,_name:Ei,annotated_image:$i,audio:Pi,blocks:Ai,chatbot:Wi,checkbox:Ti,code:Oi,color_picker:ji,common:Ci,dataframe:Li,default:C0,dropdown:Di,errors:Ii,file:zi,highlighted_text:Ri,image:Ni,label:Hi,login:Bi,number:Mi,plot:Vi,radio:qi,slider:Gi,upload_text:Ui,video:Fi},Symbol.toStringTag,{value:"Module"})),Ki="فارسی",Xi={annotated_image:"تصویر حاشیه‌نویسی شده"},Zi={allow_recording_access:"لطفاً برای ضبط صدا، دسترسی به میکروفون را مجاز کنید.",audio:"صدا",record_from_microphone:"ضبط از میکروفون",stop_recording:"توقف ضبط",no_device_support:"دسترسی به دستگاه‌های رسانه‌ای امکان‌پذیر نیست. اطمینان حاصل کنید که در یک منبع امن (https) یا localhost اجرا می‌شود (یا یک گواهی SSL معتبر به ssl_verify داده‌اید)، و به مرورگر اجازه دسترسی به دستگاه خود را داده‌اید.",stop:"توقف",resume:"ادامه",record:"ضبط",no_microphone:"میکروفونی یافت نشد",pause:"مکث",play:"پخش",waiting:"در حال انتظار",drop_to_upload:"فایل صوتی خود را اینجا رها کنید تا بارگذاری شود"},Ji={connection_can_break:"در موبایل، اگر این تب از حالت فعال خارج شود یا دستگاه به خواب برود، اتصال می‌تواند قطع شود و موقعیت شما در صف از دست می‌رود.",long_requests_queue:"صف طولانی از درخواست‌های در انتظار وجود دارد. برای رد کردن صف، این Space را تکثیر کنید.",lost_connection:"اتصال به دلیل ترک صفحه قطع شد. در حال بازگشت به صف...",waiting_for_inputs:"در انتظار به پایان رسیدن بارگذاری فایل‌ها، لطفاً مجدداً امتحان کنید."},Yi={checkbox:"چک‌باکس",checkbox_group:"گروه چک‌باکس"},Qi={code:"کد"},en={color_picker:"انتخابگر رنگ"},on={built_with:"ساخته شده با",built_with_gradio:"ساخته شده با Gradio",clear:"پاک کردن",download:"دانلود",edit:"ویرایش",empty:"خالی",error:"خطا",hosted_on:"میزبانی شده در",loading:"در حال بارگذاری",logo:"لوگو",or:"یا",remove:"حذف",settings:"تنظیمات",share:"اشتراک‌گذاری",submit:"ارسال",undo:"واگرد",no_devices:"هیچ دستگاهی یافت نشد",language:"زبان",display_theme:"تم نمایش",pwa:"وب‌اپلیکیشن پیشرونده"},tn={incorrect_format:"فرمت نادرست، فقط فایل‌های CSV و TSV پشتیبانی می‌شوند",new_column:"افزودن ستون",new_row:"سطر جدید",add_row_above:"افزودن سطر در بالا",add_row_below:"افزودن سطر در پایین",add_column_left:"افزودن ستون در چپ",add_column_right:"افزودن ستون در راست",delete_row:"حذف سطر",delete_column:"حذف ستون",sort_column:"ستون را مرتب کنید",sort_ascending:"مرتب‌سازی صعودی",sort_descending:"مرتب‌سازی نزولی",drop_to_upload:"فایل‌های CSV یا TSV را اینجا رها کنید تا داده‌ها را به دیتافریم وارد کنید",clear_sort:"مرتب‌سازی را پاک کنید"},rn={dropdown:"منوی کشویی"},an={build_error:"خطای ساخت وجود دارد",config_error:"خطای پیکربندی وجود دارد",contact_page_author:"لطفاً با نویسنده صفحه تماس بگیرید تا به او اطلاع دهید.",no_app_file:"فایل برنامه وجود ندارد",runtime_error:"خطای زمان اجرا وجود دارد",space_not_working:'"Space کار نمی‌کند زیرا" {0}',space_paused:"Space متوقف شده است",use_via_api:"استفاده از طریق API"},nn={uploading:"در حال آپلود..."},sn={highlighted_text:"متن برجسته شده"},ln={allow_webcam_access:"لطفاً برای ضبط، دسترسی به وب‌کم را مجاز کنید.",brush_color:"رنگ قلم",brush_radius:"اندازه قلم",image:"تصویر",remove_image:"حذف تصویر",select_brush_color:"انتخاب رنگ قلم",start_drawing:"شروع طراحی",use_brush:"استفاده از قلم",drop_to_upload:"فایل تصویر را اینجا رها کنید تا آپلود شود"},cn={label:"برچسب"},dn={enable_cookies:"اگر در حالت ناشناس از HuggingFace Space بازدید می‌کنید، باید کوکی‌های شخص ثالث را فعال کنید.",incorrect_credentials:"اطلاعات ورود نادرست",username:"نام کاربری",password:"رمز عبور",login:"ورود"},un={number:"عدد"},_n={plot:"نمودار"},pn={radio:"دکمه رادیویی"},mn={slider:"اسلایدر"},hn={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"فایل صوتی را اینجا رها کنید",drop_csv:"CSV را اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید",drop_gallery:"رسانه را اینجا رها کنید",paste_clipboard:"چسباندن از کلیپ‌بورد"},gn={drop_to_upload:"فایل ویدیو را اینجا رها کنید تا آپلود شود"},fn={edit:"ویرایش",retry:"تلاش مجدد",undo:"بازگشت",submit:"ارسال",cancel:"لغو",like:"پسندیدم",dislike:"نپسندیدم",clear:"پاک کردن گفتگو"},D0={_name:Ki,"3D_model":{"3d_model":"مدل سه‌بعدی",drop_to_upload:"فایل مدل سه‌بعدی (.obj، .glb، .stl، .gltf، .splat یا .ply) را اینجا رها کنید تا بارگذاری شود."},annotated_image:Xi,audio:Zi,blocks:Ji,checkbox:Yi,code:Qi,color_picker:en,common:on,dataframe:tn,dropdown:rn,errors:an,file:nn,highlighted_text:sn,image:ln,label:cn,login:dn,number:un,plot:_n,radio:pn,slider:mn,upload_text:hn,video:gn,chatbot:fn},I0=Object.freeze(Object.defineProperty({__proto__:null,_name:Ki,annotated_image:Xi,audio:Zi,blocks:Ji,chatbot:fn,checkbox:Yi,code:Qi,color_picker:en,common:on,dataframe:tn,default:D0,dropdown:rn,errors:an,file:nn,highlighted_text:sn,image:ln,label:cn,login:dn,number:un,plot:_n,radio:pn,slider:mn,upload_text:hn,video:gn},Symbol.toStringTag,{value:"Module"})),bn="Suomi",vn={annotated_image:"Merkitty kuva"},wn={allow_recording_access:"Anna lupa mikrofonin käyttöön nauhoitusta varten.",audio:"Ääni",record_from_microphone:"Nauhoita mikrofonista",stop_recording:"Lopeta nauhoitus",no_device_support:"Medialaitteisiin ei saada yhteyttä. Varmista, että käytät suojattua lähdettä (https) tai localhostia (tai olet antanut kelvollisen SSL-varmenteen ssl_verify:lle), ja olet antanut selaimelle luvan käyttää laitettasi.",stop:"Pysäytä",resume:"Jatka",record:"Nauhoita",no_microphone:"Mikrofonia ei löydy",pause:"Tauko",play:"Toista",waiting:"Odotetaan",drop_to_upload:"Lataa kuvatiedosto tähän pudottamalla se"},yn={connection_can_break:"Mobiililaitteilla yhteys voi katketa, jos tämä välilehti menettää fokuksen tai laite menee lepotilaan, jolloin paikkasi jonossa menetetään.",long_requests_queue:"Jonossa on paljon odottavia pyyntöjä. Monista tämä Space ohittaaksesi jonon.",lost_connection:"Yhteys katkesi sivulta poistumisen vuoksi. Palataan jonoon...",waiting_for_inputs:"Odotetaan tiedostojen lataamisen päättymistä, yritä uudelleen."},kn={checkbox:"Valintaruutu",checkbox_group:"Valintaruuturyhmä"},Sn={code:"Koodi"},xn={color_picker:"Värivalitsin"},En={built_with:"Tehty käyttäen",built_with_gradio:"Tehty Gradiolla",clear:"Tyhjennä",download:"Lataa",edit:"Muokkaa",empty:"Tyhjä",error:"Virhe",hosted_on:"Isännöity palvelimella",loading:"Ladataan",logo:"logo",or:"tai",remove:"Poista",settings:"Asetukset",share:"Jaa",submit:"Lähetä",undo:"Kumoa",no_devices:"Laitteita ei löytynyt",language:"Kieli",display_theme:"Näyttöteema",pwa:"Progressiivinen verkkosovellus"},$n={incorrect_format:"Väärä muoto, vain CSV- ja TSV-tiedostot ovat tuettuja",new_column:"Lisää sarake",new_row:"Uusi rivi",add_row_above:"Lisää rivi yläpuolelle",add_row_below:"Lisää rivi alapuolelle",add_column_left:"Lisää sarake vasemmalle",add_column_right:"Lisää sarake oikealle",delete_row:"Poista rivi",delete_column:"Poista sarake",sort_column:"Lajittele sarake",sort_ascending:"Lajittele nousevaan järjestykseen",sort_descending:"Lajittele laskevaan järjestykseen",drop_to_upload:"Lataa CSV- tai TSV-tiedostoja tähän tuodaaksesi datan dataframeen.",clear_sort:"Tyhjennä järjestys"},Pn={dropdown:"Pudotusvalikko"},An={build_error:"rakennusvirhe",config_error:"määritysvirhe",contact_page_author:"Ota yhteyttä sivun tekijään ilmoittaaksesi.",no_app_file:"sovellustiedostoa ei ole",runtime_error:"ajonaikainen virhe",space_not_working:'"Space ei toimi koska" {0}',space_paused:"Space on keskeytetty",use_via_api:"Käytä API:n kautta"},Tn={uploading:"Ladataan..."},On={highlighted_text:"Korostettu teksti"},jn={allow_webcam_access:"Anna lupa web-kameran käyttöön nauhoitusta varten.",brush_color:"Siveltimen väri",brush_radius:"Siveltimen koko",image:"Kuva",remove_image:"Poista kuva",select_brush_color:"Valitse siveltimen väri",start_drawing:"Aloita piirtäminen",use_brush:"Käytä sivellintä",drop_to_upload:"Lataa kuva tiedosto tähän pudottamalla se"},Cn={label:"Tunniste"},Ln={enable_cookies:"Jos vierailet HuggingFace Spacessa Incognito-tilassa, sinun täytyy sallia kolmannen osapuolen evästeet.",incorrect_credentials:"Virheelliset kirjautumistiedot",username:"käyttäjätunnus",password:"salasana",login:"Kirjaudu sisään"},Dn={number:"Numero"},In={plot:"Kaavio"},zn={radio:"Valintanappi"},Rn={slider:"Liukusäädin"},Nn={click_to_upload:"Napsauta ladataksesi",drop_audio:"Pudota äänitiedosto tähän",drop_csv:"Pudota CSV-tiedosto tähän",drop_file:"Pudota tiedosto tähän",drop_image:"Pudota kuva tähän",drop_video:"Pudota video tähän",drop_gallery:"Pudota media tähän",paste_clipboard:"Liitä leikepöydältä"},Hn={drop_to_upload:"Lataa videotiedosto pudottamalla se tähän."},Bn={edit:"Muokkaa",retry:"Yritä uudelleen",undo:"Kumoa",submit:"Lähetä",cancel:"Peruuta",like:"Tykkää",dislike:"En tykkää",clear:"Tyhjennä keskustelu"},z0={_name:bn,"3D_model":{"3d_model":"3D-malli",drop_to_upload:"Lataa 3D-malli (.obj, .glb, .stl, .gltf, .splat tai .ply) tähän pudottamalla tiedosto."},annotated_image:vn,audio:wn,blocks:yn,checkbox:kn,code:Sn,color_picker:xn,common:En,dataframe:$n,dropdown:Pn,errors:An,file:Tn,highlighted_text:On,image:jn,label:Cn,login:Ln,number:Dn,plot:In,radio:zn,slider:Rn,upload_text:Nn,video:Hn,chatbot:Bn},R0=Object.freeze(Object.defineProperty({__proto__:null,_name:bn,annotated_image:vn,audio:wn,blocks:yn,chatbot:Bn,checkbox:kn,code:Sn,color_picker:xn,common:En,dataframe:$n,default:z0,dropdown:Pn,errors:An,file:Tn,highlighted_text:On,image:jn,label:Cn,login:Ln,number:Dn,plot:In,radio:zn,slider:Rn,upload_text:Nn,video:Hn},Symbol.toStringTag,{value:"Module"})),Mn="Français",Vn={annotated_image:"Image annotée"},qn={allow_recording_access:"Veuillez autoriser l'accès au microphone pour l'enregistrement.",audio:"Audio",record_from_microphone:"Enregistrer depuis le microphone",stop_recording:"Arrêter l'enregistrement",no_device_support:"Impossible d'accéder aux périphériques multimédias. Assurez-vous que vous êtes sur une source sécurisée (https) ou localhost (ou avez passé un certificat SSL valide à ssl_verify), et que vous avez autorisé l'accès du navigateur à votre appareil.",stop:"Arrêter",resume:"Reprendre",record:"Enregistrer",no_microphone:"Aucun microphone trouvé",pause:"Pause",play:"Lecture",waiting:"En attente",drop_to_upload:"Déposez un fichier audio ici pour le télécharger"},Gn={connection_can_break:"Sur mobile, la connexion peut être interrompue si cet onglet perd le focus ou si l'appareil se met en veille, perdant votre position dans la file d'attente.",long_requests_queue:"Il y a une longue file d'attente de requêtes en attente. Dupliquez ce Space pour la contourner.",lost_connection:"Connexion perdue en quittant la page. Retour à la file d'attente...",waiting_for_inputs:"En attente de la fin du téléchargement des fichier(s), veuillez réessayer."},Un={checkbox:"Case à cocher",checkbox_group:"Groupe de cases à cocher"},Fn={code:"Code"},Wn={color_picker:"Sélecteur de couleur"},Kn={built_with:"créé avec",built_with_gradio:"Créé avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Modifier",empty:"Vide",flag:"Marquer",error:"Erreur",hosted_on:"Hébergé sur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",settings:"Paramètres",share:"Partager",submit:"Envoyer",undo:"Annuler",no_devices:"Aucun périphérique trouvé",language:"Langue",display_theme:"Thème d'affichage",pwa:"Application web progressive",run:"Exécuter"},Xn={incorrect_format:"Format incorrect, seuls les fichiers CSV et TSV sont pris en charge",new_column:"Ajouter une colonne",new_row:"Nouvelle ligne",add_row_above:"Ajouter une ligne au-dessus",add_row_below:"Ajouter une ligne en dessous",add_column_left:"Ajouter une colonne à gauche",add_column_right:"Ajouter une colonne à droite",delete_row:"Supprimer la ligne",delete_column:"Supprimer la colonne",sort_column:"Trier la colonne",sort_ascending:"Trier par ordre croissant",sort_descending:"Trier par ordre décroissant",drop_to_upload:"Déposez ici les fichiers CSV ou TSV pour importer les données dans le dataframe.",clear_sort:"Effacer le tri"},Zn={dropdown:"Menu déroulant"},Jn={build_error:"il y a une erreur de construction",config_error:"il y a une erreur de configuration",contact_page_author:"Veuillez contacter l'auteur de la page pour l'informer.",no_app_file:"il n'y a pas de fichier d'application",runtime_error:"il y a une erreur d'exécution",space_not_working:'"Le Space ne fonctionne pas car" {0}',space_paused:"le Space est en pause",use_via_api:"Utiliser via API",use_via_api_or_mcp:"Utiliser via API ou MCP"},Yn={uploading:"Téléchargement..."},Qn={highlighted_text:"Texte surligné"},es={allow_webcam_access:"Veuillez autoriser l'accès à la webcam pour l'enregistrement.",brush_color:"Couleur du pinceau",brush_radius:"Taille du pinceau",image:"Image",remove_image:"Supprimer l'image",select_brush_color:"Sélectionner la couleur du pinceau",start_drawing:"Commencer à dessiner",use_brush:"Utiliser le pinceau",drop_to_upload:"Déposez un fichier image ici pour télécharger"},os={label:"Étiquette"},ts={enable_cookies:"Si vous visitez un Space HuggingFace en mode Incognito, vous devez activer les cookies tiers.",incorrect_credentials:"Identifiants incorrects",username:"nom d'utilisateur",password:"mot de passe",login:"Connexion"},rs={number:"Nombre"},as={plot:"Graphique"},is={radio:"Bouton radio"},ns={slider:"Curseur"},ss={click_to_upload:"Cliquez pour télécharger un fichier",drop_audio:"Déposez l'audio ici",drop_csv:"Déposez le fichier CSV ici",drop_file:"Déposez le fichier ici",drop_image:"Déposez l'image ici",drop_video:"Déposez la vidéo ici",drop_gallery:"Déposez les médias ici",paste_clipboard:"Coller depuis le presse-papiers"},ls={drop_to_upload:"Déposez un fichier vidéo ici pour le télécharger"},cs={edit:"Modifier",retry:"Réessayer",undo:"Annuler",submit:"Envoyer",cancel:"Annuler",like:"J'aime",dislike:"Je n'aime pas",clear:"Effacer la conversation"},N0={_name:Mn,"3D_model":{"3d_model":"Modèle 3D",drop_to_upload:"Déposez un fichier image ici pour le télécharger"},annotated_image:Vn,audio:qn,blocks:Gn,checkbox:Un,code:Fn,color_picker:Wn,common:Kn,dataframe:Xn,dropdown:Zn,errors:Jn,file:Yn,highlighted_text:Qn,image:es,label:os,login:ts,number:rs,plot:as,radio:is,slider:ns,upload_text:ss,video:ls,chatbot:cs},H0=Object.freeze(Object.defineProperty({__proto__:null,_name:Mn,annotated_image:Vn,audio:qn,blocks:Gn,chatbot:cs,checkbox:Un,code:Fn,color_picker:Wn,common:Kn,dataframe:Xn,default:N0,dropdown:Zn,errors:Jn,file:Yn,highlighted_text:Qn,image:es,label:os,login:ts,number:rs,plot:as,radio:is,slider:ns,upload_text:ss,video:ls},Symbol.toStringTag,{value:"Module"})),ds="עברית",us={annotated_image:"תמונה מוערת"},_s={allow_recording_access:"אנא אפשר/י גישה למיקרופון להקלטה.",audio:"שמע",record_from_microphone:"הקלט ממיקרופון",stop_recording:"עצור הקלטה",no_device_support:"לא ניתן לגשת להתקני מדיה. וודא שאתה מריץ ממקור מאובטח (https) או localhost (או שהעברת אישור SSL תקף ל-ssl_verify), ושאישרת לדפדפן גישה למכשיר שלך.",stop:"עצור",resume:"המשך",record:"הקלט",no_microphone:"לא נמצא מיקרופון",pause:"השהה",play:"נגן",waiting:"ממתין",drop_to_upload:"הטילו קובץ אודיו כאן כדי להעלות"},ps={connection_can_break:"בנייד, החיבור עלול להתנתק אם כרטיסייה זו מאבדת מיקוד או המכשיר נכנס למצב שינה, מה שיגרום לאיבוד מקומך בתור.",long_requests_queue:"יש תור ארוך של בקשות ממתינות. שכפל Space זה כדי לדלג על התור.",lost_connection:"החיבור אבד בגלל עזיבת העמוד. חוזר לתור...",waiting_for_inputs:"מחכים לסיום העלאה של הקבצים, אנא נסה שוב."},ms={checkbox:"תיבת סימון",checkbox_group:"קבוצת תיבות סימון"},hs={code:"קוד"},gs={color_picker:"בוחר צבעים"},fs={built_with:"נבנה עם",built_with_gradio:"נבנה עם Gradio",clear:"נקה",download:"הורד",edit:"ערוך",empty:"ריק",error:"שגיאה",hosted_on:"מאוחסן ב",loading:"טוען",logo:"לוגו",or:"או",remove:"הסר",settings:"הגדרות",share:"שתף",submit:"שלח",undo:"בטל",no_devices:"לא נמצאו התקנים",language:"שפה",display_theme:"ערכת נושא",pwa:"יישום ווב מתקדם"},bs={incorrect_format:"פורמט שגוי, רק קבצי CSV ו-TSV נתמכים",new_column:"הוסף עמודה",new_row:"שורה חדשה",add_row_above:"הוסף שורה מעל",add_row_below:"הוסף שורה מתחת",add_column_left:"הוסף עמודה משמאל",add_column_right:"הוסף עמודה מימין",delete_row:"מחק שורה",delete_column:"מחק עמודה",sort_column:"מיין עמודה",sort_ascending:"מיין בסדר עולה",sort_descending:"מיין בסדר יורד",drop_to_upload:"הטילו קבצי CSV או TSV כאן כדי לייבא נתונים לתוך dataframe",clear_sort:"נקה סדר"},vs={dropdown:"תפריט נפתח"},ws={build_error:"יש שגיאת בנייה",config_error:"יש שגיאת תצורה",contact_page_author:"אנא צור קשר עם מחבר העמוד כדי ליידע אותו.",no_app_file:"אין קובץ יישום",runtime_error:"יש שגיאת זמן ריצה",space_not_working:'"ה-Space לא עובד כי" {0}',space_paused:"ה-Space מושהה",use_via_api:"השתמש דרך API"},ys={uploading:"מעלה..."},ks={highlighted_text:"טקסט מודגש"},Ss={allow_webcam_access:"אנא אפשר גישה למצלמת האינטרנט להקלטה.",brush_color:"צבע מברשת",brush_radius:"גודל מברשת",image:"תמונה",remove_image:"הסר תמונה",select_brush_color:"בחר צבע מברשת",start_drawing:"התחל לצייר",use_brush:"השתמש במברשת",drop_to_upload:"הטילו קובץ תמונה כאן כדי להעלות"},xs={label:"תווית"},Es={enable_cookies:"אם אתה מבקר ב-Space של HuggingFace במצב גלישה בסתר, עליך לאפשר קובצי cookie של צד שלישי.",incorrect_credentials:"פרטי התחברות שגויים",username:"שם משתמש",password:"סיסמה",login:"התחבר"},$s={number:"מספר"},Ps={plot:"תרשים"},As={radio:"כפתור רדיו"},Ts={slider:"מחוון"},Os={click_to_upload:"לחץ להעלאה",drop_audio:"שחרר קובץ שמע כאן",drop_csv:"שחרר קובץ CSV כאן",drop_file:"שחרר קובץ כאן",drop_image:"שחרר תמונה כאן",drop_video:"שחרר וידאו כאן",drop_gallery:"שחרר מדיה כאן",paste_clipboard:"הדבק מהלוח"},js={drop_to_upload:"הטילו קובץ וידאו כאן כדי להעלות"},Cs={edit:"עריכה",retry:"נסה שוב",undo:"בטל",submit:"שלח",cancel:"ביטול",like:"אהבתי",dislike:"לא אהבתי",clear:"נקה שיחה"},B0={_name:ds,"3D_model":{"3d_model":"מודל תלת מימד",drop_to_upload:"הטילו קובץ מודל 3D (.obj, .glb, .stl, .gltf, .splat או .ply) כאן כדי להעלות"},annotated_image:us,audio:_s,blocks:ps,checkbox:ms,code:hs,color_picker:gs,common:fs,dataframe:bs,dropdown:vs,errors:ws,file:ys,highlighted_text:ks,image:Ss,label:xs,login:Es,number:$s,plot:Ps,radio:As,slider:Ts,upload_text:Os,video:js,chatbot:Cs},M0=Object.freeze(Object.defineProperty({__proto__:null,_name:ds,annotated_image:us,audio:_s,blocks:ps,chatbot:Cs,checkbox:ms,code:hs,color_picker:gs,common:fs,dataframe:bs,default:B0,dropdown:vs,errors:ws,file:ys,highlighted_text:ks,image:Ss,label:xs,login:Es,number:$s,plot:Ps,radio:As,slider:Ts,upload_text:Os,video:js},Symbol.toStringTag,{value:"Module"})),Ls="हिंदी",Ds={annotated_image:"एनोटेट की गई छवि"},Is={allow_recording_access:"कृपया रिकॉर्डिंग के लिए माइक्रोफ़ोन एक्सेस की अनुमति दें।",audio:"ऑडियो",record_from_microphone:"माइक्रोफ़ोन से रिकॉर्ड करें",stop_recording:"रिकॉर्डिंग रोकें",no_device_support:"मीडिया डिवाइस तक पहुंच नहीं हो सकी। सुनिश्चित करें कि आप एक सुरक्षित स्रोत (https) या localhost पर चल रहे हैं (या ssl_verify को एक वैध SSL प्रमाणपत्र दिया है), और आपने अपने ब्राउज़र को अपने डिवाइस तक पहुंच की अनुमति दी है।",stop:"रोकें",resume:"जारी रखें",record:"रिकॉर्ड",no_microphone:"कोई माइक्रोफ़ोन नहीं मिला",pause:"रोकें",play:"चलाएं",waiting:"प्रतीक्षा कर रहा है",drop_to_upload:"यहाँ एक ऑडियो फाइल डालें अपलोड करने के लिए"},zs={connection_can_break:"मोबाइल पर, यदि यह टैब फोकस खो देता है या डिवाइस स्लीप मोड में चला जाता है, तो कनेक्शन टूट सकता है, जिससे कतार में आपका स्थान खो जाएगा।",long_requests_queue:"लंबित अनुरोधों की एक लंबी कतार है। इसे छोड़ने के लिए इस Space को डुप्लिकेट करें।",lost_connection:"पेज छोड़ने के कारण कनेक्शन टूट गया। कतार में वापस जा रहा है...",waiting_for_inputs:"फाइलें अपलोड होने का इंतजार कर रहे हैं, कृपया पुन: प्रयास करें।"},Rs={checkbox:"चेकबॉक्स",checkbox_group:"चेकबॉक्स समूह"},Ns={code:"कोड"},Hs={color_picker:"रंग चयनकर्ता"},Bs={built_with:"के साथ बनाया गया",built_with_gradio:"Gradio के साथ बनाया गया",clear:"साफ़ करें",download:"डाउनलोड",edit:"संपादित करें",empty:"खाली",error:"त्रुटि",hosted_on:"पर होस्ट किया गया",loading:"लोड हो रहा है",logo:"लोगो",or:"या",remove:"हटाएं",settings:"सेटिंग्स",share:"साझा करें",submit:"सबमिट करें",undo:"पूर्ववत करें",no_devices:"कोई डिवाइस नहीं मिला",language:"भाषा",display_theme:"प्रदर्शन थीम",pwa:"प्रोग्रेसिव वेब ऐप"},Ms={incorrect_format:"गलत प्रारूप, केवल CSV और TSV फ़ाइलें समर्थित हैं",new_column:"कॉलम जोड़ें",new_row:"नई पंक्ति",add_row_above:"ऊपर पंक्ति जोड़ें",add_row_below:"नीचे पंक्ति जोड़ें",add_column_left:"बाएं कॉलम जोड़ें",add_column_right:"दाएं कॉलम जोड़ें",delete_row:"पंक्ति हटाएं",delete_column:"कॉलम हटाएं",sort_column:"कॉलम को क्रमबद्ध करें",sort_ascending:"आरोही क्रम में क्रमबद्ध करें",sort_descending:"अवरोही क्रम में क्रमबद्ध करें",drop_to_upload:"यहाँ CSV या TSV फाइलें ड्रॉप करें ताकि डेटा डेटाफ़्रेम में आयात किया जा सके",clear_sort:"क्रमबद्धता हटाएं"},Vs={dropdown:"ड्रॉपडाउन"},qs={build_error:"एक बिल्ड त्रुटि है",config_error:"एक कॉन्फ़िगरेशन त्रुटि है",contact_page_author:"कृपया पेज के लेखक को सूचित करने के लिए संपर्क करें।",no_app_file:"कोई ऐप फ़ाइल नहीं है",runtime_error:"एक रनटाइम त्रुटि है",space_not_working:'"Space काम नहीं कर रहा है क्योंकि" {0}',space_paused:"Space रोका गया है",use_via_api:"API के माध्यम से उपयोग करें"},Gs={uploading:"अपलोड हो रहा है..."},Us={highlighted_text:"हाइलाइट किया गया टेक्स्ट"},Fs={allow_webcam_access:"कृपया रिकॉर्डिंग के लिए वेबकैम एक्सेस की अनुमति दें।",brush_color:"ब्रश का रंग",brush_radius:"ब्रश का आकार",image:"छवि",remove_image:"छवि हटाएं",select_brush_color:"ब्रश का रंग चुनें",start_drawing:"चित्रकारी शुरू करें",use_brush:"ब्रश का उपयोग करें",drop_to_upload:"यहाँ एक छवि फाइल डालें अपलोड करने के लिए"},Ws={label:"लेबल"},Ks={enable_cookies:"यदि आप इनकॉग्निटो मोड में HuggingFace Space का दौरा कर रहे हैं, तो आपको थर्ड-पार्टी कुकीज़ को सक्षम करना होगा।",incorrect_credentials:"गलत क्रेडेंशियल्स",username:"उपयोगकर्ता नाम",password:"पासवर्ड",login:"लॉग इन करें"},Xs={number:"संख्या"},Zs={plot:"प्लॉट"},Js={radio:"रेडियो बटन"},Ys={slider:"स्लाइडर"},Qs={click_to_upload:"अपलोड करने के लिए क्लिक करें",drop_audio:"ऑडियो यहाँ छोड़ें",drop_csv:"CSV फाइल यहाँ छोड़ें",drop_file:"फ़ाइल यहाँ छोड़ें",drop_image:"छवि यहाँ छोड़ें",drop_video:"वीडियो यहाँ छोड़ें",drop_gallery:"मीडिया यहाँ छोड़ें",paste_clipboard:"क्लिपबोर्ड से पेस्ट करें"},el={drop_to_upload:"यहाँ एक वीडियो फाइल डालें अपलोड करने के लिए"},ol={edit:"संपादित करें",retry:"पुनः प्रयास करें",undo:"वापस लें",submit:"भेजें",cancel:"रद्द करें",like:"पसंद",dislike:"नापसंद",clear:"चैट साफ़ करें"},V0={_name:Ls,"3D_model":{"3d_model":"3D मॉडल",drop_to_upload:"यहाँ 3D मॉडल (.obj, .glb, .stl, .gltf, .splat, या .ply) फ़ाइल ड्रॉप करें अपलोड करने के लिए"},annotated_image:Ds,audio:Is,blocks:zs,checkbox:Rs,code:Ns,color_picker:Hs,common:Bs,dataframe:Ms,dropdown:Vs,errors:qs,file:Gs,highlighted_text:Us,image:Fs,label:Ws,login:Ks,number:Xs,plot:Zs,radio:Js,slider:Ys,upload_text:Qs,video:el,chatbot:ol},q0=Object.freeze(Object.defineProperty({__proto__:null,_name:Ls,annotated_image:Ds,audio:Is,blocks:zs,chatbot:ol,checkbox:Rs,code:Ns,color_picker:Hs,common:Bs,dataframe:Ms,default:V0,dropdown:Vs,errors:qs,file:Gs,highlighted_text:Us,image:Fs,label:Ws,login:Ks,number:Xs,plot:Zs,radio:Js,slider:Ys,upload_text:Qs,video:el},Symbol.toStringTag,{value:"Module"})),tl="日本語",rl={annotated_image:"注釈付き画像"},al={allow_recording_access:"録音のためにマイクへのアクセスを許可してください",audio:"音声",record_from_microphone:"マイクから録音",stop_recording:"録音を停止",no_device_support:"メディアデバイスにアクセスできません。安全なソース（https）またはlocalhostで実行していること（またはssl_verifyに有効なSSL証明書を渡していること）、ブラウザにデバイスへのアクセスを許可していることを確認してください",stop:"停止",resume:"再開",record:"録音",no_microphone:"マイクが見つかりません",pause:"一時停止",play:"再生",waiting:"待機中",drop_to_upload:"ここにオーディオファイルをドロップしてアップロードしてください"},il={connection_can_break:"モバイルでは、このタブがフォーカスを失うかデバイスがスリープモードになると接続が切断され、キュー内の位置が失われる可能性があります",long_requests_queue:"保留中のリクエストの長いキューがあります。このSpaceを複製してスキップしてください",lost_connection:"ページを離れたため接続が切断されました。キューに戻ります...",waiting_for_inputs:"ファイルのアップロードが完了するのを待っています。再度お試しください"},nl={checkbox:"チェックボックス",checkbox_group:"チェックボックスグループ"},sl={code:"コード"},ll={color_picker:"カラーピッカー"},cl={built_with:"で作成",built_with_gradio:"Gradioで作成",clear:"クリア",download:"ダウンロード",edit:"編集",empty:"空",error:"エラー",hosted_on:"でホスト",loading:"読み込み中",logo:"ロゴ",or:"または",remove:"削除",settings:"設定",share:"共有",submit:"送信",undo:"元に戻す",no_devices:"デバイスが見つかりません",language:"言語",display_theme:"表示テーマ",pwa:"プログレッシブウェブアプリ"},dl={incorrect_format:"不正なフォーマット、CSVとTSVファイルのみサポートされています",new_column:"列を追加",new_row:"新しい行",add_row_above:"上に行を追加",add_row_below:"下に行を追加",add_column_left:"左に列を追加",add_column_right:"右に列を追加",delete_row:"行を削除",delete_column:"列を削除",sort_column:"列をソート",sort_ascending:"昇順に並べ替え",sort_descending:"降順に並べ替え",drop_to_upload:"CSVまたはTSVファイルをここにドロップして、データをデータフレームにインポートします",clear_sort:"ソートをクリア"},ul={dropdown:"ドロップダウン"},_l={build_error:"ビルドエラーがあります",config_error:"設定エラーがあります",contact_page_author:"ページの作者に連絡して知らせてください",no_app_file:"アプリファイルがありません",runtime_error:"ランタイムエラーがあります",space_not_working:'"Spaceが動作していません。理由：" {0}',space_paused:"Spaceが一時停止されています",use_via_api:"APIを介して使用",use_via_api_or_mcp:"APIまたはMCP経由で使用"},pl={uploading:"アップロード中..."},ml={highlighted_text:"ハイライトされたテキスト"},hl={allow_webcam_access:"録画のためにウェブカメラへのアクセスを許可してください",brush_color:"ブラシの色",brush_radius:"ブラシのサイズ",image:"画像",remove_image:"画像を削除",select_brush_color:"ブラシの色を選択",start_drawing:"描画を開始",use_brush:"ブラシを使用",drop_to_upload:"ここに画像ファイルをドロップしてアップロードしてください"},gl={label:"ラベル"},fl={enable_cookies:"シークレットモードでHuggingFace Spaceを訪問している場合、サードパーティのCookieを有効にする必要があります",incorrect_credentials:"認証情報が正しくありません",username:"ユーザー名",password:"パスワード",login:"ログイン"},bl={number:"数値"},vl={plot:"プロット"},wl={radio:"ラジオボタン"},yl={slider:"スライダー"},kl={click_to_upload:"クリックしてアップロード",drop_audio:"音声をここにドロップ",drop_csv:"CSVファイルをここにドロップ",drop_file:"ファイルをここにドロップ",drop_image:"画像をここにドロップ",drop_video:"動画をここにドロップ",drop_gallery:"メディアをここにドロップ",paste_clipboard:"クリップボードから貼り付け"},Sl={drop_to_upload:"ビデオファイルをここにドロップしてアップロードしてください"},xl={edit:"編集",retry:"再試行",undo:"元に戻す",submit:"送信",cancel:"キャンセル",like:"いいね",dislike:"よくないね",clear:"会話をクリア"},G0={_name:tl,"3D_model":{"3d_model":"3Dモデル",drop_to_upload:"3D モデル (.obj, .glb, .stl, .gltf, .splat, .ply) ファイルをここにドロップしてアップロードしてください"},annotated_image:rl,audio:al,blocks:il,checkbox:nl,code:sl,color_picker:ll,common:cl,dataframe:dl,dropdown:ul,errors:_l,file:pl,highlighted_text:ml,image:hl,label:gl,login:fl,number:bl,plot:vl,radio:wl,slider:yl,upload_text:kl,video:Sl,chatbot:xl},U0=Object.freeze(Object.defineProperty({__proto__:null,_name:tl,annotated_image:rl,audio:al,blocks:il,chatbot:xl,checkbox:nl,code:sl,color_picker:ll,common:cl,dataframe:dl,default:G0,dropdown:ul,errors:_l,file:pl,highlighted_text:ml,image:hl,label:gl,login:fl,number:bl,plot:vl,radio:wl,slider:yl,upload_text:kl,video:Sl},Symbol.toStringTag,{value:"Module"})),El="한국어",$l={annotated_image:"주석이 달린 이미지"},Pl={allow_recording_access:"녹음을 위해 마이크 접근을 허용해 주세요.",audio:"오디오",record_from_microphone:"마이크로 녹음",stop_recording:"녹음 중지",no_device_support:"미디어 장치에 접근할 수 없습니다. 보안 소스(https) 또는 localhost에서 실행 중인지(또는 ssl_verify에 유효한 SSL 인증서를 전달했는지), 브라우저에 장치 접근 권한을 부여했는지 확인하세요.",stop:"중지",resume:"재개",record:"녹음",no_microphone:"마이크를 찾을 수 없습니다",pause:"일시정지",play:"재생",waiting:"대기 중",drop_to_upload:"오디오 파일을 여기에 놓아 업로드하세요."},Al={connection_can_break:"모바일에서는 이 탭이 포커스를 잃거나 기기가 절전 모드로 전환되면 연결이 끊어져 대기열의 위치를 잃을 수 있습니다.",long_requests_queue:"대기 중인 요청의 긴 대기열이 있습니다. 이 Space를 복제하여 건너뛰세요.",lost_connection:"페이지를 떠나 연결이 끊어졌습니다. 대기열로 돌아가는 중...",waiting_for_inputs:"파일 업로드가 완료될 때까지 기다렸다가 다시 시도해주세요."},Tl={checkbox:"체크박스",checkbox_group:"체크박스 그룹"},Ol={code:"코드"},jl={color_picker:"색상 선택기"},Cl={built_with:"로 제작됨",built_with_gradio:"Gradio로 제작됨",clear:"지우기",download:"다운로드",edit:"편집",empty:"비어 있음",error:"오류",hosted_on:"에서 호스팅됨",loading:"로딩 중",logo:"로고",or:"또는",remove:"제거",settings:"설정",share:"공유",submit:"제출",undo:"실행 취소",no_devices:"장치를 찾을 수 없습니다",language:"언어",display_theme:"디스플레이 테마",pwa:"프로그레시브 웹 앱"},Ll={incorrect_format:"잘못된 형식입니다. CSV 및 TSV 파일만 지원됩니다",new_column:"열 추가",new_row:"새 행",add_row_above:"위에 행 추가",add_row_below:"아래에 행 추가",add_column_left:"왼쪽에 열 추가",add_column_right:"오른쪽에 열 추가",delete_row:"행 삭제",delete_column:"열 삭제",sort_column:"정렬 열",sort_ascending:"오름차순 정렬",sort_descending:"내림차순 정렬",drop_to_upload:"CSV 또는 TSV 파일을 여기에 끌어다 놓아 데이터프레임에 데이터를 가져올 수 있습니다.",clear_sort:"정렬 해제"},Dl={dropdown:"드롭다운"},Il={build_error:"빌드 오류가 있습니다",config_error:"구성 오류가 있습니다",contact_page_author:"페이지 작성자에게 연락하여 알려주세요.",no_app_file:"앱 파일이 없습니다",runtime_error:"런타임 오류가 있습니다",space_not_working:'"Space가 작동하지 않는 이유:" {0}',space_paused:"space가 일시 중지되었습니다",use_via_api:"API를 통해 사용",use_via_api_or_mcp:"API 또는 MCP를 통해 사용"},zl={uploading:"업로드 중..."},Rl={highlighted_text:"강조 표시된 텍스트"},Nl={allow_webcam_access:"녹화를 위해 웹캠 접근을 허용해 주세요.",brush_color:"브러시 색상",brush_radius:"브러시 크기",image:"이미지",remove_image:"이미지 제거",select_brush_color:"브러시 색상 선택",start_drawing:"그리기 시작",use_brush:"브러시 사용",drop_to_upload:"이곳에 이미지 파일을 끌어다 놓으세요."},Hl={label:"레이블"},Bl={enable_cookies:"시크릿 모드에서 HuggingFace Space를 방문하는 경우 타사 쿠키를 활성화해야 합니다.",incorrect_credentials:"잘못된 자격 증명",username:"사용자 이름",password:"비밀번호",login:"로그인"},Ml={number:"숫자"},Vl={plot:"플롯"},ql={radio:"라디오"},Gl={slider:"슬라이더"},Ul={click_to_upload:"클릭하여 업로드",drop_audio:"오디오를 여기에 드롭",drop_csv:"CSV를 여기에 드롭",drop_file:"파일을 여기에 드롭",drop_image:"이미지를 여기에 드롭",drop_video:"비디오를 여기에 드롭",drop_gallery:"미디어를 여기에 드롭",paste_clipboard:"클립보드에서 붙여넣기"},Fl={drop_to_upload:"여기에 비디오 파일을 끌어다 놓으세요."},Wl={edit:"편집",retry:"다시 시도",undo:"실행 취소",submit:"제출",cancel:"취소",like:"좋아요",dislike:"싫어요",clear:"대화 지우기"},F0={_name:El,"3D_model":{"3d_model":"3D 모델",drop_to_upload:"3D 모델 (.obj, .glb, .stl, .gltf, .splat, 또는 .ply) 파일을 여기에 놓아 업로드하세요."},annotated_image:$l,audio:Pl,blocks:Al,checkbox:Tl,code:Ol,color_picker:jl,common:Cl,dataframe:Ll,dropdown:Dl,errors:Il,file:zl,highlighted_text:Rl,image:Nl,label:Hl,login:Bl,number:Ml,plot:Vl,radio:ql,slider:Gl,upload_text:Ul,video:Fl,chatbot:Wl},W0=Object.freeze(Object.defineProperty({__proto__:null,_name:El,annotated_image:$l,audio:Pl,blocks:Al,chatbot:Wl,checkbox:Tl,code:Ol,color_picker:jl,common:Cl,dataframe:Ll,default:F0,dropdown:Dl,errors:Il,file:zl,highlighted_text:Rl,image:Nl,label:Hl,login:Bl,number:Ml,plot:Vl,radio:ql,slider:Gl,upload_text:Ul,video:Fl},Symbol.toStringTag,{value:"Module"})),Kl="Lietuvių",Xl={built_with_gradio:"Sukurta su Gradio",clear:"Išvalyti",or:"arba",submit:"Pateikti",settings:"Nustatymai",built_with:"sukurta su",download:"Atsisiųsti",edit:"Redaguoti",empty:"Tuščia",error:"Klaida",hosted_on:"Talpinama",loading:"Kraunama",logo:"logotipas",remove:"Pašalinti",share:"Dalintis",undo:"Atšaukti",no_devices:"Nerasta jokių įrenginių",language:"Kalba",display_theme:"Ekrano tema",pwa:"Progresyvi interneto programėlė"},Zl={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite failą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia",drop_gallery:"Įkelkite mediją čia",paste_clipboard:"Įklijuoti iš iškarpinės"},Jl={annotated_image:"Pažymėtas paveikslėlis"},Yl={allow_recording_access:"Prašome suteikti prieigą prie mikrofono įrašymui.",audio:"Garsas",drop_to_upload:"Įkelkite garso failą čia",record_from_microphone:"Įrašyti iš mikrofono",stop_recording:"Stabdyti įrašymą",no_device_support:"Nepavyko pasiekti medijos įrenginių. Patikrinkite, ar naudojate saugią prieigą (https) arba localhost (arba perduodate galiojantį SSL sertifikatą į ssl_verify), ir leidžiate naršyklei prieiti prie jūsų įrenginio.",stop:"Stabdyti",resume:"Tęsti",record:"Įrašyti",no_microphone:"Mikrofonas nerastas",pause:"Pristabdyti",play:"Groti",waiting:"Laukiama"},Ql={connection_can_break:"Mobiliajame įrenginyje ryšys gali nutrūkti, jei šis skirtukas bus neaktyvus arba įrenginys užmigs, todėl galite prarasti savo vietą eilėje.",long_requests_queue:"Yra ilga laukiančių užklausų eilė. Dubliuokite šią erdvę, kad praleistumėte eilę.",lost_connection:"Ryšys nutrūko išėjus iš puslapio. Vėl jungiamasi į eilę...",waiting_for_inputs:"Laukiama, kol bus baigti įkelti failai, prašome bandyti dar kartą."},ec={checkbox:"Žymimasis langelis",checkbox_group:"Žymimųjų langelių grupė"},oc={code:"Kodas"},tc={color_picker:"Spalvos pasirinkimas"},rc={incorrect_format:"Neteisingas formatas, palaikomi tik CSV ir TSV failai",new_column:"Pridėti stulpelį",new_row:"Nauja eilutė",add_row_above:"Pridėti eilutę viršuje",add_row_below:"Pridėti eilutę žemiau",delete_row:"Ištrinti eilutę",delete_column:"Ištrinti stulpelį",add_column_left:"Pridėti stulpelį kairėje",add_column_right:"Pridėti stulpelį dešinėje",sort_column:"Rikiuoti stulpelį",sort_ascending:"Rikiuoti didėjančia tvarka",sort_descending:"Rikiuoti mažėjančia tvarka",drop_to_upload:"Įkelkite CSV arba TSV failus čia, kad importuotumėte duomenis į duomenų lentelę",clear_sort:"Išvalyti rikiavimą"},ac={dropdown:"Išskleidžiamasis sąrašas"},ic={build_error:"yra kompiliavimo klaida",config_error:"yra konfigūracijos klaida",contact_page_author:"Prašome susisiekti su puslapio autoriumi.",no_app_file:"nėra programos failo",runtime_error:"yra vykdymo klaida",space_not_working:'"Erdvė neveikia, nes" {0}',space_paused:"erdvė yra pristabdyta",use_via_api:"Naudoti per API",use_via_api_or_mcp:"Naudoti per API arba MCP"},nc={uploading:"Įkeliama..."},sc={highlighted_text:"Paryškintas tekstas"},lc={allow_webcam_access:"Prašome suteikti prieigą prie internetinės kameros įrašymui.",brush_color:"Teptuko spalva",brush_radius:"Teptuko dydis",image:"Paveikslėlis",remove_image:"Pašalinti paveikslėlį",select_brush_color:"Pasirinkite teptuko spalvą",start_drawing:"Pradėti piešti",use_brush:"Naudoti teptuką",drop_to_upload:"Įkelkite paveikslėlio failą čia"},cc={label:"Etiketė"},dc={enable_cookies:"Jei lankotės HuggingFace erdvėje inkognito režimu, turite leisti trečiųjų šalių slapukus.",incorrect_credentials:"Neteisingi prisijungimo duomenys",username:"Vartotojo vardas",password:"Slaptažodis",login:"Prisijungti"},uc={number:"Skaičius"},_c={plot:"Grafikas"},pc={radio:"Pasirinkimo mygtukas"},mc={slider:"Slankiklis"},hc={drop_to_upload:"Įkelkite vaizdo įrašo failą čia"},gc={edit:"Redaguoti",retry:"Bandyti dar kartą",undo:"Atšaukti",submit:"Pateikti",cancel:"Atšaukti",like:"Patinka",dislike:"Nepatinka",clear:"Išvalyti pokalbį"},K0={_name:Kl,common:Xl,upload_text:Zl,"3D_model":{"3d_model":"3D modelis",drop_to_upload:"Įkelkite 3D modelio failą (.obj, .glb, .stl, .gltf, .splat arba .ply) čia"},annotated_image:Jl,audio:Yl,blocks:Ql,checkbox:ec,code:oc,color_picker:tc,dataframe:rc,dropdown:ac,errors:ic,file:nc,highlighted_text:sc,image:lc,label:cc,login:dc,number:uc,plot:_c,radio:pc,slider:mc,video:hc,chatbot:gc},X0=Object.freeze(Object.defineProperty({__proto__:null,_name:Kl,annotated_image:Jl,audio:Yl,blocks:Ql,chatbot:gc,checkbox:ec,code:oc,color_picker:tc,common:Xl,dataframe:rc,default:K0,dropdown:ac,errors:ic,file:nc,highlighted_text:sc,image:lc,label:cc,login:dc,number:uc,plot:_c,radio:pc,slider:mc,upload_text:Zl,video:hc},Symbol.toStringTag,{value:"Module"})),fc="Norsk bokmål",bc={annotated_image:"Annotert bilde"},vc={allow_recording_access:"Vennligst tillat mikrofontilgang for opptak.",audio:"Lyd",record_from_microphone:"Ta opp fra mikrofon",stop_recording:"Stopp opptak",no_device_support:"Kan ikke få tilgang til medieenheter. Sørg for at du kjører på en sikker kilde (https) eller localhost (eller har gitt et gyldig SSL-sertifikat til ssl_verify), og at du har gitt nettleseren tilgang til enheten din.",stop:"Stopp",resume:"Fortsett",record:"Ta opp",no_microphone:"Ingen mikrofon funnet",pause:"Pause",play:"Spill av",waiting:"Venter",drop_to_upload:"Slipp en lydfil her for å laste opp"},wc={connection_can_break:"På mobil kan tilkoblingen brytes hvis denne fanen mister fokus eller enheten går i dvale, og du mister plassen din i køen.",long_requests_queue:"Det er en lang kø med ventende forespørsler. Dupliser denne Space for å hoppe over køen.",lost_connection:"Mistet tilkobling på grunn av at siden ble forlatt. Går tilbake til køen...",waiting_for_inputs:"Venter på at fil(er) skal bli lastet opp, vennligst prøv igjen."},yc={checkbox:"Avkrysningsboks",checkbox_group:"Avkrysningsboksgruppe"},kc={code:"Kode"},Sc={color_picker:"Fargevelger"},xc={built_with:"bygget med",built_with_gradio:"Bygget med Gradio",clear:"Tøm",download:"Last ned",edit:"Rediger",empty:"Tom",error:"Feil",hosted_on:"Hostet på",loading:"Laster",logo:"Logo",or:"eller",remove:"Fjern",settings:"Innstillinger",share:"Del",submit:"Send",undo:"Angre",no_devices:"Ingen enheter funnet",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webapplikasjon"},Ec={incorrect_format:"Feil format, kun CSV- og TSV-filer støttes",new_column:"Legg til kolonne",new_row:"Ny rad",add_row_above:"Legg til rad over",add_row_below:"Legg til rad under",add_column_left:"Legg til kolonne til venstre",add_column_right:"Legg til kolonne til høyre",delete_row:"Slett rad",delete_column:"Slett kolonne",sort_column:"Sorter kolonne",sort_ascending:"Sorter stigende",sort_descending:"Sorter synkende",drop_to_upload:"Slipp CSV- eller TSV-filer her for å importere data til dataramme",clear_sort:"Fjern sortering"},$c={dropdown:"Nedtrekksmeny"},Pc={build_error:"Det er en byggefeil",config_error:"Det er en konfigurasjonsfeil",contact_page_author:"Vennligst kontakt sidens forfatter for å informere dem.",no_app_file:"Det er ingen app-fil",runtime_error:"Det er en kjøretidsfeil",space_not_working:'"Space fungerer ikke fordi" {0}',space_paused:"Space er pauset",use_via_api:"Bruk via API",use_via_api_or_mcp:"Bruk via API eller MCP"},Ac={uploading:"Laster opp..."},Tc={highlighted_text:"Uthevet tekst"},Oc={allow_webcam_access:"Vennligst tillat webkameratilgang for opptak.",brush_color:"Penselfarge",brush_radius:"Penselstørrelse",image:"Bilde",remove_image:"Fjern bilde",select_brush_color:"Velg penselfarge",start_drawing:"Start tegning",use_brush:"Bruk pensel",drop_to_upload:"Slipp en bildefil her for å laste opp"},jc={label:"Etikett"},Cc={enable_cookies:"Hvis du besøker en HuggingFace Space i inkognitomodus, må du aktivere tredjeparts informasjonskapsler.",incorrect_credentials:"Feil påloggingsinformasjon",username:"Brukernavn",password:"Passord",login:"Logg inn"},Lc={number:"Tall"},Dc={plot:"Plott"},Ic={radio:"Radioknapp"},zc={slider:"Glidebryter"},Rc={click_to_upload:"Klikk for å laste opp",drop_audio:"Slipp lyd her",drop_csv:"Slipp CSV her",drop_file:"Slipp fil her",drop_image:"Slipp bilde her",drop_video:"Slipp video her",drop_gallery:"Slipp media her",paste_clipboard:"Lim inn fra utklippstavle"},Nc={drop_to_upload:"Slipp en videofil her for å laste opp"},Hc={edit:"Rediger",retry:"Prøv igjen",undo:"Angre",submit:"Send",cancel:"Avbryt",like:"Liker",dislike:"Liker ikke",clear:"Tøm samtalen"},Z0={_name:fc,"3D_model":{"3d_model":"3D-modell",drop_to_upload:"Slipp en 3D-modell (.obj, .glb, .stl, .gltf, .splat eller .ply) fil her for å laste opp"},annotated_image:bc,audio:vc,blocks:wc,checkbox:yc,code:kc,color_picker:Sc,common:xc,dataframe:Ec,dropdown:$c,errors:Pc,file:Ac,highlighted_text:Tc,image:Oc,label:jc,login:Cc,number:Lc,plot:Dc,radio:Ic,slider:zc,upload_text:Rc,video:Nc,chatbot:Hc},J0=Object.freeze(Object.defineProperty({__proto__:null,_name:fc,annotated_image:bc,audio:vc,blocks:wc,chatbot:Hc,checkbox:yc,code:kc,color_picker:Sc,common:xc,dataframe:Ec,default:Z0,dropdown:$c,errors:Pc,file:Ac,highlighted_text:Tc,image:Oc,label:jc,login:Cc,number:Lc,plot:Dc,radio:Ic,slider:zc,upload_text:Rc,video:Nc},Symbol.toStringTag,{value:"Module"})),Bc="Nederlands",Mc={built_with_gradio:"Gemaakt met Gradio",clear:"Wissen",or:"of",submit:"Verzenden",settings:"Instellingen",built_with:"Gebouwd met",download:"Downloaden",edit:"Bewerken",empty:"Leeg",error:"Fout",hosted_on:"Gehost op",loading:"Laden",logo:"Logo",remove:"Verwijderen",share:"Delen",undo:"Ongedaan maken",no_devices:"Geen apparaten gevonden",language:"Taal",display_theme:"Weergavethema",pwa:"Progressieve Web App"},Vc={click_to_upload:"Klik om te uploaden",drop_audio:"Sleep een geluidsbestand hier",drop_csv:"Sleep een CSV-bestand hier",drop_file:"Sleep een bestand hier",drop_image:"Sleep een afbeelding hier",drop_video:"Sleep een video hier",drop_gallery:"Sleep media hier",paste_clipboard:"Plakken vanuit klembord"},qc={annotated_image:"Geannoteerde afbeelding"},Gc={allow_recording_access:"Geef alstublieft toegang tot de microfoon voor opname.",audio:"Audio",drop_to_upload:"Sleep een audiobestand hier om te uploaden",record_from_microphone:"Opnemen via microfoon",stop_recording:"Opname stoppen",no_device_support:"Media-apparaten konden niet worden benaderd. Controleer of u werkt vanaf een beveiligde oorsprong (https) of localhost (of u een geldig SSL-certificaat heeft doorgegeven aan ssl_verify), en of u browsertoestemming heeft gegeven voor toegang tot uw apparaat.",stop:"Stoppen",resume:"Hervatten",record:"Opnemen",no_microphone:"Geen microfoon gevonden",pause:"Pauzeren",play:"Afspelen",waiting:"Wachten"},Uc={connection_can_break:"Op mobiel kan de verbinding verbroken worden als dit tabblad niet actief is of het apparaat in slaapstand gaat, waardoor je plaats in de wachtrij verloren gaat.",long_requests_queue:"Er is een lange wachtrij van aanvragen. Dupliceer deze Space om de wachtrij over te slaan.",lost_connection:"Verbinding verloren door het verlaten van de pagina. Opnieuw aansluiten in de wachtrij...",waiting_for_inputs:"Wachten tot het uploaden van bestand(en) is voltooid, probeer het opnieuw."},Fc={checkbox:"Selectievakje",checkbox_group:"Selectievakjesgroep"},Wc={code:"Code"},Kc={color_picker:"Kleurkiezer"},Xc={incorrect_format:"Onjuist formaat, alleen CSV- en TSV-bestanden worden ondersteund",new_column:"Kolom toevoegen",new_row:"Nieuwe rij",add_row_above:"Rij boven toevoegen",add_row_below:"Rij onder toevoegen",delete_row:"Rij verwijderen",delete_column:"Kolom verwijderen",add_column_left:"Kolom links toevoegen",add_column_right:"Kolom rechts toevoegen",sort_column:"Kolom sorteren",sort_ascending:"Oplopend sorteren",sort_descending:"Aflopend sorteren",drop_to_upload:"Sleep CSV- of TSV-bestanden hier om gegevens in het dataframe te importeren",clear_sort:"Sortering wissen"},Zc={dropdown:"Vervolgkeuzelijst"},Jc={build_error:"Er is een bouwfout",config_error:"Er is een configuratiefout",contact_page_author:"Neem contact op met de auteur van de pagina om dit te melden.",no_app_file:"Er is geen app-bestand",runtime_error:"Er is een runtime-fout",space_not_working:'"Space werkt niet omdat" {0}',space_paused:"De Space is gepauzeerd",use_via_api:"Gebruik via API",use_via_api_or_mcp:"Gebruik via API of MCP"},Yc={uploading:"Uploaden..."},Qc={highlighted_text:"Gemarkeerde tekst"},ed={allow_webcam_access:"Geef alstublieft toegang tot de webcam voor opname.",brush_color:"Penseelkleur",brush_radius:"Penseelgrootte",image:"Afbeelding",remove_image:"Afbeelding verwijderen",select_brush_color:"Penseelkleur kiezen",start_drawing:"Beginnen met tekenen",use_brush:"Penseel gebruiken",drop_to_upload:"Sleep een afbeelding hier om te uploaden"},od={label:"Label"},td={enable_cookies:"Als u een HuggingFace Space bezoekt in incognitomodus, moet u cookies van derden inschakelen.",incorrect_credentials:"Onjuiste inloggegevens",username:"Gebruikersnaam",password:"Wachtwoord",login:"Inloggen"},rd={number:"Getal"},ad={plot:"Grafiek"},id={radio:"Keuzerondje"},nd={slider:"Schuifbalk"},sd={drop_to_upload:"Sleep een videobestand hier om te uploaden"},ld={edit:"Bewerken",retry:"Opnieuw proberen",undo:"Ongedaan maken",submit:"Verzenden",cancel:"Annuleren",like:"Vind ik leuk",dislike:"Vind ik niet leuk",clear:"Gesprek wissen"},Y0={_name:Bc,common:Mc,upload_text:Vc,"3D_model":{"3d_model":"3D-model",drop_to_upload:"Sleep een 3D-model (.obj, .glb, .stl, .gltf, .splat of .ply) bestand hier om te uploaden"},annotated_image:qc,audio:Gc,blocks:Uc,checkbox:Fc,code:Wc,color_picker:Kc,dataframe:Xc,dropdown:Zc,errors:Jc,file:Yc,highlighted_text:Qc,image:ed,label:od,login:td,number:rd,plot:ad,radio:id,slider:nd,video:sd,chatbot:ld},Q0=Object.freeze(Object.defineProperty({__proto__:null,_name:Bc,annotated_image:qc,audio:Gc,blocks:Uc,chatbot:ld,checkbox:Fc,code:Wc,color_picker:Kc,common:Mc,dataframe:Xc,default:Y0,dropdown:Zc,errors:Jc,file:Yc,highlighted_text:Qc,image:ed,label:od,login:td,number:rd,plot:ad,radio:id,slider:nd,upload_text:Vc,video:sd},Symbol.toStringTag,{value:"Module"})),cd="Polski",dd={annotated_image:"Obraz z adnotacjami"},ud={allow_recording_access:"Proszę zezwolić na dostęp do mikrofonu w celu nagrywania.",audio:"Audio",record_from_microphone:"Nagraj z mikrofonu",stop_recording:"Zatrzymaj nagrywanie",no_device_support:"Nie można uzyskać dostępu do urządzeń multimedialnych. Upewnij się, że działasz na bezpiecznym źródle (https) lub localhost (lub przekazałeś prawidłowy certyfikat SSL do ssl_verify), i że zezwoliłeś przeglądarce na dostęp do twojego urządzenia.",stop:"Stop",resume:"Wznów",record:"Nagrywaj",no_microphone:"Nie znaleziono mikrofonu",pause:"Pauza",play:"Odtwórz",waiting:"Oczekiwanie",drop_to_upload:"Upuść plik audio tutaj, aby go przesłać"},_d={connection_can_break:"Na urządzeniu mobilnym połączenie może zostać przerwane, jeśli ta karta straci fokus lub urządzenie przejdzie w tryb uśpienia, tracąc twoje miejsce w kolejce.",long_requests_queue:"Istnieje długa kolejka oczekujących żądań. Zduplikuj ten Space, aby ją pominąć.",lost_connection:"Utracono połączenie z powodu opuszczenia strony. Powrót do kolejki...",waiting_for_inputs:"Oczekiwanie na zakończenie przesyłania pliku(ów), proszę spróbować ponownie."},pd={checkbox:"Pole wyboru",checkbox_group:"Grupa pól wyboru"},md={code:"Kod"},hd={color_picker:"Wybór koloru"},gd={built_with:"Zbudowane z",built_with_gradio:"Zbudowane z Gradio",clear:"Wyczyść",download:"Pobierz",edit:"Edytuj",empty:"Pusty",error:"Błąd",hosted_on:"Hostowane na",loading:"Ładowanie",logo:"Logo",or:"lub",remove:"Usuń",settings:"Ustawienia",share:"Udostępnij",submit:"Wyślij",undo:"Cofnij",no_devices:"Nie znaleziono urządzeń",language:"Język",display_theme:"Motyw wyświetlania",pwa:"Progresywna aplikacja webowa"},fd={incorrect_format:"Nieprawidłowy format, obsługiwane są tylko pliki CSV i TSV",new_column:"Dodaj kolumnę",new_row:"Nowy wiersz",add_row_above:"Dodaj wiersz powyżej",add_row_below:"Dodaj wiersz poniżej",add_column_left:"Dodaj kolumnę po lewej",add_column_right:"Dodaj kolumnę po prawej",delete_row:"Usuń wiersz",delete_column:"Usuń kolumnę",sort_column:"Sortuj kolumnę",sort_ascending:"Sortuj rosnąco",sort_descending:"Sortuj malejąco",drop_to_upload:"Upuść pliki CSV lub TSV tutaj, aby zaimportować dane do ramki danych",clear_sort:"Wyczyść sortowanie"},bd={dropdown:"Lista rozwijana"},vd={build_error:"Wystąpił błąd kompilacji",config_error:"Wystąpił błąd konfiguracji",contact_page_author:"Proszę skontaktować się z autorem strony, aby go poinformować.",no_app_file:"Brak pliku aplikacji",runtime_error:"Wystąpił błąd wykonania",space_not_working:'"Space nie działa, ponieważ" {0}',space_paused:"Space jest wstrzymany",use_via_api:"Użyj przez API",use_via_api_or_mcp:"Użyj przez API lub MCP"},wd={uploading:"Przesyłanie..."},yd={highlighted_text:"Wyróżniony tekst"},kd={allow_webcam_access:"Proszę zezwolić na dostęp do kamery internetowej w celu nagrywania.",brush_color:"Kolor pędzla",brush_radius:"Rozmiar pędzla",image:"Obraz",remove_image:"Usuń obraz",select_brush_color:"Wybierz kolor pędzla",start_drawing:"Rozpocznij rysowanie",use_brush:"Użyj pędzla",drop_to_upload:"Upuść plik obrazu tutaj, aby go przesłać"},Sd={label:"Etykieta"},xd={enable_cookies:"Jeśli odwiedzasz HuggingFace Space w trybie incognito, musisz włączyć pliki cookie stron trzecich.",incorrect_credentials:"Nieprawidłowe dane logowania",username:"Nazwa użytkownika",password:"Hasło",login:"Zaloguj się"},Ed={number:"Liczba"},$d={plot:"Wykres"},Pd={radio:"Przycisk opcji"},Ad={slider:"Suwak"},Td={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Upuść plik audio tutaj",drop_csv:"Upuść plik CSV tutaj",drop_file:"Upuść plik tutaj",drop_image:"Upuść obraz tutaj",drop_video:"Upuść plik wideo tutaj",drop_gallery:"Upuść media tutaj",paste_clipboard:"Wklej ze schowka"},Od={drop_to_upload:"Upuść plik wideo tutaj, aby go przesłać"},jd={edit:"Edytuj",retry:"Spróbuj ponownie",undo:"Cofnij",submit:"Wyślij",cancel:"Anuluj",like:"Lubię to",dislike:"Nie lubię tego",clear:"Wyczyść czat"},ev={_name:cd,"3D_model":{"3d_model":"Model 3D",drop_to_upload:"Upuść plik modelu 3D (.obj, .glb, .stl, .gltf, .splat lub .ply) tutaj, aby go przesłać"},annotated_image:dd,audio:ud,blocks:_d,checkbox:pd,code:md,color_picker:hd,common:gd,dataframe:fd,dropdown:bd,errors:vd,file:wd,highlighted_text:yd,image:kd,label:Sd,login:xd,number:Ed,plot:$d,radio:Pd,slider:Ad,upload_text:Td,video:Od,chatbot:jd},ov=Object.freeze(Object.defineProperty({__proto__:null,_name:cd,annotated_image:dd,audio:ud,blocks:_d,chatbot:jd,checkbox:pd,code:md,color_picker:hd,common:gd,dataframe:fd,default:ev,dropdown:bd,errors:vd,file:wd,highlighted_text:yd,image:kd,label:Sd,login:xd,number:Ed,plot:$d,radio:Pd,slider:Ad,upload_text:Td,video:Od},Symbol.toStringTag,{value:"Module"})),Cd="Português do Brasil",Ld={built_with_gradio:"Construído com Gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar",built_with:"Construído com",download:"Baixar",edit:"Editar",empty:"Vazio",hosted_on:"Hospedado em",logo:"Logotipo",settings:"Configurações",undo:"Desfazer",no_devices:"Nenhum dispositivo encontrado",language:"Idioma",display_theme:"Tema de exibição",pwa:"Aplicativo web progressivo",share:"Compartilhar",remove:"Remover"},Dd={click_to_upload:"Clique para fazer upload",drop_audio:"Solte o áudio aqui",drop_csv:"Solte o CSV aqui",drop_file:"Solte o arquivo aqui",drop_image:"Solte a imagem aqui",drop_video:"Solte o vídeo aqui",drop_gallery:"Solte a mídia aqui",paste_clipboard:"Colar da área de transferência"},Id={annotated_image:"Imagem anotada"},zd={allow_recording_access:"Por favor, permita o acesso ao microfone para gravação.",audio:"Áudio",drop_to_upload:"Solte um arquivo de áudio aqui para fazer upload",record_from_microphone:"Gravar do microfone",stop_recording:"Parar gravação",no_device_support:"Os dispositivos de mídia não puderam ser acessados. Verifique se você está executando em uma origem segura (https) ou localhost (ou se forneceu um certificado SSL válido para ssl_verify), e se permitiu o acesso do navegador ao seu dispositivo.",stop:"Parar",resume:"Retomar",record:"Gravar",no_microphone:"Nenhum microfone encontrado",pause:"Pausar",play:"Reproduzir",waiting:"Aguardando"},Rd={connection_can_break:"No celular, a conexão pode ser interrompida se esta aba ficar em segundo plano ou se o dispositivo entrar em modo de repouso, fazendo você perder sua posição na fila.",long_requests_queue:"Há uma longa fila de solicitações pendentes. Duplique este Space para pular a fila.",lost_connection:"Conexão perdida ao sair da página. Retornando para a fila...",waiting_for_inputs:"Aguardando o(s) arquivo(s) terminar(em) de ser carregado(s), por favor, tente novamente."},Nd={checkbox:"Caixa de seleção",checkbox_group:"Grupo de caixas de seleção"},Hd={code:"Código"},Bd={color_picker:"Seletor de cor"},Md={incorrect_format:"Formato incorreto, apenas arquivos CSV e TSV são suportados",new_column:"Adicionar coluna",new_row:"Nova linha",add_row_above:"Adicionar linha acima",add_row_below:"Adicionar linha abaixo",delete_row:"Excluir linha",delete_column:"Excluir coluna",add_column_left:"Adicionar coluna à esquerda",add_column_right:"Adicionar coluna à direita",sort_column:"Classificar coluna",sort_ascending:"Ordenar em ordem crescente",sort_descending:"Ordenar em ordem decrescente",drop_to_upload:"Solte arquivos CSV ou TSV aqui para importar dados para o dataframe",clear_sort:"Limpar classificação"},Vd={dropdown:"Menu suspenso"},qd={build_error:"Houve um erro de compilação",config_error:"Há um erro de configuração",contact_page_author:"Por favor, entre em contato com o autor da página para informá-lo.",no_app_file:"Não há arquivo de aplicativo",space_not_working:'"O Space não está funcionando porque" {0}',space_paused:"O Space está pausado",use_via_api:"Usar via API",use_via_api_or_mcp:"Usar via API ou MCP",runtime_error:"Houve um erro de execução"},Gd={uploading:"Carregando..."},Ud={highlighted_text:"Texto destacado"},Fd={allow_webcam_access:"Por favor, permita o acesso à webcam para gravação.",brush_color:"Cor do pincel",brush_radius:"Tamanho do pincel",image:"Imagem",remove_image:"Remover imagem",select_brush_color:"Selecione a cor do pincel",start_drawing:"Começar a desenhar",use_brush:"Usar pincel",drop_to_upload:"Solte um arquivo de imagem aqui para fazer upload"},Wd={label:"Rótulo"},Kd={enable_cookies:"Se você estiver visitando um HuggingFace Space no modo incógnito, você deve habilitar cookies de terceiros.",incorrect_credentials:"Credenciais incorretas",username:"Nome de usuário",password:"Senha",login:"Entrar"},Xd={number:"Número"},Zd={plot:"Gráfico"},Jd={radio:"Botão de opção"},Yd={slider:"Controle deslizante"},Qd={drop_to_upload:"Solte um arquivo de vídeo aqui para fazer upload"},eu={edit:"Editar",retry:"Tentar novamente",undo:"Desfazer",submit:"Enviar",cancel:"Cancelar",like:"Gostei",dislike:"Não gostei",clear:"Limpar conversa"},tv={_name:Cd,common:Ld,upload_text:Dd,"3D_model":{"3d_model":"Modelo 3D",drop_to_upload:"Solte um arquivo de modelo 3D (.obj, .glb, .stl, .gltf, .splat ou .ply) aqui para fazer upload"},annotated_image:Id,audio:zd,blocks:Rd,checkbox:Nd,code:Hd,color_picker:Bd,dataframe:Md,dropdown:Vd,errors:qd,file:Gd,highlighted_text:Ud,image:Fd,label:Wd,login:Kd,number:Xd,plot:Zd,radio:Jd,slider:Yd,video:Qd,chatbot:eu},rv=Object.freeze(Object.defineProperty({__proto__:null,_name:Cd,annotated_image:Id,audio:zd,blocks:Rd,chatbot:eu,checkbox:Nd,code:Hd,color_picker:Bd,common:Ld,dataframe:Md,default:tv,dropdown:Vd,errors:qd,file:Gd,highlighted_text:Ud,image:Fd,label:Wd,login:Kd,number:Xd,plot:Zd,radio:Jd,slider:Yd,upload_text:Dd,video:Qd},Symbol.toStringTag,{value:"Module"})),ou="Português",tu={annotated_image:"Imagem anotada"},ru={allow_recording_access:"Por favor, permita o acesso ao microfone para gravação.",audio:"Áudio",record_from_microphone:"Gravar do microfone",stop_recording:"Parar gravação",no_device_support:"Não é possível aceder a dispositivos de média. Certifique-se de que está a executar numa fonte segura (https) ou localhost (ou passou um certificado SSL válido para ssl_verify), e que permitiu o acesso do navegador ao seu dispositivo.",stop:"Parar",resume:"Retomar",record:"Gravar",no_microphone:"Nenhum microfone encontrado",pause:"Pausar",play:"Reproduzir",waiting:"Aguardando",drop_to_upload:"Solte um arquivo de áudio aqui para fazer upload"},au={connection_can_break:"No telemóvel, a ligação pode ser interrompida se este separador perder o foco ou o dispositivo entrar em modo de suspensão, perdendo a sua posição na fila.",long_requests_queue:"Há uma longa fila de solicitações pendentes. Duplique este Space para saltar a fila.",lost_connection:"Ligação perdida devido à saída da página. A voltar à fila...",waiting_for_inputs:"A aguardar o término do carregamento do(s) ficheiro(s), por favor, tente novamente."},iu={checkbox:"Caixa de seleção",checkbox_group:"Grupo de caixas de seleção"},nu={code:"Código"},su={color_picker:"Seletor de cor"},lu={built_with:"Construído com",built_with_gradio:"Construído com Gradio",clear:"Limpar",download:"Descarregar",edit:"Editar",empty:"Vazio",error:"Erro",hosted_on:"Alojado em",loading:"A carregar",logo:"Logótipo",or:"ou",remove:"Remover",settings:"Configurações",share:"Partilhar",submit:"Enviar",undo:"Desfazer",no_devices:"Nenhum dispositivo encontrado",language:"Idioma",display_theme:"Tema de exibição",pwa:"Aplicação web progressiva"},cu={incorrect_format:"Formato incorreto, apenas ficheiros CSV e TSV são suportados",new_column:"Adicionar coluna",new_row:"Nova linha",add_row_above:"Adicionar linha acima",add_row_below:"Adicionar linha abaixo",add_column_left:"Adicionar coluna à esquerda",add_column_right:"Adicionar coluna à direita",delete_row:"Eliminar linha",delete_column:"Eliminar coluna",sort_column:"Ordenar coluna",sort_ascending:"Ordenar por ordem crescente",sort_descending:"Ordenar por ordem decrescente",drop_to_upload:"Solte ficheiros CSV ou TSV aqui para importar dados para o dataframe",clear_sort:"Limpar ordenação"},du={dropdown:"Lista suspensa"},uu={build_error:"Há um erro de compilação",config_error:"Há um erro de configuração",contact_page_author:"Por favor, entre em contacto com o autor da página para informá-lo.",no_app_file:"Não há ficheiro de aplicação",runtime_error:"Há um erro de execução",space_not_working:'"O Space não está a funcionar porque" {0}',space_paused:"O Space está em pausa",use_via_api:"Usar via API",use_via_api_or_mcp:"Usar via API ou MCP"},_u={uploading:"A carregar..."},pu={highlighted_text:"Texto destacado"},mu={allow_webcam_access:"Por favor, permita o acesso à webcam para gravação.",brush_color:"Cor do pincel",brush_radius:"Tamanho do pincel",image:"Imagem",remove_image:"Remover imagem",select_brush_color:"Selecionar cor do pincel",start_drawing:"Começar a desenhar",use_brush:"Usar pincel",drop_to_upload:"Solte um ficheiro de imagem aqui para fazer upload"},hu={label:"Rótulo"},gu={enable_cookies:"Se estiver a visitar um Space HuggingFace no modo anónimo, precisa de ativar cookies de terceiros.",incorrect_credentials:"Credenciais incorretas",username:"Nome de utilizador",password:"Palavra-passe",login:"Iniciar sessão"},fu={number:"Número"},bu={plot:"Gráfico"},vu={radio:"Botão de opção"},wu={slider:"Controlo deslizante"},yu={click_to_upload:"Clique para carregar",drop_audio:"Solte o áudio aqui",drop_csv:"Solte o CSV aqui",drop_file:"Solte o ficheiro aqui",drop_image:"Solte a imagem aqui",drop_video:"Solte o vídeo aqui",drop_gallery:"Solte a média aqui",paste_clipboard:"Colar da área de transferência"},ku={drop_to_upload:"Solte um ficheiro de vídeo aqui para fazer upload"},Su={edit:"Editar",retry:"Tentar novamente",undo:"Desfazer",submit:"Enviar",cancel:"Cancelar",like:"Gosto",dislike:"Não gosto",clear:"Limpar conversa"},av={_name:ou,"3D_model":{"3d_model":"Modelo 3D",drop_to_upload:"Solte um arquivo de modelo 3D (.obj, .glb, .stl, .gltf, .splat ou .ply) aqui para fazer upload"},annotated_image:tu,audio:ru,blocks:au,checkbox:iu,code:nu,color_picker:su,common:lu,dataframe:cu,dropdown:du,errors:uu,file:_u,highlighted_text:pu,image:mu,label:hu,login:gu,number:fu,plot:bu,radio:vu,slider:wu,upload_text:yu,video:ku,chatbot:Su},iv=Object.freeze(Object.defineProperty({__proto__:null,_name:ou,annotated_image:tu,audio:ru,blocks:au,chatbot:Su,checkbox:iu,code:nu,color_picker:su,common:lu,dataframe:cu,default:av,dropdown:du,errors:uu,file:_u,highlighted_text:pu,image:mu,label:hu,login:gu,number:fu,plot:bu,radio:vu,slider:wu,upload_text:yu,video:ku},Symbol.toStringTag,{value:"Module"})),xu="Română",Eu={annotated_image:"Imagine adnotată"},$u={allow_recording_access:"Vă rugăm să permiteți accesul la microfon pentru înregistrare.",audio:"Audio",record_from_microphone:"Înregistrare de la microfon",stop_recording:"Oprire înregistrare",no_device_support:"Nu se poate accesa dispozitivele media. Asigurați-vă că rulați pe o sursă sigură (https) sau localhost (sau ați transmis un certificat SSL valid la ssl_verify), și că ați permis browserului accesul la dispozitivul dvs.",stop:"Stop",resume:"Reluare",record:"Înregistrare",no_microphone:"Nu s-a găsit niciun microfon",pause:"Pauză",play:"Redare",waiting:"Așteptare",drop_to_upload:"Plasați un fișier audio aici pentru a-l încărca"},Pu={connection_can_break:"Pe mobil, conexiunea poate fi întreruptă dacă această filă își pierde focusul sau dispozitivul intră în modul de repaus, pierzându-vă poziția în coadă.",long_requests_queue:"Există o coadă lungă de cereri în așteptare. Duplicați acest Space pentru a o evita.",lost_connection:"Conexiune pierdută din cauza părăsirii paginii. Revenire la coadă...",waiting_for_inputs:"Așteptarea finalizării încărcării fișierului (fișierelor), vă rugăm să reîncercați."},Au={checkbox:"Casetă de bifat",checkbox_group:"Grup de casete de bifat"},Tu={code:"Cod"},Ou={color_picker:"Selector de culoare"},ju={built_with:"Construit cu",built_with_gradio:"Construit cu Gradio",clear:"Șterge",download:"Descarcă",edit:"Editează",empty:"Gol",error:"Eroare",hosted_on:"Găzduit pe",loading:"Se încarcă",logo:"Logo",or:"sau",remove:"Elimină",settings:"Setări",share:"Distribuie",submit:"Trimite",undo:"Anulează",no_devices:"Nu s-au găsit dispozitive",language:"Limbă",display_theme:"Temă de afișare",pwa:"Aplicație web progresivă"},Cu={incorrect_format:"Format incorect, sunt acceptate doar fișiere CSV și TSV",new_column:"Adaugă coloană",new_row:"Rând nou",add_row_above:"Adaugă rând deasupra",add_row_below:"Adaugă rând dedesubt",add_column_left:"Adaugă coloană la stânga",add_column_right:"Adaugă coloană la dreapta",delete_row:"Șterge rând",delete_column:"Șterge coloana",sort_column:"Sortează coloana",sort_ascending:"Sortează crescător",sort_descending:"Sortează descrescător",drop_to_upload:"Plasați fișiere CSV sau TSV aici pentru a importa datele în tabelul de date",clear_sort:"Șterge sortarea"},Lu={dropdown:"Listă derulantă"},Du={build_error:"Există o eroare de compilare",config_error:"Există o eroare de configurare",contact_page_author:"Vă rugăm să contactați autorul paginii pentru a-l informa.",no_app_file:"Nu există fișier de aplicație",runtime_error:"Există o eroare de execuție",space_not_working:'"Space-ul nu funcționează deoarece" {0}',space_paused:"Space-ul este în pauză",use_via_api:"Utilizați prin API",use_via_api_or_mcp:"Utilizați prin API sau MCP"},Iu={uploading:"Se încarcă..."},zu={highlighted_text:"Text evidențiat"},Ru={allow_webcam_access:"Vă rugăm să permiteți accesul la camera web pentru înregistrare.",brush_color:"Culoare pensulă",brush_radius:"Dimensiune pensulă",image:"Imagine",remove_image:"Elimină imaginea",select_brush_color:"Selectează culoarea pensulei",start_drawing:"Începe să desenezi",use_brush:"Folosește pensula",drop_to_upload:"Plasați un fișier imagine aici pentru a-l încărca"},Nu={label:"Etichetă"},Hu={enable_cookies:"Dacă vizitați un Space HuggingFace în modul incognito, trebuie să activați cookie-urile terțe.",incorrect_credentials:"Credențiale incorecte",username:"Nume de utilizator",password:"Parolă",login:"Autentificare"},Bu={number:"Număr"},Mu={plot:"Grafic"},Vu={radio:"Buton radio"},qu={slider:"Glisor"},Gu={click_to_upload:"Click pentru încărcare",drop_audio:"Plasați audio aici",drop_csv:"Plasați CSV aici",drop_file:"Plasați fișierul aici",drop_image:"Plasați imaginea aici",drop_video:"Plasați video aici",drop_gallery:"Plasați media aici",paste_clipboard:"Lipește din clipboard"},Uu={drop_to_upload:"Plasați un fișier video aici pentru a-l încărca"},Fu={edit:"Editează",retry:"Încearcă din nou",undo:"Anulează",submit:"Trimite",cancel:"Anulează",like:"Îmi place",dislike:"Nu-mi place",clear:"Șterge conversația"},nv={_name:xu,"3D_model":{"3d_model":"Model 3D",drop_to_upload:"Plasați un fișier cu model 3D (.obj, .glb, .stl, .gltf, .splat sau .ply) aici pentru a-l încărca"},annotated_image:Eu,audio:$u,blocks:Pu,checkbox:Au,code:Tu,color_picker:Ou,common:ju,dataframe:Cu,dropdown:Lu,errors:Du,file:Iu,highlighted_text:zu,image:Ru,label:Nu,login:Hu,number:Bu,plot:Mu,radio:Vu,slider:qu,upload_text:Gu,video:Uu,chatbot:Fu},sv=Object.freeze(Object.defineProperty({__proto__:null,_name:xu,annotated_image:Eu,audio:$u,blocks:Pu,chatbot:Fu,checkbox:Au,code:Tu,color_picker:Ou,common:ju,dataframe:Cu,default:nv,dropdown:Lu,errors:Du,file:Iu,highlighted_text:zu,image:Ru,label:Nu,login:Hu,number:Bu,plot:Mu,radio:Vu,slider:qu,upload_text:Gu,video:Uu},Symbol.toStringTag,{value:"Module"})),Wu="Русский",Ku={annotated_image:"Изображение с аннотациями"},Xu={allow_recording_access:"Пожалуйста, разрешите доступ к микрофону для записи.",audio:"Аудио",record_from_microphone:"Запись с микрофона",stop_recording:"Остановить запись",no_device_support:"Невозможно получить доступ к медиа-устройствам. Убедитесь, что вы работаете с безопасного источника (https) или localhost (или передали действительный SSL-сертификат в ssl_verify), и что вы разрешили браузеру доступ к вашему устройству.",stop:"Стоп",resume:"Продолжить",record:"Запись",no_microphone:"Микрофон не найден",pause:"Пауза",play:"Воспроизвести",waiting:"Ожидание",drop_to_upload:"Перетащите аудиофайл сюда для загрузки"},Zu={connection_can_break:"На мобильном устройстве соединение может прерваться, если эта вкладка потеряет фокус или устройство перейдет в спящий режим, что приведет к потере вашего места в очереди.",long_requests_queue:"Существует длинная очередь ожидающих запросов. Создайте дубликат этого Space, чтобы пропустить очередь.",lost_connection:"Соединение потеряно из-за ухода со страницы. Возвращение в очередь...",waiting_for_inputs:"Ожидание завершения загрузки файла(ов), пожалуйста, повторите попытку."},Ju={checkbox:"Флажок",checkbox_group:"Группа флажков"},Yu={code:"Код"},Qu={color_picker:"Выбор цвета"},e_={built_with:"Создано с помощью",built_with_gradio:"Создано с помощью Gradio",clear:"Очистить",download:"Скачать",edit:"Редактировать",empty:"Пусто",error:"Ошибка",hosted_on:"Размещено на",loading:"Загрузка",logo:"Логотип",or:"или",remove:"Удалить",settings:"Настройки",share:"Поделиться",submit:"Отправить",undo:"Отменить",no_devices:"Устройства не найдены",language:"Язык",display_theme:"Тема оформления",pwa:"Прогрессивное веб-приложение"},o_={incorrect_format:"Неверный формат, поддерживаются только файлы CSV и TSV",new_column:"Добавить столбец",new_row:"Новая строка",add_row_above:"Добавить строку выше",add_row_below:"Добавить строку ниже",add_column_left:"Добавить столбец слева",add_column_right:"Добавить столбец справа",delete_row:"Удалить строку",delete_column:"Удалить столбец",sort_column:"Сортировать столбец",sort_ascending:"Сортировать по возрастанию",sort_descending:"Сортировать по убыванию",drop_to_upload:"Перетащите файлы CSV или TSV сюда для импорта данных в таблицу данных",clear_sort:"Очистить сортировку"},t_={dropdown:"Выпадающий список"},r_={build_error:"Произошла ошибка сборки",config_error:"Произошла ошибка конфигурации",contact_page_author:"Пожалуйста, свяжитесь с автором страницы, чтобы сообщить об этом.",no_app_file:"Отсутствует файл приложения",runtime_error:"Произошла ошибка выполнения",space_not_working:'"Space не работает, потому что" {0}',space_paused:"Space приостановлен",use_via_api:"Использовать через API",use_via_api_or_mcp:"Использовать через API или MCP"},a_={uploading:"Загрузка..."},i_={highlighted_text:"Выделенный текст"},n_={allow_webcam_access:"Пожалуйста, разрешите доступ к веб-камере для записи.",brush_color:"Цвет кисти",brush_radius:"Размер кисти",image:"Изображение",remove_image:"Удалить изображение",select_brush_color:"Выбрать цвет кисти",start_drawing:"Начать рисование",use_brush:"Использовать кисть",drop_to_upload:"Перетащите изображение сюда для загрузки"},s_={label:"Метка"},l_={enable_cookies:"Если вы посещаете Space HuggingFace в режиме инкогнито, вам необходимо включить сторонние куки.",incorrect_credentials:"Неверные учетные данные",username:"Имя пользователя",password:"Пароль",login:"Войти"},c_={number:"Число"},d_={plot:"График"},u_={radio:"Переключатель"},__={slider:"Ползунок"},p_={click_to_upload:"Нажмите для загрузки",drop_audio:"Перетащите аудио сюда",drop_csv:"Перетащите CSV сюда",drop_file:"Перетащите файл сюда",drop_image:"Перетащите изображение сюда",drop_video:"Перетащите видео сюда",drop_gallery:"Перетащите медиа сюда",paste_clipboard:"Вставить из буфера обмена"},m_={drop_to_upload:"Перетащите видеофайл сюда для загрузки"},h_={edit:"Редактировать",retry:"Повторить",undo:"Отменить",submit:"Отправить",cancel:"Отмена",like:"Нравится",dislike:"Не нравится",clear:"Очистить чат"},lv={_name:Wu,"3D_model":{"3d_model":"3D-модель",drop_to_upload:"Перетащите сюда 3D-модель (.obj, .glb, .stl, .gltf, .splat или .ply) для загрузки"},annotated_image:Ku,audio:Xu,blocks:Zu,checkbox:Ju,code:Yu,color_picker:Qu,common:e_,dataframe:o_,dropdown:t_,errors:r_,file:a_,highlighted_text:i_,image:n_,label:s_,login:l_,number:c_,plot:d_,radio:u_,slider:__,upload_text:p_,video:m_,chatbot:h_},cv=Object.freeze(Object.defineProperty({__proto__:null,_name:Wu,annotated_image:Ku,audio:Xu,blocks:Zu,chatbot:h_,checkbox:Ju,code:Yu,color_picker:Qu,common:e_,dataframe:o_,default:lv,dropdown:t_,errors:r_,file:a_,highlighted_text:i_,image:n_,label:s_,login:l_,number:c_,plot:d_,radio:u_,slider:__,upload_text:p_,video:m_},Symbol.toStringTag,{value:"Module"})),g_="Svenska",f_={annotated_image:"Annoterad bild"},b_={allow_recording_access:"Tillåt mikrofonåtkomst för inspelning.",audio:"Ljud",record_from_microphone:"Spela in från mikrofon",stop_recording:"Stoppa inspelning",no_device_support:"Kan inte få åtkomst till medieenheter. Se till att du använder en säker källa (https) eller localhost (eller har gett ett giltigt SSL-certifikat till ssl_verify) och att du har gett webbläsaren åtkomst till din enhet.",stop:"Stopp",resume:"Fortsätt",record:"Spela in",no_microphone:"Ingen mikrofon hittades",pause:"Paus",play:"Spela upp",waiting:"Väntar",drop_to_upload:"Släpp en ljudfil här för att ladda upp"},v_={connection_can_break:"På mobila enheter kan anslutningen brytas om fliken lämnas eller om enheten går i viloläge, och du förlorar din plats i kön.",long_requests_queue:"Det är en lång kö med väntande förfrågningar. Duplicera detta Space för att hoppa över kön.",lost_connection:"Anslutningen bröts eftersom sidan lämnades. Återgår till kön...",waiting_for_inputs:"Väntar på att fil(er) ska vara klara att ladda upp, vänligen försök igen."},w_={checkbox:"Kryssruta",checkbox_group:"Kryssrutgrupp"},y_={code:"Kod"},k_={color_picker:"Färgväljare"},S_={built_with:"Byggt med",built_with_gradio:"Byggt med Gradio",clear:"Rensa",download:"Ladda ner",edit:"Redigera",empty:"Tom",error:"Fel",hosted_on:"Värd på",loading:"Laddar",logo:"Logotyp",or:"eller",remove:"Ta bort",settings:"Inställningar",share:"Dela",submit:"Skicka",undo:"Ångra",no_devices:"Inga enheter hittades",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webbapplikation"},x_={incorrect_format:"Fel format, endast CSV- och TSV-filer stöds",new_column:"Lägg till kolumn",new_row:"Ny rad",add_row_above:"Lägg till rad ovanför",add_row_below:"Lägg till rad nedanför",add_column_left:"Lägg till kolumn till vänster",add_column_right:"Lägg till kolumn till höger",delete_row:"Ta bort rad",delete_column:"Ta bort kolumn",sort_column:"Sortera kolumn",sort_ascending:"Sortera stigande",sort_descending:"Sortera fallande",drop_to_upload:"Släpp CSV- eller TSV-filer här för att importera data till dataramverk",clear_sort:"Rensa sortering"},E_={dropdown:"Rullgardinsmeny"},$_={build_error:"Det finns ett byggfel",config_error:"Det finns ett konfigurationsfel",contact_page_author:"Kontakta sidans författare för att informera dem.",no_app_file:"Det finns ingen app-fil",runtime_error:"Det finns ett körtidsfel",space_not_working:'"Space fungerar inte eftersom" {0}',space_paused:"Space är pausat",use_via_api:"Använd via API",use_via_api_or_mcp:"Använd via API eller MCP"},P_={uploading:"Laddar upp..."},A_={highlighted_text:"Markerad text"},T_={allow_webcam_access:"Tillåt webbkameråtkomst för inspelning.",brush_color:"Penselfärg",brush_radius:"Penselstorlek",image:"Bild",remove_image:"Ta bort bild",select_brush_color:"Välj penselfärg",start_drawing:"Börja rita",use_brush:"Använd pensel",drop_to_upload:"Släpp en bildfil här för att ladda upp"},O_={label:"Etikett"},j_={enable_cookies:"Om du besöker ett HuggingFace Space i inkognitoläge måste du aktivera tredjepartscookies.",incorrect_credentials:"Felaktiga inloggningsuppgifter",username:"Användarnamn",password:"Lösenord",login:"Logga in"},C_={number:"Nummer"},L_={plot:"Diagram"},D_={radio:"Radioknapp"},I_={slider:"Skjutreglage"},z_={click_to_upload:"Klicka för att ladda upp",drop_audio:"Släpp ljud här",drop_csv:"Släpp CSV här",drop_file:"Släpp fil här",drop_image:"Släpp bild här",drop_video:"Släpp video här",drop_gallery:"Släpp media här",paste_clipboard:"Klistra in från urklipp"},R_={drop_to_upload:"Släpp en videofil här för att ladda upp"},N_={edit:"Redigera",retry:"Försök igen",undo:"Ångra",submit:"Skicka",cancel:"Avbryt",like:"Gilla",dislike:"Ogilla",clear:"Rensa chatten"},dv={_name:g_,"3D_model":{"3d_model":"3D-modell",drop_to_upload:"Släpp en 3D-modellfil (.obj, .glb, .stl, .gltf, .splat eller .ply) här för att ladda upp"},annotated_image:f_,audio:b_,blocks:v_,checkbox:w_,code:y_,color_picker:k_,common:S_,dataframe:x_,dropdown:E_,errors:$_,file:P_,highlighted_text:A_,image:T_,label:O_,login:j_,number:C_,plot:L_,radio:D_,slider:I_,upload_text:z_,video:R_,chatbot:N_},uv=Object.freeze(Object.defineProperty({__proto__:null,_name:g_,annotated_image:f_,audio:b_,blocks:v_,chatbot:N_,checkbox:w_,code:y_,color_picker:k_,common:S_,dataframe:x_,default:dv,dropdown:E_,errors:$_,file:P_,highlighted_text:A_,image:T_,label:O_,login:j_,number:C_,plot:L_,radio:D_,slider:I_,upload_text:z_,video:R_},Symbol.toStringTag,{value:"Module"})),H_="தமிழ்",B_={built_with_gradio:"கிரேடியோவுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்",built_with:"உருவாக்கப்பட்டது",download:"பதிவிறக்கம்",edit:"திருத்து",empty:"காலியாக உள்ளது",error:"பிழை",hosted_on:"இதில் ஹோஸ்ட் செய்யப்பட்டது",loading:"ஏற்றுகிறது",logo:"லோகோ",remove:"நீக்கு",settings:"அமைப்புகள்",share:"பகிர்",undo:"செயல்தவிர்",no_devices:"சாதனங்கள் எதுவும் கிடைக்கவில்லை",language:"மொழி",display_theme:"காட்சி தீம்",pwa:"முன்னேற்ற வலை பயன்பாடு"},M_={click_to_upload:"பதிவேற்ற கிளிக் செய்யவும்",drop_audio:"ஆடியோவை இங்கே விடவும்",drop_csv:"CSV ஐ இங்கே விடவும்",drop_file:"கோப்பை இங்கே விடவும்",drop_image:"படத்தை இங்கே விடவும்",drop_video:"வீடியோவை இங்கே விடவும்",drop_gallery:"மீடியாவை இங்கே விடவும்",paste_clipboard:"கிளிப்போர்டிலிருந்து ஒட்டவும்"},V_={annotated_image:"குறிப்பிடப்பட்ட படம்"},q_={allow_recording_access:"தயவுசெய்து பதிவு செய்ய மைக்ரோஃபோன் அணுகலை அனுமதிக்கவும்.",audio:"ஒலி",drop_to_upload:"ஒலிக் கோப்பை பதிவேற்ற இங்கே விடவும்",record_from_microphone:"மைக்ரோஃபோனிலிருந்து பதிவு செய்யவும்",stop_recording:"பதிவை நிறுத்தவும்",no_device_support:"மீடியா சாதனங்களை அணுக முடியவில்லை. நீங்கள் பாதுகாப்பான மூலத்தில் (https) அல்லது localhost இல் இயங்குகிறீர்களா (அல்லது ssl_verify க்கு செல்லுபடியாகும் SSL சான்றிதழை அனுப்பியுள்ளீர்களா) என்பதையும், உங்கள் சாதனத்திற்கான உலாவி அணுகலை அனுமதித்துள்ளீர்களா என்பதையும் சரிபார்க்கவும்.",stop:"நிறுத்து",resume:"தொடரவும்",record:"பதிவு செய்",no_microphone:"மைக்ரோஃபோன் எதுவும் கிடைக்கவில்லை",pause:"இடைநிறுத்து",play:"இயக்கு",waiting:"காத்திருக்கிறது"},G_={connection_can_break:"மொபைலில், இந்தத் தாவல் ஃபோகஸை இழந்தால் அல்லது சாதனம் உறங்கினால், இணைப்பு துண்டிக்கப்பட்டு, வரிசையில் உங்கள் இடத்தை இழக்கலாம்.",long_requests_queue:"காத்திருக்கும் கோரிக்கைகளின் நீண்ட வரிசை உள்ளது. வரிசையைத் தவிர்க்க இந்த Space ஐ நகலெடுக்கவும்.",lost_connection:"பக்கத்தை விட்டு வெளியேறியதால் இணைப்பு துண்டிக்கப்பட்டது. வரிசைக்கு மீண்டும் இணைகிறது...",waiting_for_inputs:"கோப்பு(கள்) பதிவேற்றம் முடிவடைய காத்திருக்கிறது, தயவுசெய்து மீண்டும் முயற்சிக்கவும்."},U_={checkbox:"தேர்வுப்பெட்டி",checkbox_group:"தேர்வுப்பெட்டி குழு"},F_={code:"குறியீடு"},W_={color_picker:"வண்ணத் தேர்வி"},K_={incorrect_format:"தவறான வடிவம், CSV மற்றும் TSV கோப்புகள் மட்டுமே ஆதரிக்கப்படுகின்றன",new_column:"நெடுவரிசை சேர்க்க",new_row:"புதிய வரிசை",add_row_above:"மேலே வரிசை சேர்க்க",add_row_below:"கீழே வரிசை சேர்க்க",delete_row:"வரிசையை நீக்கு",delete_column:"நெடுவரிசையை நீக்கு",add_column_left:"இடது பக்கத்தில் நெடுவரிசை சேர்க்க",add_column_right:"வலது பக்கத்தில் நெடுவரிசை சேர்க்க",sort_column:"நெடுவரிசையை வரிசைப்படுத்து",sort_ascending:"ஏறுவரிசையில் வரிசைப்படுத்து",sort_descending:"இறங்குவரிசையில் வரிசைப்படுத்து",drop_to_upload:"தரவுத்தளத்திற்கு தரவை இறக்குமதி செய்ய CSV அல்லது TSV கோப்புகளை இங்கே விடவும்",clear_sort:"வரிசைப்படுத்தலை அழி"},X_={dropdown:"கீழ்தோன்றல் பட்டியல்"},Z_={build_error:"உருவாக்க பிழை உள்ளது",config_error:"கட்டமைப்பு பிழை உள்ளது",contact_page_author:"தயவுசெய்து பக்க ஆசிரியரைத் தொடர்பு கொண்டு தெரிவிக்கவும்.",no_app_file:"பயன்பாட்டுக் கோப்பு இல்லை",runtime_error:"இயக்க நேர பிழை உள்ளது",space_not_working:'"Space செயல்படவில்லை ஏனெனில்" {0}',space_paused:"Space இடைநிறுத்தப்பட்டுள்ளது",use_via_api:"API மூலம் பயன்படுத்தவும்",use_via_api_or_mcp:"API அல்லது MCP மூலம் பயன்படுத்தவும்"},J_={uploading:"பதிவேற்றுகிறது..."},Y_={highlighted_text:"சிறப்பிக்கப்பட்ட உரை"},Q_={allow_webcam_access:"தயவுசெய்து பதிவு செய்ய வெப்கேம் அணுகலை அனுமதிக்கவும்.",brush_color:"தூரிகை நிறம்",brush_radius:"தூரிகை அளவு",image:"படம்",remove_image:"படத்தை நீக்கு",select_brush_color:"தூரிகை நிறத்தைத் தேர்ந்தெடுக்கவும்",start_drawing:"வரைய தொடங்கவும்",use_brush:"தூரிகையைப் பயன்படுத்தவும்",drop_to_upload:"பட கோப்பை பதிவேற்ற இங்கே விடவும்"},ep={label:"லேபிள்"},op={enable_cookies:"நீங்கள் மறைநிலை பயன்முறையில் HuggingFace Space ஐப் பார்வையிட்டால், மூன்றாம் தரப்பு குக்கீகளை இயக்க வேண்டும்.",incorrect_credentials:"தவறான சான்றுகள்",username:"பயனர்பெயர்",password:"கடவுச்சொல்",login:"உள்நுழைய"},tp={number:"எண்"},rp={plot:"வரைபடம்"},ap={radio:"ரேடியோ பொத்தான்"},ip={slider:"சறுக்கி"},np={drop_to_upload:"வீடியோ கோப்பை பதிவேற்ற இங்கே விடவும்"},sp={edit:"திருத்து",retry:"மீண்டும் முயற்சி",undo:"செயல்தவிர்",submit:"சமர்ப்பிக்கவும்",cancel:"ரத்து",like:"விரும்புகிறேன்",dislike:"விரும்பவில்லை",clear:"உரையாடலை அழி"},_v={_name:H_,common:B_,upload_text:M_,"3D_model":{"3d_model":"3D மாதிரி",drop_to_upload:"3D மாதிரி கோப்பை (.obj, .glb, .stl, .gltf, .splat, அல்லது .ply) பதிவேற்ற இங்கே விடவும்"},annotated_image:V_,audio:q_,blocks:G_,checkbox:U_,code:F_,color_picker:W_,dataframe:K_,dropdown:X_,errors:Z_,file:J_,highlighted_text:Y_,image:Q_,label:ep,login:op,number:tp,plot:rp,radio:ap,slider:ip,video:np,chatbot:sp},pv=Object.freeze(Object.defineProperty({__proto__:null,_name:H_,annotated_image:V_,audio:q_,blocks:G_,chatbot:sp,checkbox:U_,code:F_,color_picker:W_,common:B_,dataframe:K_,default:_v,dropdown:X_,errors:Z_,file:J_,highlighted_text:Y_,image:Q_,label:ep,login:op,number:tp,plot:rp,radio:ap,slider:ip,upload_text:M_,video:np},Symbol.toStringTag,{value:"Module"})),lp="ภาษาไทย",cp={annotated_image:"รูปภาพที่มีคำอธิบาย"},dp={allow_recording_access:"กรุณาอนุญาตการเข้าถึงไมโครโฟนเพื่อบันทึกเสียง",audio:"เสียง",drop_to_upload:"ลากและวางไฟล์เสียงที่นี่เพื่ออัปโหลด",record_from_microphone:"บันทึกจากไมโครโฟน",stop_recording:"หยุดบันทึก",no_device_support:"ไม่สามารถเข้าถึงอุปกรณ์สื่อได้ โปรดตรวจสอบว่าคุณใช้งานบนโดเมนที่ปลอดภัย (https) หรือ localhost (หรือคุณได้กำหนดค่า SSL ที่ถูกต้อง) และอนุญาตให้เบราว์เซอร์เข้าถึงอุปกรณ์ของคุณ",stop:"หยุด",resume:"ทำงานต่อ",record:"บันทึก",no_microphone:"ไม่พบไมโครโฟน",pause:"หยุดชั่วคราว",play:"เล่น",waiting:"กำลังรอ"},up={connection_can_break:"บนมือถือ การเชื่อมต่ออาจหลุดหากแท็บนี้ไม่ได้อยู่ในโฟกัสหรืออุปกรณ์เข้าสู่โหมดพัก ส่งผลให้เสียตำแหน่งในคิว",long_requests_queue:"มีคิวคำขอรออยู่เป็นจำนวนมาก คัดลอก Space นี้เพื่อข้ามคิว",lost_connection:"การเชื่อมต่อขาดหายเนื่องจากออกจากหน้า กำลังเข้าคิวใหม่...",waiting_for_inputs:"กำลังรอไฟล์อัปโหลดเสร็จ กรุณาลองใหม่"},_p={checkbox:"กล่องเลือก",checkbox_group:"กลุ่มกล่องเลือก"},pp={code:"โค้ด"},mp={color_picker:"ตัวเลือกสี"},hp={built_with:"สร้างด้วย",built_with_gradio:"สร้างด้วย Gradio",clear:"ล้าง",download:"ดาวน์โหลด",edit:"แก้ไข",empty:"ว่างเปล่า",error:"ข้อผิดพลาด",hosted_on:"โฮสต์บน",loading:"กำลังโหลด",logo:"โลโก้",or:"หรือ",remove:"ลบ",settings:"การตั้งค่า",share:"แชร์",submit:"ส่ง",undo:"เลิกทำ",no_devices:"ไม่พบอุปกรณ์",language:"ภาษา",display_theme:"ธีมการแสดงผล",pwa:"โปรเกรสซีฟเว็บแอป"},gp={incorrect_format:"รูปแบบไม่ถูกต้อง รองรับเฉพาะไฟล์ CSV และ TSV",new_column:"เพิ่มคอลัมน์",new_row:"แถวใหม่",add_row_above:"เพิ่มแถวด้านบน",add_row_below:"เพิ่มแถวด้านล่าง",delete_row:"ลบแถว",delete_column:"ลบคอลัมน์",add_column_left:"เพิ่มคอลัมน์ทางซ้าย",add_column_right:"เพิ่มคอลัมน์ทางขวา",sort_column:"เรียงลำดับคอลัมน์",sort_ascending:"เรียงจากน้อยไปมาก",sort_descending:"เรียงจากมากไปน้อย",drop_to_upload:"ลากและวางไฟล์ CSV หรือ TSV ที่นี่เพื่อนำเข้าข้อมูลลงในตาราง",clear_sort:"ล้างการเรียงลำดับ"},fp={dropdown:"เมนูดรอปดาวน์"},bp={build_error:"เกิดข้อผิดพลาดในการสร้าง",config_error:"เกิดข้อผิดพลาดในการกำหนดค่า",contact_page_author:"โปรดติดต่อผู้ดูแลเพจเพื่อแจ้งให้ทราบ",no_app_file:"ไม่พบไฟล์แอป",runtime_error:"เกิดข้อผิดพลาดขณะทำงาน",space_not_working:'"Space ใช้งานไม่ได้เนื่องจาก" {0}',space_paused:"Space ถูกหยุดชั่วคราว",use_via_api:"ใช้งานผ่าน API",use_via_api_or_mcp:"ใช้งานผ่าน API หรือ MCP"},vp={uploading:"กำลังอัปโหลด..."},wp={highlighted_text:"ข้อความที่ถูกเน้น"},yp={allow_webcam_access:"กรุณาอนุญาตการเข้าถึงเว็บแคมเพื่อบันทึกภาพ",brush_color:"สีแปรง",brush_radius:"ขนาดแปรง",image:"รูปภาพ",remove_image:"ลบรูปภาพ",select_brush_color:"เลือกสีแปรง",start_drawing:"เริ่มวาด",use_brush:"ใช้แปรง",drop_to_upload:"ลากและวางไฟล์รูปภาพที่นี่เพื่ออัปโหลด"},kp={label:"ป้ายกำกับ"},Sp={enable_cookies:"หากคุณเข้าใช้งาน HuggingFace Space ในโหมดไม่ระบุตัวตน คุณต้องเปิดใช้งานคุกกี้ของบุคคลที่สาม",incorrect_credentials:"ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",username:"ชื่อผู้ใช้",password:"รหัสผ่าน",login:"เข้าสู่ระบบ"},xp={number:"ตัวเลข"},Ep={plot:"กราฟ"},$p={radio:"ปุ่มตัวเลือก"},Pp={slider:"แถบเลื่อน"},Ap={click_to_upload:"คลิกเพื่ออัปโหลด",drop_audio:"ลากและวางไฟล์เสียงที่นี่",drop_csv:"ลากและวางไฟล์ CSV ที่นี่",drop_file:"ลากและวางไฟล์ที่นี่",drop_image:"ลากและวางไฟล์รูปภาพที่นี่",drop_video:"ลากและวางไฟล์วิดีโอที่นี่",drop_gallery:"ลากและวางไฟล์สื่อที่นี่",paste_clipboard:"วางจากคลิปบอร์ด"},Tp={drop_to_upload:"ลากและวางไฟล์วิดีโอที่นี่เพื่ออัปโหลด"},Op={edit:"แก้ไข",retry:"ลองใหม่",undo:"เลิกทำ",submit:"ส่ง",cancel:"ยกเลิก",like:"ถูกใจ",dislike:"ไม่ถูกใจ",clear:"ล้างการสนทนา"},mv={_name:lp,"3D_model":{"3d_model":"โมเดล 3 มิติ",drop_to_upload:"ลากและวางไฟล์โมเดล 3 มิติ (.obj, .glb, .stl, .gltf, .splat, หรือ .ply) ที่นี่เพื่ออัปโหลด"},annotated_image:cp,audio:dp,blocks:up,checkbox:_p,code:pp,color_picker:mp,common:hp,dataframe:gp,dropdown:fp,errors:bp,file:vp,highlighted_text:wp,image:yp,label:kp,login:Sp,number:xp,plot:Ep,radio:$p,slider:Pp,upload_text:Ap,video:Tp,chatbot:Op},hv=Object.freeze(Object.defineProperty({__proto__:null,_name:lp,annotated_image:cp,audio:dp,blocks:up,chatbot:Op,checkbox:_p,code:pp,color_picker:mp,common:hp,dataframe:gp,default:mv,dropdown:fp,errors:bp,file:vp,highlighted_text:wp,image:yp,label:kp,login:Sp,number:xp,plot:Ep,radio:$p,slider:Pp,upload_text:Ap,video:Tp},Symbol.toStringTag,{value:"Module"})),jp="Türkçe",Cp={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Gönder",built_with:"İle oluşturuldu",download:"İndir",edit:"Düzenle",empty:"Boş",error:"Hata",hosted_on:"Şurada barındırılıyor:",loading:"Yükleniyor",logo:"Logo",remove:"Kaldır",settings:"Ayarlar",share:"Paylaş",undo:"Geri Al",no_devices:"Hiçbir cihaz bulunamadı",language:"Dil",display_theme:"Görünüm Teması",pwa:"Progressive Web Uygulaması"},Lp={click_to_upload:"Yüklemek için tıkla",drop_audio:"Ses dosyasını buraya sürükle",drop_csv:"CSV dosyasını buraya sürükle",drop_file:"Dosyayı buraya sürükle",drop_image:"Resmi buraya sürükle",drop_video:"Videoyu buraya sürükle",drop_gallery:"Medyayı buraya bırak",paste_clipboard:"Panodan yapıştır"},Dp={annotated_image:"Açıklamalı Görüntü"},Ip={allow_recording_access:"Lütfen kayıt yapmak için mikrofona erişim izni verin.",audio:"Ses",drop_to_upload:"Yüklemek için bir ses dosyasını buraya bırakın",record_from_microphone:"Mikrofondan kaydet",stop_recording:"Kaydı durdur",no_device_support:"Medya cihazlarına erişilemedi. Güvenli bir kökende (https) veya yerel makinede (localhost) çalıştığınızdan emin olun (veya geçerli bir SSL sertifikası ile ssl_verify'yi geçtiniz) ve tarayıcınızın cihazınıza erişim izni verdiğini kontrol edin.",stop:"Durdur",resume:"Devam Et",record:"Kaydet",no_microphone:"Hiçbir mikrofon bulunamadı",pause:"Duraklat",play:"Oynat",waiting:"Bekliyor"},zp={connection_can_break:"Mobil cihazda, bu sekme odaklanmadığında veya cihaz uyku moduna geçtiğinde bağlantı kopabilir ve kuyrukta olduğunuz yer kaybedilebilir.",long_requests_queue:"Bekleyen isteklerin uzun bir kuyruğu var. Kuyruktan atlamak için bu Space'i kopyalayın.",lost_connection:"Sayfa terk edildiği için bağlantı kaybedildi. Kuyruğa yeniden katılıyor...",waiting_for_inputs:"Dosya(lar)ın yükleme işlemini tamamlaması bekleniyor, lütfen yeniden deneyin."},Rp={checkbox:"Onay Kutusu",checkbox_group:"Onay Kutusu Grubu"},Np={code:"Kod"},Hp={color_picker:"Renk Seçici"},Bp={incorrect_format:"Yanlış format, sadece CSV ve TSV dosyaları destekleniyor",new_column:"Sütun ekle",new_row:"Yeni satır",add_row_above:"Yukarıya satır ekle",add_row_below:"Aşağıya satır ekle",delete_row:"Satırı sil",delete_column:"Sütunu sil",add_column_left:"Sola sütun ekle",add_column_right:"Sağa sütun ekle",sort_column:"Sütunu sırala",sort_ascending:"Artan sırala",sort_descending:"Azalan sırala",drop_to_upload:"Veriyi veri çerçevesine aktarmak için CSV veya TSV dosyalarını buraya bırakın",clear_sort:"Sıralamayı temizle"},Mp={dropdown:"Açılır Liste"},Vp={build_error:"Bir derleme hatası var",config_error:"Bir yapılandırma hatası var",contact_page_author:"Lütfen sayfanın yazarıyla iletişime geçin ve bildirin.",no_app_file:"Uygulama dosyası yok",runtime_error:"Bir çalışma zamanı hatası var",space_not_working:'"Space çalışmıyor çünkü" {0}',space_paused:"Space duraklatıldı",use_via_api:"API üzerinden kullan",use_via_api_or_mcp:"API veya MCP üzerinden kullan"},qp={uploading:"Yükleniyor..."},Gp={highlighted_text:"Vurgulanan Metin"},Up={allow_webcam_access:"Lütfen kayıt için web kamerasına erişim izni verin.",brush_color:"Fırça rengi",brush_radius:"Fırça boyutu",image:"Görüntü",remove_image:"Resmi kaldır",select_brush_color:"Fırça rengini seç",start_drawing:"Çizim yapmaya başla",use_brush:"Fırça kullan",drop_to_upload:"Yüklemek için bir görüntü dosyasını buraya bırakın"},Fp={label:"Etiket"},Wp={enable_cookies:"Eğer HuggingFace Space'i Gizli Mod'da ziyaret ediyorsanız, üçüncü taraf çerezleri etkinleştirmelisiniz.",incorrect_credentials:"Yanlış kimlik bilgileri",username:"Kullanıcı adı",password:"Parola",login:"Giriş yap"},Kp={number:"Sayı"},Xp={plot:"Grafik"},Zp={radio:"Radyo Düğmesi"},Jp={slider:"Kaydırıcı"},Yp={drop_to_upload:"Yüklemek için bir video dosyasını buraya bırakın"},Qp={edit:"Düzenle",retry:"Tekrar Dene",undo:"Geri Al",submit:"Gönder",cancel:"İptal",like:"Beğen",dislike:"Beğenme",clear:"Sohbeti Temizle"},gv={_name:jp,common:Cp,upload_text:Lp,"3D_model":{"3d_model":"3D Model",drop_to_upload:"Yüklemek için bir 3D model dosyasını (.obj, .glb, .stl, .gltf, .splat veya .ply) buraya bırakın"},annotated_image:Dp,audio:Ip,blocks:zp,checkbox:Rp,code:Np,color_picker:Hp,dataframe:Bp,dropdown:Mp,errors:Vp,file:qp,highlighted_text:Gp,image:Up,label:Fp,login:Wp,number:Kp,plot:Xp,radio:Zp,slider:Jp,video:Yp,chatbot:Qp},fv=Object.freeze(Object.defineProperty({__proto__:null,_name:jp,annotated_image:Dp,audio:Ip,blocks:zp,chatbot:Qp,checkbox:Rp,code:Np,color_picker:Hp,common:Cp,dataframe:Bp,default:gv,dropdown:Mp,errors:Vp,file:qp,highlighted_text:Gp,image:Up,label:Fp,login:Wp,number:Kp,plot:Xp,radio:Zp,slider:Jp,upload_text:Lp,video:Yp},Symbol.toStringTag,{value:"Module"})),em="Українська",om={built_with_gradio:"Зроблено на основі Gradio",clear:"Очистити",or:"або",submit:"Надіслати",settings:"Налаштування",built_with:"Зроблено з",download:"Завантажити",edit:"Редагувати",empty:"Порожній",error:"Помилка",hosted_on:"Розміщено на",loading:"Завантаження",logo:"Логотип",remove:"Видалити",share:"Поділитися",undo:"Скасувати",no_devices:"Пристроїв не знайдено",language:"Мова",display_theme:"Тема оформлення",pwa:"Прогресивний веб-додаток"},tm={click_to_upload:"Натисніть, щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди",drop_gallery:"Перетягніть медіа сюди",paste_clipboard:"Вставити з буфера обміну"},rm={annotated_image:"Анотоване зображення"},am={allow_recording_access:"Будь ласка, дайте доступ до мікрофона для запису.",audio:"Аудіо",drop_to_upload:"Перетягніть аудіофайл сюди, щоб завантажити",record_from_microphone:"Записати з мікрофона",stop_recording:"Зупинити запис",no_device_support:"Медійні пристрої не можуть бути доступні. Переконайтеся, що ви запускаєте з захищеного джерела (https) або localhost (або ви передали дійсний SSL-сертифікат до ssl_verify), і ви дозволили браузеру доступ до вашого пристрою.",stop:"Зупинити",resume:"Відновити",record:"Записати",no_microphone:"Мікрофон не виявлено",pause:"Пауза",play:"Відтворити",waiting:"Очікування"},im={connection_can_break:"На мобільному пристрої з'єднання може перерватися, якщо ця вкладка втрачає фокус або пристрій увійде в режим сну, в результаті чого ви втратите своє місце в черзі.",long_requests_queue:"У черзі залишилось багато запитів. Скопіюйте цей Space, щоб пропустити чергу.",lost_connection:"З'єднання втрачено через вихід зі сторінки. Переміщення до кінця черги...",waiting_for_inputs:"Чекаємо завершення завантаження файлу(ів), будь ласка, спробуйте ще раз."},nm={checkbox:"Прапорець",checkbox_group:"Група прапорців"},sm={code:"Код"},lm={color_picker:"Вибір кольору"},cm={incorrect_format:"Неправильний формат, підтримуються лише файли CSV та TSV",new_column:"Додати стовпець",new_row:"Новий рядок",add_row_above:"Додати рядок вище",add_row_below:"Додати рядок нижче",delete_row:"Видалити рядок",delete_column:"Видалити стовпець",add_column_left:"Додати стовпець зліва",add_column_right:"Додати стовпець справа",sort_column:"Сортувати стовпець",sort_ascending:"Сортувати за зростанням",sort_descending:"Сортувати за спаданням",drop_to_upload:"Перетягніть CSV або TSV файли сюди, щоб імпортувати дані в таблицю даних",clear_sort:"Очистити сортування"},dm={dropdown:"Випадне меню"},um={build_error:"Є помилка побудови",config_error:"Є помилка конфігурації",contact_page_author:"Будь ласка, зв'яжіться з автором сторінки, щоб повідомити.",no_app_file:"Немає файлу додатку",runtime_error:"Є помилка виконання",space_not_working:'"Space не працює, оскільки" {0}',space_paused:"Space призупинено",use_via_api:"Використовувати через API",use_via_api_or_mcp:"Використовувати через API або MCP"},_m={uploading:"Завантаження..."},pm={highlighted_text:"Виділений текст"},mm={allow_webcam_access:"Будь ласка, дайте доступ до веб-камери для запису.",brush_color:"Колір пензля",brush_radius:"Розмір пензля",image:"Зображення",remove_image:"Видалити зображення",select_brush_color:"Виберіть колір пензля",start_drawing:"Почніть малювати",use_brush:"Використовуйте пензель",drop_to_upload:"Перетягніть файл зображення сюди, щоб завантажити"},hm={label:"Мітка"},gm={enable_cookies:"Якщо ви відвідуєте HuggingFace Space у режимі Інкогніто, вам потрібно увімкнути сторонні куки.",incorrect_credentials:"Неправильні облікові дані",username:"Ім'я користувача",password:"Пароль",login:"Увійти"},fm={number:"Число"},bm={plot:"Діаграма"},vm={radio:"Перемикач"},wm={slider:"Повзунок"},ym={drop_to_upload:"Перетягніть відеофайл сюди, щоб завантажити"},km={edit:"Редагувати",retry:"Повторити",undo:"Скасувати",submit:"Надіслати",cancel:"Скасувати",like:"Подобається",dislike:"Не подобається",clear:"Очистити чат"},bv={_name:em,common:om,upload_text:tm,"3D_model":{"3d_model":"3D-модель",drop_to_upload:"Перетягніть файл 3D-моделі (.obj, .glb, .stl, .gltf, .splat або .ply) сюди, щоб завантажити"},annotated_image:rm,audio:am,blocks:im,checkbox:nm,code:sm,color_picker:lm,dataframe:cm,dropdown:dm,errors:um,file:_m,highlighted_text:pm,image:mm,label:hm,login:gm,number:fm,plot:bm,radio:vm,slider:wm,video:ym,chatbot:km},vv=Object.freeze(Object.defineProperty({__proto__:null,_name:em,annotated_image:rm,audio:am,blocks:im,chatbot:km,checkbox:nm,code:sm,color_picker:lm,common:om,dataframe:cm,default:bv,dropdown:dm,errors:um,file:_m,highlighted_text:pm,image:mm,label:hm,login:gm,number:fm,plot:bm,radio:vm,slider:wm,upload_text:tm,video:ym},Symbol.toStringTag,{value:"Module"})),Sm="اردو",xm={built_with_gradio:"Gradio کے ساتھ بنایا گیا",clear:"صاف کریں",or:"یا",submit:"جمع کریں",settings:"ترتیبات",built_with:"کے ساتھ بنایا گیا",download:"ڈاؤن لوڈ کریں",edit:"ترمیم کریں",empty:"خالی",error:"خطا",hosted_on:"پر میزبانی کی جا رہی ہے",loading:"لوڈ ہو رہا ہے",logo:"لوگو",remove:"حذف کریں",share:"شیئر کریں",undo:"واپس لیں",no_devices:"کوئی ڈیوائس نہیں ملی",language:"زبان",display_theme:"تھیم کی نمائش",pwa:"پروگریسیو ویب ایپ"},Em={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں CSV ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں",drop_gallery:"میڈیا یہاں ڈالیں",paste_clipboard:"کلپ بورڈ سے پیسٹ کریں"},$m={annotated_image:"تشریحی تصویر"},Pm={allow_recording_access:"براہ مہربانی ریکارڈنگ کے لیے مائیکروفون تک رسائی کی اجازت دیں۔",audio:"آڈیو",drop_to_upload:"اپلوڈ کرنے کے لیے آڈیو فائل یہاں ڈراپ کریں",record_from_microphone:"مائیکروفون سے ریکارڈ کریں",stop_recording:"ریکارڈنگ بند کریں",no_device_support:"میڈیا ڈیوائسز تک رسائی حاصل نہیں کی جا سکی۔ یقینی بنائیں کہ آپ محفوظ ذریعہ (https) یا localhost پر چلا رہے ہیں (یا آپ نے ssl_verify میں درست SSL سرٹیفکیٹ منتقل کیا ہے)، اور آپ نے براؤزر کو اپنے ڈیوائس تک رسائی کی اجازت دی ہے۔",stop:"روکیں",resume:"دوبارہ شروع کریں",record:"ریکارڈ کریں",no_microphone:"کوئی مائیکروفون نہیں ملا",pause:"روکیں",play:"چلائیں",waiting:"انتظار کر رہا ہے"},Am={connection_can_break:"موبائل پر، اگر اس ٹیب کا فوکس کھو جائے یا ڈیوائس سلیپ موڈ میں چلا جائے تو کنکشن ٹوٹ سکتا ہے، جس سے آپ قطار میں اپنی جگہ کھو سکتے ہیں۔",long_requests_queue:"درخواستوں کی ایک لمبی قطار زیر التواء ہے۔ قطار چھوڑنے کے لیے اس Space کو ڈپلیکیٹ کریں۔",lost_connection:"صفحہ چھوڑنے کی وجہ سے کنکشن کھو گیا۔ قطار میں دوبارہ شامل ہو رہا ہے...",waiting_for_inputs:"فائل(وں) کے اپ لوڈ ہونے کا انتظار ہے، براہ کرم دوبارہ کوشش کریں۔"},Tm={checkbox:"چیک باکس",checkbox_group:"چیک باکس گروپ"},Om={code:"کوڈ"},jm={color_picker:"رنگ منتخب کرنے والا"},Cm={incorrect_format:"غلط فارمیٹ، صرف CSV اور TSV فائلوں کی حمایت کی جاتی ہے",new_column:"کالم شامل کریں",new_row:"نئی قطار",add_row_above:"اوپر قطار شامل کریں",add_row_below:"نیچے قطار شامل کریں",delete_row:"قطار حذف کریں",delete_column:"کالم حذف کریں",add_column_left:"بائیں طرف کالم شامل کریں",add_column_right:"دائیں طرف کالم شامل کریں",sort_column:"کالم کو ترتیب دیں",sort_ascending:"صعودی ترتیب دیں",sort_descending:"نزولی ترتیب دیں",drop_to_upload:"ڈیٹا کو ڈیٹا فریم میں درآمد کرنے کے لیے CSV یا TSV فائلیں یہاں ڈراپ کریں",clear_sort:"ترتیب صاف کریں"},Lm={dropdown:"ڈراپ ڈاؤن"},Dm={build_error:"تعمیر میں خطا ہے",config_error:"ترتیب میں خطا ہے",contact_page_author:"براہ کرم صفحہ کے مصنف سے رابطہ کریں تاکہ انہیں مطلع کیا جا سکے۔",no_app_file:"کوئی ایپ فائل نہیں ہے",runtime_error:"رن ٹائم میں خطا ہے",space_not_working:'"Space کام نہیں کر رہا ہے کیونکہ" {0}',space_paused:"Space موقوف ہے",use_via_api:"API کے ذریعے استعمال کریں",use_via_api_or_mcp:"API یا MCP کے ذریعے استعمال کریں"},Im={uploading:"اپلوڈ ہو رہا ہے..."},zm={highlighted_text:"نمایاں کردہ متن"},Rm={allow_webcam_access:"براہ مہربانی ریکارڈنگ کے لیے ویب کیم تک رسائی کی اجازت دیں۔",brush_color:"برش کا رنگ",brush_radius:"برش کا سائز",image:"تصویر",remove_image:"تصویر حذف کریں",select_brush_color:"برش کا رنگ منتخب کریں",start_drawing:"ڈرائنگ شروع کریں",use_brush:"برش استعمال کریں",drop_to_upload:"اپلوڈ کرنے کے لیے تصویر فائل یہاں ڈراپ کریں"},Nm={label:"لیبل"},Hm={enable_cookies:"اگر آپ انکوگنیٹو موڈ میں HuggingFace Space ملاحظہ کر رہے ہیں تو، آپ کو تیسرے فریق کے کوکیز کو فعال کرنا ہوگا۔",incorrect_credentials:"غلط اسناد",username:"صارف نام",password:"پاس ورڈ",login:"لاگ ان کریں"},Bm={number:"نمبر"},Mm={plot:"گراف"},Vm={radio:"ریڈیو بٹن"},qm={slider:"سلائیڈر"},Gm={drop_to_upload:"اپلوڈ کرنے کے لیے ویڈیو فائل یہاں ڈراپ کریں"},Um={edit:"ترمیم",retry:"دوبارہ کوشش",undo:"واپس",submit:"جمع کریں",cancel:"منسوخ",like:"پسند",dislike:"ناپسند",clear:"گفتگو صاف کریں"},wv={_name:Sm,common:xm,upload_text:Em,"3D_model":{"3d_model":"3D ماڈل",drop_to_upload:"اپلوڈ کرنے کے لیے 3D ماڈل (.obj, .glb, .stl, .gltf, .splat، یا .ply) فائل یہاں ڈراپ کریں"},annotated_image:$m,audio:Pm,blocks:Am,checkbox:Tm,code:Om,color_picker:jm,dataframe:Cm,dropdown:Lm,errors:Dm,file:Im,highlighted_text:zm,image:Rm,label:Nm,login:Hm,number:Bm,plot:Mm,radio:Vm,slider:qm,video:Gm,chatbot:Um},yv=Object.freeze(Object.defineProperty({__proto__:null,_name:Sm,annotated_image:$m,audio:Pm,blocks:Am,chatbot:Um,checkbox:Tm,code:Om,color_picker:jm,common:xm,dataframe:Cm,default:wv,dropdown:Lm,errors:Dm,file:Im,highlighted_text:zm,image:Rm,label:Nm,login:Hm,number:Bm,plot:Mm,radio:Vm,slider:qm,upload_text:Em,video:Gm},Symbol.toStringTag,{value:"Module"})),Fm="O'zbek",Wm={built_with_gradio:"Gradio bilan yaratilgan",clear:"Tozalash",submit:"Yuborish",built_with:"Bilan yaratilgan",download:"Yuklab olish",edit:"Tahrirlash",empty:"Bo'sh",error:"Xato",hosted_on:"Joylashtirilgan",loading:"Yuklanmoqda",logo:"Logo",or:"yoki",remove:"O'chirish",settings:"Sozlamalar",share:"Ulashish",undo:"Bekor qilish",no_devices:"Hech qanday qurilma topilmadi",language:"Til",display_theme:"Ko'rinish mavzusi",pwa:"Progressive web-ilova"},Km={click_to_upload:"Yuklash uchun bosing",drop_audio:"Audioni shu yerga tashlang",drop_csv:"CSV faylni shu yerga tashlang",drop_file:"Faylni shu yerga tashlang",drop_image:"Rasmni shu yerga tashlang",drop_video:"Videoni shu yerga tashlang",drop_gallery:"Mediafaylni shu yerga tashlang",paste_clipboard:"Klipboarddan qo'yish"},Xm={annotated_image:"Izohli rasm"},Zm={allow_recording_access:"Iltimos, yozib olish uchun mikrofonga ruxsat bering.",audio:"Audio",drop_to_upload:"Audio faylni yuklash uchun shu yerga tashlang",record_from_microphone:"Mikrofondan yozib olish",stop_recording:"Yozib olishni to'xtatish",no_device_support:"Media qurilmalariga kira olmadik. Xavfsiz manbada (https) yoki localhost-da ishlayotganingizni tekshiring (yoki to'g'ri SSL sertifikatini ssl_verify-ga berganingizni) va brauzer qurilmangizga kirishiga ruxsat berganingizni tekshiring.",stop:"To'xtatish",resume:"Davom ettirish",record:"Yozib olish",no_microphone:"Mikrofon topilmadi",pause:"Pauza",play:"Ijro etish",waiting:"Kutilmoqda"},Jm={connection_can_break:"Mobil qurilmada, agar bu yorliq fokusdan chiqsa yoki qurilma uyqu rejimiga o'tsa, aloqa uzilib, navbatdagi o'rningizni yo'qotishingiz mumkin.",long_requests_queue:"So'rovlar navbati juda uzun. Navbatni chetlab o'tish uchun bu Space-ni nusxalang.",lost_connection:"Sahifadan chiqib ketganligi sababli aloqa uzildi. Navbatga qayta qo'shilmoqda...",waiting_for_inputs:"Fayl(lar) yuklanib bo'lishini kutish, iltimos, qayta urinib ko'ring."},Ym={checkbox:"Belgilash katagi",checkbox_group:"Belgilash kataklari guruhi"},Qm={code:"Kod"},eh={color_picker:"Rang tanlagich"},oh={incorrect_format:"Noto'g'ri format, faqat CSV va TSV fayllari qo'llab-quvvatlanadi",new_column:"Ustun qo'shish",new_row:"Yangi qator",add_row_above:"Yuqoriga qator qo'shish",add_row_below:"Pastga qator qo'shish",delete_row:"Qatorni o'chirish",delete_column:"Ustunni o'chirish",add_column_left:"Chapga ustun qo'shish",add_column_right:"O'ngga ustun qo'shish",sort_column:"Ustunni saralash",sort_ascending:"O'sish tartibida saralash",sort_descending:"Kamayish tartibida saralash",drop_to_upload:"Ma'lumotlarni ma'lumotlar jadvaliga import qilish uchun CSV yoki TSV fayllarini shu yerga tashlang",clear_sort:"Saralashni tozalash"},th={dropdown:"Ochiluvchi ro'yxat"},rh={build_error:"Tuzilish xatosi mavjud",config_error:"Konfiguratsiya xatosi mavjud",contact_page_author:"Iltimos, sahifa muallifiga bog'laning va ularga xabar bering.",no_app_file:"Ilova fayli yo'q",runtime_error:"Bajarilish vaqti xatosi mavjud",space_not_working:'"Space ishlamayapti, chunki" {0}',space_paused:"Space to'xtatilgan",use_via_api:"API orqali foydalaning",use_via_api_or_mcp:"API yoki MCP orqali foydalaning"},ah={uploading:"Yuklanmoqda..."},ih={highlighted_text:"Ajratilgan matn"},nh={allow_webcam_access:"Iltimos, yozib olish uchun veb-kameraga ruxsat bering.",brush_color:"Cho'tka rangi",brush_radius:"Cho'tka o'lchami",image:"Rasm",remove_image:"Rasmni o'chirish",select_brush_color:"Cho'tka rangini tanlang",start_drawing:"Chizishni boshlang",use_brush:"Cho'tkadan foydalaning",drop_to_upload:"Rasm faylini yuklash uchun shu yerga tashlang"},sh={label:"Yorliq"},lh={enable_cookies:"Agar HuggingFace Space-ni Inkognito rejimida ko'rayotgan bo'lsangiz, uchinchi tomon kukilarini yoqishingiz kerak.",incorrect_credentials:"Noto'g'ri kirish ma'lumotlari",username:"Foydalanuvchi nomi",password:"Parol",login:"Kirish"},ch={number:"Raqam"},dh={plot:"Grafik"},uh={radio:"Radio tugma"},_h={slider:"Slayder"},ph={drop_to_upload:"Video faylni yuklash uchun shu yerga tashlang"},mh={edit:"Tahrirlash",retry:"Qayta urinish",undo:"Bekor qilish",submit:"Yuborish",cancel:"Bekor qilish",like:"Yoqdi",dislike:"Yoqmadi",clear:"Suhbatni tozalash"},kv={_name:Fm,common:Wm,upload_text:Km,"3D_model":{"3d_model":"3D model",drop_to_upload:"3D model (.obj, .glb, .stl, .gltf, .splat yoki .ply) faylni yuklash uchun shu yerga tashlang"},annotated_image:Xm,audio:Zm,blocks:Jm,checkbox:Ym,code:Qm,color_picker:eh,dataframe:oh,dropdown:th,errors:rh,file:ah,highlighted_text:ih,image:nh,label:sh,login:lh,number:ch,plot:dh,radio:uh,slider:_h,video:ph,chatbot:mh},Sv=Object.freeze(Object.defineProperty({__proto__:null,_name:Fm,annotated_image:Xm,audio:Zm,blocks:Jm,chatbot:mh,checkbox:Ym,code:Qm,color_picker:eh,common:Wm,dataframe:oh,default:kv,dropdown:th,errors:rh,file:ah,highlighted_text:ih,image:nh,label:sh,login:lh,number:ch,plot:dh,radio:uh,slider:_h,upload_text:Km,video:ph},Symbol.toStringTag,{value:"Module"})),hh="简体中文",gh={annotated_image:"标注图像"},fh={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放",waiting:"等待中",drop_to_upload:"将音频文件拖放到此处以上传"},bh={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此 Space 以跳过队列。",lost_connection:"由于离开页面，连接已丢失。正在重新加入队列...",waiting_for_inputs:"等待文件上传完成，请稍后重试。"},vh={checkbox:"复选框",checkbox_group:"复选框组"},wh={code:"代码"},yh={color_picker:"颜色选择器"},kh={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销",settings:"设置",no_devices:"未找到设备",language:"语言",display_theme:"显示主题",pwa:"渐进式 Web 应用"},Sh={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"添加列",new_row:"新行",add_row_above:"在上方添加行",add_row_below:"在下方添加行",delete_row:"删除行",delete_column:"删除列",add_column_left:"在左侧添加列",add_column_right:"在右侧添加列",sort_column:"排序列",sort_ascending:"升序排序",sort_descending:"降序排序",drop_to_upload:"将 CSV 或 TSV 文件拖放到此处以将数据导入数据框",clear_sort:"清除排序"},xh={dropdown:"下拉菜单"},Eh={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"Space 无法工作，原因：" {0}',space_paused:"Space 已暂停",use_via_api:"通过 API 使用",use_via_api_or_mcp:"通过 API 或 MCP 使用"},$h={uploading:"正在上传..."},Ph={highlighted_text:"高亮文本"},Ah={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔大小",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔",drop_to_upload:"将图像文件拖放到此处以上传"},Th={label:"标签"},Oh={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace Space，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录",username:"用户名",password:"密码"},jh={number:"数字"},Ch={plot:"图表"},Lh={radio:"单选框"},Dh={slider:"滑块"},Ih={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处",drop_gallery:"在此处放置媒体文件",paste_clipboard:"从剪贴板粘贴"},zh={drop_to_upload:"将视频文件拖放到此处以上传"},Rh={edit:"编辑",retry:"重试",undo:"撤销",submit:"发送",cancel:"取消",like:"赞",dislike:"踩",clear:"清空对话"},xv={_name:hh,"3D_model":{"3d_model":"3D模型",drop_to_upload:"将 3D 模型（.obj、.glb、.stl、.gltf、.splat 或 .ply）文件拖放到此处以上传"},annotated_image:gh,audio:fh,blocks:bh,checkbox:vh,code:wh,color_picker:yh,common:kh,dataframe:Sh,dropdown:xh,errors:Eh,file:$h,highlighted_text:Ph,image:Ah,label:Th,login:Oh,number:jh,plot:Ch,radio:Lh,slider:Dh,upload_text:Ih,video:zh,chatbot:Rh},Ev=Object.freeze(Object.defineProperty({__proto__:null,_name:hh,annotated_image:gh,audio:fh,blocks:bh,chatbot:Rh,checkbox:vh,code:wh,color_picker:yh,common:kh,dataframe:Sh,default:xv,dropdown:xh,errors:Eh,file:$h,highlighted_text:Ph,image:Ah,label:Th,login:Oh,number:jh,plot:Ch,radio:Lh,slider:Dh,upload_text:Ih,video:zh},Symbol.toStringTag,{value:"Module"})),Nh="繁體中文",Hh={built_with_gradio:"使用 Gradio 建構",clear:"清除",or:"或",submit:"提交",built_with:"使用",download:"下載",edit:"編輯",empty:"空白",error:"錯誤",hosted_on:"托管於",loading:"載入中",logo:"標誌",remove:"移除",settings:"設定",share:"分享",undo:"復原",no_devices:"未找到裝置",language:"語言",display_theme:"顯示主題",pwa:"漸進式網頁應用程式"},Bh={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放 CSV 至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處",drop_gallery:"在此處拖放媒體",paste_clipboard:"從剪貼簿貼上"},Mh={annotated_image:"標註圖像"},Vh={allow_recording_access:"請允許存取麥克風以進行錄音。",audio:"音訊",drop_to_upload:"將音訊檔案拖放到這裡上傳",record_from_microphone:"從麥克風錄音",stop_recording:"停止錄製",no_device_support:"無法存取媒體裝置。請確認您正在使用安全來源（https）或本機主機（或您已提供有效的 SSL 憑證以進行 ssl_verify），並且已允許瀏覽器存取您的裝置。",stop:"停止",resume:"繼續",record:"錄製",no_microphone:"未找到麥克風",pause:"暫停",play:"播放",waiting:"等待中"},qh={connection_can_break:"在行動裝置上，如果此分頁失去焦點或裝置進入睡眠模式，連線可能會中斷，導致您在隊列中的位置遺失。",long_requests_queue:"目前有許多待處理的請求。複製此 Space 以跳過隊列。",lost_connection:"因離開頁面而失去連接。正在重新加入隊列...",waiting_for_inputs:"等待檔案上傳完成，請稍後重試。"},Gh={checkbox:"核取方塊",checkbox_group:"核取方塊群組"},Uh={code:"程式碼"},Fh={color_picker:"顏色選擇器"},Wh={incorrect_format:"格式不正確，僅支援 CSV 和 TSV 檔案",new_column:"新增欄位",new_row:"新增列",add_row_above:"在上方新增列",add_row_below:"在下方新增列",delete_row:"刪除列",delete_column:"刪除欄位",add_column_left:"在左側新增欄位",add_column_right:"在右側新增欄位",sort_column:"排序欄位",sort_ascending:"升冪排序",sort_descending:"降冪排序",drop_to_upload:"將 CSV 或 TSV 檔案拖放到這裡以匯入資料到資料框中",clear_sort:"清除排序"},Kh={dropdown:"下拉選單"},Xh={build_error:"有建置錯誤",config_error:"有設定錯誤",contact_page_author:"請聯繫頁面的作者以通知他們。",no_app_file:"沒有應用程式檔案",runtime_error:"有執行時錯誤",space_not_working:'"Space 無法運作，因為" {0}',space_paused:"Space 已暫停",use_via_api:"透過 API 使用",use_via_api_or_mcp:"透過 API 或 MCP 使用"},Zh={uploading:"上傳中..."},Jh={highlighted_text:"醒目提示的文字"},Yh={allow_webcam_access:"請允許存取網路攝影機以進行錄製。",brush_color:"筆刷顏色",brush_radius:"筆刷大小",image:"圖像",remove_image:"移除圖片",select_brush_color:"選擇筆刷顏色",start_drawing:"開始繪圖",use_brush:"使用筆刷",drop_to_upload:"將圖像檔案拖放到這裡上傳"},Qh={label:"標籤"},eg={enable_cookies:"如果您在無痕模式下訪問 HuggingFace Space，您必須啟用第三方 Cookie。",incorrect_credentials:"憑證錯誤",username:"使用者名稱",password:"密碼",login:"登入"},og={number:"數字"},tg={plot:"圖表"},rg={radio:"單選按鈕"},ag={slider:"滑桿"},ig={drop_to_upload:"將影片檔案拖放到這裡上傳"},ng={edit:"編輯",retry:"重試",undo:"復原",submit:"送出",cancel:"取消",like:"讚",dislike:"不讚",clear:"清空對話"},$v={_name:Nh,common:Hh,upload_text:Bh,"3D_model":{"3d_model":"3D 模型",drop_to_upload:"將 3D 模型 (.obj、.glb、.stl、.gltf、.splat 或 .ply) 檔案拖放到這裡上傳"},annotated_image:Mh,audio:Vh,blocks:qh,checkbox:Gh,code:Uh,color_picker:Fh,dataframe:Wh,dropdown:Kh,errors:Xh,file:Zh,highlighted_text:Jh,image:Yh,label:Qh,login:eg,number:og,plot:tg,radio:rg,slider:ag,video:ig,chatbot:ng},Pv=Object.freeze(Object.defineProperty({__proto__:null,_name:Nh,annotated_image:Mh,audio:Vh,blocks:qh,chatbot:ng,checkbox:Gh,code:Uh,color_picker:Fh,common:Hh,dataframe:Wh,default:$v,dropdown:Kh,errors:Xh,file:Zh,highlighted_text:Jh,image:Yh,label:Qh,login:eg,number:og,plot:tg,radio:rg,slider:ag,upload_text:Bh,video:ig},Symbol.toStringTag,{value:"Module"})),Gv=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],Av=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],it={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"}},Uv=Av.reduce((e,{color:o,primary:t,secondary:r})=>({...e,[o]:{primary:it[o][t],secondary:it[o][r]}}),{});function sg(e){if(e==null)return"";const o=String(e),t=wt(Ft);let r=t(o);if(r!==o)return r;const a=o.toLowerCase();for(const i of lg){const n=i.substring(i.indexOf(".")+1);if(a===n){const s=t(i);if(s!==i)return s;break}}return o}const Fv=me(Ft,()=>sg),Tv=Object.assign({"./lang/ar.json":y0,"./lang/ca.json":S0,"./lang/ckb.json":E0,"./lang/de.json":P0,"./lang/en.json":T0,"./lang/es.json":j0,"./lang/eu.json":L0,"./lang/fa.json":I0,"./lang/fi.json":R0,"./lang/fr.json":H0,"./lang/he.json":M0,"./lang/hi.json":q0,"./lang/ja.json":U0,"./lang/ko.json":W0,"./lang/lt.json":X0,"./lang/nb.json":J0,"./lang/nl.json":Q0,"./lang/pl.json":ov,"./lang/pt-BR.json":rv,"./lang/pt.json":iv,"./lang/ro.json":sv,"./lang/ru.json":cv,"./lang/sv.json":uv,"./lang/ta.json":pv,"./lang/th.json":hv,"./lang/tr.json":fv,"./lang/uk.json":vv,"./lang/ur.json":yv,"./lang/uz.json":Sv,"./lang/zh-CN.json":Ev,"./lang/zh-TW.json":Pv});function Wv(e){return console.log(e),e&&typeof e=="object"&&e.__type__==="translation_metadata"&&typeof e.key=="string"}function Kv(e){if(typeof e!="string")return e;const o="__i18n__",t=e.indexOf(o);if(t===-1)return e;try{const r=t>0?e.substring(0,t):"",a=t+o.length,i=e.indexOf("{",a);let n=-1,s=0;for(let c=i;c<e.length;c++)if(e[c]==="{"&&s++,e[c]==="}"&&s--,s===0){n=c+1;break}if(n===-1)return console.error("Could not find end of JSON in i18n string"),e;const d=e.substring(i,n),l=n<e.length?e.substring(n):"";try{const c=JSON.parse(d);if(c&&c.key){const u=sg(c.key);return r+u+l}}catch(c){console.error("Error parsing i18n JSON:",c)}return e}catch(r){return console.error("Error processing translation:",r),e}}function Ov(){return Object.fromEntries(Object.entries(Tv).map(([e,o])=>[e.split("/").pop().split(".")[0],o.default]))}const ie=Ov(),nt=Object.keys(ie),Xv=Object.entries(ie).map(([e,o])=>[o._name||e,e]);let lg=new Set,so=!1,st;async function Zv(e){if(so&&!(so&&e!==st))return;st=e,jv({...ie,...e??{}});const t=Jb();let r=t&&nt.includes(t)?t:null;if(!r){const a=t?.split("-")[0];r=a&&nt.includes(a)?a:"en"}await Vb({fallbackLocale:"en",initialLocale:r});for(const a in ie)if(ie[a]&&typeof ie[a]=="object"&&ie[a].common&&typeof ie[a].common=="object"){const i=ie[a].common;for(const n in i)lg.add(`common.${n}`)}so=!0}function Jv(e){he.set(e)}function Yv(e,o,t="en"){return e&&o.includes(e)?e:t}function jv(e){if(e)try{for(const o in e)Mt(o,e[o])}catch(o){console.error("Error loading translations:",o)}}const Cv="./assets/index-Dz5CWoA7.css";let bo;bo=[];let vo,cg,Lv=new Promise(e=>{cg=e});async function Dv(){vo=(await p(()=>import("./Index-BzI0zy4H.js"),__vite__mapDeps([214,87,6,7,8,3,4,5,9,2,10,21,22,215]),import.meta.url)).default,cg()}function Iv(){const e={SvelteComponent:Je.SvelteComponent};for(const t in Je)t!=="SvelteComponent"&&(t==="SvelteComponentDev"?e[t]=e.SvelteComponent:e[t]=Je[t]);window.__gradio__svelte__internal=e;class o extends HTMLElement{control_page_title;initial_height;is_embed;container;info;autoscroll;eager;theme_mode;host;space;src;app;loading;updating;constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await Dv(),this.loading=!0,this.app&&this.app.$destroy(),typeof bo!="string"&&bo.forEach(i=>Yo(i,document.head)),await Yo(Cv,document.head);const r=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(i=>{this.dispatchEvent(r)}).observe(this,{childList:!0}),this.app=new vo({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-31-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:He,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(r,a,i){if(await Lv,(r==="host"||r==="space"||r==="src")&&i!==a){if(this.updating={name:r,value:i},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,r==="host"?this.host=i:r==="space"?this.space=i:r==="src"&&(this.src=i),this.app=new vo({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-31-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:He,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",o)}Iv();export{Ft as $,Qo as A,g0 as B,Mv as C,Ge as F,p as _,Ov as a,lg as b,qv as c,Jv as d,jv as e,kf as f,Yv as g,Rv as h,Wv as i,Hv as j,Bv as k,Xv as l,Yo as m,zv as n,Gv as o,Vv as p,ve as q,vf as r,Zv as s,Kv as t,Uv as u,Nv as v,de as w,wt as x,he as y,Fv as z};
//# sourceMappingURL=index-Ccc2t4AG.js.map
