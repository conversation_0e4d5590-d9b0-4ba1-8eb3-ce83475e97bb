{"version": 3, "file": "flowGraphDebounceBlock-DEdqoOLr.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDebounceBlock.js"], "sourcesContent": ["import { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * This block debounces the execution of a input, i.e. ensures that the input is only executed once every X times\n */\nexport class FlowGraphDebounceBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.count = this.registerDataInput(\"count\", RichTypeNumber);\n        this.reset = this._registerSignalInput(\"reset\");\n        this.currentCount = this.registerDataOutput(\"currentCount\", RichTypeNumber);\n    }\n    _execute(context, callingSignal) {\n        if (callingSignal === this.reset) {\n            context._setExecutionVariable(this, \"debounceCount\", 0);\n            return;\n        }\n        const count = this.count.getValue(context);\n        const currentCount = context._getExecutionVariable(this, \"debounceCount\", 0);\n        const newCount = currentCount + 1;\n        this.currentCount.setValue(newCount, context);\n        context._setExecutionVariable(this, \"debounceCount\", newCount);\n        if (newCount >= count) {\n            this.out._activateSignal(context);\n            context._setExecutionVariable(this, \"debounceCount\", 0);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphDebounceBlock\" /* FlowGraphBlockNames.Debounce */;\n    }\n}\nRegisterClass(\"FlowGraphDebounceBlock\" /* FlowGraphBlockNames.Debounce */, FlowGraphDebounceBlock);\n//# sourceMappingURL=flowGraphDebounceBlock.js.map"], "names": ["FlowGraphDebounceBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeNumber", "context", "callingSignal", "count", "newCount", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAA+BC,CAAqC,CAC7E,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,MAAQ,KAAK,kBAAkB,QAASC,CAAc,EAC3D,KAAK,MAAQ,KAAK,qBAAqB,OAAO,EAC9C,KAAK,aAAe,KAAK,mBAAmB,eAAgBA,CAAc,CAC7E,CACD,SAASC,EAASC,EAAe,CAC7B,GAAIA,IAAkB,KAAK,MAAO,CAC9BD,EAAQ,sBAAsB,KAAM,gBAAiB,CAAC,EACtD,MACH,CACD,MAAME,EAAQ,KAAK,MAAM,SAASF,CAAO,EAEnCG,EADeH,EAAQ,sBAAsB,KAAM,gBAAiB,CAAC,EAC3C,EAChC,KAAK,aAAa,SAASG,EAAUH,CAAO,EAC5CA,EAAQ,sBAAsB,KAAM,gBAAiBG,CAAQ,EACzDA,GAAYD,IACZ,KAAK,IAAI,gBAAgBF,CAAO,EAChCA,EAAQ,sBAAsB,KAAM,gBAAiB,CAAC,EAE7D,CAID,cAAe,CACX,MAAO,wBACV,CACL,CACAI,EAAc,yBAA6DR,CAAsB", "x_google_ignoreList": [0]}