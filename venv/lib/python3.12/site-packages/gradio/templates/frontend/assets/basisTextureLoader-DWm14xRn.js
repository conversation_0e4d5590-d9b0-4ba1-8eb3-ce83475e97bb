import{b as h,I as v,T as U}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";function D(){const e={cTFETC1:0,cTFETC2:1,cTFBC1:2,cTFBC3:3,cTFBC4:4,cTFBC5:5,cTFBC7:6,cTFPVRTC1_4_RGB:8,cTFPVRTC1_4_RGBA:9,cTFASTC_4x4:10,cTFATC_RGB:11,cTFATC_RGBA_INTERPOLATED_ALPHA:12,cTFRGBA32:13,cTFRGB565:14,cTFBGR565:15,cTFRGBA4444:16,cTFFXT1_RGB:17,cTFPVRTC2_4_RGB:18,cTFPVRTC2_4_RGBA:19,cTFETC2_EAC_R11:20,cTFETC2_EAC_RG11:21};let o=null;onmessage=t=>{if(t.data.action==="init"){if(t.data.url)try{importScripts(t.data.url)}catch(n){postMessage({action:"error",error:n})}o||(o=BASIS({wasmBinary:t.data.wasmBinary})),o!==null&&o.then(n=>{BASIS=n,n.initializeBasis(),postMessage({action:"init"})})}else if(t.data.action==="transcode"){const n=t.data.config,a=t.data.imageData,d=new BASIS.BasisFile(a),r=l(d);let f=t.data.ignoreSupportedFormats?null:s(t.data.config,r),T=!1;f===null&&(T=!0,f=r.hasAlpha?e.cTFBC3:e.cTFBC1);let m=!0;d.startTranscoding()||(m=!1);const p=[];for(let g=0;g<r.images.length&&m;g++){const F=r.images[g];if(n.loadSingleImage===void 0||n.loadSingleImage===g){let b=F.levels.length;n.loadMipmapLevels===!1&&(b=1);for(let C=0;C<b;C++){const E=F.levels[C],P=i(d,g,C,f,T);if(!P){m=!1;break}E.transcodedPixels=P,p.push(E.transcodedPixels.buffer)}}}d.close(),d.delete(),T&&(f=-1),m?postMessage({action:"transcode",success:m,id:t.data.id,fileInfo:r,format:f},p):postMessage({action:"transcode",success:m,id:t.data.id})}};function s(t,n){let a=null;return t.supportedCompressionFormats&&(t.supportedCompressionFormats.astc?a=e.cTFASTC_4x4:t.supportedCompressionFormats.bc7?a=e.cTFBC7:t.supportedCompressionFormats.s3tc?a=n.hasAlpha?e.cTFBC3:e.cTFBC1:t.supportedCompressionFormats.pvrtc?a=n.hasAlpha?e.cTFPVRTC1_4_RGBA:e.cTFPVRTC1_4_RGB:t.supportedCompressionFormats.etc2?a=e.cTFETC2:t.supportedCompressionFormats.etc1?a=e.cTFETC1:a=e.cTFRGB565),a}function l(t){const n=t.getHasAlpha(),a=t.getNumImages(),d=[];for(let f=0;f<a;f++){const T={levels:[]},m=t.getNumLevels(f);for(let p=0;p<m;p++){const g={width:t.getImageWidth(f,p),height:t.getImageHeight(f,p)};T.levels.push(g)}d.push(T)}return{hasAlpha:n,images:d}}function i(t,n,a,d,r){const f=t.getImageTranscodedSizeInBytes(n,a,d);let T=new Uint8Array(f);if(!t.transcodeImage(T,n,a,d,1,0))return null;if(r){const m=t.getImageWidth(n,a)+3&-4,p=t.getImageHeight(n,a)+3&-4;T=c(T,0,m,p)}return T}function c(t,n,a,d){const r=new Uint16Array(4),f=new Uint16Array(a*d),T=a/4,m=d/4;for(let p=0;p<m;p++)for(let g=0;g<T;g++){const F=n+8*(p*T+g);r[0]=t[F]|t[F+1]<<8,r[1]=t[F+2]|t[F+3]<<8,r[2]=(2*(r[0]&31)+1*(r[1]&31))/3|(2*(r[0]&2016)+1*(r[1]&2016))/3&2016|(2*(r[0]&63488)+1*(r[1]&63488))/3&63488,r[3]=(2*(r[1]&31)+1*(r[0]&31))/3|(2*(r[1]&2016)+1*(r[0]&2016))/3&2016|(2*(r[1]&63488)+1*(r[0]&63488))/3&63488;for(let b=0;b<4;b++){const C=t[F+4+b];let E=(p*4+b)*a+g*4;f[E++]=r[C&3],f[E++]=r[C>>2&3],f[E++]=r[C>>4&3],f[E++]=r[C>>6&3]}}return f}}function W(e,o,s){return new Promise((l,i)=>{const c=t=>{t.data.action==="init"?(e.removeEventListener("message",c),l(e)):t.data.action==="error"&&i(t.data.error||"error initializing worker")};e.addEventListener("message",c),e.postMessage({action:"init",url:s?h.GetBabylonScriptURL(s):void 0,wasmBinary:o},[o])})}var u;(function(e){e[e.cTFETC1=0]="cTFETC1",e[e.cTFETC2=1]="cTFETC2",e[e.cTFBC1=2]="cTFBC1",e[e.cTFBC3=3]="cTFBC3",e[e.cTFBC4=4]="cTFBC4",e[e.cTFBC5=5]="cTFBC5",e[e.cTFBC7=6]="cTFBC7",e[e.cTFPVRTC1_4_RGB=8]="cTFPVRTC1_4_RGB",e[e.cTFPVRTC1_4_RGBA=9]="cTFPVRTC1_4_RGBA",e[e.cTFASTC_4x4=10]="cTFASTC_4x4",e[e.cTFATC_RGB=11]="cTFATC_RGB",e[e.cTFATC_RGBA_INTERPOLATED_ALPHA=12]="cTFATC_RGBA_INTERPOLATED_ALPHA",e[e.cTFRGBA32=13]="cTFRGBA32",e[e.cTFRGB565=14]="cTFRGB565",e[e.cTFBGR565=15]="cTFBGR565",e[e.cTFRGBA4444=16]="cTFRGBA4444",e[e.cTFFXT1_RGB=17]="cTFFXT1_RGB",e[e.cTFPVRTC2_4_RGB=18]="cTFPVRTC2_4_RGB",e[e.cTFPVRTC2_4_RGBA=19]="cTFPVRTC2_4_RGBA",e[e.cTFETC2_EAC_R11=20]="cTFETC2_EAC_R11",e[e.cTFETC2_EAC_RG11=21]="cTFETC2_EAC_RG11"})(u||(u={}));const _={JSModuleURL:`${h._DefaultCdnUrl}/basisTranscoder/1/basis_transcoder.js`,WasmModuleURL:`${h._DefaultCdnUrl}/basisTranscoder/1/basis_transcoder.wasm`},V=(e,o)=>{let s;switch(e){case u.cTFETC1:s=36196;break;case u.cTFBC1:s=33776;break;case u.cTFBC4:s=33779;break;case u.cTFASTC_4x4:s=37808;break;case u.cTFETC2:s=37496;break;case u.cTFBC7:s=36492;break}if(s===void 0)throw"The chosen Basis transcoder format is not currently supported";return s};let L=null,G=null,k=0;const H=!1,x=()=>(L||(L=new Promise((e,o)=>{G?e(G):h.LoadFileAsync(h.GetBabylonScriptURL(_.WasmModuleURL)).then(s=>{if(typeof URL!="function")return o("Basis transcoder requires an environment with a URL constructor");const l=URL.createObjectURL(new Blob([`(${D})()`],{type:"application/javascript"}));G=new Worker(l),W(G,s,_.JSModuleURL).then(e,o)}).catch(o)})),L),R=(e,o)=>{const s=e instanceof ArrayBuffer?new Uint8Array(e):e;return new Promise((l,i)=>{x().then(()=>{const c=k++,t=a=>{a.data.action==="transcode"&&a.data.id===c&&(G.removeEventListener("message",t),a.data.success?l(a.data):i("Transcode is not supported on this device"))};G.addEventListener("message",t);const n=new Uint8Array(s.byteLength);n.set(new Uint8Array(s.buffer,s.byteOffset,s.byteLength)),G.postMessage({action:"transcode",id:c,imageData:n,config:o,ignoreSupportedFormats:H},[n.buffer])},c=>{i(c)})})},B=(e,o)=>{let s=o._gl?.TEXTURE_2D;e.isCube&&(s=o._gl?.TEXTURE_CUBE_MAP),o._bindTextureDirectly(s,e,!0)},w=(e,o)=>{const s=e.getEngine();for(let l=0;l<o.fileInfo.images.length;l++){const i=o.fileInfo.images[l].levels[0];if(e._invertVScale=e.invertY,o.format===-1||o.format===u.cTFRGB565)if(e.type=10,e.format=4,s._features.basisNeedsPOT&&(Math.log2(i.width)%1!==0||Math.log2(i.height)%1!==0)){const c=new v(s,2);e._invertVScale=e.invertY,c.type=10,c.format=4,c.width=i.width+3&-4,c.height=i.height+3&-4,B(c,s),s._uploadDataToTextureDirectly(c,new Uint16Array(i.transcodedPixels.buffer),l,0,4,!0),s._rescaleTexture(c,e,s.scenes[0],s._getInternalFormat(4),()=>{s._releaseTexture(c),B(e,s)})}else e._invertVScale=!e.invertY,e.width=i.width+3&-4,e.height=i.height+3&-4,e.samplingMode=2,B(e,s),s._uploadDataToTextureDirectly(e,new Uint16Array(i.transcodedPixels.buffer),l,0,4,!0);else{e.width=i.width,e.height=i.height,e.generateMipMaps=o.fileInfo.images[l].levels.length>1;const c=y.GetInternalFormatFromBasisFormat(o.format,s);e.format=c,B(e,s),o.fileInfo.images[l].levels.forEach((t,n)=>{s._uploadCompressedDataToTextureDirectly(e,c,t.width,t.height,t.transcodedPixels,l,n)}),s._features.basisNeedsPOT&&(Math.log2(e.width)%1!==0||Math.log2(e.height)%1!==0)&&(h.Warn("Loaded .basis texture width and height are not a power of two. Texture wrapping will be set to Texture.CLAMP_ADDRESSMODE as other modes are not supported with non power of two dimensions in webGL 1."),e._cachedWrapU=U.CLAMP_ADDRESSMODE,e._cachedWrapV=U.CLAMP_ADDRESSMODE)}}},y={JSModuleURL:_.JSModuleURL,WasmModuleURL:_.WasmModuleURL,GetInternalFormatFromBasisFormat:V,TranscodeAsync:R,LoadTextureFromTranscodeResult:w};Object.defineProperty(y,"JSModuleURL",{get:function(){return _.JSModuleURL},set:function(e){_.JSModuleURL=e}});Object.defineProperty(y,"WasmModuleURL",{get:function(){return _.WasmModuleURL},set:function(e){_.WasmModuleURL=e}});class X{constructor(){this.supportCascades=!1}loadCubeData(o,s,l,i,c){if(Array.isArray(o))return;const t=s.getEngine().getCaps(),n={supportedCompressionFormats:{etc1:!!t.etc1,s3tc:!!t.s3tc,pvrtc:!!t.pvrtc,etc2:!!t.etc2,astc:!!t.astc,bc7:!!t.bptc}};R(o,n).then(a=>{const d=a.fileInfo.images[0].levels.length>1&&s.generateMipMaps;w(s,a),s.getEngine()._setCubeMapTextureParams(s,d),s.isReady=!0,s.onLoadedObservable.notifyObservers(s),s.onLoadedObservable.clear(),i&&i()}).catch(a=>{h.Warn("Failed to transcode Basis file, transcoding may not be supported on this device"),s.isReady=!0,c&&c(a)})}loadData(o,s,l){const i=s.getEngine().getCaps(),c={supportedCompressionFormats:{etc1:!!i.etc1,s3tc:!!i.s3tc,pvrtc:!!i.pvrtc,etc2:!!i.etc2,astc:!!i.astc,bc7:!!i.bptc}};R(o,c).then(t=>{const n=t.fileInfo.images[0].levels[0],a=t.fileInfo.images[0].levels.length>1&&s.generateMipMaps;l(n.width,n.height,a,t.format!==-1,()=>{w(s,t)})}).catch(t=>{h.Warn("Failed to transcode Basis file, transcoding may not be supported on this device"),h.Warn(`Failed to transcode Basis file: ${t}`),l(0,0,!1,!1,()=>{},!0)})}}export{X as _BasisTextureLoader};
//# sourceMappingURL=basisTextureLoader-DWm14xRn.js.map
