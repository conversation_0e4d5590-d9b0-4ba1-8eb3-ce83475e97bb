{"version": 3, "file": "flowGraphCancelDelayBlock-DR3yfbrj.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCancelDelayBlock.js"], "sourcesContent": ["import { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\n/**\n * This block cancels a delay that was previously scheduled.\n */\nexport class FlowGraphCancelDelayBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.delayIndex = this.registerDataInput(\"delayIndex\", RichTypeNumber);\n    }\n    _execute(context, _callingSignal) {\n        const delayIndex = this.delayIndex.getValue(context);\n        if (delayIndex <= 0 || isNaN(delayIndex) || !isFinite(delayIndex)) {\n            return this._reportError(context, \"Invalid delay index\");\n        }\n        const timers = context._getExecutionVariable(this, \"pendingDelays\", []);\n        const timer = timers[delayIndex];\n        if (timer) {\n            timer.dispose();\n            // not removing it from the array. Disposing it will clear all of its resources\n        }\n        // activate the out output flow\n        this.out._activateSignal(context);\n    }\n    getClassName() {\n        return \"FlowGraphCancelDelayBlock\" /* FlowGraphBlockNames.CancelDelay */;\n    }\n}\nRegisterClass(\"FlowGraphCancelDelayBlock\" /* FlowGraphBlockNames.CancelDelay */, FlowGraphCancelDelayBlock);\n//# sourceMappingURL=flowGraphCancelDelayBlock.js.map"], "names": ["FlowGraphCancelDelayBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeNumber", "context", "_callingSignal", "delayIndex", "timer", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAAkCC,CAAqC,CAChF,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,WAAa,KAAK,kBAAkB,aAAcC,CAAc,CACxE,CACD,SAASC,EAASC,EAAgB,CAC9B,MAAMC,EAAa,KAAK,WAAW,SAASF,CAAO,EACnD,GAAIE,GAAc,GAAK,MAAMA,CAAU,GAAK,CAAC,SAASA,CAAU,EAC5D,OAAO,KAAK,aAAaF,EAAS,qBAAqB,EAG3D,MAAMG,EADSH,EAAQ,sBAAsB,KAAM,gBAAiB,CAAA,CAAE,EACjDE,CAAU,EAC3BC,GACAA,EAAM,QAAO,EAIjB,KAAK,IAAI,gBAAgBH,CAAO,CACnC,CACD,cAAe,CACX,MAAO,2BACV,CACL,CACAI,EAAc,4BAAmER,CAAyB", "x_google_ignoreList": [0]}