{"version": 3, "file": "stateDiagram-v2-WR7QG3WR-BSCKtWyM.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-WR7QG3WR.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-4IRHCMPZ.mjs\";\nimport \"./chunk-2O5F6CEG.mjs\";\nimport \"./chunk-SSJB2B2L.mjs\";\nimport \"./chunk-XWQKHCUW.mjs\";\nimport \"./chunk-JXS2JFWQ.mjs\";\nimport \"./chunk-VRARSN5C.mjs\";\nimport \"./chunk-6Q42YGA5.mjs\";\nimport \"./chunk-KIRMWWLE.mjs\";\nimport \"./chunk-4BQVQIO5.mjs\";\nimport \"./chunk-ABD7OU7K.mjs\";\nimport {\n  __name\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(2);\n  },\n  renderer: stateRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["diagram", "stateDiagram_default", "StateDB", "stateRenderer_v3_unified_default", "styles_default", "__name", "cnf"], "mappings": "wSAoBG,IAACA,EAAU,CACZ,OAAQC,EACR,IAAI,IAAK,CACP,OAAO,IAAIC,EAAQ,CAAC,CACrB,EACD,SAAUC,EACV,OAAQC,EACR,KAAsBC,EAAQC,GAAQ,CAC/BA,EAAI,QACPA,EAAI,MAAQ,IAEdA,EAAI,MAAM,oBAAsBA,EAAI,mBACrC,EAAE,MAAM,CACX", "x_google_ignoreList": [0]}