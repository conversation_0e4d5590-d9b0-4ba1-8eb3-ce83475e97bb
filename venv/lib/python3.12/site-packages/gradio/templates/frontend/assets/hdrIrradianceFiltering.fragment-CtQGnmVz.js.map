{"version": 3, "file": "hdrIrradianceFiltering.fragment-CtQGnmVz.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nimport \"./ShadersInclude/importanceSampling.js\";\nimport \"./ShadersInclude/pbrBRDFFunctions.js\";\nimport \"./ShadersInclude/hdrFilteringFunctions.js\";\nconst name = \"hdrIrradianceFilteringPixelShader\";\nconst shader = `#include<helperFunctions>\n#include<importanceSampling>\n#include<pbrBRDFFunctions>\n#include<hdrFilteringFunctions>\nuniform samplerCube inputTexture;\n#ifdef IBL_CDF_FILTERING\nuniform sampler2D icdfTexture;\n#endif\nuniform vec2 vFilteringInfo;uniform float hdrScale;varying vec3 direction;void main() {vec3 color=irradiance(inputTexture,direction,vFilteringInfo\n#ifdef IBL_CDF_FILTERING\n,icdfTexture\n#endif\n);gl_FragColor=vec4(color*hdrScale,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const hdrIrradianceFilteringPixelShader = { name, shader };\n//# sourceMappingURL=hdrIrradianceFiltering.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "hdrIrradianceFilteringPixelShader"], "mappings": "iLAMA,MAAMA,EAAO,oCACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAcVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAoC,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}