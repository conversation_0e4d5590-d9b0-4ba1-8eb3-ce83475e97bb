import{V as c,Q as h,ap as m}from"./index-Cb4A4-Xi.js";import{S as l}from"./objectModelMapping-D3Nr8hfO.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";function T(r,n,t,o){return c.FromArray(n,t).scaleInPlace(o)}function f(r,n,t,o){return h.FromArray(n,t).scaleInPlace(o)}function y(r,n,t,o){const e=new Array(r._numMorphTargets);for(let i=0;i<e.length;i++)e[i]=n[t++]*o;return e}class g{constructor(n,t,o,e){this.type=n,this.name=t,this.getValue=o,this.getStride=e}_buildAnimation(n,t,o){const e=new m(n,this.name,t,this.type);return e.setKeys(o),e}}class u extends g{buildAnimations(n,t,o,e){const i=[];return i.push({babylonAnimatable:n._babylonTransformNode,babylonAnimation:this._buildAnimation(t,o,e)}),i}}class d extends g{buildAnimations(n,t,o,e){const i=[];if(n._numMorphTargets)for(let s=0;s<n._numMorphTargets;s++){const b=new m(`${t}_${s}`,this.name,o,this.type);if(b.setKeys(e.map(a=>({frame:a.frame,inTangent:a.inTangent?a.inTangent[s]:void 0,value:a.value[s],outTangent:a.outTangent?a.outTangent[s]:void 0,interpolation:a.interpolation}))),n._primitiveBabylonMeshes){for(const a of n._primitiveBabylonMeshes)if(a.morphTargetManager){const A=a.morphTargetManager.getTarget(s),p=b.clone();A.animations.push(p),i.push({babylonAnimatable:A,babylonAnimation:p})}}}return i}}l("/nodes/{}/translation",[new u(m.ANIMATIONTYPE_VECTOR3,"position",T,()=>3)]);l("/nodes/{}/rotation",[new u(m.ANIMATIONTYPE_QUATERNION,"rotationQuaternion",f,()=>4)]);l("/nodes/{}/scale",[new u(m.ANIMATIONTYPE_VECTOR3,"scaling",T,()=>3)]);l("/nodes/{}/weights",[new d(m.ANIMATIONTYPE_FLOAT,"influence",y,r=>r._numMorphTargets)]);export{g as AnimationPropertyInfo,u as TransformNodeAnimationPropertyInfo,d as WeightAnimationPropertyInfo,f as getQuaternion,T as getVector3,y as getWeights};
//# sourceMappingURL=glTFLoaderAnimation-BVab4Q5G.js.map
