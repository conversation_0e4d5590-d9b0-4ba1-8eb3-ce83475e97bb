import{f as a}from"./KHR_interactivity-DVSiPm30.js";import{c as e}from"./declarationMapper-r-RREw_K.js";import{R as l}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class o extends a{constructor(t){super(t),this.onOn=this._registerSignalOutput("onOn"),this.onOff=this._registerSignalOutput("onOff"),this.value=this.registerDataOutput("value",e)}_execute(t,r){let i=t._getExecutionVariable(this,"value",typeof this.config?.startValue=="boolean"?!this.config.startValue:!1);i=!i,t._setExecutionVariable(this,"value",i),this.value.setValue(i,t),i?this.onOn._activateSignal(t):this.onOff._activateSignal(t)}getClassName(){return"FlowGraphFlipFlopBlock"}}l("FlowGraphFlipFlopBlock",o);export{o as FlowGraphFlipFlopBlock};
//# sourceMappingURL=flowGraphFlipFlopBlock-D-QhtGXk.js.map
