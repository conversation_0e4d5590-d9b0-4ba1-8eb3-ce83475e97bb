import{a as _}from"./KHR_interactivity-DVSiPm30.js";import{b as h}from"./declarationMapper-r-RREw_K.js";import{O as n,h as m,R as v}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";var d;(function(a){a[a.INIT=0]="INIT",a[a.STARTED=1]="STARTED",a[a.ENDED=2]="ENDED"})(d||(d={}));class u{constructor(e){this.onEachCountObservable=new n,this.onTimerAbortedObservable=new n,this.onTimerEndedObservable=new n,this.onStateChangedObservable=new n,this._observer=null,this._breakOnNextTick=!1,this._tick=t=>{const s=Date.now();this._timer=s-this._startTime;const i={startTime:this._startTime,currentTime:s,deltaTime:this._timer,completeRate:this._timer/this._timeToEnd,payload:t},r=this._breakOnNextTick||this._breakCondition(i);r||this._timer>=this._timeToEnd?this._stop(i,r):this.onEachCountObservable.notifyObservers(i)},this._setState(0),this._contextObservable=e.contextObservable,this._observableParameters=e.observableParameters??{},this._breakCondition=e.breakCondition??(()=>!1),this._timeToEnd=e.timeout,e.onEnded&&this.onTimerEndedObservable.add(e.onEnded),e.onTick&&this.onEachCountObservable.add(e.onTick),e.onAborted&&this.onTimerAbortedObservable.add(e.onAborted)}set breakCondition(e){this._breakCondition=e}clearObservables(){this.onEachCountObservable.clear(),this.onTimerAbortedObservable.clear(),this.onTimerEndedObservable.clear(),this.onStateChangedObservable.clear()}start(e=this._timeToEnd){if(this._state===1)throw new Error("Timer already started. Please stop it before starting again");this._timeToEnd=e,this._startTime=Date.now(),this._timer=0,this._observer=this._contextObservable.add(this._tick,this._observableParameters.mask,this._observableParameters.insertFirst,this._observableParameters.scope),this._setState(1)}stop(){this._state===1&&(this._breakOnNextTick=!0)}dispose(){this._observer&&this._contextObservable.remove(this._observer),this.clearObservables()}_setState(e){this._state=e,this.onStateChangedObservable.notifyObservers(this._state)}_stop(e,t=!1){this._contextObservable.remove(this._observer),this._setState(2),t?this.onTimerAbortedObservable.notifyObservers(e):this.onTimerEndedObservable.notifyObservers(e)}}class l extends _{constructor(e){super(e),this.cancel=this._registerSignalInput("cancel"),this.duration=this.registerDataInput("duration",h),this.lastDelayIndex=this.registerDataOutput("lastDelayIndex",h,-1)}_preparePendingTasks(e){const t=this.duration.getValue(e);if(t<0||isNaN(t)||!isFinite(t))return this._reportError(e,"Invalid duration in SetDelay block");if(e._getGlobalContextVariable("activeDelays",0)>=l.MaxParallelDelayCount)return this._reportError(e,"Max parallel delays reached");const i=e._getGlobalContextVariable("lastDelayIndex",-1),r=e._getExecutionVariable(this,"pendingDelays",[]),c=e.configuration.scene,o=new u({timeout:t*1e3,contextObservable:c.onBeforeRenderObservable,onEnded:()=>this._onEnded(o,e)});o.start();const b=i+1;this.lastDelayIndex.setValue(b,e),e._setGlobalContextVariable("lastDelayIndex",b),r[b]=o,e._setExecutionVariable(this,"pendingDelays",r)}_cancelPendingTasks(e){const t=e._getExecutionVariable(this,"pendingDelays",[]);for(const s of t)s?.dispose();e._deleteExecutionVariable(this,"pendingDelays"),this.lastDelayIndex.setValue(-1,e)}_execute(e,t){if(t===this.cancel){this._cancelPendingTasks(e);return}else this._preparePendingTasks(e),this.out._activateSignal(e)}getClassName(){return"FlowGraphSetDelayBlock"}_onEnded(e,t){const s=t._getExecutionVariable(this,"pendingDelays",[]),i=s.indexOf(e);i!==-1?s.splice(i,1):m.Warn("FlowGraphTimerBlock: Timer ended but was not found in the running timers list"),t._removePendingBlock(this),this.done._activateSignal(t)}}l.MaxParallelDelayCount=100;v("FlowGraphSetDelayBlock",l);export{l as FlowGraphSetDelayBlock};
//# sourceMappingURL=flowGraphSetDelayBlock-DsrOvLHy.js.map
