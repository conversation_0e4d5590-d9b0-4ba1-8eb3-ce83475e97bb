{"version": 3, "file": "diagram-DHSB7DV3-DzQPZow3.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/diagram-DHSB7DV3.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-IUKPXING.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-ABD7OU7K.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-XYJ2X5CJ.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/packet/db.ts\nvar defaultPacketData = {\n  packet: []\n};\nvar data = structuredClone(defaultPacketData);\nvar DEFAULT_PACKET_CONFIG = defaultConfig_default.packet;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...getConfig().packet\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n}, \"getConfig\");\nvar getPacket = /* @__PURE__ */ __name(() => data.packet, \"getPacket\");\nvar pushWord = /* @__PURE__ */ __name((word) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n}, \"pushWord\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultPacketData);\n}, \"clear\");\nvar db = {\n  pushWord,\n  getPacket,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/packet/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar maxPacketSize = 1e4;\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  let lastByte = -1;\n  let word = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, label } of ast.blocks) {\n    if (end && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    if (start !== lastByte + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${lastByte + 1}.`\n      );\n    }\n    lastByte = end ?? start;\n    log.debug(`Packet block ${start} - ${lastByte} with label ${label}`);\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n}, \"populate\");\nvar getNextFittingBlock = /* @__PURE__ */ __name((block, row, bitsPerRow) => {\n  if (block.end === void 0) {\n    block.end = block.start;\n  }\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block, void 0];\n  }\n  return [\n    {\n      start: block.start,\n      end: row * bitsPerRow - 1,\n      label: block.label\n    },\n    {\n      start: row * bitsPerRow,\n      end: block.end,\n      label: block.label\n    }\n  ];\n}, \"getNextFittingBlock\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"packet\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/packet/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const config = db2.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db2.getPacket();\n  const title = db2.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg = selectSvgElement(id);\n  svg.attr(\"viewbox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n  svg.append(\"text\").text(title).attr(\"x\", svgWidth / 2).attr(\"y\", svgHeight - totalRowHeight / 2).attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"packetTitle\");\n}, \"draw\");\nvar drawWord = /* @__PURE__ */ __name((svg, word, rowNumber, { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }) => {\n  const group = svg.append(\"g\");\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = block.start % bitsPerRow * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    group.append(\"rect\").attr(\"x\", blockX).attr(\"y\", wordY).attr(\"width\", width).attr(\"height\", rowHeight).attr(\"class\", \"packetBlock\");\n    group.append(\"text\").attr(\"x\", blockX + width / 2).attr(\"y\", wordY + rowHeight / 2).attr(\"class\", \"packetLabel\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").text(block.label);\n    if (!showBits) {\n      continue;\n    }\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group.append(\"text\").attr(\"x\", blockX + (isSingleBlock ? width / 2 : 0)).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte start\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", isSingleBlock ? \"middle\" : \"start\").text(block.start);\n    if (!isSingleBlock) {\n      group.append(\"text\").attr(\"x\", blockX + width).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte end\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", \"end\").text(block.end);\n    }\n  }\n}, \"drawWord\");\nvar renderer = { draw };\n\n// src/diagrams/packet/styles.ts\nvar defaultPacketStyleOptions = {\n  byteFontSize: \"10px\",\n  startByteColor: \"black\",\n  endByteColor: \"black\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\",\n  blockStrokeColor: \"black\",\n  blockStrokeWidth: \"1\",\n  blockFillColor: \"#efefef\"\n};\nvar styles = /* @__PURE__ */ __name(({ packet } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n}, \"styles\");\n\n// src/diagrams/packet/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "names": ["defaultPacketData", "data", "DEFAULT_PACKET_CONFIG", "defaultConfig_default", "getConfig2", "__name", "config", "cleanAndMerge", "getConfig", "getPacket", "pushWord", "word", "clear2", "clear", "db", "setAccTitle", "getAccTitle", "setDiagramTitle", "getDiagramTitle", "getAccDescription", "setAccDescription", "maxPacketSize", "populate", "ast", "populateCommonDb", "lastByte", "row", "bitsPerRow", "start", "end", "label", "log", "block", "nextBlock", "getNextFittingBlock", "parser", "input", "parse", "draw", "_text", "id", "_version", "diagram2", "db2", "rowHeight", "paddingY", "bitWidth", "words", "title", "totalRowHeight", "svgHeight", "svgWidth", "svg", "selectSvgElement", "configureSvgSize", "packet", "drawWord", "rowNumber", "paddingX", "showBits", "group", "wordY", "blockX", "width", "isSingleBlock", "bitNumberY", "renderer", "defaultPacketStyleOptions", "styles", "options", "diagram"], "mappings": "6dAyBA,IAAIA,EAAoB,CACtB,OAAQ,CAAE,CACZ,EACIC,EAAO,gBAAgBD,CAAiB,EACxCE,EAAwBC,EAAsB,OAC9CC,EAA6BC,EAAO,IAAM,CAC5C,MAAMC,EAASC,EAAc,CAC3B,GAAGL,EACH,GAAGM,EAAW,EAAC,MACnB,CAAG,EACD,OAAIF,EAAO,WACTA,EAAO,UAAY,IAEdA,CACT,EAAG,WAAW,EACVG,EAA4BJ,EAAO,IAAMJ,EAAK,OAAQ,WAAW,EACjES,EAA2BL,EAAQM,GAAS,CAC1CA,EAAK,OAAS,GAChBV,EAAK,OAAO,KAAKU,CAAI,CAEzB,EAAG,UAAU,EACTC,EAAyBP,EAAO,IAAM,CACxCQ,IACAZ,EAAO,gBAAgBD,CAAiB,CAC1C,EAAG,OAAO,EACNc,EAAK,CACP,SAAAJ,EACA,UAAAD,EACA,UAAWL,EACX,MAAOQ,EACP,YAAAG,EACA,YAAAC,EACA,gBAAAC,EACA,gBAAAC,EACA,kBAAAC,EACA,kBAAAC,CACF,EAIIC,EAAgB,IAChBC,EAA2BjB,EAAQkB,GAAQ,CAC7CC,EAAiBD,EAAKT,CAAE,EACxB,IAAIW,EAAW,GACXd,EAAO,CAAA,EACPe,EAAM,EACV,KAAM,CAAE,WAAAC,CAAU,EAAKb,EAAG,UAAS,EACnC,OAAS,CAAE,MAAAc,EAAO,IAAAC,EAAK,MAAAC,CAAO,IAAIP,EAAI,OAAQ,CAC5C,GAAIM,GAAOA,EAAMD,EACf,MAAM,IAAI,MAAM,gBAAgBA,CAAK,MAAMC,CAAG,8CAA8C,EAE9F,GAAID,IAAUH,EAAW,EACvB,MAAM,IAAI,MACR,gBAAgBG,CAAK,MAAMC,GAAOD,CAAK,4CAA4CH,EAAW,CAAC,GACvG,EAII,IAFAA,EAAWI,GAAOD,EAClBG,EAAI,MAAM,gBAAgBH,CAAK,MAAMH,CAAQ,eAAeK,CAAK,EAAE,EAC5DnB,EAAK,QAAUgB,EAAa,GAAKb,EAAG,UAAS,EAAG,OAASO,GAAe,CAC7E,KAAM,CAACW,EAAOC,CAAS,EAAIC,EAAoB,CAAE,MAAAN,EAAO,IAAAC,EAAK,MAAAC,CAAK,EAAIJ,EAAKC,CAAU,EAOrF,GANAhB,EAAK,KAAKqB,CAAK,EACXA,EAAM,IAAM,IAAMN,EAAMC,IAC1Bb,EAAG,SAASH,CAAI,EAChBA,EAAO,CAAA,EACPe,KAEE,CAACO,EACH,OAED,CAAE,MAAAL,EAAO,IAAAC,EAAK,MAAAC,CAAK,EAAKG,EAC1B,CACF,CACDnB,EAAG,SAASH,CAAI,CAClB,EAAG,UAAU,EACTuB,EAAsC7B,EAAO,CAAC2B,EAAON,EAAKC,IAAe,CAI3E,GAHIK,EAAM,MAAQ,SAChBA,EAAM,IAAMA,EAAM,OAEhBA,EAAM,MAAQA,EAAM,IACtB,MAAM,IAAI,MAAM,eAAeA,EAAM,KAAK,8BAA8BA,EAAM,GAAG,GAAG,EAEtF,OAAIA,EAAM,IAAM,GAAKN,EAAMC,EAClB,CAACK,EAAO,MAAM,EAEhB,CACL,CACE,MAAOA,EAAM,MACb,IAAKN,EAAMC,EAAa,EACxB,MAAOK,EAAM,KACd,EACD,CACE,MAAON,EAAMC,EACb,IAAKK,EAAM,IACX,MAAOA,EAAM,KACd,CACL,CACA,EAAG,qBAAqB,EACpBG,EAAS,CACX,MAAuB9B,EAAO,MAAO+B,GAAU,CAC7C,MAAMb,EAAM,MAAMc,EAAM,SAAUD,CAAK,EACvCL,EAAI,MAAMR,CAAG,EACbD,EAASC,CAAG,CACb,EAAE,OAAO,CACZ,EAGIe,EAAuBjC,EAAO,CAACkC,EAAOC,EAAIC,EAAUC,IAAa,CACnE,MAAMC,EAAMD,EAAS,GACfpC,EAASqC,EAAI,YACb,CAAE,UAAAC,EAAW,SAAAC,EAAU,SAAAC,EAAU,WAAAnB,CAAU,EAAKrB,EAChDyC,EAAQJ,EAAI,YACZK,EAAQL,EAAI,kBACZM,EAAiBL,EAAYC,EAC7BK,EAAYD,GAAkBF,EAAM,OAAS,IAAMC,EAAQ,EAAIJ,GAC/DO,EAAWL,EAAWnB,EAAa,EACnCyB,EAAMC,EAAiBb,CAAE,EAC/BY,EAAI,KAAK,UAAW,OAAOD,CAAQ,IAAID,CAAS,EAAE,EAClDI,EAAiBF,EAAKF,EAAWC,EAAU7C,EAAO,WAAW,EAC7D,SAAW,CAACK,EAAM4C,CAAM,IAAKR,EAAM,QAAO,EACxCS,EAASJ,EAAKG,EAAQ5C,EAAML,CAAM,EAEpC8C,EAAI,OAAO,MAAM,EAAE,KAAKJ,CAAK,EAAE,KAAK,IAAKG,EAAW,CAAC,EAAE,KAAK,IAAKD,EAAYD,EAAiB,CAAC,EAAE,KAAK,oBAAqB,QAAQ,EAAE,KAAK,cAAe,QAAQ,EAAE,KAAK,QAAS,aAAa,CAChM,EAAG,MAAM,EACLO,EAA2BnD,EAAO,CAAC+C,EAAKzC,EAAM8C,EAAW,CAAE,UAAAb,EAAW,SAAAc,EAAU,SAAAb,EAAU,SAAAC,EAAU,WAAAnB,EAAY,SAAAgC,CAAQ,IAAO,CACjI,MAAMC,EAAQR,EAAI,OAAO,GAAG,EACtBS,EAAQJ,GAAab,EAAYC,GAAYA,EACnD,UAAWb,KAASrB,EAAM,CACxB,MAAMmD,EAAS9B,EAAM,MAAQL,EAAamB,EAAW,EAC/CiB,GAAS/B,EAAM,IAAMA,EAAM,MAAQ,GAAKc,EAAWY,EAGzD,GAFAE,EAAM,OAAO,MAAM,EAAE,KAAK,IAAKE,CAAM,EAAE,KAAK,IAAKD,CAAK,EAAE,KAAK,QAASE,CAAK,EAAE,KAAK,SAAUnB,CAAS,EAAE,KAAK,QAAS,aAAa,EAClIgB,EAAM,OAAO,MAAM,EAAE,KAAK,IAAKE,EAASC,EAAQ,CAAC,EAAE,KAAK,IAAKF,EAAQjB,EAAY,CAAC,EAAE,KAAK,QAAS,aAAa,EAAE,KAAK,oBAAqB,QAAQ,EAAE,KAAK,cAAe,QAAQ,EAAE,KAAKZ,EAAM,KAAK,EAC/L,CAAC2B,EACH,SAEF,MAAMK,EAAgBhC,EAAM,MAAQA,EAAM,MACpCiC,EAAaJ,EAAQ,EAC3BD,EAAM,OAAO,MAAM,EAAE,KAAK,IAAKE,GAAUE,EAAgBD,EAAQ,EAAI,EAAE,EAAE,KAAK,IAAKE,CAAU,EAAE,KAAK,QAAS,kBAAkB,EAAE,KAAK,oBAAqB,MAAM,EAAE,KAAK,cAAeD,EAAgB,SAAW,OAAO,EAAE,KAAKhC,EAAM,KAAK,EACtOgC,GACHJ,EAAM,OAAO,MAAM,EAAE,KAAK,IAAKE,EAASC,CAAK,EAAE,KAAK,IAAKE,CAAU,EAAE,KAAK,QAAS,gBAAgB,EAAE,KAAK,oBAAqB,MAAM,EAAE,KAAK,cAAe,KAAK,EAAE,KAAKjC,EAAM,GAAG,CAEnL,CACH,EAAG,UAAU,EACTkC,EAAW,CAAE,KAAA5B,GAGb6B,EAA4B,CAC9B,aAAc,OACd,eAAgB,QAChB,aAAc,QACd,WAAY,QACZ,cAAe,OACf,WAAY,QACZ,cAAe,OACf,iBAAkB,QAClB,iBAAkB,IAClB,eAAgB,SAClB,EACIC,EAAyB/D,EAAO,CAAC,CAAE,OAAAkD,CAAM,EAAK,CAAA,IAAO,CACvD,MAAMc,EAAU9D,EAAc4D,EAA2BZ,CAAM,EAC/D,MAAO;AAAA;AAAA,eAEMc,EAAQ,YAAY;AAAA;AAAA;AAAA,UAGzBA,EAAQ,cAAc;AAAA;AAAA;AAAA,UAGtBA,EAAQ,YAAY;AAAA;AAAA;AAAA,UAGpBA,EAAQ,UAAU;AAAA,eACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,UAG1BA,EAAQ,UAAU;AAAA,eACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,YAGxBA,EAAQ,gBAAgB;AAAA,kBAClBA,EAAQ,gBAAgB;AAAA,UAChCA,EAAQ,cAAc;AAAA;AAAA,EAGhC,EAAG,QAAQ,EAGPC,GAAU,CACZ,OAAAnC,EACA,GAAArB,EACA,SAAAoD,EACA,OAAAE,CACF", "x_google_ignoreList": [0]}