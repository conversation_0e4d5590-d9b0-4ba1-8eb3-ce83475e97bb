import{F as c}from"./KHR_interactivity-DVSiPm30.js";import{g as d,d as g,b as h,R as p}from"./declarationMapper-r-RREw_K.js";import{ap as y,R as F}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class f extends c{constructor(t={}){super(t),this.keyFrames=[];const a=typeof t?.animationType=="string"?d(t.animationType):g(t?.animationType??0),i=t?.keyFramesCount??1,n=this.registerDataInput("duration_0",h,0),e=this.registerDataInput("value_0",a);this.keyFrames.push({duration:n,value:e});for(let s=1;s<i+1;s++){const u=this.registerDataInput(`duration_${s}`,h,s===i?t.duration:void 0),m=this.registerDataInput(`value_${s}`,a);this.keyFrames.push({duration:u,value:m})}this.initialValue=this.keyFrames[0].value,this.endValue=this.keyFrames[i].value,this.easingFunction=this.registerDataInput("easingFunction",p),this.animation=this.registerDataOutput("animation",p),this.propertyName=this.registerDataInput("propertyName",p,t?.propertyName),this.customBuildAnimation=this.registerDataInput("customBuildAnimation",p)}_updateOutputs(t){const a=t._getGlobalContextVariable("interpolationAnimations",[]),i=this.propertyName.getValue(t),n=this.easingFunction.getValue(t),e=this._createAnimation(t,i,n);if(this.animation.setValue(e,t),Array.isArray(e))for(const s of e)a.push(s.uniqueId);else a.push(e.uniqueId);t._setGlobalContextVariable("interpolationAnimations",a)}_createAnimation(t,a,i){const n=this.initialValue.richType,e=[],s=this.initialValue.getValue(t)||n.defaultValue;e.push({frame:0,value:s});const u=this.config?.numberOfKeyFrames??1;for(let r=1;r<u+1;r++){const l=this.keyFrames[r].duration?.getValue(t);let o=this.keyFrames[r].value?.getValue(t);r===u-1&&(o=o||n.defaultValue),l!==void 0&&o&&e.push({frame:l*60,value:o})}const m=this.customBuildAnimation.getValue(t);if(m)return m()(e,60,n.animationType,i);if(typeof a=="string"){const r=y.CreateAnimation(a,n.animationType,60,i);return r.setKeys(e),[r]}else return a.map(l=>{const o=y.CreateAnimation(l,n.animationType,60,i);return o.setKeys(e),o})}getClassName(){return"FlowGraphInterpolationBlock"}}F("FlowGraphInterpolationBlock",f);export{f as FlowGraphInterpolationBlock};
//# sourceMappingURL=flowGraphInterpolationBlock-BqJW2qFy.js.map
