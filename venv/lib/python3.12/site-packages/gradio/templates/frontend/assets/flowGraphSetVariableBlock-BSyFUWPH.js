import{R as p}from"./index-Cb4A4-Xi.js";import{b as u}from"./KHR_interactivity-DVSiPm30.js";import{R as n}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class v extends u{constructor(a){if(super(a),!a.variable&&!a.variables)throw new Error("FlowGraphSetVariableBlock: variable/variables is not defined");if(a.variables&&a.variable)throw new Error("FlowGraphSetVariableBlock: variable and variables are both defined");if(a.variables)for(const i of a.variables)this.registerDataInput(i,n);else this.registerDataInput("value",n)}_execute(a,i){if(this.config?.variables)for(const r of this.config.variables)this._saveVariable(a,r);else this._saveVariable(a,this.config?.variable,"value");this.out._activateSignal(a)}_saveVariable(a,i,r){const e=a._getGlobalContextVariable("currentlyRunningAnimationGroups",[]);for(const l of e){const s=a.assetsContext.animationGroups[l];for(const t of s.targetedAnimations)if(t.target===a&&t.target===a&&t.animation.targetProperty===i){s.stop();const o=e.indexOf(l);o>-1&&e.splice(o,1),a._setGlobalContextVariable("currentlyRunningAnimationGroups",e);break}}const b=this.getDataInput(r||i)?.getValue(a);a.setVariable(i,b)}getClassName(){return"FlowGraphSetVariableBlock"}serialize(a){super.serialize(a),a.config.variable=this.config?.variable}}p("FlowGraphSetVariableBlock",v);export{v as FlowGraphSetVariableBlock};
//# sourceMappingURL=flowGraphSetVariableBlock-BSyFUWPH.js.map
