{"version": 3, "file": "audioEngine-D-crK827.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Audio/audioEngine.js"], "sourcesContent": ["import { Observable } from \"../Misc/observable.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { AbstractEngine } from \"../Engines/abstractEngine.js\";\nimport { IsWindowObjectExist } from \"../Misc/domManagement.js\";\n// Sets the default audio engine to Babylon.js\nAbstractEngine.AudioEngineFactory = (hostElement, audioContext, audioDestination) => {\n    return new AudioEngine(hostElement, audioContext, audioDestination);\n};\n/**\n * This represents the default audio engine used in babylon.\n * It is responsible to play, synchronize and analyse sounds throughout the  application.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\n */\nexport class AudioEngine {\n    /**\n     * Gets the current AudioContext if available.\n     */\n    get audioContext() {\n        if (!this._audioContextInitialized) {\n            this._initializeAudioContext();\n        }\n        return this._audioContext;\n    }\n    /**\n     * Instantiates a new audio engine.\n     *\n     * There should be only one per page as some browsers restrict the number\n     * of audio contexts you can create.\n     * @param hostElement defines the host element where to display the mute icon if necessary\n     * @param audioContext defines the audio context to be used by the audio engine\n     * @param audioDestination defines the audio destination node to be used by audio engine\n     */\n    constructor(hostElement = null, audioContext = null, audioDestination = null) {\n        this._audioContext = null;\n        this._audioContextInitialized = false;\n        this._muteButton = null;\n        this._audioDestination = null;\n        /**\n         * Gets whether the current host supports Web Audio and thus could create AudioContexts.\n         */\n        this.canUseWebAudio = false;\n        /**\n         * Defines if Babylon should emit a warning if WebAudio is not supported.\n         * @ignoreNaming\n         */\n        // eslint-disable-next-line @typescript-eslint/naming-convention\n        this.WarnedWebAudioUnsupported = false;\n        /**\n         * Gets whether or not mp3 are supported by your browser.\n         */\n        this.isMP3supported = false;\n        /**\n         * Gets whether or not ogg are supported by your browser.\n         */\n        this.isOGGsupported = false;\n        /**\n         * Gets whether audio has been unlocked on the device.\n         * Some Browsers have strong restrictions about Audio and won't autoplay unless\n         * a user interaction has happened.\n         */\n        this.unlocked = false;\n        /**\n         * Defines if the audio engine relies on a custom unlocked button.\n         * In this case, the embedded button will not be displayed.\n         */\n        this.useCustomUnlockedButton = false;\n        /**\n         * Event raised when audio has been unlocked on the browser.\n         */\n        this.onAudioUnlockedObservable = new Observable();\n        /**\n         * Event raised when audio has been locked on the browser.\n         */\n        this.onAudioLockedObservable = new Observable();\n        this._tryToRun = false;\n        this._onResize = () => {\n            this._moveButtonToTopLeft();\n        };\n        if (!IsWindowObjectExist()) {\n            return;\n        }\n        if (typeof window.AudioContext !== \"undefined\") {\n            this.canUseWebAudio = true;\n        }\n        const audioElem = document.createElement(\"audio\");\n        this._hostElement = hostElement;\n        this._audioContext = audioContext;\n        this._audioDestination = audioDestination;\n        try {\n            if (audioElem &&\n                !!audioElem.canPlayType &&\n                (audioElem.canPlayType('audio/mpeg; codecs=\"mp3\"').replace(/^no$/, \"\") || audioElem.canPlayType(\"audio/mp3\").replace(/^no$/, \"\"))) {\n                this.isMP3supported = true;\n            }\n        }\n        catch (e) {\n            // protect error during capability check.\n        }\n        try {\n            if (audioElem && !!audioElem.canPlayType && audioElem.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, \"\")) {\n                this.isOGGsupported = true;\n            }\n        }\n        catch (e) {\n            // protect error during capability check.\n        }\n    }\n    /**\n     * Flags the audio engine in Locked state.\n     * This happens due to new browser policies preventing audio to autoplay.\n     */\n    lock() {\n        this._triggerSuspendedState();\n    }\n    /**\n     * Unlocks the audio engine once a user action has been done on the dom.\n     * This is helpful to resume play once browser policies have been satisfied.\n     */\n    unlock() {\n        if (this._audioContext?.state === \"running\") {\n            this._hideMuteButton();\n            if (!this.unlocked) {\n                // Notify users that the audio stack is unlocked/unmuted\n                this.unlocked = true;\n                this.onAudioUnlockedObservable.notifyObservers(this);\n            }\n            return;\n        }\n        // On iOS, if the audio context resume request was sent from an event other than a `click` event, then\n        // the resume promise will never resolve and the only way to get the audio context unstuck is to\n        // suspend it and make another resume request.\n        if (this._tryToRun) {\n            this._audioContext?.suspend().then(() => {\n                this._tryToRun = false;\n                this._triggerRunningState();\n            });\n        }\n        else {\n            this._triggerRunningState();\n        }\n    }\n    /** @internal */\n    _resumeAudioContextOnStateChange() {\n        this._audioContext?.addEventListener(\"statechange\", () => {\n            if (this.unlocked && this._audioContext?.state !== \"running\") {\n                this._resumeAudioContext();\n            }\n        }, {\n            once: true,\n            passive: true,\n            signal: AbortSignal.timeout(3000),\n        });\n    }\n    _resumeAudioContext() {\n        if (this._audioContext?.resume) {\n            return this._audioContext.resume();\n        }\n        return Promise.resolve();\n    }\n    _initializeAudioContext() {\n        try {\n            if (this.canUseWebAudio) {\n                if (!this._audioContext) {\n                    this._audioContext = new AudioContext();\n                }\n                // create a global volume gain node\n                this.masterGain = this._audioContext.createGain();\n                this.masterGain.gain.value = 1;\n                if (!this._audioDestination) {\n                    this._audioDestination = this._audioContext.destination;\n                }\n                this.masterGain.connect(this._audioDestination);\n                this._audioContextInitialized = true;\n                if (this._audioContext.state === \"running\") {\n                    // Do not wait for the promise to unlock.\n                    this._triggerRunningState();\n                }\n            }\n        }\n        catch (e) {\n            this.canUseWebAudio = false;\n            Logger.Error(\"Web Audio: \" + e.message);\n        }\n    }\n    _triggerRunningState() {\n        if (this._tryToRun) {\n            return;\n        }\n        this._tryToRun = true;\n        this._resumeAudioContext()\n            .then(() => {\n            this._tryToRun = false;\n            if (this._muteButton) {\n                this._hideMuteButton();\n            }\n            // Notify users that the audio stack is unlocked/unmuted\n            this.unlocked = true;\n            this.onAudioUnlockedObservable.notifyObservers(this);\n        })\n            .catch(() => {\n            this._tryToRun = false;\n            this.unlocked = false;\n        });\n    }\n    _triggerSuspendedState() {\n        this.unlocked = false;\n        this.onAudioLockedObservable.notifyObservers(this);\n        this._displayMuteButton();\n    }\n    _displayMuteButton() {\n        if (this.useCustomUnlockedButton || this._muteButton) {\n            return;\n        }\n        this._muteButton = document.createElement(\"BUTTON\");\n        this._muteButton.className = \"babylonUnmuteIcon\";\n        this._muteButton.id = \"babylonUnmuteIconBtn\";\n        this._muteButton.title = \"Unmute\";\n        const imageUrl = !window.SVGSVGElement\n            ? \"https://cdn.babylonjs.com/Assets/audio.png\"\n            : \"data:image/svg+xml;charset=UTF-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2239%22%20height%3D%2232%22%20viewBox%3D%220%200%2039%2032%22%3E%3Cpath%20fill%3D%22white%22%20d%3D%22M9.625%2018.938l-0.031%200.016h-4.953q-0.016%200-0.031-0.016v-12.453q0-0.016%200.031-0.016h4.953q0.031%200%200.031%200.016v12.453zM12.125%207.688l8.719-8.703v27.453l-8.719-8.719-0.016-0.047v-9.938zM23.359%207.875l1.406-1.406%204.219%204.203%204.203-4.203%201.422%201.406-4.219%204.219%204.219%204.203-1.484%201.359-4.141-4.156-4.219%204.219-1.406-1.422%204.219-4.203z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E\";\n        const css = \".babylonUnmuteIcon { position: absolute; left: 20px; top: 20px; height: 40px; width: 60px; background-color: rgba(51,51,51,0.7); background-image: url(\" +\n            imageUrl +\n            \");  background-size: 80%; background-repeat:no-repeat; background-position: center; background-position-y: 4px; border: none; outline: none; transition: transform 0.125s ease-out; cursor: pointer; z-index: 9999; } .babylonUnmuteIcon:hover { transform: scale(1.05) } .babylonUnmuteIcon:active { background-color: rgba(51,51,51,1) }\";\n        const style = document.createElement(\"style\");\n        style.appendChild(document.createTextNode(css));\n        document.getElementsByTagName(\"head\")[0].appendChild(style);\n        document.body.appendChild(this._muteButton);\n        this._moveButtonToTopLeft();\n        this._muteButton.addEventListener(\"touchend\", () => {\n            this._triggerRunningState();\n        }, true);\n        this._muteButton.addEventListener(\"click\", () => {\n            this.unlock();\n        }, true);\n        window.addEventListener(\"resize\", this._onResize);\n    }\n    _moveButtonToTopLeft() {\n        if (this._hostElement && this._muteButton) {\n            this._muteButton.style.top = this._hostElement.offsetTop + 20 + \"px\";\n            this._muteButton.style.left = this._hostElement.offsetLeft + 20 + \"px\";\n        }\n    }\n    _hideMuteButton() {\n        if (this._muteButton) {\n            document.body.removeChild(this._muteButton);\n            this._muteButton = null;\n        }\n    }\n    /**\n     * Destroy and release the resources associated with the audio context.\n     */\n    dispose() {\n        if (this.canUseWebAudio && this._audioContextInitialized) {\n            if (this._connectedAnalyser && this._audioContext) {\n                this._connectedAnalyser.stopDebugCanvas();\n                this._connectedAnalyser.dispose();\n                this.masterGain.disconnect();\n                this.masterGain.connect(this._audioContext.destination);\n                this._connectedAnalyser = null;\n            }\n            this.masterGain.gain.value = 1;\n        }\n        this.WarnedWebAudioUnsupported = false;\n        this._hideMuteButton();\n        window.removeEventListener(\"resize\", this._onResize);\n        this.onAudioUnlockedObservable.clear();\n        this.onAudioLockedObservable.clear();\n        this._audioContext?.close();\n        this._audioContext = null;\n    }\n    /**\n     * Gets the global volume sets on the master gain.\n     * @returns the global volume if set or -1 otherwise\n     */\n    getGlobalVolume() {\n        if (this.canUseWebAudio && this._audioContextInitialized) {\n            return this.masterGain.gain.value;\n        }\n        else {\n            return -1;\n        }\n    }\n    /**\n     * Sets the global volume of your experience (sets on the master gain).\n     * @param newVolume Defines the new global volume of the application\n     */\n    setGlobalVolume(newVolume) {\n        if (this.canUseWebAudio && this._audioContextInitialized) {\n            this.masterGain.gain.value = newVolume;\n        }\n    }\n    /**\n     * Connect the audio engine to an audio analyser allowing some amazing\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\n     * @param analyser The analyser to connect to the engine\n     */\n    connectToAnalyser(analyser) {\n        if (this._connectedAnalyser) {\n            this._connectedAnalyser.stopDebugCanvas();\n        }\n        if (this.canUseWebAudio && this._audioContextInitialized && this._audioContext) {\n            this._connectedAnalyser = analyser;\n            this.masterGain.disconnect();\n            this._connectedAnalyser.connectAudioNodes(this.masterGain, this._audioContext.destination);\n        }\n    }\n}\n//# sourceMappingURL=audioEngine.js.map"], "names": ["AbstractEngine", "hostElement", "audioContext", "audioDestination", "AudioEngine", "Observable", "IsWindowObjectExist", "audioElem", "e", "<PERSON><PERSON>", "css", "style", "newVolume", "analyser"], "mappings": "6DAKAA,EAAe,mBAAqB,CAACC,EAAaC,EAAcC,IACrD,IAAIC,EAAYH,EAAaC,EAAcC,CAAgB,EAO/D,MAAMC,CAAY,CAIrB,IAAI,cAAe,CACf,OAAK,KAAK,0BACN,KAAK,wBAAuB,EAEzB,KAAK,aACf,CAUD,YAAYH,EAAc,KAAMC,EAAe,KAAMC,EAAmB,KAAM,CA8C1E,GA7CA,KAAK,cAAgB,KACrB,KAAK,yBAA2B,GAChC,KAAK,YAAc,KACnB,KAAK,kBAAoB,KAIzB,KAAK,eAAiB,GAMtB,KAAK,0BAA4B,GAIjC,KAAK,eAAiB,GAItB,KAAK,eAAiB,GAMtB,KAAK,SAAW,GAKhB,KAAK,wBAA0B,GAI/B,KAAK,0BAA4B,IAAIE,EAIrC,KAAK,wBAA0B,IAAIA,EACnC,KAAK,UAAY,GACjB,KAAK,UAAY,IAAM,CACnB,KAAK,qBAAoB,CACrC,EACY,CAACC,EAAmB,EACpB,OAEA,OAAO,OAAO,aAAiB,MAC/B,KAAK,eAAiB,IAE1B,MAAMC,EAAY,SAAS,cAAc,OAAO,EAChD,KAAK,aAAeN,EACpB,KAAK,cAAgBC,EACrB,KAAK,kBAAoBC,EACzB,GAAI,CACII,GACEA,EAAU,cACXA,EAAU,YAAY,0BAA0B,EAAE,QAAQ,OAAQ,EAAE,GAAKA,EAAU,YAAY,WAAW,EAAE,QAAQ,OAAQ,EAAE,KAC/H,KAAK,eAAiB,GAE7B,MACS,CAET,CACD,GAAI,CACIA,GAAeA,EAAU,aAAeA,EAAU,YAAY,4BAA4B,EAAE,QAAQ,OAAQ,EAAE,IAC9G,KAAK,eAAiB,GAE7B,MACS,CAET,CACJ,CAKD,MAAO,CACH,KAAK,uBAAsB,CAC9B,CAKD,QAAS,CACL,GAAI,KAAK,eAAe,QAAU,UAAW,CACzC,KAAK,gBAAe,EACf,KAAK,WAEN,KAAK,SAAW,GAChB,KAAK,0BAA0B,gBAAgB,IAAI,GAEvD,MACH,CAIG,KAAK,UACL,KAAK,eAAe,QAAS,EAAC,KAAK,IAAM,CACrC,KAAK,UAAY,GACjB,KAAK,qBAAoB,CACzC,CAAa,EAGD,KAAK,qBAAoB,CAEhC,CAED,kCAAmC,CAC/B,KAAK,eAAe,iBAAiB,cAAe,IAAM,CAClD,KAAK,UAAY,KAAK,eAAe,QAAU,WAC/C,KAAK,oBAAmB,CAExC,EAAW,CACC,KAAM,GACN,QAAS,GACT,OAAQ,YAAY,QAAQ,GAAI,CAC5C,CAAS,CACJ,CACD,qBAAsB,CAClB,OAAI,KAAK,eAAe,OACb,KAAK,cAAc,SAEvB,QAAQ,SAClB,CACD,yBAA0B,CACtB,GAAI,CACI,KAAK,iBACA,KAAK,gBACN,KAAK,cAAgB,IAAI,cAG7B,KAAK,WAAa,KAAK,cAAc,WAAU,EAC/C,KAAK,WAAW,KAAK,MAAQ,EACxB,KAAK,oBACN,KAAK,kBAAoB,KAAK,cAAc,aAEhD,KAAK,WAAW,QAAQ,KAAK,iBAAiB,EAC9C,KAAK,yBAA2B,GAC5B,KAAK,cAAc,QAAU,WAE7B,KAAK,qBAAoB,EAGpC,OACMC,EAAG,CACN,KAAK,eAAiB,GACtBC,EAAO,MAAM,cAAgBD,EAAE,OAAO,CACzC,CACJ,CACD,sBAAuB,CACf,KAAK,YAGT,KAAK,UAAY,GACjB,KAAK,oBAAqB,EACrB,KAAK,IAAM,CACZ,KAAK,UAAY,GACb,KAAK,aACL,KAAK,gBAAe,EAGxB,KAAK,SAAW,GAChB,KAAK,0BAA0B,gBAAgB,IAAI,CAC/D,CAAS,EACI,MAAM,IAAM,CACb,KAAK,UAAY,GACjB,KAAK,SAAW,EAC5B,CAAS,EACJ,CACD,wBAAyB,CACrB,KAAK,SAAW,GAChB,KAAK,wBAAwB,gBAAgB,IAAI,EACjD,KAAK,mBAAkB,CAC1B,CACD,oBAAqB,CACjB,GAAI,KAAK,yBAA2B,KAAK,YACrC,OAEJ,KAAK,YAAc,SAAS,cAAc,QAAQ,EAClD,KAAK,YAAY,UAAY,oBAC7B,KAAK,YAAY,GAAK,uBACtB,KAAK,YAAY,MAAQ,SAIzB,MAAME,EAAM,2JAHM,OAAO,cAEnB,qnBADA,8CAIF,6UACEC,EAAQ,SAAS,cAAc,OAAO,EAC5CA,EAAM,YAAY,SAAS,eAAeD,CAAG,CAAC,EAC9C,SAAS,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAYC,CAAK,EAC1D,SAAS,KAAK,YAAY,KAAK,WAAW,EAC1C,KAAK,qBAAoB,EACzB,KAAK,YAAY,iBAAiB,WAAY,IAAM,CAChD,KAAK,qBAAoB,CAC5B,EAAE,EAAI,EACP,KAAK,YAAY,iBAAiB,QAAS,IAAM,CAC7C,KAAK,OAAM,CACd,EAAE,EAAI,EACP,OAAO,iBAAiB,SAAU,KAAK,SAAS,CACnD,CACD,sBAAuB,CACf,KAAK,cAAgB,KAAK,cAC1B,KAAK,YAAY,MAAM,IAAM,KAAK,aAAa,UAAY,GAAK,KAChE,KAAK,YAAY,MAAM,KAAO,KAAK,aAAa,WAAa,GAAK,KAEzE,CACD,iBAAkB,CACV,KAAK,cACL,SAAS,KAAK,YAAY,KAAK,WAAW,EAC1C,KAAK,YAAc,KAE1B,CAID,SAAU,CACF,KAAK,gBAAkB,KAAK,2BACxB,KAAK,oBAAsB,KAAK,gBAChC,KAAK,mBAAmB,kBACxB,KAAK,mBAAmB,UACxB,KAAK,WAAW,aAChB,KAAK,WAAW,QAAQ,KAAK,cAAc,WAAW,EACtD,KAAK,mBAAqB,MAE9B,KAAK,WAAW,KAAK,MAAQ,GAEjC,KAAK,0BAA4B,GACjC,KAAK,gBAAe,EACpB,OAAO,oBAAoB,SAAU,KAAK,SAAS,EACnD,KAAK,0BAA0B,QAC/B,KAAK,wBAAwB,QAC7B,KAAK,eAAe,QACpB,KAAK,cAAgB,IACxB,CAKD,iBAAkB,CACd,OAAI,KAAK,gBAAkB,KAAK,yBACrB,KAAK,WAAW,KAAK,MAGrB,EAEd,CAKD,gBAAgBC,EAAW,CACnB,KAAK,gBAAkB,KAAK,2BAC5B,KAAK,WAAW,KAAK,MAAQA,EAEpC,CAOD,kBAAkBC,EAAU,CACpB,KAAK,oBACL,KAAK,mBAAmB,kBAExB,KAAK,gBAAkB,KAAK,0BAA4B,KAAK,gBAC7D,KAAK,mBAAqBA,EAC1B,KAAK,WAAW,aAChB,KAAK,mBAAmB,kBAAkB,KAAK,WAAY,KAAK,cAAc,WAAW,EAEhG,CACL", "x_google_ignoreList": [0]}