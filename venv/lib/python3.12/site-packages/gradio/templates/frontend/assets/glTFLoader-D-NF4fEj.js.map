{"version": 3, "mappings": ";quBAUO,MAAMA,CAA4B,CACrC,aAAc,CAIV,KAAK,OAAS,CAAC,EAAE,EAIjB,KAAK,WAAa,CAAC,EAAE,EAIrB,KAAK,SAAW,CAAC,EAAE,EAInB,KAAK,aAAe,CAAC,EAAE,EAIvB,KAAK,SAAW,CAAC,EAAE,EAInB,KAAK,UAAY,CAAC,EAAE,EAIpB,KAAK,cAAgB,GAIrB,KAAK,eAAiB,GAItB,KAAK,gBAAkB,GAIvB,KAAK,aAAe,GAIpB,KAAK,eAAiB,GACtB,KAAK,MAAQ,IAAI,KACpB,CAKD,cAAcC,EAAkB,CAE5BA,EAAmBC,EAAM,iCAAiC,SAAS,EAC/D,MAAK,wBAGT,KAAK,OAAS,KAAK,OAAO,SAAQ,EAClC,KAAK,QAAU,KAAK,OAAO,UAAS,EACpC,KAAK,sBAAwB,KAAK,QAAQ,uBAAuB,IAAI,IAAM,CACvE,KAAK,MAAM,OAAS,CAChC,CAAS,EACD,KAAK,oBAAsB,KAAK,OAAO,qBAAqB,IAAKC,GAAS,CACtE,MAAMC,EAAMD,EAAK,MACjB,GAAI,CAACC,EAAI,SACL,GAAID,EAAK,OAASE,GAAmB,SAC7B,KAAK,OAAO,QAAQD,EAAI,OAAO,IAAM,IACrC,KAAK,SAAS,QAAQA,EAAI,OAAO,IAAM,IACvC,KAAK,SAAS,QAAQA,EAAI,OAAO,IAAM,IACvC,KAAK,UAAU,QAAQA,EAAI,OAAO,IAAM,IACxC,KAAK,WAAW,QAAQA,EAAI,OAAO,IAAM,IACzC,KAAK,aAAa,QAAQA,EAAI,OAAO,IAAM,IAC3C,KAAK,eAAe,QAAQA,EAAI,OAAO,IAAM,IAC7C,KAAK,gBAAgB,QAAQA,EAAI,OAAO,IAAM,IAC9C,KAAK,aAAa,QAAQA,EAAI,OAAO,IAAM,IAC3C,KAAK,eAAe,QAAQA,EAAI,OAAO,IAAM,MAC/B,KAAK,MAAM,QAAQA,EAAI,OAAO,IAC9B,IACV,KAAK,MAAM,KAAKA,EAAI,OAAO,EAE1BH,GACDG,EAAI,eAAc,WAKtB,KAAK,OAAO,QAAQA,EAAI,OAAO,IAAM,IACrC,KAAK,SAAS,QAAQA,EAAI,OAAO,IAAM,IACvC,KAAK,SAAS,QAAQA,EAAI,OAAO,IAAM,IACvC,KAAK,UAAU,QAAQA,EAAI,OAAO,IAAM,IACxC,KAAK,WAAW,QAAQA,EAAI,OAAO,IAAM,IACzC,KAAK,aAAa,QAAQA,EAAI,OAAO,IAAM,IAC3C,KAAK,eAAe,QAAQA,EAAI,OAAO,IAAM,IAC7C,KAAK,gBAAgB,QAAQA,EAAI,OAAO,IAAM,IAC9C,KAAK,aAAa,QAAQA,EAAI,OAAO,IAAM,IAC3C,KAAK,eAAe,QAAQA,EAAI,OAAO,IAAM,GAAI,CACjD,MAAME,EAAQ,KAAK,MAAM,QAAQF,EAAI,OAAO,EACxCE,GAAS,GACT,KAAK,MAAM,OAAOA,EAAO,CAAC,EAEzBL,GACDG,EAAI,eAAc,CAEzB,EAGrB,CAAS,EACJ,CAID,eAAgB,CACR,KAAK,SACD,KAAK,qBACL,KAAK,OAAO,qBAAqB,OAAO,KAAK,mBAAmB,EAEhE,KAAK,uBACL,KAAK,QAAQ,uBAAuB,OAAO,KAAK,qBAAqB,EAEzE,KAAK,oBAAsB,KAC3B,KAAK,sBAAwB,MAEjC,KAAK,MAAM,OAAS,CACvB,CAKD,aAAc,CACV,GAAI,KAAK,oBAAqB,CAC1B,MAAMG,EAAS,KAAK,OAEpB,QAASD,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAAS,CACpD,MAAME,EAAU,KAAK,MAAMF,CAAK,EAC1BG,EAAQF,EAAO,2BACjB,KAAK,SAAS,QAAQC,CAAO,IAAM,GACnCD,EAAO,gBAAgB,eAAe,CAACE,EAAO,EAAG,CAAC,EAE7C,KAAK,OAAO,QAAQD,CAAO,IAAM,GACtCD,EAAO,gBAAgB,eAAe,EAAG,EAAGE,CAAK,EAE5C,KAAK,UAAU,QAAQD,CAAO,IAAM,GACzCD,EAAO,gBAAgB,eAAeE,EAAO,EAAG,CAAC,EAE5C,KAAK,SAAS,QAAQD,CAAO,IAAM,GACxCD,EAAO,gBAAgB,eAAe,EAAG,EAAG,CAACE,CAAK,EAE7C,KAAK,WAAW,QAAQD,CAAO,IAAM,GAC1CD,EAAO,gBAAgB,eAAe,EAAGE,EAAO,CAAC,EAE5C,KAAK,aAAa,QAAQD,CAAO,IAAM,GAC5CD,EAAO,gBAAgB,eAAe,EAAG,CAACE,EAAO,CAAC,EAE7C,KAAK,eAAe,QAAQD,CAAO,IAAM,IAC9CD,EAAO,gBAAgB,eAAe,EAAG,EAAG,CAAC,EAC7CA,EAAO,eAAe,GAAK,KAAK,kBAAiB,GAE5C,KAAK,gBAAgB,QAAQC,CAAO,IAAM,IAC/CD,EAAO,gBAAgB,eAAe,EAAG,EAAG,CAAC,EAC7CA,EAAO,eAAe,GAAK,KAAK,kBAAiB,GAE5C,KAAK,aAAa,QAAQC,CAAO,IAAM,IAC5CD,EAAO,gBAAgB,eAAe,EAAG,EAAG,CAAC,EAC7CA,EAAO,eAAe,GAAK,KAAK,kBAAiB,GAE5C,KAAK,eAAe,QAAQC,CAAO,IAAM,KAC9CD,EAAO,gBAAgB,eAAe,EAAG,EAAG,CAAC,EAC7CA,EAAO,eAAe,GAAK,KAAK,kBAAiB,GAEjDA,EAAO,SAAU,EAAC,uBAClBA,EAAO,gBAAgB,GAAK,IAEhCA,EAAO,cAAe,EAAC,YAAYA,EAAO,sBAAsB,EAChEG,EAAQ,qBAAqBH,EAAO,gBAAiBA,EAAO,uBAAwBA,EAAO,qBAAqB,EAChHA,EAAO,gBAAgB,WAAWA,EAAO,qBAAqB,CACjE,CACJ,CACJ,CAKD,cAAe,CACX,MAAO,6BACV,CAED,cAAe,CACX,KAAK,MAAM,OAAS,CACvB,CAKD,eAAgB,CACZ,MAAO,UACV,CACD,mBAAoB,CAChB,MAAMI,EAAuB,KAAK,OAAO,+BAA8B,EAEvE,OADmB,KAAK,cAAgB,KAAK,QAAQ,aAAY,EAAM,IAAQA,CAElF,CACL,CACAC,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,SAAU,MAAM,EAC1DY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,aAAc,MAAM,EAC9DY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,WAAY,MAAM,EAC5DY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,eAAgB,MAAM,EAChEY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,WAAY,MAAM,EAC5DY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,YAAa,MAAM,EAC7DY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,gBAAiB,MAAM,EACjEY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,iBAAkB,MAAM,EAClEY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,kBAAmB,MAAM,EACnEY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,eAAgB,MAAM,EAChEY,EAAW,CACPC,EAAW,CACf,EAAGb,EAA4B,UAAW,iBAAkB,MAAM,EAClEc,GAAiB,4BAAiCd,EC5O3C,MAAMe,EAAqB,CAM9B,YAIAC,EAAe,GAAM,CACjB,KAAK,aAAeA,EAIpB,KAAK,QAAU,CAAC,EAAG,EAAG,CAAC,EAIvB,KAAK,mBAAqB,IAC1B,KAAK,kBAAoB,KAIzB,KAAK,yBAA2B,IAAIC,EAKpC,KAAK,qBAAuB,GAC5B,KAAK,qBAAuB,GAC5B,KAAK,iBAAmB,EAC3B,CAKD,cAAchB,EAAkB,CAE5BA,EAAmBC,EAAM,iCAAiC,SAAS,EACnE,MAAMgB,EAAS,KAAK,OAAO,UAAS,EAC9BC,EAAUD,EAAO,kBAClB,KAAK,gBACN,KAAK,cAAiBE,GAAM,CACxB,MAAMhB,EAAMgB,EAAE,MACRC,EAAUjB,EAAI,cAAgB,QAIpC,GAHI,CAAC,KAAK,cAAgBiB,GAGtBD,EAAE,OAASE,EAAkB,aAAe,KAAK,QAAQ,QAAQlB,EAAI,MAAM,IAAM,GACjF,OAEJ,MAAMmB,EAAanB,EAAI,OACvB,GAAIgB,EAAE,OAASE,EAAkB,YAAa,CAE1C,GAAKD,GAAW,KAAK,mBAAqB,IAAQ,CAACA,GAAW,KAAK,uBAAyB,GACxF,OAEJ,KAAK,iBAAmBjB,EAAI,UAC5B,GAAI,CACAmB,GAAY,kBAAkBnB,EAAI,SAAS,CAC9C,MACS,CAET,CACG,KAAK,uBAAyB,KAC9B,KAAK,qBAAuBA,EAAI,QAEpC,KAAK,kBAAoB,CACrB,EAAGA,EAAI,QACP,EAAGA,EAAI,OAC/B,EACyBH,IACDG,EAAI,eAAc,EAClBe,GAAWA,EAAQ,SAGnBD,EAAO,eAAiB,KAAK,cAC7B,KAAK,aAAaE,EAAE,KAAK,CAEhC,SACQA,EAAE,OAASE,EAAkB,UAAW,CAE7C,GAAKD,GAAW,KAAK,mBAAqBjB,EAAI,WAAe,CAACiB,GAAW,KAAK,uBAAyBjB,EAAI,OACvG,OAEJ,GAAI,CACAmB,GAAY,sBAAsBnB,EAAI,SAAS,CAClD,MACS,CAET,CACD,KAAK,qBAAuB,GAC5B,KAAK,kBAAoB,KACpBH,GACDG,EAAI,eAAc,EAEtB,KAAK,iBAAmB,EAC3B,SACQgB,EAAE,OAASE,EAAkB,cAAgB,KAAK,mBAAqBlB,EAAI,WAAa,CAACiB,IAC9F,GAAIH,EAAO,eAAiB,KAAK,aAC7B,KAAK,aAAaE,EAAE,KAAK,UAEpB,KAAK,kBAAmB,CAC7B,MAAMT,EAAuB,KAAK,OAAO,+BAA8B,EACjEa,GAAWpB,EAAI,QAAU,KAAK,kBAAkB,GAAKO,EACrDc,EAAUrB,EAAI,QAAU,KAAK,kBAAkB,EACjD,KAAK,uBACL,KAAK,OAAO,eAAe,GAAKoB,EAAU,KAAK,mBAC/C,KAAK,OAAO,eAAe,GAAKC,EAAU,KAAK,oBAEnD,KAAK,yBAAyB,gBAAgB,CAAE,QAASD,EAAS,QAASC,CAAO,CAAE,EACpF,KAAK,kBAAoB,CACrB,EAAGrB,EAAI,QACP,EAAGA,EAAI,OACnC,EAC6BH,GACDG,EAAI,eAAc,CAEzB,EAErB,GAEQ,KAAK,aAAgBA,GAAQ,CACzB,GAAI,CAACc,EAAO,cACR,OAEJ,MAAMP,EAAuB,KAAK,OAAO,+BAA8B,EACjEa,EAAUpB,EAAI,UAAYO,EAChC,KAAK,OAAO,eAAe,GAAKa,EAAU,KAAK,mBAC/C,MAAMC,EAAUrB,EAAI,UACpB,KAAK,OAAO,eAAe,GAAKqB,EAAU,KAAK,mBAC/C,KAAK,kBAAoB,KACpBxB,GACDG,EAAI,eAAc,CAElC,EACQ,KAAK,UAAY,KAAK,OACjB,SAAU,EACV,cAAc,0BAA0B,KAAK,cAAekB,EAAkB,YAAcA,EAAkB,UAAYA,EAAkB,WAAW,EACxJH,IACA,KAAK,iBAAoBf,GAAQ,KAAK,cAAcA,CAAG,EACvDe,EAAQ,iBAAiB,cAAe,KAAK,iBAAkB,EAAK,EAE3E,CAMD,cAAcf,EAAK,CACfA,EAAI,eAAc,CACrB,CAID,eAAgB,CACZ,GAAI,KAAK,UAAW,CAEhB,GADA,KAAK,OAAO,SAAU,EAAC,cAAc,6BAA6B,KAAK,SAAS,EAC5E,KAAK,iBAAkB,CAEvB,MAAMe,EADS,KAAK,OAAO,UAAS,EACb,kBACvBA,GAAWA,EAAQ,oBAAoB,cAAe,KAAK,gBAAgB,CAC9E,CACG,KAAK,0BACL,KAAK,yBAAyB,QAElC,KAAK,UAAY,KACjB,KAAK,aAAe,KACpB,KAAK,kBAAoB,IAC5B,CACD,KAAK,iBAAmB,GACxB,KAAK,qBAAuB,EAC/B,CAKD,cAAe,CACX,MAAO,sBACV,CAKD,eAAgB,CACZ,MAAO,OACV,CACL,CACAP,EAAW,CACPC,EAAW,CACf,EAAGE,GAAqB,UAAW,UAAW,MAAM,EACpDH,EAAW,CACPC,EAAW,CACf,EAAGE,GAAqB,UAAW,qBAAsB,MAAM,EAC/DD,GAAiB,qBAA0BC,GClMpC,MAAMW,EAA0B,CACnC,aAAc,CAKV,KAAK,gBAAkB,EAKvB,KAAK,gBAAkB,EAKvB,KAAK,gBAAkB,EAIvB,KAAK,oBAAsB,IAAIT,EAK/B,KAAK,aAAe,EAKpB,KAAK,aAAe,EAKpB,KAAK,aAAe,EAQpB,KAAK,cAAgB,GAMrB,KAAK,WAAa,GACrB,CAOD,cAAchB,EAAkB,CAC5BA,EAAmBC,EAAM,iCAAiC,SAAS,EACnE,KAAK,OAAUyB,GAAY,CAEvB,GAAIA,EAAQ,OAASL,EAAkB,aACnC,OAEJ,MAAMM,EAAQD,EAAQ,MAChBE,EAAgBD,EAAM,YAAcE,GAAe,eAAiB,KAAK,cAAgB,EAC/F,KAAK,cAAiB,KAAK,gBAAkBD,EAAgBD,EAAM,OAAU,KAAK,WAClF,KAAK,cAAiB,KAAK,gBAAkBC,EAAgBD,EAAM,OAAU,KAAK,WAClF,KAAK,cAAiB,KAAK,gBAAkBC,EAAgBD,EAAM,OAAU,KAAK,WAC9EA,EAAM,iBACD3B,GACD2B,EAAM,eAAc,EAGxC,EACQ,KAAK,UAAY,KAAK,OAAO,SAAU,EAAC,cAAc,0BAA0B,KAAK,OAAQN,EAAkB,YAAY,CAC9H,CAID,eAAgB,CACR,KAAK,YACL,KAAK,OAAO,SAAU,EAAC,cAAc,6BAA6B,KAAK,SAAS,EAChF,KAAK,UAAY,KACjB,KAAK,OAAS,MAEd,KAAK,qBACL,KAAK,oBAAoB,OAEhC,CAID,aAAc,CACV,KAAK,oBAAoB,gBAAgB,CACrC,YAAa,KAAK,aAClB,YAAa,KAAK,aAClB,YAAa,KAAK,YAC9B,CAAS,EAED,KAAK,aAAe,EACpB,KAAK,aAAe,EACpB,KAAK,aAAe,CACvB,CAKD,cAAe,CACX,MAAO,2BACV,CAKD,eAAgB,CACZ,MAAO,YACV,CACL,CACAV,EAAW,CACPC,EAAW,CACf,EAAGa,GAA0B,UAAW,kBAAmB,MAAM,EACjEd,EAAW,CACPC,EAAW,CACf,EAAGa,GAA0B,UAAW,kBAAmB,MAAM,EACjEd,EAAW,CACPC,EAAW,CACf,EAAGa,GAA0B,UAAW,kBAAmB,MAAM,ECpIjE,IAAIK,GACH,SAAUA,EAAiB,CACxBA,EAAgBA,EAAgB,aAAkB,CAAC,EAAI,eACvDA,EAAgBA,EAAgB,eAAoB,CAAC,EAAI,iBACzDA,EAAgBA,EAAgB,UAAe,CAAC,EAAI,WACxD,GAAGA,IAAoBA,EAAkB,CAAE,EAAC,EAKrC,MAAMC,UAAkCN,EAA0B,CACrE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,cAAgBhB,EAAQ,OAC7B,KAAK,gBAAkBA,EAAQ,OAC/B,KAAK,WAAaA,EAAQ,OAI1B,KAAK,cAAgBqB,EAAgB,aACrC,KAAK,wBAA0B,EAC/B,KAAK,cAAgBA,EAAgB,aACrC,KAAK,wBAA0B,EAC/B,KAAK,cAAgB,KACrB,KAAK,wBAA0B,IAClC,CAKD,cAAe,CACX,MAAO,2BACV,CAMD,IAAI,mBAAmBE,EAAM,CACrBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,eAI5D,KAAK,cAAgBA,EAAgB,aACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,oBAAqB,CACrB,OAAI,KAAK,gBAAkBF,EAAgB,aAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,mBAAmBE,EAAM,CACrBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,eAI5D,KAAK,cAAgBA,EAAgB,aACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,oBAAqB,CACrB,OAAI,KAAK,gBAAkBF,EAAgB,aAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,mBAAmBE,EAAM,CACrBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,eAI5D,KAAK,cAAgBA,EAAgB,aACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,oBAAqB,CACrB,OAAI,KAAK,gBAAkBF,EAAgB,aAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,qBAAqBE,EAAM,CACvBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,iBAI5D,KAAK,cAAgBA,EAAgB,eACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,sBAAuB,CACvB,OAAI,KAAK,gBAAkBF,EAAgB,eAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,qBAAqBE,EAAM,CACvBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,iBAI5D,KAAK,cAAgBA,EAAgB,eACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,sBAAuB,CACvB,OAAI,KAAK,gBAAkBF,EAAgB,eAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,qBAAqBE,EAAM,CACvBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,iBAI5D,KAAK,cAAgBA,EAAgB,eACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,sBAAuB,CACvB,OAAI,KAAK,gBAAkBF,EAAgB,eAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,gBAAgBE,EAAM,CAClBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,YAI5D,KAAK,cAAgBA,EAAgB,UACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,iBAAkB,CAClB,OAAI,KAAK,gBAAkBF,EAAgB,UAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,gBAAgBE,EAAM,CAClBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,YAI5D,KAAK,cAAgBA,EAAgB,UACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,iBAAkB,CAClB,OAAI,KAAK,gBAAkBF,EAAgB,UAChC,KAEJ,KAAK,uBACf,CAMD,IAAI,gBAAgBE,EAAM,CAClBA,IAAS,MAAQ,KAAK,gBAAkBF,EAAgB,YAI5D,KAAK,cAAgBA,EAAgB,UACrC,KAAK,wBAA0BE,EAClC,CAMD,IAAI,iBAAkB,CAClB,OAAI,KAAK,gBAAkBF,EAAgB,UAChC,KAEJ,KAAK,uBACf,CAID,aAAc,CACV,GAAI,KAAK,eAAiB,GAAK,KAAK,eAAiB,GAAK,KAAK,cAAgB,EAC3E,OAGJ,KAAK,cAAc,OAAO,CAAC,EAC3B,KAAK,gBAAgB,OAAO,CAAC,EAC7B,KAAK,WAAW,OAAO,CAAC,EAExB,KAAK,cAAa,EACd,KAAK,OAAO,SAAQ,EAAG,uBAEvB,KAAK,cAAc,GAAK,IAG5B,MAAMG,EAAwBC,EAAO,OACrC,KAAK,OAAO,cAAe,EAAC,YAAYD,CAAqB,EAC7D,MAAME,EAAuB1B,EAAQ,OACrCA,EAAQ,qBAAqB,KAAK,cAAewB,EAAuBE,CAAoB,EAE5F,KAAK,OAAO,eAAe,GAAK,KAAK,gBAAgB,EAAI,IACzD,KAAK,OAAO,eAAe,GAAK,KAAK,gBAAgB,EAAI,IACzD,KAAK,OAAO,gBAAgB,WAAWA,CAAoB,EAC3D,KAAK,OAAO,gBAAgB,WAAW,KAAK,UAAU,EAEtD,MAAM,YAAW,CACpB,CAKD,eAAgB,CAEZ,KAAK,sBAAsB,KAAK,aAAc,KAAK,cAAe,KAAK,uBAAuB,EAC9F,KAAK,sBAAsB,KAAK,aAAc,KAAK,cAAe,KAAK,uBAAuB,EAC9F,KAAK,sBAAsB,KAAK,aAAc,KAAK,cAAe,KAAK,uBAAuB,CACjG,CAOD,sBAEAC,EAEAC,EAEAC,EAAY,CAKR,GAJIF,IAAU,GAIVC,IAAmB,MAAQC,IAAe,KAE1C,OAEJ,IAAIC,EAAS,KACb,OAAQF,EAAc,CAClB,KAAKP,EAAgB,aACjBS,EAAS,KAAK,cACd,MACJ,KAAKT,EAAgB,eACjBS,EAAS,KAAK,gBACd,MACJ,KAAKT,EAAgB,UACjBS,EAAS,KAAK,WACd,KACP,CACD,OAAQD,EAAU,CACd,IAAK,GACDC,EAAO,IAAIH,EAAO,EAAG,CAAC,EACtB,MACJ,IAAK,GACDG,EAAO,IAAI,EAAGH,EAAO,CAAC,EACtB,MACJ,IAAK,GACDG,EAAO,IAAI,EAAG,EAAGH,CAAK,EACtB,KACP,CACJ,CACL,CACAzB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,qBAAsB,IAAI,EAClEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,qBAAsB,IAAI,EAClEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,qBAAsB,IAAI,EAClEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,uBAAwB,IAAI,EACpEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,uBAAwB,IAAI,EACpEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,uBAAwB,IAAI,EACpEpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,kBAAmB,IAAI,EAC/DpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,kBAAmB,IAAI,EAC/DpB,EAAW,CACPC,EAAW,CACf,EAAGmB,EAA0B,UAAW,kBAAmB,IAAI,EAC/DlB,GAAiB,0BAA+BkB,ECtWzC,MAAMS,EAAqB,CAM9B,YAIAC,EAAa,GAAO,CAChB,KAAK,WAAaA,EAKlB,KAAK,wBAA0B,IAK/B,KAAK,qBAAuB,IAI5B,KAAK,mBAAqB,GAC1B,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,KAAK,gBAAkB,IAAI,MAC3B,KAAK,UAAYxC,EAAM,UAC1B,CAKD,cAAcD,EAAkB,CAE5BA,EAAmBC,EAAM,iCAAiC,SAAS,EACnE,IAAIyC,EAAmB,KA4DvB,GA3DI,KAAK,gBAAkB,SACvB,KAAK,aAAe,IAAM,CACtB,KAAK,SAAW,KAChB,KAAK,SAAW,IAChC,EACY,KAAK,cAAiBvB,GAAM,CACxB,MAAMhB,EAAMgB,EAAE,MACRwB,EAAexC,EAAI,cAAgB,SAAY,KAAK,WAAa,OAAOA,EAAI,YAAgB,IAClG,GAAI,GAAC,KAAK,YAAcwC,IAGxB,GAAIxB,EAAE,OAASE,EAAkB,YAAa,CAK1C,GAJKrB,GACDG,EAAI,eAAc,EAEtB,KAAK,gBAAgB,KAAKA,EAAI,SAAS,EACnC,KAAK,gBAAgB,SAAW,EAChC,OAEJuC,EAAmB,CACf,EAAGvC,EAAI,QACP,EAAGA,EAAI,OAC/B,CACiB,SACQgB,EAAE,OAASE,EAAkB,UAAW,CACxCrB,GACDG,EAAI,eAAc,EAEtB,MAAME,EAAQ,KAAK,gBAAgB,QAAQF,EAAI,SAAS,EAKxD,GAJIE,IAAU,KAGd,KAAK,gBAAgB,OAAOA,EAAO,CAAC,EAChCA,GAAS,GACT,OAEJqC,EAAmB,KACnB,KAAK,SAAW,KAChB,KAAK,SAAW,IACnB,SACQvB,EAAE,OAASE,EAAkB,YAAa,CAQ/C,GAPKrB,GACDG,EAAI,eAAc,EAElB,CAACuC,GAGS,KAAK,gBAAgB,QAAQvC,EAAI,SAAS,GAC3C,EACT,OAEJ,KAAK,SAAWA,EAAI,QAAUuC,EAAiB,EAC/C,KAAK,SAAW,EAAEvC,EAAI,QAAUuC,EAAiB,EACpD,EACjB,GAEQ,KAAK,UAAY,KAAK,OACjB,SAAU,EACV,cAAc,0BAA0B,KAAK,cAAerB,EAAkB,YAAcA,EAAkB,UAAYA,EAAkB,WAAW,EACxJ,KAAK,aAAc,CAEnB,MAAMH,EADS,KAAK,OAAO,UAAS,EACb,kBACvBA,GAAWA,EAAQ,iBAAiB,OAAQ,KAAK,YAAY,CAChE,CACJ,CAID,eAAgB,CACZ,GAAI,KAAK,cAAe,CAKpB,GAJI,KAAK,YACL,KAAK,OAAO,SAAU,EAAC,cAAc,6BAA6B,KAAK,SAAS,EAChF,KAAK,UAAY,MAEjB,KAAK,aAAc,CAEnB,MAAMA,EADS,KAAK,OAAO,UAAS,EACb,kBACvBA,GAAWA,EAAQ,oBAAoB,OAAQ,KAAK,YAAY,EAChE,KAAK,aAAe,IACvB,CACD,KAAK,gBAAgB,OAAS,EAC9B,KAAK,SAAW,KAChB,KAAK,SAAW,IACnB,CACJ,CAKD,aAAc,CAIV,GAHI,KAAK,WAAa,MAAQ,KAAK,WAAa,MAG5C,KAAK,WAAa,GAAK,KAAK,WAAa,EACzC,OAEJ,MAAMZ,EAAS,KAAK,OACdI,EAAuBJ,EAAO,iCAGpC,GAFAA,EAAO,eAAe,EAAKI,EAAuB,KAAK,SAAY,KAAK,wBAClD,KAAK,oBAAsB,KAAK,gBAAgB,SAAW,GAAO,CAAC,KAAK,oBAAsB,KAAK,gBAAgB,OAAS,EAE9IJ,EAAO,eAAe,EAAI,CAAC,KAAK,SAAW,KAAK,4BAE/C,CACD,MAAME,EAAQF,EAAO,2BACfsC,EAAY,IAAInC,EAAQ,EAAG,EAAG,KAAK,uBAAyB,EAAKD,EAAQ,KAAK,SAAY,KAAK,qBAAuB,CAAC,EAC7H0B,EAAO,0BAA0B5B,EAAO,SAAS,EAAGA,EAAO,SAAS,EAAG,EAAGA,EAAO,qBAAqB,EACtGA,EAAO,gBAAgB,WAAWG,EAAQ,qBAAqBmC,EAAWtC,EAAO,qBAAqB,CAAC,CAC1G,CACJ,CAKD,cAAe,CACX,MAAO,sBACV,CAKD,eAAgB,CACZ,MAAO,OACV,CACL,CACAK,EAAW,CACPC,EAAW,CACf,EAAG4B,GAAqB,UAAW,0BAA2B,MAAM,EACpE7B,EAAW,CACPC,EAAW,CACf,EAAG4B,GAAqB,UAAW,uBAAwB,MAAM,EACjE3B,GAAiB,qBAA0B2B,GC1KpC,MAAMK,WAAgCC,EAAoB,CAK7D,YAAYxC,EAAQ,CAChB,MAAMA,CAAM,EAIZ,KAAK,YAAc,KAInB,KAAK,iBAAmB,IAC3B,CAKD,aAAc,CACV,YAAK,IAAI,IAAIP,CAA6B,EACnC,IACV,CAMD,SAASgB,EAAe,GAAM,CAC1B,OAAK,KAAK,cACN,KAAK,YAAc,IAAID,GAAqBC,CAAY,EACxD,KAAK,IAAI,KAAK,WAAW,GAEtB,IACV,CAKD,aAAc,CACV,OAAI,KAAK,aACL,KAAK,OAAO,KAAK,WAAW,EAEzB,IACV,CAKD,eAAgB,CACZ,OAAK,KAAK,mBACN,KAAK,iBAAmB,IAAIgB,EAC5B,KAAK,IAAI,KAAK,gBAAgB,GAE3B,IACV,CAKD,kBAAmB,CACf,OAAI,KAAK,kBACL,KAAK,OAAO,KAAK,gBAAgB,EAE9B,IACV,CAKD,UAAW,CACP,YAAK,IAAI,IAAIS,EAAsB,EAC5B,IACV,CAID,OAAQ,CACJ,MAAM,MAAK,EACX,KAAK,YAAc,IACtB,CACL,CC/EO,MAAMO,UAAmBC,EAAa,CAKzC,IAAI,oBAAqB,CACrB,MAAMC,EAAQ,KAAK,OAAO,SAAS,MACnC,OAAIA,EACOA,EAAM,mBAEV,CACV,CAKD,IAAI,mBAAmBb,EAAO,CAC1B,MAAMa,EAAQ,KAAK,OAAO,SAAS,MAC/BA,IACAA,EAAM,mBAAqBb,EAElC,CAID,IAAI,QAAS,CACT,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,OAEb,EACV,CACD,IAAI,OAAOd,EAAO,CACd,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,OAASd,EAEzB,CAID,IAAI,YAAa,CACb,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,WAEb,EACV,CACD,IAAI,WAAWd,EAAO,CAClB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,WAAad,EAE7B,CAID,IAAI,UAAW,CACX,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,SAEb,EACV,CACD,IAAI,SAASd,EAAO,CAChB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,SAAWd,EAE3B,CAID,IAAI,cAAe,CACf,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,aAEb,EACV,CACD,IAAI,aAAad,EAAO,CACpB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,aAAed,EAE/B,CAID,IAAI,UAAW,CACX,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,SAEb,EACV,CACD,IAAI,SAASd,EAAO,CAChB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,SAAWd,EAE3B,CAID,IAAI,WAAY,CACZ,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,UAEb,EACV,CACD,IAAI,UAAUd,EAAO,CACjB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,UAAYd,EAE5B,CAID,IAAI,gBAAiB,CACjB,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,eAEb,EACV,CACD,IAAI,eAAed,EAAO,CACtB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,eAAiBd,EAEjC,CAID,IAAI,iBAAkB,CAClB,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,gBAEb,EACV,CACD,IAAI,gBAAgBd,EAAO,CACvB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,gBAAkBd,EAElC,CAID,IAAI,cAAe,CACf,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,aAEb,EACV,CACD,IAAI,aAAad,EAAO,CACpB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,aAAed,EAE/B,CAID,IAAI,gBAAiB,CACjB,MAAMc,EAAW,KAAK,OAAO,SAAS,SACtC,OAAIA,EACOA,EAAS,eAEb,EACV,CACD,IAAI,eAAed,EAAO,CACtB,MAAMc,EAAW,KAAK,OAAO,SAAS,SAClCA,IACAA,EAAS,eAAiBd,EAEjC,CAWD,YAAYe,EAAMC,EAAUC,EAAOC,EAA+B,GAAM,CACpE,MAAMH,EAAMC,EAAUC,EAAOC,CAA4B,EAMzD,KAAK,UAAY,IAAI7C,EAAQ,GAAK,EAAG,EAAG,EAMxC,KAAK,gBAAkB,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAI1C,KAAK,gBAAkB,GAIvB,KAAK,aAAe,GACpB,KAAK,oBAAsB,GAC3B,KAAK,aAAeA,EAAQ,OAC5B,KAAK,cAAgBA,EAAQ,OAC7B,KAAK,aAAeA,EAAQ,OAE5B,KAAK,eAAiB,GACtB,KAAK,2BAA6B,CAAC8C,EAAaC,EAAaC,EAAe,OAAS,CACjF,KAAK,aAAa,SAASD,CAAW,EACtC,KAAK,aAAa,cAAc,KAAK,aAAc,KAAK,aAAa,EACjE,KAAK,cAAc,OAAM,EAAKE,GAAe,oBAC7C,KAAK,SAAS,SAAS,KAAK,cAAe,KAAK,uBAAuB,EAClE,KAAK,WAIN,KAAK,iBAAmB,GAHxB,KAAK,SAAS,SAAS,KAAK,uBAAuB,EAMnD,KAAK,WAAaD,GAClB,KAAK,UAAUA,CAAY,EAG/C,EACQ,KAAK,OAAS,IAAIZ,GAAwB,IAAI,EAC9C,KAAK,OAAO,YAAa,EAAC,SAAQ,CACrC,CAMD,cAAcc,EAAS3D,EAAkB,CAErCA,EAAmBC,EAAM,iCAAiC,SAAS,EACnE,KAAK,OAAO,cAAcD,CAAgB,CAC7C,CAID,eAAgB,CACZ,KAAK,OAAO,gBACZ,KAAK,gBAAkB,IAAIS,EAAQ,EAAG,EAAG,CAAC,EAC1C,KAAK,eAAiB,IAAImD,GAAQ,EAAG,CAAC,CACzC,CAID,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CACD,IAAI,cAAcC,EAAM,CACpB,KAAK,eAAkB,MAAMA,CAAI,EAAW,GAAPA,CACxC,CAID,kBAAkBC,EAAc,CAC5B,IAAIC,EACA,KAAK,OACLA,EAAiBtD,EAAQ,qBAAqB,KAAK,SAAU,KAAK,OAAO,eAAc,CAAE,EAGzFsD,EAAiB,KAAK,SAE1BA,EAAe,wBAAwB,EAAG,KAAK,UAAU,EAAG,EAAG,KAAK,YAAY,EAChF,KAAK,aAAa,WAAW,KAAK,eAAe,EACjD,MAAMC,EAAc,KAAK,SAAQ,EAAG,qBAC/B,KAAK,YACN,KAAK,UAAYA,EAAY,kBAEjC,KAAK,UAAU,QAAU,KAAK,UAC9B,KAAK,UAAU,cAAgB,KAAK,eAEpC,IAAIC,EAAqBH,EAErB,KAAK,eAELG,EAAqBH,EAAa,IAAI,KAAK,SAAQ,EAAG,OAAO,GAEjEE,EAAY,eAAe,KAAK,aAAcC,EAAoB,KAAK,UAAW,EAAG,KAAM,KAAK,2BAA4B,KAAK,QAAQ,CAC5I,CAED,cAAe,CACN,KAAK,kBACN,KAAK,gBAAkBxD,EAAQ,OAC/B,KAAK,sBAAwBA,EAAQ,QAEzC,KAAK,OAAO,cACZ,MAAM,aAAY,CACrB,CAID,IAAI,mBAAmB2B,EAAO,CAC1B,KAAK,oBAAsBA,CAC9B,CAID,IAAI,oBAAqB,CACrB,OAAO,KAAK,mBACf,CAED,sBAAuB,CACnB,OAAO,KAAK,qBAAuB,KAAK,IAAI,KAAK,gBAAgB,CAAC,EAAI,GAAK,KAAK,IAAI,KAAK,gBAAgB,CAAC,EAAI,GAAK,KAAK,IAAI,KAAK,gBAAgB,CAAC,EAAI,CACzJ,CAED,iBAAkB,CACV,KAAK,iBAAmB,KAAK,SAAQ,EAAG,kBACxC,KAAK,kBAAkB,KAAK,eAAe,EAG3C,MAAM,gBAAe,CAE5B,CAID,SAAU,CACN,KAAK,OAAO,QACZ,MAAM,QAAO,CAChB,CAKD,cAAe,CACX,MAAO,YACV,CACL,CACAzB,EAAW,CACPuD,GAAoB,CACxB,EAAGnB,EAAW,UAAW,YAAa,MAAM,EAC5CpC,EAAW,CACPuD,GAAoB,CACxB,EAAGnB,EAAW,UAAW,kBAAmB,MAAM,EAClDpC,EAAW,CACPC,EAAW,CACf,EAAGmC,EAAW,UAAW,kBAAmB,MAAM,EAClDpC,EAAW,CACPC,EAAW,CACf,EAAGmC,EAAW,UAAW,eAAgB,MAAM,EAE/CoB,GAAc,qBAAsBpB,CAAU,ECpWvC,MAAMqB,EAAS,CAKlB,IAAI,+BAAgC,CAChC,OAAO,KAAK,8BACf,CACD,IAAI,8BAA8BhC,EAAO,CACrC,KAAK,+BAAiCA,EACtC,KAAK,aAAY,CACpB,CAID,IAAI,6BAA8B,CAC9B,OAAK,KAAK,6BAGH,KAAK,6BAFD,KAAK,OAAO,2BAG1B,CACD,IAAI,4BAA4BA,EAAO,CACnC,KAAK,6BAA+BA,CACvC,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,+BAAiC,KAAK,sBACrD,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAOD,YAEAe,EAEAkB,EAAIhB,EAAO,CACP,KAAK,KAAOF,EACZ,KAAK,GAAKkB,EAIV,KAAK,MAAQ,GAIb,KAAK,sBAAwB,GAC7B,KAAK,SAAW,GAChB,KAAK,sBAAwB,IAAI,MACjC,KAAK,UAAYnC,EAAO,WACxB,KAAK,iBAAmB,GACxB,KAAK,QAAU,GACf,KAAK,0BAA4B,GACjC,KAAK,uBAAyB,GAC9B,KAAK,UAAY,EAEjB,KAAK,iCAAmC,EAExC,KAAK,gBAAkB,KAEvB,KAAK,iBAAmB,KAIxB,KAAK,eAAiB,GACtB,KAAK,+BAAiC,GACtC,KAAK,6BAA+B,KAKpC,KAAK,0BAA4B,IAAIlB,EACrC,KAAK,MAAQ,GACb,KAAK,OAASqC,GAASiB,GAAY,iBACnC,KAAK,UAAY,KAAK,OAAO,YAAW,EACxC,KAAK,OAAO,YAAY,IAAI,EAE5B,KAAK,SAAW,GAChB,MAAMC,EAAa,KAAK,OAAO,UAAW,EAAC,QAAO,EAClD,KAAK,uBAAyBA,EAAW,cAAgBA,EAAW,2BAA6B,CACpG,CAKD,cAAe,CACX,MAAO,UACV,CAKD,aAAc,CACV,OAAO,KAAK,MAAM,OAAQC,GAAM,CAACA,EAAE,UAAS,CAAE,CACjD,CAOD,qBAAqBC,EAAM,CACvB,GAAI,KAAK,sBAAuB,CAC5B,GAAI,CAACA,EACD,MAAM,IAAI,MAAM,0FAA0F,EAE9G,OAAKA,EAAK,yBACN,KAAK,QAAQ,EAAI,EAEdA,EAAK,uBACf,CACD,OAAI,CAAC,KAAK,oBAAsB,KAAK,WACjC,KAAK,QAAQ,CAAC,KAAK,kBAAkB,EAElC,KAAK,kBACf,CAMD,0BAA0BA,EAAM,CAC5B,OAAI,KAAK,uBAAyBA,EAAK,wBAC5BA,EAAK,wBAET,KAAK,uBACf,CAKD,UAAW,CACP,OAAO,KAAK,MACf,CAOD,SAASC,EAAa,CAClB,IAAIC,EAAM,SAAS,KAAK,IAAI,aAAa,KAAK,MAAM,MAAM,GAE1D,GADAA,GAAO,uBAAuB,KAAK,QAAU,OAAO,KAAK,KAAK,OAAO,EAAE,OAAS,MAAM,GAClFD,EAAa,CACbC,GAAO,cACP,IAAIC,EAAQ,GACZ,UAAWzB,KAAQ,KAAK,QAChByB,IACAD,GAAO,KACPC,EAAQ,IAEZD,GAAOxB,EAEXwB,GAAO,GACV,CACD,OAAOA,CACV,CAMD,mBAAmBxB,EAAM,CACrB,QAAS0B,EAAY,EAAGC,EAAQ,KAAK,MAAM,OAAQD,EAAYC,EAAOD,IAClE,GAAI,KAAK,MAAMA,CAAS,EAAE,OAAS1B,EAC/B,OAAO0B,EAGf,MAAO,EACV,CAOD,qBAAqB1B,EAAM4B,EAAMC,EAAI,CAEjC,GAAI,CAAC,KAAK,QAAQ7B,CAAI,EAAG,CACrB,KAAK,QAAQA,CAAI,EAAI,IAAI8B,GAAe9B,EAAM4B,EAAMC,CAAE,EACtD,QAASE,EAAI,EAAGC,EAAS,KAAK,MAAM,OAAQD,EAAIC,EAAQD,IAChD,KAAK,MAAMA,CAAC,EAAE,WAAW,CAAC,GAC1B,KAAK,MAAMA,CAAC,EAAE,WAAW,CAAC,EAAE,YAAY/B,EAAM4B,EAAMC,CAAE,CAGjE,CACJ,CAMD,qBAAqB7B,EAAMiC,EAAe,GAAM,CAC5C,QAASF,EAAI,EAAGC,EAAS,KAAK,MAAM,OAAQD,EAAIC,EAAQD,IAChD,KAAK,MAAMA,CAAC,EAAE,WAAW,CAAC,GAC1B,KAAK,MAAMA,CAAC,EAAE,WAAW,CAAC,EAAE,YAAY/B,EAAMiC,CAAY,EAGlE,KAAK,QAAQjC,CAAI,EAAI,IACxB,CAMD,kBAAkBA,EAAM,CACpB,OAAO,KAAK,QAAQA,CAAI,GAAK,IAChC,CAKD,oBAAqB,CACjB,MAAMkC,EAAkB,GACxB,IAAIlC,EACJ,IAAKA,KAAQ,KAAK,QACdkC,EAAgB,KAAK,KAAK,QAAQlC,CAAI,CAAC,EAE3C,OAAOkC,CACV,CASD,mBAAmBC,EAAQnC,EAAMoC,EAAoB,GAAO,CACxD,GAAI,KAAK,QAAQpC,CAAI,GAAK,CAACmC,EAAO,kBAAkBnC,CAAI,EACpD,MAAO,GAEX,IAAIwB,EAAM,GACV,MAAMa,EAAc,KAAK,0BAAyB,EAAK,EAEjDC,EAAW,GACXC,EAAcJ,EAAO,MAC3B,IAAIH,EACAD,EACJ,IAAKA,EAAI,EAAGC,EAASO,EAAY,OAAQR,EAAIC,EAAQD,IACjDO,EAASC,EAAYR,CAAC,EAAE,IAAI,EAAIQ,EAAYR,CAAC,EAE7C,KAAK,MAAM,SAAWQ,EAAY,SAClCC,EAAO,KAAK,oCAAoC,KAAK,MAAM,MAAM,2BAA2BD,EAAY,MAAM,EAAE,EAChHf,EAAM,IAEV,MAAMiB,EAAsBL,GAAqB,KAAK,kBAAoBD,EAAO,iBAAmB,KAAK,iBAAiB,OAAOA,EAAO,gBAAgB,EAAI,KAC5J,IAAKJ,EAAI,EAAGC,EAAS,KAAK,MAAM,OAAQD,EAAIC,EAAQD,IAAK,CACrD,MAAMW,EAAW,KAAK,MAAMX,CAAC,EAAE,KACzBY,EAAaL,EAASI,CAAQ,EAChCC,EACAnB,EAAMA,GAAO,KAAK,MAAMO,CAAC,EAAE,mBAAmBY,EAAY3C,EAAMqC,EAAaD,EAAmBK,CAAmB,GAGnHD,EAAO,KAAK,yDAA2DE,CAAQ,EAC/ElB,EAAM,GAEb,CAED,MAAMoB,EAAQT,EAAO,kBAAkBnC,CAAI,EAC3C,OAAI4C,IACA,KAAK,QAAQ5C,CAAI,EAAI,IAAI8B,GAAe9B,EAAM4C,EAAM,KAAOP,EAAaO,EAAM,GAAKP,CAAW,GAE3Fb,CACV,CAID,cAAe,CACX,UAAWqB,KAAQ,KAAK,MAChBA,EAAK,SAAW,IAChBA,EAAK,aAAY,CAG5B,CACD,2BAA4B,CACxB,IAAIrB,EAAM,EACV,QAASO,EAAI,EAAGC,EAAS,KAAK,MAAM,OAAQD,EAAIC,EAAQD,IACpD,GAAI,KAAK,MAAMA,CAAC,EAAE,WAAW,CAAC,EAAG,CAC7B,MAAMe,EAAU,KAAK,MAAMf,CAAC,EAAE,WAAW,CAAC,EAAE,kBACxCP,EAAMsB,IACNtB,EAAMsB,EAEb,CAEL,OAAOtB,CACV,CASD,eAAexB,EAAM+C,EAAMC,EAAYC,EAAgB,CACnD,MAAML,EAAQ,KAAK,kBAAkB5C,CAAI,EACzC,OAAK4C,EAGE,KAAK,OAAO,eAAe,KAAMA,EAAM,KAAMA,EAAM,GAAIG,EAAMC,EAAYC,CAAc,EAFnF,IAGd,CAQD,OAAO,sBAAsBC,EAAUC,EAAiB,EAAGP,EAAO,CAC9D,MAAMQ,EAAaF,EAAS,kBAAkBN,CAAK,EAEnD,GAAI,CAACQ,EACD,OAAO,KAGX,MAAMC,EAAmBH,EAAS,OAAO,0BAA0BA,CAAQ,EAC3E,IAAII,EAAkB,KACtB,QAASpG,EAAQ,EAAGA,EAAQmG,EAAiB,OAAQnG,IAAS,CAC1D,MAAMqG,EAAkBF,EAAiBnG,CAAK,EAC9C,GAAIqG,EAAgB,YAAcH,GAAY,MAAQG,EAAgB,UAAYH,GAAY,GAAI,CAC9FE,EAAkBC,EAClB,KACH,CACJ,CAED,MAAMC,EAAcN,EAAS,iBAC7B,QAAShG,EAAQ,EAAGA,EAAQsG,EAAY,OAAQtG,IAAS,CAErD,MAAMuG,EADaD,EAAYtG,CAAK,EACN,WAC9B,GAAKuG,EAGL,QAASC,EAAY,EAAGA,EAAYD,EAAW,OAAQC,IACnDC,GAAU,sBAAsBF,EAAWC,CAAS,EAAGP,EAAgBP,CAAK,CAEnF,CAED,OAAIU,IACAA,EAAgB,WAAa,IAE1BJ,CACV,CAED,cAAe,CACX,KAAK,SAAW,GAChB,KAAK,0BAA4B,EACpC,CAID,4BAA4B5B,EAAM,CAC9B,KAAK,sBAAsB,KAAKA,CAAI,CACvC,CAID,8BAA8BA,EAAM,CAChC,MAAMpE,EAAQ,KAAK,sBAAsB,QAAQoE,CAAI,EACjDpE,EAAQ,IACR,KAAK,sBAAsB,OAAOA,EAAO,CAAC,CAEjD,CACD,0BAA0B0G,EAAcC,EAAmB,CACvD,KAAK,0BAA0B,gBAAgB,IAAI,EACnD,QAAS3G,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAAS,CACpD,MAAM2F,EAAO,KAAK,MAAM3F,CAAK,EAC7B2F,EAAK,iBACL,MAAMiB,EAAajB,EAAK,YAYxB,GAXIiB,EACAjB,EAAK,eAAgB,EAAC,cAAciB,EAAW,iBAAkBjB,EAAK,eAAc,CAAE,EAGlFgB,EACAhB,EAAK,eAAc,EAAG,cAAcgB,EAAmBhB,EAAK,eAAc,CAAE,EAG5EA,EAAK,eAAc,EAAG,SAASA,EAAK,eAAgB,GAGxDA,EAAK,SAAW,GAAI,CACpB,MAAMkB,EAAclB,EAAK,SAAW,KAAO3F,EAAQ2F,EAAK,OACxDA,EAAK,+BAA+B,gBAAgBA,EAAK,eAAc,EAAIe,EAAcG,EAAc,EAAE,CAC5G,CACJ,CACD,KAAK,UAAU,YAAYH,EAAc,KAAK,MAAM,OAAS,EAAE,CAClE,CAKD,QAAQI,EAAmB,GAAO,CAC9B,GAAI,CAACA,EAAkB,CACnB,MAAMC,EAAkB,KAAK,SAAU,EAAC,YAAW,EACnD,GAAI,KAAK,mBAAqBA,EAC1B,OAEJ,KAAK,iBAAmBA,CAC3B,CAED,GAAI,KAAK,iCAAmC,GACxC,UAAWpB,KAAQ,KAAK,MACpB,GAAIA,EAAK,qBAAsB,CAC3B,MAAMqB,EAAOrB,EAAK,qBAClBA,EAAK,SAAWqB,EAAK,SACjBA,EAAK,mBACLrB,EAAK,mBAAqBqB,EAAK,mBAG/BrB,EAAK,SAAWqB,EAAK,SAEzBrB,EAAK,QAAUqB,EAAK,OACvB,EAGT,GAAI,KAAK,sBACL,UAAW5C,KAAQ,KAAK,sBAAuB,CAC3C,MAAM6C,EAAa7C,EAAK,gBACxB,IAAI8C,EAAc,KAAK,SAKvB,IAJI,CAAC9C,EAAK,yBAA2BA,EAAK,wBAAwB,SAAW,IAAM,KAAK,MAAM,OAAS,MACnGA,EAAK,wBAA0B,IAAI,aAAa,IAAM,KAAK,MAAM,OAAS,EAAE,EAC5E8C,EAAc,IAEd,EAACA,EAGL,IAAI,KAAK,wBAA0B9C,EAAM,CACrC,KAAK,sBAAwBA,EAE7B,UAAWuB,KAAQ,KAAK,MACfA,EAAK,cACSA,EAAK,gBACb,cAAcsB,EAAYE,EAAW,OAAO,CAAC,CAAC,EACrDxB,EAAK,4BAA4BwB,EAAW,OAAO,CAAC,CAAC,GAG7D,GAAI,KAAK,0BAA2B,CAChC,MAAMC,GAAgB,KAAK,MAAM,OAAS,GAAK,GAC3C,CAAChD,EAAK,yBAA2BA,EAAK,wBAAwB,QAAS,EAAC,QAAUgD,KAC9EhD,EAAK,yBACLA,EAAK,wBAAwB,UAEjCA,EAAK,wBAA0BiD,GAAW,kBAAkBjD,EAAK,yBAA0B,KAAK,MAAM,OAAS,GAAK,EAAG,EAAG,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAEhK,CACJ,CACD,KAAK,0BAA0BA,EAAK,wBAAyB6C,CAAU,EACnE,KAAK,2BAA6B7C,EAAK,yBACvCA,EAAK,wBAAwB,OAAOA,EAAK,uBAAuB,EAEvE,KAEA,CACD,GAAI,CAAC,KAAK,SACN,QAEA,CAAC,KAAK,oBAAsB,KAAK,mBAAmB,SAAW,IAAM,KAAK,MAAM,OAAS,MACzF,KAAK,mBAAqB,IAAI,aAAa,IAAM,KAAK,MAAM,OAAS,EAAE,EACnE,KAAK,4BACD,KAAK,yBACL,KAAK,wBAAwB,UAEjC,KAAK,wBAA0BiD,GAAW,kBAAkB,KAAK,oBAAqB,KAAK,MAAM,OAAS,GAAK,EAAG,EAAG,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,IAG5J,KAAK,0BAA0B,KAAK,mBAAoB,IAAI,EACxD,KAAK,2BAA6B,KAAK,yBACvC,KAAK,wBAAwB,OAAO,KAAK,kBAAkB,CAElE,CACD,KAAK,SAAW,EACnB,CAKD,gBAAiB,CACb,GAAI,CAAC,KAAK,cAAgB,KAAK,aAAa,SAAW,KAAK,MAAM,OAAQ,CACtE,KAAK,aAAe,GACpB,QAASrH,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAC3C,KAAK,aAAa,KAAK,KAAK,MAAMA,CAAK,CAAC,CAE/C,CACD,OAAO,KAAK,YACf,CAOD,MAAM8C,EAAMkB,EAAI,CACZ,MAAMsD,EAAS,IAAIvD,GAASjB,EAAMkB,GAAMlB,EAAM,KAAK,MAAM,EACzDwE,EAAO,sBAAwB,KAAK,sBACpC,QAAStH,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAAS,CACpD,MAAMiF,EAAS,KAAK,MAAMjF,CAAK,EAC/B,IAAI4G,EAAa,KACjB,MAAMW,EAAStC,EAAO,YACtB,GAAIsC,EAAQ,CACR,MAAMC,EAAc,KAAK,MAAM,QAAQD,CAAM,EAC7CX,EAAaU,EAAO,MAAME,CAAW,CACxC,CACD,MAAM7B,EAAO,IAAI8B,GAAKxC,EAAO,KAAMqC,EAAQV,EAAY3B,EAAO,cAAe,EAAC,MAAK,EAAIA,EAAO,cAAe,EAAC,MAAK,CAAE,EACrHU,EAAK,OAASV,EAAO,OACjBA,EAAO,sBACPU,EAAK,kBAAkBV,EAAO,oBAAoB,EAEtDyC,GAAW,SAASzC,EAAO,WAAYU,EAAK,UAAU,CACzD,CACD,GAAI,KAAK,QAAS,CACd2B,EAAO,QAAU,GACjB,UAAWK,KAAa,KAAK,QAAS,CAClC,MAAMjC,EAAQ,KAAK,QAAQiC,CAAS,EAChCjC,IACA4B,EAAO,QAAQK,CAAS,EAAIjC,EAAM,MAAK,EAE9C,CACJ,CACD,YAAK,SAAW,GAChB4B,EAAO,QAAQ,EAAI,EACZA,CACV,CAMD,eAAeM,EAAgB,IAAM,CACjC,KAAK,MAAM,QAASjC,GAAS,CACzBA,EAAK,WAAW,QAASkC,GAAc,CACnCA,EAAU,eAAiB,GAC3BA,EAAU,cAAgBD,CAC1C,CAAa,CACb,CAAS,CACJ,CAID,SAAU,CAMN,GALA,KAAK,sBAAsB,OAAS,EAEpC,KAAK,SAAQ,EAAG,cAAc,IAAI,EAElC,KAAK,SAAQ,EAAG,eAAe,IAAI,EAC/B,KAAK,iBAAkB,CACvB,MAAM5H,EAAQ,KAAK,iBAAiB,UAAU,QAAQ,IAAI,EACtDA,EAAQ,IACR,KAAK,iBAAiB,UAAU,OAAOA,EAAO,CAAC,EAEnD,KAAK,iBAAmB,IAC3B,CACG,KAAK,0BACL,KAAK,wBAAwB,UAC7B,KAAK,wBAA0B,KAEtC,CAKD,WAAY,CACR,MAAM8H,EAAsB,GAC5BA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,GAAK,KAAK,GAC1B,KAAK,mBACLA,EAAoB,iBAAmB,KAAK,iBAAiB,QAAO,GAExEA,EAAoB,MAAQ,GAC5BA,EAAoB,sBAAwB,KAAK,sBACjD,QAAS9H,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAAS,CACpD,MAAM2F,EAAO,KAAK,MAAM3F,CAAK,EACvBuH,EAAS5B,EAAK,YACdoC,EAAiB,CACnB,gBAAiBR,EAAS,KAAK,MAAM,QAAQA,CAAM,EAAI,GACvD,MAAO5B,EAAK,SAAU,EACtB,KAAMA,EAAK,KACX,GAAIA,EAAK,GACT,OAAQA,EAAK,cAAe,EAAC,QAAS,EACtC,KAAMA,EAAK,cAAe,EAAC,QAAS,EACpC,sBAAuBA,EAAK,iBAAgB,GAAI,EAChE,EACYmC,EAAoB,MAAM,KAAKC,CAAc,EACzCpC,EAAK,SACLoC,EAAe,OAASpC,EAAK,QAE7BA,EAAK,WACLoC,EAAe,SAAWpC,EAAK,UAE/BA,EAAK,YAAcA,EAAK,WAAW,OAAS,IAC5CoC,EAAe,UAAYpC,EAAK,WAAW,CAAC,EAAE,aAElDmC,EAAoB,OAAS,GAC7B,UAAWhF,KAAQ,KAAK,QAAS,CAC7B,MAAMmC,EAAS,KAAK,QAAQnC,CAAI,EAChC,GAAI,CAACmC,EACD,SAEJ,MAAMS,EAAQ,GACdA,EAAM,KAAO5C,EACb4C,EAAM,KAAOT,EAAO,KACpBS,EAAM,GAAKT,EAAO,GAClB6C,EAAoB,OAAO,KAAKpC,CAAK,CACxC,CACJ,CACD,OAAOoC,CACV,CAOD,OAAO,MAAME,EAAgBhF,EAAO,CAChC,MAAMgD,EAAW,IAAIjC,GAASiE,EAAe,KAAMA,EAAe,GAAIhF,CAAK,EACvEgF,EAAe,mBACfhC,EAAS,iBAAmB5F,EAAQ,UAAU4H,EAAe,gBAAgB,GAEjFhC,EAAS,sBAAwBgC,EAAe,sBAChD,IAAIhI,EACJ,IAAKA,EAAQ,EAAGA,EAAQgI,EAAe,MAAM,OAAQhI,IAAS,CAC1D,MAAMiI,EAAaD,EAAe,MAAMhI,CAAK,EACvCkI,EAAkBF,EAAe,MAAMhI,CAAK,EAAE,MACpD,IAAI4G,EAAa,KACbqB,EAAW,gBAAkB,KAC7BrB,EAAaZ,EAAS,MAAMiC,EAAW,eAAe,GAE1D,MAAME,EAAOF,EAAW,KAAOpG,EAAO,UAAUoG,EAAW,IAAI,EAAI,KAC7DtC,EAAO,IAAI8B,GAAKQ,EAAW,KAAMjC,EAAUY,EAAY/E,EAAO,UAAUoG,EAAW,MAAM,EAAGE,EAAM,KAAMD,CAAe,EACzHD,EAAW,KAAO,QAAaA,EAAW,KAAO,OACjDtC,EAAK,GAAKsC,EAAW,IAErBA,EAAW,SACXtC,EAAK,OAASsC,EAAW,QAEzBA,EAAW,WACXtC,EAAK,SAAWsC,EAAW,UAE3BA,EAAW,WACXtC,EAAK,WAAW,KAAKc,GAAU,MAAMwB,EAAW,SAAS,CAAC,EAE1DA,EAAW,wBAA0B,QAAaA,EAAW,wBAA0B,OACvFjC,EAAS,gBAAkB,GAC3BL,EAAK,wBAA0BsC,EAAW,sBAEjD,CAED,GAAID,EAAe,OACf,IAAKhI,EAAQ,EAAGA,EAAQgI,EAAe,OAAO,OAAQhI,IAAS,CAC3D,MAAMoI,EAAOJ,EAAe,OAAOhI,CAAK,EACxCgG,EAAS,qBAAqBoC,EAAK,KAAMA,EAAK,KAAMA,EAAK,EAAE,CAC9D,CAEL,OAAOpC,CACV,CAKD,wBAAwBqC,EAAc,GAAO,EACrC,KAAK,2BAA6BA,KAClC,KAAK,MAAM,CAAC,EAAE,wBAAuB,EACrC,KAAK,0BAA4B,GAExC,CAMD,0BAA0BA,EAAc,GAAO,CAC3C,KAAK,wBAAwBA,CAAW,CAC3C,CAKD,eAAgB,CACZ,IAAIpB,EAAa,KACjB,OAAI,KAAK,sBAAsB,OAAS,IACpCA,EAAa,KAAK,sBAAsB,CAAC,EAAE,cAAa,GAErDA,CACV,CAID,WAAY,CACR,MAAMqB,EAAQ,GACRC,EAAU,IAAI,MAAM,KAAK,MAAM,MAAM,EAC3C,QAASvI,EAAQ,EAAGA,EAAQ,KAAK,MAAM,OAAQA,IAC3C,KAAK,WAAWA,EAAOsI,EAAOC,CAAO,EAEzC,KAAK,MAAQD,CAChB,CACD,WAAWtI,EAAOsI,EAAOC,EAAS,CAC9B,GAAIA,EAAQvI,CAAK,EACb,OAEJuI,EAAQvI,CAAK,EAAI,GACjB,MAAM2F,EAAO,KAAK,MAAM3F,CAAK,EAC7B,GAAI,CAAC2F,EACD,OACAA,EAAK,SAAW,SAChBA,EAAK,OAAS3F,GAElB,MAAM4G,EAAajB,EAAK,YACpBiB,GACA,KAAK,WAAW,KAAK,MAAM,QAAQA,CAAU,EAAG0B,EAAOC,CAAO,EAElED,EAAM,KAAK3C,CAAI,CAClB,CAID,sBAAuB,CACnB,KAAK,MAAM,QAASxB,GAAM,CACtBA,EAAE,qBAAoB,CAClC,CAAS,CACJ,CACL,CC5tBO,MAAMqE,CAAY,CAIrB,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CACD,IAAI,UAAUC,EAAW,CACrB,GAAI,KAAK,aAAeA,EACpB,OAEJ,MAAMC,EAAW,KAAK,WACtB,KAAK,WAAaD,EACd,KAAK,mBAAmB,gBACxB,KAAK,mBAAmB,gBAAgBC,IAAa,GAAKD,IAAc,CAAC,CAEhF,CAID,IAAI,6BAA8B,CAC9B,MAAI,CAAC,KAAK,8BAAgC,KAAK,OACpC,KAAK,OAAO,4BAEhB,KAAK,4BACf,CACD,IAAI,4BAA4B1G,EAAO,CACnC,KAAK,6BAA+BA,CACvC,CAOD,YAEAe,EAAM2F,EAAY,EAAGzF,EAAQ,KAAM,CAC/B,KAAK,KAAOF,EAIZ,KAAK,WAAa,GAClB,KAAK,WAAa,KAClB,KAAK,SAAW,KAChB,KAAK,UAAY,KACjB,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,QAAU,KACf,KAAK,UAAY,EAIjB,KAAK,mBAAqB,IAAInC,EAE9B,KAAK,qBAAuB,IAAIA,EAChC,KAAK,6BAA+B,KACpC,KAAK,GAAKmC,EACV,KAAK,OAASE,GAASiB,GAAY,iBACnC,KAAK,UAAYwE,EACb,KAAK,SACL,KAAK,UAAY,KAAK,OAAO,YAAW,EAE/C,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAID,IAAI,cAAe,CACf,MAAO,CAAC,CAAC,KAAK,UACjB,CAID,IAAI,YAAa,CACb,MAAO,CAAC,CAAC,KAAK,QACjB,CAID,IAAI,aAAc,CACd,MAAO,CAAC,CAAC,KAAK,SACjB,CAID,IAAI,QAAS,CACT,MAAO,CAAC,CAAC,KAAK,IACjB,CAID,IAAI,SAAU,CACV,MAAO,CAAC,CAAC,KAAK,KACjB,CACD,IAAI,WAAY,CACZ,MAAO,CAAC,CAAC,KAAK,OACjB,CAID,IAAI,aAAc,CACd,OAAO,KAAK,WACN,KAAK,WAAW,OAAS,EACzB,KAAK,SACD,KAAK,SAAS,OAAS,EACvB,KAAK,UACD,KAAK,UAAU,OAAS,EACxB,KAAK,KACD,KAAK,KAAK,OAAS,EACnB,KAAK,MACD,KAAK,MAAM,OAAS,EACpB,KAAK,QACD,KAAK,QAAQ,OAAS,EACtB,CAC7B,CAKD,aAAaL,EAAM,CACf,MAAMO,EAAe,KAAK,aAC1B,KAAK,WAAaP,EACdO,IAAiB,KAAK,cACtB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CAKD,WAAWP,EAAM,CACb,MAAMQ,EAAa,KAAK,WACxB,KAAK,SAAWR,EACZQ,IAAe,KAAK,YACpB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,YAAa,CACT,OAAO,KAAK,QACf,CAKD,YAAYR,EAAM,CACd,MAAMS,EAAc,KAAK,YACzB,KAAK,UAAYT,EACbS,IAAgB,KAAK,aACrB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,aAAc,CACV,OAAO,KAAK,SACf,CAKD,OAAOT,EAAM,CACT,MAAMU,EAAS,KAAK,OACpB,KAAK,KAAOV,EACRU,IAAW,KAAK,QAChB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,QAAS,CACL,OAAO,KAAK,IACf,CAKD,QAAQV,EAAM,CACV,MAAMW,EAAU,KAAK,QACrB,KAAK,MAAQX,EACTW,IAAY,KAAK,SACjB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,SAAU,CACN,OAAO,KAAK,KACf,CAKD,UAAUX,EAAM,CACZ,MAAMY,EAAY,KAAK,UACvB,KAAK,QAAUZ,EACXY,IAAc,KAAK,WACnB,KAAK,qBAAqB,gBAAgB,MAAS,CAE1D,CAKD,WAAY,CACR,OAAO,KAAK,OACf,CAKD,OAAQ,CACJ,MAAMC,EAASC,GAAoB,MAAM,IAAM,IAAIV,EAAY,KAAK,KAAM,KAAK,UAAW,KAAK,MAAM,EAAG,IAAI,EAC5G,OAAAS,EAAO,WAAa,KAAK,WACzBA,EAAO,SAAW,KAAK,SACvBA,EAAO,UAAY,KAAK,UACxBA,EAAO,KAAO,KAAK,KACnBA,EAAO,MAAQ,KAAK,MACpBA,EAAO,QAAU,KAAK,QACfA,CACV,CAKD,WAAY,CACR,MAAMnB,EAAsB,GAC5B,OAAAA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,UAAY,KAAK,UACrCA,EAAoB,UAAY,MAAM,UAAU,MAAM,KAAK,KAAK,aAAY,CAAE,EAC1E,KAAK,IAAM,OACXA,EAAoB,GAAK,KAAK,IAE9B,KAAK,aACLA,EAAoB,QAAU,MAAM,UAAU,MAAM,KAAK,KAAK,WAAU,CAAE,GAE1E,KAAK,cACLA,EAAoB,SAAW,MAAM,UAAU,MAAM,KAAK,KAAK,YAAW,CAAE,GAE5E,KAAK,SACLA,EAAoB,IAAM,MAAM,UAAU,MAAM,KAAK,KAAK,OAAM,CAAE,GAElE,KAAK,UACLA,EAAoB,KAAO,MAAM,UAAU,MAAM,KAAK,KAAK,QAAO,CAAE,GAEpE,KAAK,YACLA,EAAoB,OAAS,MAAM,UAAU,MAAM,KAAK,KAAK,UAAS,CAAE,GAG5EoB,GAAoB,2BAA2B,KAAMpB,CAAmB,EACjEA,CACV,CAKD,cAAe,CACX,MAAO,aACV,CAQD,OAAO,MAAMA,EAAqB9E,EAAO,CACrC,MAAMsE,EAAS,IAAIkB,EAAYV,EAAoB,KAAMA,EAAoB,SAAS,EAqBtF,GApBAR,EAAO,aAAaQ,EAAoB,SAAS,EAC7CA,EAAoB,IAAM,OAC1BR,EAAO,GAAKQ,EAAoB,IAEhCA,EAAoB,SACpBR,EAAO,WAAWQ,EAAoB,OAAO,EAE7CA,EAAoB,UACpBR,EAAO,YAAYQ,EAAoB,QAAQ,EAE/CA,EAAoB,KACpBR,EAAO,OAAOQ,EAAoB,GAAG,EAErCA,EAAoB,MACpBR,EAAO,QAAQQ,EAAoB,IAAI,EAEvCA,EAAoB,QACpBR,EAAO,UAAUQ,EAAoB,MAAM,EAG3CA,EAAoB,WAAY,CAChC,QAASqB,EAAiB,EAAGA,EAAiBrB,EAAoB,WAAW,OAAQqB,IAAkB,CACnG,MAAMC,EAAkBtB,EAAoB,WAAWqB,CAAc,EAC/DE,EAAgBC,GAAS,mBAAmB,EAC9CD,GACA/B,EAAO,WAAW,KAAK+B,EAAc,MAAMD,CAAe,CAAC,CAElE,CACGtB,EAAoB,aAAe9E,GACnCA,EAAM,eAAesE,EAAQQ,EAAoB,gBAAiBA,EAAoB,cAAeA,EAAoB,gBAAiBA,EAAoB,kBAAoB,CAAG,CAE5L,CACD,OAAOR,CACV,CAQD,OAAO,SAASlD,EAAMtB,EAAM2F,EAAW,CAC9B3F,IACDA,EAAOsB,EAAK,MAEhB,MAAMkD,EAAS,IAAIkB,EAAY1F,EAAM2F,EAAWrE,EAAK,SAAQ,CAAE,EAC/D,OAAAkD,EAAO,aAAalD,EAAK,gBAAgBmF,EAAa,YAAY,CAAC,EAC/DnF,EAAK,sBAAsBmF,EAAa,UAAU,GAClDjC,EAAO,WAAWlD,EAAK,gBAAgBmF,EAAa,UAAU,CAAC,EAE/DnF,EAAK,sBAAsBmF,EAAa,WAAW,GACnDjC,EAAO,YAAYlD,EAAK,gBAAgBmF,EAAa,WAAW,CAAC,EAEjEnF,EAAK,sBAAsBmF,EAAa,MAAM,GAC9CjC,EAAO,OAAOlD,EAAK,gBAAgBmF,EAAa,MAAM,CAAC,EAEvDnF,EAAK,sBAAsBmF,EAAa,OAAO,GAC/CjC,EAAO,QAAQlD,EAAK,gBAAgBmF,EAAa,OAAO,CAAC,EAEzDnF,EAAK,sBAAsBmF,EAAa,SAAS,GACjDjC,EAAO,UAAUlD,EAAK,gBAAgBmF,EAAa,SAAS,CAAC,EAE1DjC,CACV,CACL,CACAhH,EAAW,CACPC,EAAW,CACf,EAAGiI,EAAY,UAAW,KAAM,MAAM,EC3W/B,MAAMgB,WAA0BC,CAAQ,CAI3C,IAAI,OAAQ,CACR,OAAO,KAAK,MACf,CAeD,YAAYrB,EAAMsB,EAAOC,EAAQC,EAEjCC,EAAQ7G,EAAO8G,EAAkB,GAAMC,EAAU,GAAOC,EAAeP,EAAQ,uBAAwBQ,EAAc,EAAGC,EAAe,CACnI,MAAM,KAAMlH,EAAO,CAAC8G,EAAiBC,CAAO,EAC5C,KAAK,OAASF,EACd,KAAK,SAAW7G,EAAM,UAAW,EAAC,wBAAwBoF,EAAMsB,EAAOC,EAAQC,EAAOC,EAAQC,EAAiBC,EAASC,EAAc,KAAMC,EAAaC,CAAa,EACtK,KAAK,OAASN,EACd,KAAK,UAAY,EACpB,CAKD,OAAOxB,EAAM,CACJ,KAAK,UAGV,KAAK,WAAU,EAAG,wBAAwB,KAAK,SAAUA,EAAM,KAAK,SAAS,OAAQ,KAAK,SAAS,QAAS,KAAM,KAAK,SAAS,IAAI,CACvI,CAcD,OAAO,kBAAkBA,EAAMsB,EAAOC,EAAQC,EAAO5G,EAAO8G,EAAkB,GAAMC,EAAU,GAAOC,EAAe,EAAGG,EAAO,EAAG,CAC7H,OAAO,IAAIX,GAAkBpB,EAAMsB,EAAOC,EAAQC,EAAO,EAAG5G,EAAO8G,EAAiBC,EAASC,EAAcG,CAAI,CAClH,CACL,CCnDO,MAAMC,CAAmB,CAI5B,IAAI,iBAAiBC,EAAO,CACpBA,EACA,KAAK,iBAGL,KAAK,gBACD,KAAK,eAAiB,IACtB,KAAK,cAAgB,EACrB,KAAK,mBAAmB,KAAK,wBAAwB,EACrD,KAAK,yBAA2B,IAG3C,CACD,IAAI,kBAAmB,CACnB,OAAO,KAAK,cAAgB,CAC/B,CAKD,YAAYrH,EAAQ,KAAM,CA4DtB,GA3DA,KAAK,SAAW,IAAI,MACpB,KAAK,iCAAmC,IAAI,MAC5C,KAAK,kCAAoC,IAAI,MAC7C,KAAK,eAAiB,IAAIsH,GAAW,EAAE,EACvC,KAAK,mBAAqB,GAC1B,KAAK,iBAAmB,GACxB,KAAK,kBAAoB,GACzB,KAAK,aAAe,GACpB,KAAK,cAAgB,GACrB,KAAK,gBAAkB,GACvB,KAAK,aAAe,EACpB,KAAK,UAAY,EACjB,KAAK,gBAAkB,IAAI,MAC3B,KAAK,yBAA2B,GAChC,KAAK,cAAgB,EACrB,KAAK,iBAAmB,GACxB,KAAK,yBAA2B,GAEhC,KAAK,qBAAuB,EAE5B,KAAK,cAAgB,EAErB,KAAK,eAAiB,EAEtB,KAAK,iBAAmB,KAIxB,KAAK,oBAAsB,GAI3B,KAAK,uBAAyB,GAI9B,KAAK,qBAAuB,GAI5B,KAAK,sBAAwB,GAI7B,KAAK,iBAAmB,GAIxB,KAAK,kBAAoB,GAIzB,KAAK,oBAAsB,GAC3B,KAAK,mBAAqB,EAC1B,KAAK,0BAA4B,GAC5BtH,IACDA,EAAQiB,GAAY,kBAExB,KAAK,OAASjB,EACV,KAAK,OAAQ,CACb,KAAK,OAAO,sBAAsB,IAAI,EACtC,KAAK,UAAY,KAAK,OAAO,YAAW,EACxC,MAAMkB,EAAa,KAAK,OAAO,UAAW,EAAC,QAAO,EAClD,KAAK,yBACDA,EAAW,kBAAoBA,EAAW,cAAgBA,EAAW,2BAA6B,GAAKA,EAAW,4BAA8B,CACvJ,CACJ,CASD,IAAI,mBAAoB,CACpB,OAAO,KAAK,kBACf,CACD,IAAI,kBAAkBnC,EAAO,CACrB,KAAK,qBAAuBA,IAGhC,KAAK,mBAAqBA,EAC1B,KAAK,iBAAmB,GACxB,KAAK,mBAAkB,EAC1B,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAID,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CAID,IAAI,mBAAoB,CACpB,OAAO,KAAK,oBAAsB,KAAK,sBAC1C,CAID,IAAI,iBAAkB,CAClB,OAAO,KAAK,kBAAoB,KAAK,oBACxC,CAID,IAAI,kBAAmB,CACnB,OAAO,KAAK,mBAAqB,KAAK,qBACzC,CAID,IAAI,aAAc,CACd,OAAO,KAAK,cAAgB,KAAK,gBACpC,CAID,IAAI,cAAe,CACf,OAAO,KAAK,eAAiB,KAAK,iBACrC,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,iBAAmB,KAAK,mBACvC,CAID,IAAI,cAAe,CACf,OAAO,KAAK,kBACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,gBACf,CAID,IAAI,aAAc,CACd,OAAO,KAAK,iBACf,CAID,IAAI,QAAS,CACT,OAAO,KAAK,YACf,CAID,IAAI,SAAU,CACV,OAAO,KAAK,aACf,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,eACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,SAAS,MACxB,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,eAAe,MAC9B,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAKD,IAAI,0BAA2B,CAC3B,OAAO,KAAK,yBACf,CACD,IAAI,yBAAyBA,EAAO,CAC5B,KAAK,4BAA8BA,IAGvC,KAAK,0BAA4BA,EACjC,KAAK,iBAAmB,GACxB,KAAK,mBAAkB,EAC1B,CAID,IAAI,0BAA2B,CAC3B,OAAQqI,EAAmB,sBACvB,KAAK,0BACL,KAAK,0BACL,CAAC,KAAK,QAAQ,UAAW,EAAC,QAAO,EAAG,yBAC3C,CAMD,gBAAgBpK,EAAO,CACnB,OAAO,KAAK,eAAe,KAAKA,CAAK,CACxC,CAMD,UAAUA,EAAO,CACb,OAAO,KAAK,SAASA,CAAK,CAC7B,CAMD,gBAAgB8C,EAAM,CAClB,UAAWyH,KAAU,KAAK,SACtB,GAAIA,EAAO,OAASzH,EAChB,OAAOyH,EAGf,OAAO,IACV,CAKD,UAAUA,EAAQ,CACd,KAAK,SAAS,KAAKA,CAAM,EACzB,KAAK,iCAAiC,KAAKA,EAAO,mBAAmB,IAAKC,GAAe,CACjF,KAAK,kBAAoBA,IACzB,KAAK,yBAA2B,IAEpC,KAAK,mBAAmBA,CAAU,CACrC,EAAC,EACF,KAAK,kCAAkC,KAAKD,EAAO,qBAAqB,IAAI,IAAM,CAC9E,KAAK,iBAAmB,GACxB,KAAK,mBAAkB,CAC1B,EAAC,EACF,KAAK,iBAAmB,GACxB,KAAK,mBAAkB,CAC1B,CAKD,aAAaA,EAAQ,CACjB,MAAMvK,EAAQ,KAAK,SAAS,QAAQuK,CAAM,EACtCvK,GAAS,IACT,KAAK,SAAS,OAAOA,EAAO,CAAC,EAC7BuK,EAAO,mBAAmB,OAAO,KAAK,iCAAiC,OAAOvK,EAAO,CAAC,EAAE,CAAC,CAAC,EAC1FuK,EAAO,qBAAqB,OAAO,KAAK,kCAAkC,OAAOvK,EAAO,CAAC,EAAE,CAAC,CAAC,EAC7F,KAAK,iBAAmB,GACxB,KAAK,mBAAkB,GAEvB,KAAK,QACL,KAAK,OAAO,cAAcuK,CAAM,CAEvC,CAID,MAAME,EAAQ,CACVA,EAAO,UAAU,yBAA0B,KAAK,qBAAsB,KAAK,cAAe,KAAK,cAAc,EAC7GA,EAAO,cAAc,4BAA6B,KAAK,0BAA0B,EACjFA,EAAO,WAAW,eAAgB,KAAK,mBAAmB,EAC1DA,EAAO,OAAO,mBAAoB,KAAK,cAAc,CACxD,CAKD,OAAQ,CACJ,MAAMC,EAAO,IAAIN,EAAmB,KAAK,MAAM,EAC/C,UAAWG,KAAU,KAAK,SACtBG,EAAK,UAAUH,EAAO,MAAO,GAEjC,OAAAG,EAAK,uBAAyB,KAAK,uBACnCA,EAAK,qBAAuB,KAAK,qBACjCA,EAAK,sBAAwB,KAAK,sBAClCA,EAAK,iBAAmB,KAAK,iBAC7BA,EAAK,kBAAoB,KAAK,kBAC9BA,EAAK,oBAAsB,KAAK,oBACzBA,CACV,CAKD,WAAY,CACR,MAAM5C,EAAsB,GAC5BA,EAAoB,GAAK,KAAK,SAC9BA,EAAoB,QAAU,GAC9B,UAAWyC,KAAU,KAAK,SACtBzC,EAAoB,QAAQ,KAAKyC,EAAO,UAAW,GAEvD,OAAOzC,CACV,CACD,mBAAmB0C,EAAa,GAAO,CACnC,GAAI,KAAK,iBACL,OAEJ,MAAMG,EAA4B,CAAC,CAAC,KAAK,oBACnCC,EAA2B,KAAK,0BAClC,KAAK,kBAAoBD,IAA8BC,KACvD,KAAK,iBAAmB,GACxB,KAAK,YAAW,GAEpB,IAAIC,EAAiB,EACrB,KAAK,eAAe,SAChB,CAAC,KAAK,4BAA8B,KAAK,2BAA2B,SAAW,KAAK,SAAS,UAC7F,KAAK,2BAA6B,IAAI,aAAa,KAAK,SAAS,MAAM,GAE3E,IAAIC,EAAc,GAClB,UAAWP,KAAU,KAAK,SAEtB,GADAO,IACI,EAAAP,EAAO,YAAc,GAAK,KAAK,qBAGnC,IAAI,KAAK,eAAe,QAAUH,EAAmB,4CAA8C,CAAC,KAAK,yBACrG,MAEJ,KAAK,eAAe,KAAKG,CAAM,EAC/B,KAAK,2BAA2BM,CAAc,EAAIC,EAClD,KAAK,gBAAgBD,GAAgB,EAAIN,EAAO,UAEhD,KAAK,2BAA2B,SAAWM,IAC3C,KAAK,2BAA6B,KAAK,2BAA2B,MAAM,EAAGA,CAAc,IAEzF,CAAC,KAAK,aAAe,KAAK,YAAY,SAAWA,KACjD,KAAK,YAAc,IAAI,aAAaA,CAAc,GAEtD,QAAS7K,EAAQ,EAAGA,EAAQ6K,EAAgB7K,IACxC,KAAK,YAAYA,CAAK,EAAI,KAAK,gBAAgBA,CAAK,EAExD,GAAIwK,GAAc,KAAK,OACnB,UAAWpG,KAAQ,KAAK,OAAO,OACvBA,EAAK,qBAAuB,OACxBwG,EACAxG,EAAK,gCAA+B,EAGpCA,EAAK,oCAAmC,EAK3D,CAID,aAAc,CACV,GAAI,CAAC,KAAK,QAAU,KAAK,iBACrB,OAEJ,MAAMxD,EAAS,KAAK,OAAO,UAAS,EACpC,KAAK,mBAAqB,GAC1B,KAAK,iBAAmB,GACxB,KAAK,kBAAoB,GACzB,KAAK,aAAe,GACpB,KAAK,cAAgB,GACrB,KAAK,gBAAkB,GACvB,KAAK,aAAe,EACpB,KAAK,qBAAqB,UAC1B,KAAK,oBAAsB,KACvB,KAAK,0BAA4B,KAAK,SAAS,OAASA,EAAO,QAAS,EAAC,8BACzE,KAAK,yBAA2B,IAEpC,UAAW2J,KAAU,KAAK,SAAU,CAChC,KAAK,mBAAqB,KAAK,oBAAsBA,EAAO,aAC5D,KAAK,iBAAmB,KAAK,kBAAoBA,EAAO,WACxD,KAAK,kBAAoB,KAAK,mBAAqBA,EAAO,YAC1D,KAAK,aAAe,KAAK,cAAgBA,EAAO,OAChD,KAAK,cAAgB,KAAK,eAAiBA,EAAO,QAClD,KAAK,gBAAkB,KAAK,iBAAmBA,EAAO,UACtD,MAAMQ,EAAcR,EAAO,YAC3B,GAAI,KAAK,eAAiB,EACtB,KAAK,aAAeQ,UAEf,KAAK,eAAiBA,EAAa,CACxCzF,EAAO,MAAM,6FAA6F,KAAK,YAAY,8BAA8BiF,EAAO,IAAI,MAAMQ,CAAW,EAAE,EACvL,MACH,CACJ,CACD,GAAI,KAAK,yBAA0B,CAC/B,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,KAAK,uBAChC,KAAK,kBAAoB,KAAK,uBAC9B,KAAK,mBAAqB,KAAK,uBAC/B,KAAK,cAAgB,KAAK,uBAC1B,KAAK,eAAiB,KAAK,uBAC3B,KAAK,iBAAmB,KAAK,uBAC7B,KAAK,cAAgB,KAAK,aAAe,KAAK,sBAAwB,EACtE,KAAK,eAAiB,EACtB,MAAMC,EAAiBpK,EAAO,QAAO,EAAG,eACpC,KAAK,cAAgBoK,IACrB,KAAK,eAAiB,KAAK,KAAK,KAAK,cAAgBA,CAAc,EACnE,KAAK,cAAgBA,GAEzB,MAAMC,EAAc,KAAK,SAAS,OAC5B7C,EAAO,IAAI,aAAa6C,EAAc,KAAK,cAAgB,KAAK,eAAiB,CAAC,EACxF,IAAIC,EAAS,EACb,QAASlL,EAAQ,EAAGA,EAAQiL,EAAajL,IAAS,CAC9C,MAAMuK,EAAS,KAAK,SAASvK,CAAK,EAC5BmL,EAAYZ,EAAO,eACnBa,EAAUb,EAAO,aACjBc,EAAMd,EAAO,SACbe,EAAWf,EAAO,cAClBgB,EAAOhB,EAAO,UACdiB,EAASjB,EAAO,YACtBW,EAASlL,EAAQ,KAAK,cAAgB,KAAK,eAAiB,EAC5D,QAASyL,EAAS,EAAGA,EAAS,KAAK,aAAcA,IACzC,KAAK,oBAAsBN,IAC3B/C,EAAK8C,CAAM,EAAIC,EAAUM,EAAS,CAAC,EACnCrD,EAAK8C,EAAS,CAAC,EAAIC,EAAUM,EAAS,EAAI,CAAC,EAC3CrD,EAAK8C,EAAS,CAAC,EAAIC,EAAUM,EAAS,EAAI,CAAC,EAC3CP,GAAU,GAEV,KAAK,kBAAoBE,IACzBhD,EAAK8C,CAAM,EAAIE,EAAQK,EAAS,CAAC,EACjCrD,EAAK8C,EAAS,CAAC,EAAIE,EAAQK,EAAS,EAAI,CAAC,EACzCrD,EAAK8C,EAAS,CAAC,EAAIE,EAAQK,EAAS,EAAI,CAAC,EACzCP,GAAU,GAEV,KAAK,cAAgBG,IACrBjD,EAAK8C,CAAM,EAAIG,EAAII,EAAS,CAAC,EAC7BrD,EAAK8C,EAAS,CAAC,EAAIG,EAAII,EAAS,EAAI,CAAC,EACrCP,GAAU,GAEV,KAAK,mBAAqBI,IAC1BlD,EAAK8C,CAAM,EAAII,EAASG,EAAS,CAAC,EAClCrD,EAAK8C,EAAS,CAAC,EAAII,EAASG,EAAS,EAAI,CAAC,EAC1CrD,EAAK8C,EAAS,CAAC,EAAII,EAASG,EAAS,EAAI,CAAC,EAC1CP,GAAU,GAEV,KAAK,eAAiBK,IACtBnD,EAAK8C,CAAM,EAAIK,EAAKE,EAAS,CAAC,EAC9BrD,EAAK8C,EAAS,CAAC,EAAIK,EAAKE,EAAS,EAAI,CAAC,EACtCP,GAAU,GAEV,KAAK,iBAAmBM,IACxBpD,EAAK8C,CAAM,EAAIM,EAAOC,EAAS,CAAC,EAChCrD,EAAK8C,EAAS,CAAC,EAAIM,EAAOC,EAAS,EAAI,CAAC,EACxCrD,EAAK8C,EAAS,CAAC,EAAIM,EAAOC,EAAS,EAAI,CAAC,EACxCrD,EAAK8C,EAAS,CAAC,EAAIM,EAAOC,EAAS,EAAI,CAAC,EACxCP,GAAU,EAGrB,CACD,KAAK,oBAAsB1B,GAAkB,kBAAkBpB,EAAM,KAAK,cAAe,KAAK,eAAgB6C,EAAa,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAC1J,KAAK,oBAAoB,KAAO,iBAAiB,KAAK,QAAQ,EACjE,CAED,UAAW7G,KAAQ,KAAK,OAAO,OACvBA,EAAK,qBAAuB,MAC5BA,EAAK,oCAAmC,CAGnD,CAID,SAAU,CAMN,GALI,KAAK,qBACL,KAAK,oBAAoB,UAE7B,KAAK,oBAAsB,KAEvB,KAAK,OAAQ,CAEb,GADA,KAAK,OAAO,yBAAyB,IAAI,EACrC,KAAK,iBAAkB,CACvB,MAAMpE,EAAQ,KAAK,iBAAiB,oBAAoB,QAAQ,IAAI,EAChEA,EAAQ,IACR,KAAK,iBAAiB,oBAAoB,OAAOA,EAAO,CAAC,EAE7D,KAAK,iBAAmB,IAC3B,CACD,UAAW0L,KAAS,KAAK,SACrB,KAAK,OAAO,cAAcA,CAAK,CAEtC,CACJ,CAQD,OAAO,MAAM5D,EAAqB9E,EAAO,CACrC,MAAMsE,EAAS,IAAI8C,EAAmBpH,CAAK,EAC3C,UAAW2I,KAAc7D,EAAoB,QACzCR,EAAO,UAAUkB,EAAY,MAAMmD,EAAY3I,CAAK,CAAC,EAEzD,OAAOsE,CACV,CACL,CAEA8C,EAAmB,qBAAuB,GAE1CA,EAAmB,2CAA6C,ECriBzD,MAAMwB,CAAW,CAKpB,YAAYC,EAAQ,CAIhB,KAAK,WAAa,EAClB,KAAK,OAASA,CACjB,CAMD,UAAUC,EAAY,CAClB,OAAO,KAAK,OAAO,UAAU,KAAK,WAAYA,CAAU,EAAE,KAAM1D,GAAS,CACrE,KAAK,UAAY,IAAI,SAASA,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EAC3E,KAAK,gBAAkB,CACnC,CAAS,CACJ,CAKD,YAAa,CACT,MAAMrG,EAAQ,KAAK,UAAU,UAAU,KAAK,gBAAiB,EAAI,EACjE,YAAK,iBAAmB,EACxB,KAAK,YAAc,EACZA,CACV,CAMD,eAAe+J,EAAY,CACvB,MAAM/J,EAAQ,IAAI,WAAW,KAAK,UAAU,OAAQ,KAAK,UAAU,WAAa,KAAK,gBAAiB+J,CAAU,EAChH,YAAK,iBAAmBA,EACxB,KAAK,YAAcA,EACZ/J,CACV,CAMD,WAAW+J,EAAY,CACnB,OAAOC,GAAO,KAAK,eAAeD,CAAU,CAAC,CAChD,CAKD,UAAUA,EAAY,CAClB,KAAK,iBAAmBA,EACxB,KAAK,YAAcA,CACtB,CACL,CC/DA,SAASE,GAAc5D,EAAM6D,EAASC,EAAUC,EAAqB,CACjE,MAAMC,EAAU,CACZ,yBAA0BD,CAClC,EACI,OAAID,IACAE,EAAQ,IAAMH,IAAY,QAAUC,EAAWD,EAAUC,GAEtD,YAAY,OAAO9D,CAAI,EAAI,cAAc,cAAcA,EAAMgE,CAAO,EAAI,cAAc,eAAehE,EAAMgE,CAAO,CAC7H,CAIA,SAASC,IAAa,CAClB,MAAMC,EAA2B,GACjC,UAAaC,GAAY,CACrB,MAAMnE,EAAOmE,EAAQ,KACrB,OAAQnE,EAAK,GAAE,CACX,IAAK,OAAQ,CACT,cAAcA,EAAK,GAAG,EACtB,KACH,CACD,IAAK,WAAY,CACb4D,GAAc5D,EAAK,KAAMA,EAAK,QAASA,EAAK,SAAWoE,GAAQ,IAAI,QAAQ,CAACC,EAASC,IAAW,CAC5F,MAAM1M,EAAQsM,EAAyB,OACvCA,EAAyB,KAAK,CAAE,QAAAG,EAAS,OAAAC,CAAQ,GACjD,YAAY,CAAE,GAAI,sBAAuB,MAAO1M,EAAO,IAAKwM,CAAG,CAAE,CACrF,CAAiB,CAAC,EAAE,KAAMzK,GAAU,CAChB,YAAY,CAAE,GAAI,mBAAoB,MAAOA,CAAO,EACvD,EAAG4K,GAAW,CACX,YAAY,CAAE,GAAI,kBAAmB,OAAQA,CAAQ,EACzE,CAAiB,EACD,KACH,CACD,IAAK,8BAA+B,CAChCL,EAAyBlE,EAAK,KAAK,EAAE,QAAQA,EAAK,KAAK,EACvD,KACH,CACD,IAAK,6BAA8B,CAC/BkE,EAAyBlE,EAAK,KAAK,EAAE,OAAOA,EAAK,MAAM,EACvD,KACH,CACJ,CACT,CACA,CAIO,MAAMwE,EAAe,CASxB,OAAO,cAAcxE,EAAM6D,EAASC,EAAUC,EAAqB,CAC/D,OAAI,OAAO,QAAW,WACX,IAAI,QAAQ,CAACM,EAASC,IAAW,CACpC,MAAMG,EAAgB,GAAGb,EAAa,IAAIK,EAAU,MAC9CS,EAAgB,IAAI,gBAAgB,IAAI,KAAK,CAACD,CAAa,EAAG,CAAE,KAAM,wBAAwB,CAAE,CAAC,EACjGE,EAAS,IAAI,OAAOD,CAAa,EACjCE,EAAWC,GAAU,CACvBF,EAAO,oBAAoB,QAASC,CAAO,EAC3CD,EAAO,oBAAoB,UAAWG,CAAS,EAC/CR,EAAOO,CAAK,CAChC,EACsBC,EAAaX,GAAY,CAC3B,MAAMnE,EAAOmE,EAAQ,KACrB,OAAQnE,EAAK,GAAE,CACX,IAAK,sBAAuB,CACxB+D,EAAoB/D,EAAK,GAAG,EAAE,KAAMrG,GAAU,CAC1CgL,EAAO,YAAY,CAAE,GAAI,8BAA+B,MAAO3E,EAAK,MAAO,MAAOrG,CAAO,EAAE,CAACA,EAAM,MAAM,CAAC,CAC5G,EAAG4K,GAAW,CACXI,EAAO,YAAY,CAAE,GAAI,6BAA8B,MAAO3E,EAAK,MAAO,OAAQuE,CAAM,CAAE,CAC1H,CAA6B,EACD,KACH,CACD,IAAK,mBAAoB,CACrBI,EAAO,oBAAoB,QAASC,CAAO,EAC3CD,EAAO,oBAAoB,UAAWG,CAAS,EAC/CT,EAAQrE,EAAK,KAAK,EAClB2E,EAAO,UAAS,EAChB,KACH,CACD,IAAK,kBACDA,EAAO,oBAAoB,QAASC,CAAO,EAC3CD,EAAO,oBAAoB,UAAWG,CAAS,EAC/CR,EAAOtE,EAAK,MAAM,EAClB2E,EAAO,UAAS,CAEvB,CACrB,EAIgB,GAHAA,EAAO,iBAAiB,QAASC,CAAO,EACxCD,EAAO,iBAAiB,UAAWG,CAAS,EAC5CH,EAAO,YAAY,CAAE,GAAI,OAAQ,IAAKnN,EAAM,oBAAoB,KAAK,cAAc,GAAG,CAAG,GACrF,YAAY,OAAOwI,CAAI,EAAG,CAE1B,MAAM+E,EAAa/E,EAAK,QACxB2E,EAAO,YAAY,CAAE,GAAI,WAAY,KAAMI,EAAY,QAASlB,EAAS,SAAUC,CAAU,EAAE,CAACiB,EAAW,MAAM,CAAC,CACrH,MAEGJ,EAAO,YAAY,CAAE,GAAI,WAAY,KAAM3E,EAAM,QAAS6D,EAAS,SAAUC,CAAU,EAE3G,CAAa,GAGI,KAAK,qBACN,KAAK,mBAAqBtM,EAAM,uBAAuB,KAAK,cAAc,GAAG,GAE1E,KAAK,mBAAmB,KAAK,IACzBoM,GAAc5D,EAAM6D,EAASC,EAAUC,CAAmB,CACpE,EAER,CACL,CAIAS,GAAe,cAAgB,CAC3B,IAAK,GAAGhN,EAAM,cAAc,oBAChC,EChHA,SAASwN,GAAUC,EAAaC,EAAYxB,EAAY,CACpD,GAAI,CACA,OAAO,QAAQ,QAAQ,IAAI,WAAWuB,EAAaC,EAAYxB,CAAU,CAAC,CAC7E,OACMyB,EAAG,CACN,OAAO,QAAQ,OAAOA,CAAC,CAC1B,CACL,CACA,SAASC,GAAcC,EAAiBH,EAAYxB,EAAY,CAC5D,GAAI,CACA,GAAIwB,EAAa,GAAKA,GAAcG,EAAgB,WAChD,MAAM,IAAI,WAAW,yBAAyB,EAElD,GAAIH,EAAaxB,EAAa2B,EAAgB,WAC1C,MAAM,IAAI,WAAW,yBAAyB,EAElD,OAAO,QAAQ,QAAQ,IAAI,WAAWA,EAAgB,OAAQA,EAAgB,WAAaH,EAAYxB,CAAU,CAAC,CACrH,OACMyB,EAAG,CACN,OAAO,QAAQ,OAAOA,CAAC,CAC1B,CACL,CAIO,IAAIG,GACV,SAAUA,EAAgC,CAIvCA,EAA+BA,EAA+B,KAAU,CAAC,EAAI,OAI7EA,EAA+BA,EAA+B,mBAAwB,CAAC,EAAI,oBAC/F,GAAGA,IAAmCA,EAAiC,CAAE,EAAC,EAInE,IAAIC,GACV,SAAUA,EAA8B,CAIrCA,EAA6BA,EAA6B,KAAU,CAAC,EAAI,OAIzEA,EAA6BA,EAA6B,MAAW,CAAC,EAAI,QAI1EA,EAA6BA,EAA6B,IAAS,CAAC,EAAI,KAC5E,GAAGA,IAAiCA,EAA+B,CAAE,EAAC,EAI/D,IAAIC,GACV,SAAUA,EAAiB,CAIxBA,EAAgBA,EAAgB,QAAa,CAAC,EAAI,UAIlDA,EAAgBA,EAAgB,MAAW,CAAC,EAAI,QAIhDA,EAAgBA,EAAgB,SAAc,CAAC,EAAI,UACvD,GAAGA,IAAoBA,EAAkB,CAAE,EAAC,EAC5C,MAAMC,EAAkB,CACpB,aAAc,CAOV,KAAK,qBAAuBH,EAA+B,KAI3D,KAAK,mBAAqBC,EAA6B,MAKvD,KAAK,mBAAqB,GAI1B,KAAK,UAAY,GAIjB,KAAK,iBAAmB,GAIxB,KAAK,iBAAmB,GAIxB,KAAK,aAAe,GAIpB,KAAK,wBAA0B,GAM/B,KAAK,uBAAyB,GAM9B,KAAK,iBAAmB,GAIxB,KAAK,gBAAkB,GAIvB,KAAK,yBAA2B,GAIhC,KAAK,iBAAmB,GAIxB,KAAK,kBAAoB,GAIzB,KAAK,cAAgB,GAIrB,KAAK,eAAiB,GAItB,KAAK,UAAY,GAKjB,KAAK,8BAAgC,GAKrC,KAAK,oBAAsB,GAM3B,KAAK,mBAAsBG,GAAQ,QAAQ,QAAQA,CAAG,EAItD,KAAK,iBAAmB,EAC3B,CAED,SAAS1B,EAAS,CACVA,IACA,KAAK,SAAWA,EAAQ,SACxB,KAAK,qBAAuBA,EAAQ,sBAAwB,KAAK,qBACjE,KAAK,mBAAqBA,EAAQ,oBAAsB,KAAK,mBAC7D,KAAK,mBAAqBA,EAAQ,oBAAsB,KAAK,mBAC7D,KAAK,UAAYA,EAAQ,WAAa,KAAK,UAC3C,KAAK,iBAAmBA,EAAQ,kBAAoB,KAAK,iBACzD,KAAK,iBAAmBA,EAAQ,kBAAoB,KAAK,iBACzD,KAAK,aAAeA,EAAQ,cAAgB,KAAK,aACjD,KAAK,wBAA0BA,EAAQ,yBAA2B,KAAK,wBACvE,KAAK,uBAAyBA,EAAQ,wBAA0B,KAAK,uBACrE,KAAK,iBAAmBA,EAAQ,kBAAoB,KAAK,iBACzD,KAAK,gBAAkBA,EAAQ,iBAAmB,KAAK,gBACvD,KAAK,yBAA2BA,EAAQ,0BAA4B,KAAK,yBACzE,KAAK,iBAAmBA,EAAQ,kBAAoB,KAAK,iBACzD,KAAK,kBAAoBA,EAAQ,mBAAqB,KAAK,kBAC3D,KAAK,cAAgBA,EAAQ,eAAiB,KAAK,cACnD,KAAK,eAAiBA,EAAQ,gBAAkB,KAAK,eACrD,KAAK,UAAYA,EAAQ,WAAa,KAAK,UAC3C,KAAK,8BAAgCA,EAAQ,+BAAiC,KAAK,8BACnF,KAAK,oBAAsBA,EAAQ,qBAAuB,KAAK,oBAC/D,KAAK,mBAAqBA,EAAQ,oBAAsB,KAAK,mBAC7D,KAAK,eAAiBA,EAAQ,eAC9B,KAAK,aAAeA,EAAQ,aAC5B,KAAK,aAAeA,EAAQ,aAC5B,KAAK,gBAAkBA,EAAQ,gBAC/B,KAAK,iBAAmBA,EAAQ,iBAChC,KAAK,eAAiBA,EAAQ,eAC9B,KAAK,iBAAmBA,EAAQ,kBAAoB,KAAK,iBAEhE,CACL,CAIO,MAAM2B,UAAuBF,EAAkB,CAKlD,YAAYzB,EAAS,CACjB,QAOA,KAAK,mBAAqB,IAAIzL,EAQ9B,KAAK,uBAAyB,IAAIA,EAOlC,KAAK,uBAAyB,IAAIA,EAIlC,KAAK,0BAA4B,IAAIA,EAIrC,KAAK,2BAA6B,IAAIA,EAItC,KAAK,yBAA2B,IAAIA,EAMpC,KAAK,qBAAuB,IAAIA,EAIhC,KAAK,kBAAoB,IAAIA,EAI7B,KAAK,oBAAsB,IAAIA,EAK/B,KAAK,4BAA8B,IAAIA,EAIvC,KAAK,SAAW,GAIhB,KAAK,sBAAwB,IAAIA,EACjC,KAAK,QAAU,KACf,KAAK,OAAS,KACd,KAAK,UAAY,IAAI,MAIrB,KAAK,KAAOqN,EAAuB,KAEnC,KAAK,WAAaA,EAAuB,WAIzC,KAAK,+BAAiC,IAAIrN,EAC1C,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,GAEvB,KAAK,KAAO,KAAK,aACjB,KAAK,4BAA8B,GAEnC,KAAK,yBAA2B,KAAK,iCAErC,KAAK,uBAAyB,KAAK,+BACnC,KAAK,SAASyL,CAAO,CACxB,CAID,IAAI,SAAS6B,EAAU,CACf,KAAK,mBACL,KAAK,mBAAmB,OAAO,KAAK,iBAAiB,EAErDA,IACA,KAAK,kBAAoB,KAAK,mBAAmB,IAAIA,CAAQ,EAEpE,CAKD,IAAI,aAAaA,EAAU,CACnB,KAAK,uBACL,KAAK,uBAAuB,OAAO,KAAK,qBAAqB,EAE7DA,IACA,KAAK,sBAAwB,KAAK,uBAAuB,IAAIA,CAAQ,EAE5E,CAKD,IAAI,aAAaA,EAAU,CACnB,KAAK,uBACL,KAAK,uBAAuB,OAAO,KAAK,qBAAqB,EAE7DA,IACA,KAAK,sBAAwB,KAAK,uBAAuB,IAAK7F,GAAS6F,EAAS7F,EAAK,KAAMA,EAAK,WAAW,CAAC,EAEnH,CAID,IAAI,gBAAgB6F,EAAU,CACtB,KAAK,0BACL,KAAK,0BAA0B,OAAO,KAAK,wBAAwB,EAEnEA,IACA,KAAK,yBAA2B,KAAK,0BAA0B,IAAIA,CAAQ,EAElF,CAID,IAAI,iBAAiBA,EAAU,CACvB,KAAK,2BACL,KAAK,2BAA2B,OAAO,KAAK,yBAAyB,EAErEA,IACA,KAAK,0BAA4B,KAAK,2BAA2B,IAAIA,CAAQ,EAEpF,CAID,IAAI,eAAeA,EAAU,CACrB,KAAK,yBACL,KAAK,yBAAyB,OAAO,KAAK,uBAAuB,EAEjEA,IACA,KAAK,wBAA0B,KAAK,yBAAyB,IAAIA,CAAQ,EAEhF,CAMD,IAAI,WAAWA,EAAU,CACjB,KAAK,qBACL,KAAK,qBAAqB,OAAO,KAAK,mBAAmB,EAE7D,KAAK,oBAAsB,KAAK,qBAAqB,IAAIA,CAAQ,CACpE,CAID,IAAI,QAAQA,EAAU,CACd,KAAK,kBACL,KAAK,kBAAkB,OAAO,KAAK,gBAAgB,EAEvD,KAAK,iBAAmB,KAAK,kBAAkB,IAAIA,CAAQ,CAC9D,CAID,IAAI,UAAUA,EAAU,CAChB,KAAK,oBACL,KAAK,oBAAoB,OAAO,KAAK,kBAAkB,EAE3D,KAAK,mBAAqB,KAAK,oBAAoB,IAAIA,CAAQ,CAClE,CAID,IAAI,kBAAkBA,EAAU,CACxB,KAAK,4BACL,KAAK,4BAA4B,OAAO,KAAK,0BAA0B,EAE3E,KAAK,2BAA6B,KAAK,4BAA4B,IAAIA,CAAQ,CAClF,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,eACf,CACD,IAAI,eAAelM,EAAO,CAClB,KAAK,kBAAoBA,IAG7B,KAAK,gBAAkBA,EACnB,KAAK,gBACL,KAAK,KAAO,KAAK,YAGjB,KAAK,KAAO,KAAK,aAExB,CAID,IAAI,4BAA6B,CAC7B,OAAO,KAAK,2BACf,CACD,IAAI,2BAA2BA,EAAO,CAC9B,KAAK,8BAAgCA,IAGzC,KAAK,4BAA8BA,EAC/B,KAAK,6BACL,KAAK,yBAA2B,KAAK,gCACrC,KAAK,uBAAyB,KAAK,gCAGnC,KAAK,yBAA2B,KAAK,iCACrC,KAAK,uBAAyB,KAAK,gCAE1C,CAID,IAAI,YAAYkM,EAAU,CAClB,KAAK,sBACL,KAAK,sBAAsB,OAAO,KAAK,oBAAoB,EAE/D,KAAK,qBAAuB,KAAK,sBAAsB,IAAIA,CAAQ,CACtE,CAID,SAAU,CACF,KAAK,UACL,KAAK,QAAQ,UACb,KAAK,QAAU,MAEnB,UAAWC,KAAW,KAAK,UACvBA,EAAQ,MAAK,EAEjB,KAAK,UAAU,OAAS,EACxB,OAAO,KAAK,kBACZ,KAAK,mBAAsBJ,GAAQ,QAAQ,QAAQA,CAAG,EACtD,KAAK,uBAAuB,QAC5B,KAAK,uBAAuB,QAC5B,KAAK,0BAA0B,QAC/B,KAAK,2BAA2B,QAChC,KAAK,yBAAyB,QAC9B,KAAK,qBAAqB,QAC1B,KAAK,4BAA4B,QACjC,KAAK,oBAAoB,gBAAgB,MAAS,EAClD,KAAK,oBAAoB,OAC5B,CAID,SAAS9K,EAAOmL,EAAWlC,EAASmC,EAAWC,EAAYC,EAAgBtB,EAASlK,EAAM,CACtF,GAAI,YAAY,OAAOqL,CAAS,EAC5B,YAAK,YAAYnL,EAAOmL,EAAWlC,EAASmC,EAAWpB,EAASlK,CAAI,EAC7D,KAEX,KAAK,kBAAoBuL,EACzB,MAAMnC,EAAWiC,EAAU,MAAQvO,EAAM,YAAYuO,CAAS,EAC9D,GAAIG,EAAgB,CAChB,GAAI,KAAK,iBAAkB,CACnB,KAAK,UACLhJ,EAAO,KAAK,kEAAkE,EAElF,MAAMiJ,EAAc,CAChB,MAAO,IAAM,CAAG,EAChB,qBAAsB,IAAI5N,CAC9C,EACsB6N,EAAa,CACf,UAAW,CAAClB,EAAYxB,IACb,IAAI,QAAQ,CAACW,EAASC,IAAW,CACpC,KAAK,UAAU1J,EAAOmL,EAAY/F,GAAS,CACvCqE,EAAQ,IAAI,WAAWrE,CAAI,CAAC,CAC5D,EAA+B,GAAO6E,GAAU,CAChBP,EAAOO,CAAK,CACf,EAAGwB,GAAe,CACfA,EAAW,iBAAiB,QAAS,SAASnB,CAAU,IAAIA,EAAaxB,EAAa,CAAC,EAAE,CACzH,CAA6B,CAC7B,CAAyB,EAEL,WAAY,CAChC,EACgB,YAAK,mBAAmB,IAAIF,EAAW4C,CAAU,CAAC,EAAE,KAAME,GAAe,CACrEH,EAAY,qBAAqB,gBAAgBA,CAAW,EAC5DH,EAAUM,CAAU,CACxC,EAAmB1B,EAAWC,GAAUD,EAAQ,OAAWC,CAAK,EAAI,MAAS,EACtDsB,CACV,CACD,OAAO,KAAK,UAAUvL,EAAOmL,EAAY/F,GAAS,CAC9C,KAAK,UAAUpF,EAAO,IAAI,WAAWoF,EAAM,EAAGA,EAAK,UAAU,EAAG6D,EAASC,CAAQ,EACjF,KAAK,mBAAmB,IAAIN,EAAW,CACnC,UAAW,CAAC0B,EAAYxB,IAAesB,GAAUhF,EAAMkF,EAAYxB,CAAU,EAC7E,WAAY1D,EAAK,UACrC,CAAiB,CAAC,EAAE,KAAMsG,GAAe,CACrBN,EAAUM,CAAU,CACxC,EAAmB1B,EAAWC,GAAUD,EAAQ,OAAWC,CAAK,EAAI,MAAS,CAC7E,EAAe,GAAMD,CAAO,CACnB,KAEG,QAAO,KAAK,UAAUhK,EAAOmL,EAAY/F,GAAS,CAC9C,GAAI,CACA,KAAK,UAAUpF,EAAOoF,EAAM6D,EAASC,CAAQ,EAC7CkC,EAAU,CAAE,KAAM,KAAK,WAAWhG,CAAI,CAAC,CAAE,CAC5C,MACK,CACE4E,GACAA,GAEP,CACjB,EAAe,GAAOA,CAAO,CAExB,CACD,YAAYhK,EAAOoF,EAAM6D,EAASmC,EAAWpB,EAASd,EAAU,CAC5D,KAAK,UAAUlJ,EAAO,IAAI,WAAWoF,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EAAG6D,EAASC,CAAQ,EACtG,KAAK,mBAAmB,IAAIN,EAAW,CACnC,UAAW,CAAC0B,EAAYxB,IAAe0B,GAAcpF,EAAMkF,EAAYxB,CAAU,EACjF,WAAY1D,EAAK,UAC7B,CAAS,CAAC,EAAE,KAAMsG,GAAe,CACrBN,EAAUM,CAAU,CAChC,EAAW1B,EAAWC,GAAUD,EAAQ,OAAWC,CAAK,EAAI,MAAS,CAChE,CAID,gBAAgB0B,EAAa3L,EAAOoF,EAAM6D,EAASoC,EAAYnC,EAAU,CACrE,OAAO,QAAQ,UAAU,KAAK,KAC1B,KAAK,mBAAmB,gBAAgB9D,CAAI,EAC5C,KAAK,mBAAmB,QACxB,KAAK,KAAK,WAAW8D,GAAY,EAAE,EAAE,EACrC,KAAK,QAAU,KAAK,WAAW9D,CAAI,EAC5B,KAAK,QAAQ,gBAAgBuG,EAAa3L,EAAO,KAAMoF,EAAM6D,EAASoC,EAAYnC,CAAQ,EACpG,CACJ,CAID,UAAUlJ,EAAOoF,EAAM6D,EAASoC,EAAYnC,EAAU,CAClD,OAAO,QAAQ,UAAU,KAAK,KAC1B,KAAK,mBAAmB,gBAAgB9D,CAAI,EAC5C,KAAK,mBAAmB,QACxB,KAAK,KAAK,WAAW8D,GAAY,EAAE,EAAE,EACrC,KAAK,QAAU,KAAK,WAAW9D,CAAI,EAC5B,KAAK,QAAQ,UAAUpF,EAAOoF,EAAM6D,EAASoC,EAAYnC,CAAQ,EAC3E,CACJ,CAID,wBAAwBlJ,EAAOoF,EAAM6D,EAASoC,EAAYnC,EAAU,CAChE,OAAO,QAAQ,UAAU,KAAK,IAAM,CAChC,KAAK,mBAAmB,gBAAgB9D,CAAI,EAC5C,KAAK,mBAAmB,QACxB,KAAK,KAAK,WAAW8D,GAAY,EAAE,EAAE,EACrC,KAAK,QAAU,KAAK,WAAW9D,CAAI,EAEnC,MAAMwG,EAAY,IAAIC,GAAe7L,CAAK,EAEpC8L,EAAY,GAClB,KAAK,2BAA2B,IAAKC,GAAa,CAC9CD,EAAU,KAAKC,CAAQ,CACvC,CAAa,EACD,MAAMC,EAAW,GACjB,KAAK,0BAA0B,IAAKC,GAAY,CAC5CD,EAAS,KAAKC,CAAO,CACrC,CAAa,EACD,MAAMC,EAAU,GAChB,KAAK,yBAAyB,IAAKjP,GAAW,CAC1CiP,EAAQ,KAAKjP,CAAM,CACnC,CAAa,EACD,MAAMkP,EAAsB,GAC5B,YAAK,uBAAuB,IAAK/K,GAAS,CAClCA,EAAK,oBACL+K,EAAoB,KAAK/K,EAAK,kBAAkB,CAEpE,CAAa,EACM,KAAK,QAAQ,gBAAgB,KAAMpB,EAAO4L,EAAWxG,EAAM6D,EAASoC,EAAYnC,CAAQ,EAAE,KAAM5E,IACnG,MAAM,UAAU,KAAK,MAAMsH,EAAU,WAAYtH,EAAO,UAAU,EAClE,MAAM,UAAU,KAAK,MAAMsH,EAAU,OAAQtH,EAAO,MAAM,EAC1D,MAAM,UAAU,KAAK,MAAMsH,EAAU,gBAAiBtH,EAAO,eAAe,EAC5E,MAAM,UAAU,KAAK,MAAMsH,EAAU,UAAWtH,EAAO,SAAS,EAChE,MAAM,UAAU,KAAK,MAAMsH,EAAU,gBAAiBtH,EAAO,eAAe,EAC5E,MAAM,UAAU,KAAK,MAAMsH,EAAU,UAAWE,CAAS,EACzD,MAAM,UAAU,KAAK,MAAMF,EAAU,SAAUI,CAAQ,EACvD,MAAM,UAAU,KAAK,MAAMJ,EAAU,OAAQtH,EAAO,MAAM,EAC1D,MAAM,UAAU,KAAK,MAAMsH,EAAU,eAAgBtH,EAAO,cAAc,EAC1E,MAAM,UAAU,KAAK,MAAMsH,EAAU,QAASM,CAAO,EACrD,MAAM,UAAU,KAAK,MAAMN,EAAU,oBAAqBO,CAAmB,EACtEP,EACV,CACb,CAAS,CACJ,CAID,cAAcxG,EAAM,CAChB,OAAO4F,EAAuB,cAAc5F,CAAI,CACnD,CAID,WAAWpF,EAAOoF,EAAM,CACpB,GAAIA,EAAK,WAAW,UAAYgH,CAAsB,GAClDhH,EAAK,WAAW,WAAagH,CAAsB,GACnDhH,EAAK,WAAW,mCAAqCgH,CAAsB,GAC3EhH,EAAK,WAAW,4BAA8BgH,CAAsB,EAAG,CACvE,MAAM/B,EAAcgC,GAAwBjH,CAAI,EAChD,YAAK,UAAUpF,EAAO,IAAI,WAAWqK,EAAa,EAAGA,EAAY,UAAU,CAAC,EACrE,KAAK,mBAAmB,IAAIzB,EAAW,CAC1C,UAAW,CAAC0B,EAAYxB,IAAesB,GAAUC,EAAaC,EAAYxB,CAAU,EACpF,WAAYuB,EAAY,UAC3B,EAAC,CACL,CACD,YAAK,UAAUrK,EAAOoF,CAAI,EACnB,QAAQ,QAAQ,CAAE,KAAM,KAAK,WAAWA,CAAI,CAAC,CAAE,CACzD,CAED,aAAagE,EAAS,CAClB,OAAO,IAAI2B,EAAe3B,EAAQ4B,EAAuB,IAAI,CAAC,CACjE,CAID,IAAI,aAAc,CACd,OAAO,KAAK,MACf,CAKD,mBAAoB,CAChB,OAAO,IAAI,QAAQ,CAACvB,EAASC,IAAW,CACpC,KAAK,qBAAqB,QAAQ,IAAM,CACpCD,GAChB,CAAa,EACD,KAAK,kBAAkB,QAASE,GAAW,CACvCD,EAAOC,CAAM,CAC7B,CAAa,CACb,CAAS,CACJ,CAID,UAAU2C,EAAO,CACT,KAAK,SAAWA,IAGpB,KAAK,OAASA,EACd,KAAK,+BAA+B,gBAAgB,KAAK,MAAM,EAC/D,KAAK,KAAK1B,EAAgB,KAAK,MAAM,CAAC,EACzC,CAID,UAAU5K,EAAOmL,EAAWC,EAAWE,EAAgBtB,EAASuC,EAAU,CACtE,MAAMrB,EAAUlL,EAAM,UAAUmL,EAAWC,EAAY9M,GAAU,CAC7D,KAAK,YAAYA,EAAO4M,CAAO,CAClC,EAAE,GAAMI,EAAgBtB,EAASuC,CAAQ,EAC1C,OAAArB,EAAQ,qBAAqB,IAAI,IAAM,CAEnCA,EAAQ,kBAAoB,GAC5BA,EAAQ,OAASA,EAAQ,OACrC,CAAS,EACD,KAAK,UAAU,KAAKA,CAAO,EACpBA,CACV,CACD,YAAY5M,EAAO4M,EAAS,CACxB,GAAI,CAAC,KAAK,kBACN,OAEJA,EAAQ,kBAAoB5M,EAAM,iBAClC4M,EAAQ,QAAU5M,EAAM,OACxB4M,EAAQ,OAAS5M,EAAM,MACvB,IAAIkO,EAAmB,GACnBC,EAAS,EACTC,EAAQ,EACZ,UAAWxB,KAAW,KAAK,UAAW,CAClC,GAAIA,EAAQ,oBAAsB,QAAaA,EAAQ,UAAY,QAAaA,EAAQ,SAAW,OAC/F,OAEJsB,EAAmBA,GAAoBtB,EAAQ,kBAC/CuB,GAAUvB,EAAQ,QAClBwB,GAASxB,EAAQ,MACpB,CACD,KAAK,kBAAkB,CACnB,iBAAkBsB,EAClB,OAAQC,EACR,MAAOD,EAAmBE,EAAQ,CAC9C,CAAS,CACJ,CACD,UAAU1M,EAAOoF,EAAM6D,EAAU,GAAIC,EAAW,GAAI,CAC3C,KAAK,WAGV,KAAK,yBAAyB,eAAe,EAC7CU,GAAe,cAAcxE,EAAM6D,EAASC,EAAWM,GAC5C,KAAK,mBAAmBP,EAAUO,CAAG,EAAE,KAAMsB,GACzC9K,EAAM,eAAe8K,EAAK,OAAW,GAAM,EAAI,EAAE,KAAM1F,GACnD,IAAI,WAAWA,EAAM,EAAGA,EAAK,UAAU,CACjD,CACJ,CACJ,EAAE,KAAMd,GAAW,CAChB,KAAK,uBAAuB,eAAe,EAC3C,KAAK,sBAAsB,gBAAgBA,CAAM,EACjD,KAAK,sBAAsB,OAC9B,EAAGqF,GAAW,CACX,KAAK,uBAAuB,eAAe,EAC3C/M,EAAM,KAAK,uBAAuB+M,EAAO,OAAO,EAAE,EAClD,KAAK,sBAAsB,OACvC,CAAS,EACJ,CACD,WAAW+B,EAAY,CACnB,MAAMiB,EAAQjB,EAAW,KAAK,OAAS,GACvC,KAAK,KAAK,kBAAkBiB,EAAM,OAAO,EAAE,EAC3CA,EAAM,YAAc,KAAK,KAAK,0BAA0BA,EAAM,UAAU,EAAE,EAC1EA,EAAM,WAAa,KAAK,KAAK,oBAAoBA,EAAM,SAAS,EAAE,EAClE,MAAMC,EAAU7B,EAAe,cAAc4B,EAAM,OAAO,EAC1D,GAAI,CAACC,EACD,MAAM,IAAI,MAAM,oBAAsBD,EAAM,OAAO,EAEvD,GAAIA,EAAM,aAAe,OAAW,CAChC,MAAME,EAAa9B,EAAe,cAAc4B,EAAM,UAAU,EAChE,GAAI,CAACE,EACD,MAAM,IAAI,MAAM,4BAA8BF,EAAM,UAAU,EAElE,GAAI5B,EAAe,gBAAgB8B,EAAY,CAAE,MAAO,EAAG,MAAO,EAAG,EAAI,EACrE,MAAM,IAAI,MAAM,iCAAmCF,EAAM,UAAU,CAE1E,CAKD,MAAMG,EAJgB,CAClB,EAAG/B,EAAe,mBAClB,EAAGA,EAAe,kBAC9B,EAC2C6B,EAAQ,KAAK,EAChD,GAAI,CAACE,EACD,MAAM,IAAI,MAAM,wBAA0BH,EAAM,OAAO,EAE3D,OAAOG,EAAa,IAAI,CAC3B,CACD,WAAWC,EAAM,CACb,KAAK,yBAAyB,YAAY,EAC1C,KAAK,KAAK,gBAAgBA,EAAK,MAAM,EAAE,EACvC,MAAMC,EAAS,KAAK,MAAMD,CAAI,EAC9B,YAAK,uBAAuB,YAAY,EACjCC,CACV,CACD,mBAAmBC,EAAY,CAC3B,YAAK,yBAAyB,eAAe,EAEtCA,EAAW,UAAU,EAAE,EAAE,KAAK,IAAM,CACvC,MAAMC,EAAS,CACX,MAAO,UACvB,EACkBC,EAAQF,EAAW,aACzB,GAAIE,IAAUD,EAAO,MACjB,MAAM,IAAIE,GAAa,qBAAuBD,EAAOE,GAAW,8BAA8B,EAElG,MAAMT,EAAUK,EAAW,aACvB,KAAK,gBACL,KAAK,KAAK,mBAAmBL,CAAO,EAAE,EAE1C,MAAMU,EAASL,EAAW,aACtB,CAAC,KAAK,kBAAoBK,IAAWL,EAAW,OAAO,YACvD3K,EAAO,KAAK,uDAAuDgL,CAAM,OAAOL,EAAW,OAAO,UAAU,EAAE,EAElH,IAAIM,EACJ,OAAQX,EAAO,CACX,IAAK,GAAG,CACJW,EAAW,KAAK,qBAAqBN,EAAYK,CAAM,EACvD,KACH,CACD,IAAK,GAAG,CACJC,EAAW,KAAK,qBAAqBN,EAAYK,CAAM,EACvD,KACH,CACD,QACI,MAAM,IAAI,MAAM,wBAA0BV,CAAO,CAExD,CACD,YAAK,uBAAuB,eAAe,EACpCW,CACnB,CAAS,CACJ,CACD,qBAAqBN,EAAYK,EAAQ,CACrC,MAAME,EAAgB,CAClB,KAAM,CAClB,EACcC,EAAgBR,EAAW,aAC3BS,EAAgBT,EAAW,aACjC,GAAIS,IAAkBF,EAAc,KAChC,MAAM,IAAI,MAAM,8BAA8BE,CAAa,EAAE,EAEjE,MAAMC,EAAaL,EAASL,EAAW,WACjC7H,EAAO,CAAE,KAAM,KAAK,WAAW6H,EAAW,WAAWQ,CAAa,CAAC,EAAG,IAAK,IAAI,EACrF,GAAIE,IAAe,EAAG,CAClB,MAAMC,EAAkBX,EAAW,WACnC7H,EAAK,IAAM,CACP,UAAW,CAACkF,EAAYxB,IAAemE,EAAW,OAAO,UAAUW,EAAkBtD,EAAYxB,CAAU,EAC3G,WAAY6E,CAC5B,CACS,CACD,OAAO,QAAQ,QAAQvI,CAAI,CAC9B,CACD,qBAAqB6H,EAAYK,EAAQ,CACrC,MAAMO,EAAc,CAChB,KAAM,WACN,IAAK,OACjB,EAEcC,EAAcb,EAAW,aAE/B,GADoBA,EAAW,eACXY,EAAY,KAC5B,MAAM,IAAI,MAAM,gCAAgC,EAGpD,OAAIZ,EAAW,WAAaa,IAAgBR,EACjCL,EAAW,UAAUa,CAAW,EAAE,KAAK,KACnC,CAAE,KAAM,KAAK,WAAWb,EAAW,WAAWa,CAAW,CAAC,EAAG,IAAK,MAC5E,EAGEb,EAAW,UAAUa,EAAc,CAAC,EAAE,KAAK,IAAM,CACpD,MAAM1I,EAAO,CAAE,KAAM,KAAK,WAAW6H,EAAW,WAAWa,CAAW,CAAC,EAAG,IAAK,IAAI,EAC7E1D,EAAY,IAAM,CACpB,MAAM0D,EAAcb,EAAW,aAE/B,OADoBA,EAAW,aACZ,CACf,KAAKY,EAAY,KACb,MAAM,IAAI,MAAM,uBAAuB,EAE3C,KAAKA,EAAY,IAAK,CAClB,MAAMD,EAAkBX,EAAW,WACnC7H,EAAK,IAAM,CACP,UAAW,CAACkF,EAAYxB,IAAemE,EAAW,OAAO,UAAUW,EAAkBtD,EAAYxB,CAAU,EAC3G,WAAYgF,CACxC,EACwBb,EAAW,UAAUa,CAAW,EAChC,KACH,CACD,QAAS,CAELb,EAAW,UAAUa,CAAW,EAChC,KACH,CACJ,CACD,OAAIb,EAAW,aAAeK,EACnBL,EAAW,UAAU,CAAC,EAAE,KAAK7C,CAAS,EAE1C,QAAQ,QAAQhF,CAAI,CAC3C,EACY,OAAOgF,EAAS,CAC5B,CAAS,CACJ,CACD,OAAO,cAAcwC,EAAS,CAC1B,GAAIA,IAAY,OAASA,IAAY,QACjC,MAAO,CACH,MAAO,EACP,MAAO,CACvB,EAEQ,MAAMmB,GAASnB,EAAU,IAAI,MAAM,eAAe,EAClD,OAAKmB,EAGE,CACH,MAAO,SAASA,EAAM,CAAC,CAAC,EACxB,MAAO,SAASA,EAAM,CAAC,CAAC,CACpC,EALmB,IAMd,CACD,OAAO,gBAAgBC,EAAG7M,EAAG,CACzB,OAAI6M,EAAE,MAAQ7M,EAAE,MACL,EAEP6M,EAAE,MAAQ7M,EAAE,MACL,GAEP6M,EAAE,MAAQ7M,EAAE,MACL,EAEP6M,EAAE,MAAQ7M,EAAE,MACL,GAEJ,CACV,CAID,SAASoI,EAAS,CACd,KAAK,KAAKA,CAAO,EACjB,KAAK,iBACR,CAED,WAAY,CACR,EAAE,KAAK,eACV,CACD,YAAYA,EAAS,CACjB,MAAM0E,EAASlD,EAAe,WAAW,UAAU,EAAG,KAAK,gBAAkB,CAAC,EAC9EzI,EAAO,IAAI,GAAG2L,CAAM,GAAG1E,CAAO,EAAE,CACnC,CACD,aAAaA,EAAS,CAAG,CACzB,gCAAgC2E,EAAa,CACzCtR,EAAM,wBAAwBsR,CAAW,CAC5C,CACD,iCAAiCA,EAAa,CAAG,CACjD,8BAA8BA,EAAa,CACvCtR,EAAM,sBAAsBsR,CAAW,CAC1C,CACD,+BAA+BA,EAAa,CAAG,CACnD,CAaAnD,EAAe,mBAAqB,GAMpCA,EAAe,uBAAyB,GACxCA,EAAe,WAAa,mCAC5BoD,GAA0B,IAAIpD,CAAgB,ECx6BvC,MAAMqD,CAAU,CAQnB,OAAO,IAAIC,EAASC,EAAOtR,EAAO,CAC9B,GAAI,CAACsR,GAAStR,GAAS,MAAa,CAACsR,EAAMtR,CAAK,EAC5C,MAAM,IAAI,MAAM,GAAGqR,CAAO,2BAA2BrR,CAAK,GAAG,EAEjE,OAAOsR,EAAMtR,CAAK,CACrB,CAOD,OAAO,OAAOsR,EAAOtR,EAAO,CACxB,MAAI,CAACsR,GAAStR,GAAS,MAAa,CAACsR,EAAMtR,CAAK,EACrC,KAEJsR,EAAMtR,CAAK,CACrB,CAKD,OAAO,OAAOsR,EAAO,CACjB,GAAIA,EACA,QAAStR,EAAQ,EAAGA,EAAQsR,EAAM,OAAQtR,IACtCsR,EAAMtR,CAAK,EAAE,MAAQA,CAGhC,CACL,CAEO,SAASuR,GAAqCC,EAAU,CAC3D,GAAIA,EAAS,KAAOA,EAAS,IAAK,CAC9B,MAAMC,EAAWD,EAAS,IACpBE,EAAWF,EAAS,IACpBG,EAAYxK,EAAW,QAAQ,CAAC,EAAE,eAAesK,EAAS,CAAC,EAAGA,EAAS,CAAC,EAAGA,EAAS,CAAC,CAAC,EACtFG,EAAYzK,EAAW,QAAQ,CAAC,EAAE,eAAeuK,EAAS,CAAC,EAAGA,EAAS,CAAC,EAAGA,EAAS,CAAC,CAAC,EAC5F,GAAIF,EAAS,YAAcA,EAAS,gBAAkB,KAAwC,CAC1F,IAAIK,EAAU,EACd,OAAQL,EAAS,cAAa,CAC1B,IAAK,MACDK,EAAU,IACV,MACJ,IAAK,MACDA,EAAU,IACV,MACJ,IAAK,MACDA,EAAU,MACV,MACJ,IAAK,MACDA,EAAU,MACV,KACP,CACD,MAAMC,EAAiB,EAAID,EAC3BF,EAAU,aAAaG,CAAc,EACrCF,EAAU,aAAaE,CAAc,CACxC,CACD,OAAO,IAAIC,GAAaJ,EAAWC,CAAS,CAC/C,CACD,OAAO,IACX,CAIO,MAAMI,CAAW,CAOpB,OAAO,kBAAkBlP,EAAMmP,EAAS,CACpCC,GAAsBpP,EAAM,GAAOmP,CAAO,CAC7C,CAOD,OAAO,oBAAoBnP,EAAM,CAC7B,OAAOqP,GAAwBrP,CAAI,CACtC,CAID,IAAI,MAAO,CACP,GAAI,CAAC,KAAK,MACN,MAAM,IAAI,MAAM,4BAA4B,EAEhD,OAAO,KAAK,KACf,CAID,IAAI,KAAM,CACN,OAAO,KAAK,IACf,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CAID,IAAI,cAAe,CACf,GAAI,CAAC,KAAK,cACN,MAAM,IAAI,MAAM,wBAAwB,EAE5C,OAAO,KAAK,aACf,CAID,IAAI,iBAAkB,CAClB,OAAO,KAAK,gBACf,CAID,IAAI,SAAU,CACV,OAAO,KAAK,QACf,CAID,YAAYyE,EAAQ,CAEhB,KAAK,kBAAoB,IAAI,MAE7B,KAAK,gBAAkB,KAEvB,KAAK,eAAiB,GAEtB,KAAK,sBAAwB,EAE7B,KAAK,2BAA6B,GAElC,KAAK,wBAA0B,GAC/B,KAAK,YAAc,IAAI,MACvB,KAAK,UAAY,GACjB,KAAK,SAAW,KAChB,KAAK,UAAY,KACjB,KAAK,eAAiB,KACtB,KAAK,KAAO,KACZ,KAAK,iBAAmB,KACxB,KAAK,4BAA8B,GACnC,KAAK,sBAAwB,IAAI,MACjC,KAAK,QAAUA,CAClB,CAED,SAAU,CACF,KAAK,YAGT,KAAK,UAAY,GACjB,KAAK,kBAAkB,OAAS,EAChC,KAAK,YAAY,QAAS6K,GAAcA,EAAU,SAAWA,EAAU,QAAO,CAAE,EAChF,KAAK,YAAY,OAAS,EAC1B,KAAK,MAAQ,KACb,KAAK,KAAO,KACZ,KAAK,cAAgB,KACrB,KAAK,iBAAmB,KACxB,KAAK,4BAA8B,GACnC,KAAK,sBAAsB,OAAS,EACpC,KAAK,QAAQ,UAChB,CAID,gBAAgBzD,EAAa3L,EAAO4L,EAAWxG,EAAM6D,EAASoC,EAAYnC,EAAW,GAAI,CACrF,OAAO,QAAQ,UAAU,KAAK,IAAM,CAChC,KAAK,cAAgBlJ,EACrB,KAAK,gBAAkB4L,EACvB,KAAK,UAAUxG,CAAI,EACnB,IAAIiK,EAAQ,KACZ,GAAI1D,EAAa,CACb,MAAM2D,EAAU,GAChB,GAAI,KAAK,MAAM,MACX,UAAWtL,KAAQ,KAAK,MAAM,MACtBA,EAAK,OACLsL,EAAQtL,EAAK,IAAI,EAAIA,EAAK,OAKtCqL,GADc1D,aAAuB,MAAQA,EAAc,CAACA,CAAW,GACzD,IAAK7L,GAAS,CACxB,MAAMkE,EAAOsL,EAAQxP,CAAI,EACzB,GAAIkE,IAAS,OACT,MAAM,IAAI,MAAM,wBAAwBlE,CAAI,GAAG,EAEnD,OAAOkE,CAC3B,CAAiB,CACJ,CACD,OAAO,KAAK,WAAWiF,EAASC,EAAUmG,EAAO,KACtC,CACH,OAAQ,KAAK,WAAY,EACzB,gBAAiB,CAAE,EACnB,UAAW,KAAK,cAAe,EAC/B,gBAAiB,KAAK,oBAAqB,EAC3C,OAAQ,KAAK,eACb,eAAgB,KAAK,mBAAoB,EACzC,WAAY,KAAK,eAAgB,EACjC,eAAgB,CAAE,CACtC,EACa,CACb,CAAS,CACJ,CAID,UAAUrP,EAAOoF,EAAM6D,EAASoC,EAAYnC,EAAW,GAAI,CACvD,OAAO,QAAQ,UAAU,KAAK,KAC1B,KAAK,cAAgBlJ,EACrB,KAAK,UAAUoF,CAAI,EACZ,KAAK,WAAW6D,EAASC,EAAU,KAAM,IAAM,EAAS,EAClE,CACJ,CACD,WAAWD,EAASC,EAAUmG,EAAOE,EAAY,CAC7C,OAAO,QAAQ,QAAS,EACnB,KAAK,SAAY,CAClB,KAAK,SAAWtG,EAChB,KAAK,eAAiB,CAACA,EAAQ,WAAW,OAAO,GAAKC,EAAWD,EAAU,GAAGA,CAAO,GAAG,KAAK,IAAK,KAClG,KAAK,UAAYC,EACjB,KAAK,2BAA6B,GAClC,MAAM,KAAK,uBACX,MAAMsG,EAA4B,GAAG5E,EAAgBA,EAAgB,OAAO,CAAC,OAAOA,EAAgBA,EAAgB,KAAK,CAAC,GACpH6E,EAA+B,GAAG7E,EAAgBA,EAAgB,OAAO,CAAC,OAAOA,EAAgBA,EAAgB,QAAQ,CAAC,GAChI,KAAK,QAAQ,yBAAyB4E,CAAyB,EAC/D,KAAK,QAAQ,yBAAyBC,CAA4B,EAClE,KAAK,QAAQ,UAAU7E,EAAgB,OAAO,EAC9C,KAAK,qBAAoB,EACzB,MAAM8E,EAAW,IAAI,MAEfC,EAAiC,KAAK,cAAc,4BAE1D,GADA,KAAK,cAAc,4BAA8B,GAC7C,CAAC,KAAK,OAAO,mBACb,GAAIN,EACAK,EAAS,KAAK,KAAK,eAAe,SAAU,CAAE,MAAOL,EAAO,MAAO,EAAE,CAAE,CAAC,UAEnE,KAAK,MAAM,OAAS,MAAc,KAAK,MAAM,QAAU,KAAK,MAAM,OAAO,CAAC,EAAI,CACnF,MAAMrP,EAAQoO,EAAU,IAAI,SAAU,KAAK,MAAM,OAAQ,KAAK,MAAM,OAAS,CAAC,EAC9EsB,EAAS,KAAK,KAAK,eAAe,WAAW1P,EAAM,KAAK,GAAIA,CAAK,CAAC,CACrE,EAEL,GAAI,CAAC,KAAK,OAAO,eAAiB,KAAK,OAAO,kBAAoB,KAAK,MAAM,UACzE,QAAS4P,EAAI,EAAGA,EAAI,KAAK,MAAM,UAAU,OAAQ,EAAEA,EAAG,CAClD,MAAM7D,EAAW,KAAK,MAAM,UAAU6D,CAAC,EACjCvB,EAAU,cAAgBuB,EAC1BC,EAAkBC,EAAS,iBACjCJ,EAAS,KAAK,KAAK,mBAAmBrB,EAAStC,EAAU,KAAM8D,EAAiB,IAAM,CAAG,EAAC,CAC7F,CAGL,OAAI,KAAK,2BAGL,KAAK,cAAc,4BAA8BF,EAKjD,KAAK,cAAc,kCAAkCA,CAA8B,EAEnF,KAAK,QAAQ,kBACbD,EAAS,KAAK,KAAK,uBAAwB,GAE3C,KAAK,QAAQ,yBACbA,EAAS,KAAK,KAAK,8BAA+B,GAEhC,QAAQ,IAAIA,CAAQ,EAAE,KAAK,IAAM,CAC/C,KAAK,kBAAoB,KAAK,mBAAqB,KAAK,QAAQ,gBAChE,KAAK,iBAAiB,WAAW,EAAI,EAGzC,UAAW3D,KAAY,KAAK,cAAc,UAAW,CACjD,MAAMgE,EAAMhE,EACRgE,EAAI,wBAA0B,SAC9BA,EAAI,sBAAwB,KAAK,IAAIA,EAAI,sBAAuB,KAAK,cAAc,OAAO,MAAM,EAEvG,CACD,YAAK,mBAAkB,EACvB,KAAK,QAAQ,UAAUnF,EAAgB,KAAK,EACvC,KAAK,yBACN,KAAK,iBAAgB,EAElB2E,EAAU,CACjC,CAAa,EACoB,KAAMjL,IACvB,KAAK,QAAQ,uBAAuBkL,CAAyB,EAC7D5S,EAAM,aAAa,IAAM,CAChB,KAAK,WACN,QAAQ,IAAI,KAAK,iBAAiB,EAAE,KAAK,IAAM,CAC3C,KAAK,QAAQ,uBAAuB6S,CAA4B,EAChE,KAAK,QAAQ,UAAU7E,EAAgB,QAAQ,EAC/C,KAAK,QAAQ,qBAAqB,gBAAgB,MAAS,EAC3D,KAAK,QAAQ,qBAAqB,QAClC,KAAK,QAAO,CACf,EAAGX,GAAU,CACV,KAAK,QAAQ,kBAAkB,gBAAgBA,CAAK,EACpD,KAAK,QAAQ,kBAAkB,QAC/B,KAAK,QAAO,CACxC,CAAyB,CAEzB,CAAiB,EACM3F,EACV,CACb,CAAS,EACI,MAAO2F,GAAU,CAClB,MAAK,KAAK,YACN,KAAK,QAAQ,kBAAkB,gBAAgBA,CAAK,EACpD,KAAK,QAAQ,kBAAkB,QAC/B,KAAK,QAAO,GAEVA,CAClB,CAAS,CACJ,CACD,UAAU7E,EAAM,CAGZ,GAFA,KAAK,MAAQA,EAAK,KAClB,KAAK,WAAU,EACXA,EAAK,IAAK,CACV,MAAM4K,EAAU,KAAK,MAAM,QAC3B,GAAIA,GAAWA,EAAQ,CAAC,GAAK,CAACA,EAAQ,CAAC,EAAE,IAAK,CAC1C,MAAMC,EAAeD,EAAQ,CAAC,GAC1BC,EAAa,WAAa7K,EAAK,IAAI,WAAa,GAAK6K,EAAa,WAAa7K,EAAK,IAAI,aACxF9C,EAAO,KAAK,yBAAyB2N,EAAa,UAAU,4CAA4C7K,EAAK,IAAI,UAAU,GAAG,EAElI,KAAK,KAAOA,EAAK,GACpB,MAEG9C,EAAO,KAAK,sBAAsB,CAEzC,CACJ,CACD,YAAa,CAcT,GAbA8L,EAAU,OAAO,KAAK,MAAM,SAAS,EACrCA,EAAU,OAAO,KAAK,MAAM,UAAU,EACtCA,EAAU,OAAO,KAAK,MAAM,OAAO,EACnCA,EAAU,OAAO,KAAK,MAAM,WAAW,EACvCA,EAAU,OAAO,KAAK,MAAM,OAAO,EACnCA,EAAU,OAAO,KAAK,MAAM,MAAM,EAClCA,EAAU,OAAO,KAAK,MAAM,SAAS,EACrCA,EAAU,OAAO,KAAK,MAAM,MAAM,EAClCA,EAAU,OAAO,KAAK,MAAM,KAAK,EACjCA,EAAU,OAAO,KAAK,MAAM,QAAQ,EACpCA,EAAU,OAAO,KAAK,MAAM,MAAM,EAClCA,EAAU,OAAO,KAAK,MAAM,KAAK,EACjCA,EAAU,OAAO,KAAK,MAAM,QAAQ,EAChC,KAAK,MAAM,MAAO,CAClB,MAAM8B,EAAc,GACpB,UAAWlM,KAAQ,KAAK,MAAM,MAC1B,GAAIA,EAAK,SACL,UAAWhH,KAASgH,EAAK,SACrBkM,EAAYlT,CAAK,EAAIgH,EAAK,MAItC,MAAMmM,EAAW,KAAK,kBACtB,UAAWnM,KAAQ,KAAK,MAAM,MAAO,CACjC,MAAMQ,EAAc0L,EAAYlM,EAAK,KAAK,EAC1CA,EAAK,OAASQ,IAAgB,OAAY2L,EAAW,KAAK,MAAM,MAAM3L,CAAW,CACpF,CACJ,CACJ,CACD,MAAM,sBAAuB,CACzB,MAAM4L,EAAoB,GAwB1B,GAvBAC,GAAyB,QAAQ,CAACC,EAAqBxQ,IAAS,CAExD,KAAK,OAAO,iBAAiBA,CAAI,GAAG,UAAY,GAE5CwQ,EAAoB,iBAAmB,KAAK,gBAAgBxQ,CAAI,GAChEwC,EAAO,KAAK,aAAaxC,CAAI,4CAA4C,GAIxE,CAACwQ,EAAoB,iBAAmB,KAAK,gBAAgBxQ,CAAI,IACtEsQ,EAAkB,MAAM,SAAY,CAChC,MAAMhB,EAAY,MAAMkB,EAAoB,QAAQ,IAAI,EACxD,OAAIlB,EAAU,OAAStP,GACnBwC,EAAO,KAAK,sFAAsF8M,EAAU,IAAI,QAAQtP,CAAI,EAAE,EAElI,KAAK,QAAQ,4BAA4B,gBAAgBsP,CAAS,EAC3DA,CACV,IAAG,CAEpB,CAAS,EACD,KAAK,YAAY,KAAK,GAAI,MAAM,QAAQ,IAAIgB,CAAiB,GAC7D,KAAK,YAAY,KAAK,CAACpC,EAAG7M,KAAO6M,EAAE,OAAS,OAAO,YAAc7M,EAAE,OAAS,OAAO,UAAU,EAC7F,KAAK,QAAQ,4BAA4B,QACrC,KAAK,MAAM,oBACX,UAAWrB,KAAQ,KAAK,MAAM,mBAE1B,GAAI,CADc,KAAK,YAAY,KAAMsP,GAAcA,EAAU,OAAStP,GAAQsP,EAAU,OAAO,EAE/F,MAAI,KAAK,OAAO,iBAAiBtP,CAAI,GAAG,UAAY,GAC1C,IAAI,MAAM,sBAAsBA,CAAI,cAAc,EAEtD,IAAI,MAAM,sBAAsBA,CAAI,mBAAmB,EAI5E,CACD,iBAAkB,CACd,GAAI,KAAK,QAAQ,iBAAmB,OAChC,YAAK,iBAAmB,KAAK,QAAQ,eAC9B,CAEH,sBAAuB,KAAK,mBAAqB,KAAO,OAAY,KAAK,iBACzE,MAAO,EACvB,EAEQ,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAMyQ,EAAW,IAAIC,GAAK,WAAY,KAAK,aAAa,EACxD,KAAK,iBAAmBD,EACxB,KAAK,iBAAiB,iBAAmB,KAAK,gBAC9C,KAAK,cAAc,uBAAyB,GAC5C,KAAK,iBAAiB,WAAW,EAAK,EACtC,MAAMJ,EAAW,CAEb,sBAAuB,KAAK,iBAC5B,MAAO,EACnB,EACQ,OAAQ,KAAK,QAAQ,qBAAoB,CACrC,KAAKzF,EAA+B,KAAM,CACjC,KAAK,cAAc,uBACpByF,EAAS,SAAW,CAAC,EAAG,EAAG,EAAG,CAAC,EAC/BA,EAAS,MAAQ,CAAC,EAAG,EAAG,EAAE,EAC1BnB,EAAW,eAAemB,EAAU,KAAK,gBAAgB,GAE7D,KACH,CACD,KAAKzF,EAA+B,mBAAoB,CACpD,KAAK,cAAc,qBAAuB,GAC1C,KACH,CACD,QACI,MAAM,IAAI,MAAM,mCAAmC,KAAK,QAAQ,oBAAoB,GAAG,CAE9F,CACD,YAAK,QAAQ,uBAAuB,gBAAgB6F,CAAQ,EACrDJ,CACV,CAOD,eAAe9B,EAASrO,EAAO,CAC3B,MAAMyQ,EAAmB,KAAK,0BAA0BpC,EAASrO,CAAK,EACtE,GAAIyQ,EACA,OAAOA,EAEX,MAAMf,EAAW,IAAI,MAErB,GADA,KAAK,QAAQ,GAAGrB,CAAO,IAAIrO,EAAM,MAAQ,EAAE,EAAE,EACzCA,EAAM,MACN,UAAWhD,KAASgD,EAAM,MAAO,CAC7B,MAAMgE,EAAOoK,EAAU,IAAI,GAAGC,CAAO,UAAUrR,CAAK,GAAI,KAAK,MAAM,MAAOA,CAAK,EAC/E0S,EAAS,KAAK,KAAK,cAAc,UAAU1L,EAAK,KAAK,GAAIA,EAAO0M,GAAgB,CAC5EA,EAAY,OAAS,KAAK,gBAC7B,EAAC,CACL,CAEL,UAAWxR,KAAU,KAAK,sBACtBA,IAEJ,OAAAwQ,EAAS,KAAK,KAAK,qBAAsB,GACzC,KAAK,SAAQ,EACN,QAAQ,IAAIA,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CACD,kBAAkB1L,EAAMiH,EAAU,CAC9B,GAAIjH,EAAK,wBACL,UAAW0M,KAAe1M,EAAK,wBAC3BiH,EAASyF,CAAW,CAG/B,CACD,gBAAiB,CACb,MAAMC,EAAa,GACbtB,EAAQ,KAAK,MAAM,MACzB,GAAIA,EACA,UAAWrL,KAAQqL,EACf,KAAK,kBAAkBrL,EAAO0M,GAAgB,CAC1C,MAAME,EAAWF,EAAY,SACzBE,GAAYD,EAAW,QAAQC,CAAQ,IAAM,IAC7CD,EAAW,KAAKC,CAAQ,CAEhD,CAAiB,EAGT,OAAOD,CACV,CACD,YAAa,CACT,MAAME,EAAS,GAEX,KAAK,4BAA4BC,IACjCD,EAAO,KAAK,KAAK,gBAAgB,EAErC,MAAMxB,EAAQ,KAAK,MAAM,MACzB,GAAIA,EACA,UAAWrL,KAAQqL,EACf,KAAK,kBAAkBrL,EAAO0M,GAAgB,CAC1CG,EAAO,KAAKH,CAAW,CAC3C,CAAiB,EAGT,OAAOG,CACV,CACD,oBAAqB,CACjB,MAAME,EAAiB,GACjB1B,EAAQ,KAAK,MAAM,MACzB,GAAIA,EACA,UAAWrL,KAAQqL,EACXrL,EAAK,uBAAyBA,EAAK,sBAAsB,aAAc,IAAK,iBAC5E+M,EAAe,KAAK/M,EAAK,qBAAqB,EAE9CA,EAAK,8BACL+M,EAAe,KAAK/M,EAAK,4BAA4B,EAIjE,OAAO+M,CACV,CACD,eAAgB,CACZ,MAAMC,EAAY,GACZC,EAAQ,KAAK,MAAM,MACzB,GAAIA,EACA,UAAWC,KAAQD,EACXC,EAAK,OACLF,EAAU,KAAKE,EAAK,MAAM,eAAe,EAIrD,OAAOF,CACV,CACD,qBAAsB,CAClB,MAAMG,EAAkB,GAClB5N,EAAa,KAAK,MAAM,WAC9B,GAAIA,EACA,UAAWsB,KAAatB,EAChBsB,EAAU,wBACVsM,EAAgB,KAAKtM,EAAU,sBAAsB,EAIjE,OAAOsM,CACV,CACD,kBAAmB,CACf,OAAQ,KAAK,QAAQ,mBAAkB,CACnC,KAAKxG,EAA6B,KAE9B,MAEJ,KAAKA,EAA6B,MAAO,CACrC,MAAMyG,EAAyB,KAAK,sBAChCA,EAAuB,SAAW,GAClCA,EAAuB,CAAC,EAAE,MAAM,EAAI,EAExC,KACH,CACD,KAAKzG,EAA6B,IAAK,CACnC,MAAMyG,EAAyB,KAAK,sBACpC,UAAWC,KAAyBD,EAChCC,EAAsB,MAAM,EAAI,EAEpC,KACH,CACD,QAAS,CACL/O,EAAO,MAAM,iCAAiC,KAAK,QAAQ,kBAAkB,GAAG,EAChF,MACH,CACJ,CACJ,CAQD,cAAc+L,EAASrK,EAAMsN,EAAS,IAAM,GAAK,CAC7C,MAAMb,EAAmB,KAAK,yBAAyBpC,EAASrK,EAAMsN,CAAM,EAC5E,GAAIb,EACA,OAAOA,EAEX,GAAIzM,EAAK,sBACL,MAAM,IAAI,MAAM,GAAGqK,CAAO,oCAAoC,EAElE,MAAMqB,EAAW,IAAI,MACrB,KAAK,QAAQ,GAAGrB,CAAO,IAAIrK,EAAK,MAAQ,EAAE,EAAE,EAC5C,MAAMuN,EAAYC,GAAyB,CAGvC,GAFAxC,EAAW,mBAAmBwC,EAAsBnD,CAAO,EAC3DW,EAAW,eAAehL,EAAMwN,CAAoB,EAChDxN,EAAK,QAAU,KAAW,CAC1B,MAAM/G,EAASmR,EAAU,IAAI,GAAGC,CAAO,UAAW,KAAK,MAAM,QAASrK,EAAK,MAAM,EACjF0L,EAAS,KAAK,KAAK,gBAAgB,YAAYzS,EAAO,KAAK,GAAIA,EAASwU,GAAkB,CACtFA,EAAc,OAASD,CAC1B,EAAC,CACL,CACD,GAAIxN,EAAK,SACL,UAAWhH,KAASgH,EAAK,SAAU,CAC/B,MAAM0N,EAAYtD,EAAU,IAAI,GAAGC,CAAO,aAAarR,CAAK,GAAI,KAAK,MAAM,MAAOA,CAAK,EACvF0S,EAAS,KAAK,KAAK,cAAc,UAAUgC,EAAU,KAAK,GAAIA,EAAYC,GAAqB,CAC3FA,EAAiB,OAASH,CAC7B,EAAC,CACL,CAELF,EAAOE,CAAoB,CACvC,EACcI,EAAU5N,EAAK,MAAQ,KACvB6N,EAAU,KAAK,QAAQ,WAAa7N,EAAK,MAAQ,KACvD,GAAI,CAAC4N,GAAWC,EAAS,CACrB,MAAMC,EAAW9N,EAAK,MAAQ,OAAOA,EAAK,KAAK,GAC/C,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAM+N,EAAgB,IAAIC,GAAcF,EAAU,KAAK,aAAa,EACpEC,EAAc,iBAAmB,KAAK,gBACtC,KAAK,cAAc,uBAAyB,GACxC/N,EAAK,MAAQ,KACbA,EAAK,sBAAwB+N,EAG7B/N,EAAK,6BAA+B+N,EAExCR,EAASQ,CAAa,CACzB,CACD,GAAIH,EACA,GAAIC,EAAS,CAIT,MAAMzQ,EAAOgN,EAAU,IAAI,GAAGC,CAAO,QAAS,KAAK,MAAM,OAAQrK,EAAK,IAAI,EAC1E0L,EAAS,KAAK,KAAK,eAAe,WAAWtO,EAAK,KAAK,GAAI4C,EAAM5C,EAAOoQ,GAAyB,CAC7F,MAAMS,EAA8BjO,EAAK,6BAEzCwN,EAAqB,SAAWU,GAAUD,EAA4B,SAAUT,EAAqB,UAAY,EAAE,EACnH,MAAMN,EAAO9C,EAAU,IAAI,GAAGC,CAAO,QAAS,KAAK,MAAM,MAAOrK,EAAK,IAAI,EACzE0L,EAAS,KAAK,KAAK,eAAe,UAAUwB,EAAK,KAAK,GAAIlN,EAAMkN,EAAOiB,GAAoB,CACvF,KAAK,kBAAkBnO,EAAO0M,GAAgB,CAC1CA,EAAY,SAAWyB,CACnD,CAAyB,EAED,KAAK,sBAAsB,KAAK,IAAM,CAClC,GAAIjB,EAAK,UAAY,KAAW,CAG5B,MAAMkB,EAAahE,EAAU,IAAI,UAAU8C,EAAK,KAAK,YAAa,KAAK,MAAM,MAAOA,EAAK,QAAQ,EAAE,OAC/FlN,EAAK,QAAUoO,EAAW,MAC1BZ,EAAqB,OAASS,EAA4B,OAG1DT,EAAqB,OAASY,EAAW,qBAEhD,MAEGZ,EAAqB,OAAS,KAAK,iBAEvC,KAAK,QAAQ,uBAAuB,gBAAgB,CAAE,KAAMS,EAA6B,YAAaT,CAAoB,CAAE,CACxJ,CAAyB,CACJ,EAAC,CACL,EAAC,CACL,KACI,CACD,MAAMpQ,EAAOgN,EAAU,IAAI,GAAGC,CAAO,QAAS,KAAK,MAAM,OAAQrK,EAAK,IAAI,EAC1E0L,EAAS,KAAK,KAAK,eAAe,WAAWtO,EAAK,KAAK,GAAI4C,EAAM5C,EAAMmQ,CAAQ,CAAC,CACnF,CAEL,YAAK,SAAQ,EACN,QAAQ,IAAI7B,CAAQ,EAAE,KAAK,KAC9B,KAAK,kBAAkB1L,EAAO0M,GAAgB,CAC1C,MAAM2B,EAAS3B,EACX,CAAC2B,EAAO,cAAgBA,EAAO,UAAYA,EAAO,SAAS,4BAE3D3B,EAAY,oBAAmB,EAG/BA,EAAY,oBAAoB,GAAM,EAAI,CAE9D,CAAa,EACM1M,EAAK,sBACf,CACJ,CACD,eAAeqK,EAASrK,EAAM5C,EAAMkQ,EAAQ,CACxC,MAAMgB,EAAalR,EAAK,WACxB,GAAI,CAACkR,GAAc,CAACA,EAAW,OAC3B,MAAM,IAAI,MAAM,GAAGjE,CAAO,0BAA0B,EAEpDiE,EAAW,CAAC,EAAE,OAAS,MACvBlE,EAAU,OAAOkE,CAAU,EAE/B,MAAM5C,EAAW,IAAI,MACrB,KAAK,QAAQ,GAAGrB,CAAO,IAAIjN,EAAK,MAAQ,EAAE,EAAE,EAC5C,MAAMtB,EAAOkE,EAAK,MAAQ,OAAOA,EAAK,KAAK,GAC3C,GAAIsO,EAAW,SAAW,EAAG,CACzB,MAAMC,EAAYnR,EAAK,WAAW,CAAC,EACnCsO,EAAS,KAAK,KAAK,wBAAwB,GAAGrB,CAAO,eAAekE,EAAU,KAAK,GAAIzS,EAAMkE,EAAM5C,EAAMmR,EAAY7B,GAAgB,CACjI1M,EAAK,sBAAwB0M,EAC7B1M,EAAK,wBAA0B,CAAC0M,CAAW,CAC9C,EAAC,CACL,KACI,CACD,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD1M,EAAK,sBAAwB,IAAIgO,GAAclS,EAAM,KAAK,aAAa,EACvEkE,EAAK,sBAAsB,iBAAmB,KAAK,gBACnD,KAAK,cAAc,uBAAyB,GAC5CA,EAAK,wBAA0B,GAC/B,UAAWuO,KAAaD,EACpB5C,EAAS,KAAK,KAAK,wBAAwB,GAAGrB,CAAO,eAAekE,EAAU,KAAK,GAAI,GAAGzS,CAAI,aAAayS,EAAU,KAAK,GAAIvO,EAAM5C,EAAMmR,EAAY7B,GAAgB,CAClKA,EAAY,OAAS1M,EAAK,sBAC1BA,EAAK,wBAAwB,KAAK0M,CAAW,CAChD,EAAC,CAET,CACD,OAAAY,EAAOtN,EAAK,qBAAqB,EACjC,KAAK,SAAQ,EACN,QAAQ,IAAI0L,CAAQ,EAAE,KAAK,IACvB1L,EAAK,qBACf,CACJ,CAWD,wBAAwBqK,EAASvO,EAAMkE,EAAM5C,EAAMmR,EAAWjB,EAAQ,CAClE,MAAMb,EAAmB,KAAK,kCAAkCpC,EAASvO,EAAMkE,EAAM5C,EAAMmR,EAAWjB,CAAM,EAC5G,GAAIb,EACA,OAAOA,EAEX,KAAK,QAAQ,GAAGpC,CAAO,EAAE,EACzB,MAAMmE,EAAiB,KAAK,wBAA0B,GAAK,KAAK,QAAQ,iBAAmBxO,EAAK,MAAQ,MAAa,CAAC5C,EAAK,WAAW,CAAC,EAAE,QACzI,IAAIqR,EACAC,EACJ,GAAIF,GAAkBD,EAAU,cAC5B,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnDE,EAAsBF,EAAU,cAAc,kBAAkB,eAAezS,CAAI,EACnF2S,EAAoB,iBAAmB,KAAK,gBAC5C,KAAK,cAAc,uBAAyB,GAC5CC,EAAUH,EAAU,cAAc,YAEjC,CACD,MAAM7C,EAAW,IAAI,MACrB,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAMgB,EAAc,IAAIF,GAAK1Q,EAAM,KAAK,aAAa,EACrD4Q,EAAY,iBAAmB,KAAK,gBACpC,KAAK,cAAc,uBAAyB,GAC5CA,EAAY,gBAAkB,KAAK,cAAc,qBAAuBZ,EAAS,gCAAkCA,EAAS,yBAC5H,KAAK,oBAAoBzB,EAASrK,EAAM5C,EAAMmR,EAAW7B,CAAW,EACpEhB,EAAS,KAAK,KAAK,qBAAqBrB,EAASkE,EAAW7B,CAAW,EAAE,KAAMiC,GACpE,KAAK,uBAAuBtE,EAASkE,EAAW7B,EAAaiC,CAAe,EAAE,KAAK,IAAM,CACxF,KAAK,YAGT,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnDA,EAAgB,YAAYjC,CAAW,EACvCiC,EAAgB,iBAAmB,KAAK,gBACxC,KAAK,cAAc,uBAAyB,GAChE,CAAiB,CACJ,CAAC,EACF,MAAM9C,EAAkBb,EAAW,aAAaX,EAASkE,EAAU,IAAI,EACvE,GAAIA,EAAU,UAAY,KAAW,CACjC,IAAIK,EAAkB,KAAK,4BAA4B/C,CAAe,EACjE+C,IACDA,EAAkB,KAAK,uBAAuB,wBAAyB/C,CAAe,EACtF,KAAK,QAAQ,2BAA2B,gBAAgB+C,CAAe,EACvE,KAAK,4BAA4B/C,CAAe,EAAI+C,GAExDlC,EAAY,SAAWkC,CAC1B,SACQ,CAAC,KAAK,OAAO,cAAe,CACjC,MAAM7G,EAAWqC,EAAU,IAAI,GAAGC,CAAO,YAAa,KAAK,MAAM,UAAWkE,EAAU,QAAQ,EAC9F7C,EAAS,KAAK,KAAK,mBAAmB,cAAc3D,EAAS,KAAK,GAAIA,EAAU2E,EAAab,EAAkB+C,GAAoB,CAC/HlC,EAAY,SAAWkC,CAC1B,EAAC,CACL,CACDF,EAAU,QAAQ,IAAIhD,CAAQ,EAC1B8C,IACAD,EAAU,cAAgB,CACtB,kBAAmB7B,EACnB,QAASgC,CAC7B,GAEYD,EAAsB/B,CACzB,CACD,OAAA1B,EAAW,mBAAmByD,EAAqBpE,CAAO,EAC1D,KAAK,QAAQ,uBAAuB,gBAAgBoE,CAAmB,EACvEnB,EAAOmB,CAAmB,EAC1B,KAAK,SAAQ,EACNC,EAAQ,KAAK,IACTD,CACV,CACJ,CACD,qBAAqBpE,EAASkE,EAAW7B,EAAa,CAClD,MAAMD,EAAmB,KAAK,+BAA+BpC,EAASkE,EAAW7B,CAAW,EAC5F,GAAID,EACA,OAAOA,EAEX,MAAMoC,EAAaN,EAAU,WAC7B,GAAI,CAACM,EACD,MAAM,IAAI,MAAM,GAAGxE,CAAO,0BAA0B,EAExD,MAAMqB,EAAW,IAAI,MACfiD,EAAkB,IAAIG,GAASpC,EAAY,KAAM,KAAK,aAAa,EACzE,GAAI6B,EAAU,SAAW,KACrB7B,EAAY,YAAc,OAEzB,CACD,MAAMlC,EAAWJ,EAAU,IAAI,GAAGC,CAAO,WAAY,KAAK,MAAM,UAAWkE,EAAU,OAAO,EAC5F7C,EAAS,KAAK,KAAK,0BAA0B,cAAclB,EAAS,KAAK,GAAIA,CAAQ,EAAE,KAAMpJ,GAAS,CAClGuN,EAAgB,WAAWvN,CAAI,CAClC,EAAC,CACL,CACD,MAAM2N,EAAgB,CAACjT,EAAMkT,EAAM/H,IAAa,CAC5C,GAAI4H,EAAW/S,CAAI,GAAK,KACpB,OAEJ4Q,EAAY,WAAaA,EAAY,YAAc,GAC/CA,EAAY,WAAW,QAAQsC,CAAI,IAAM,IACzCtC,EAAY,WAAW,KAAKsC,CAAI,EAEpC,MAAMxE,EAAWJ,EAAU,IAAI,GAAGC,CAAO,eAAevO,CAAI,GAAI,KAAK,MAAM,UAAW+S,EAAW/S,CAAI,CAAC,EACtG4P,EAAS,KAAK,KAAK,yBAAyB,cAAclB,EAAS,KAAK,GAAIA,EAAUwE,CAAI,EAAE,KAAMC,GAAwB,CACtH,GAAIA,EAAoB,YAAc1M,EAAa,cAAgB,CAAC,KAAK,OAAO,0BAA4B,CAACmK,EAAY,SAAU,CAC/H,MAAMwC,EAAsB3E,GAAqCC,CAAQ,EACrE0E,IACAP,EAAgB,cAAgBO,EAChCP,EAAgB,4BAA8B,GAErD,CACDA,EAAgB,kBAAkBM,EAAqBzE,EAAS,KAAK,CACxE,EAAC,EACEwE,GAAQzM,EAAa,2BACrBmK,EAAY,mBAAqB,GAEjCzF,GACAA,EAASuD,CAAQ,CAEjC,EACQ,OAAAuE,EAAc,WAAYxM,EAAa,YAAY,EACnDwM,EAAc,SAAUxM,EAAa,UAAU,EAC/CwM,EAAc,UAAWxM,EAAa,WAAW,EACjDwM,EAAc,aAAcxM,EAAa,MAAM,EAC/CwM,EAAc,aAAcxM,EAAa,OAAO,EAChDwM,EAAc,aAAcxM,EAAa,OAAO,EAChDwM,EAAc,aAAcxM,EAAa,OAAO,EAChDwM,EAAc,aAAcxM,EAAa,OAAO,EAChDwM,EAAc,aAAcxM,EAAa,OAAO,EAChDwM,EAAc,WAAYxM,EAAa,mBAAmB,EAC1DwM,EAAc,YAAaxM,EAAa,mBAAmB,EAC3DwM,EAAc,WAAYxM,EAAa,wBAAwB,EAC/DwM,EAAc,YAAaxM,EAAa,wBAAwB,EAChEwM,EAAc,UAAWxM,EAAa,UAAYiI,GAAa,CACvDA,EAAS,OAAS,SAClBkC,EAAY,eAAiB,GAE7C,CAAS,EACM,QAAQ,IAAIhB,CAAQ,EAAE,KAAK,IACvBiD,CACV,CACJ,CACD,oBAAoBtE,EAASrK,EAAM5C,EAAMmR,EAAW7B,EAAa,CAC7D,GAAI,CAAC6B,EAAU,SAAW,CAAC,KAAK,QAAQ,iBACpC,OAEJ,GAAIvO,EAAK,kBAAoB,KACzBA,EAAK,iBAAmBuO,EAAU,QAAQ,eAErCA,EAAU,QAAQ,SAAWvO,EAAK,iBACvC,MAAM,IAAI,MAAM,GAAGqK,CAAO,qDAAqD,EAEnF,MAAM8E,EAAc/R,EAAK,OAASA,EAAK,OAAO,YAAc,KAC5D,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnDsP,EAAY,mBAAqB,IAAItJ,EAAmB,KAAK,aAAa,EAC1EsJ,EAAY,mBAAmB,iBAAmB,KAAK,gBACvD,KAAK,cAAc,uBAAyB,GAC5CA,EAAY,mBAAmB,iBAAmB,GAClD,QAAS1T,EAAQ,EAAGA,EAAQuV,EAAU,QAAQ,OAAQvV,IAAS,CAC3D,MAAMoW,EAASpP,EAAK,QAAUA,EAAK,QAAQhH,CAAK,EAAIoE,EAAK,QAAUA,EAAK,QAAQpE,CAAK,EAAI,EACnF8C,EAAOqT,EAAcA,EAAYnW,CAAK,EAAI,cAAcA,CAAK,GACnE0T,EAAY,mBAAmB,UAAU,IAAIlL,EAAY1F,EAAMsT,EAAQ1C,EAAY,SAAU,EAAC,CAEjG,CACJ,CACD,uBAAuBrC,EAASkE,EAAW7B,EAAaiC,EAAiB,CACrE,GAAI,CAACJ,EAAU,SAAW,CAAC,KAAK,QAAQ,iBACpC,OAAO,QAAQ,UAEnB,MAAM7C,EAAW,IAAI,MACf2D,EAAqB3C,EAAY,mBACvC,QAAS1T,EAAQ,EAAGA,EAAQqW,EAAmB,WAAYrW,IAAS,CAChE,MAAMsW,EAAqBD,EAAmB,UAAUrW,CAAK,EAC7D0S,EAAS,KAAK,KAAK,gCAAgC,GAAGrB,CAAO,YAAYrR,CAAK,GAAI2V,EAAiBJ,EAAU,QAAQvV,CAAK,EAAGsW,CAAkB,CAAC,CACnJ,CACD,OAAO,QAAQ,IAAI5D,CAAQ,EAAE,KAAK,IAAM,CACpC2D,EAAmB,iBAAmB,EAClD,CAAS,CACJ,CACD,gCAAgChF,EAASsE,EAAiBE,EAAYS,EAAoB,CACtF,MAAM5D,EAAW,IAAI,MACfqD,EAAgB,CAACQ,EAAWP,EAAMQ,IAAY,CAChD,GAAIX,EAAWU,CAAS,GAAK,KACzB,OAEJ,MAAMN,EAAsBN,EAAgB,gBAAgBK,CAAI,EAChE,GAAI,CAACC,EACD,OAEJ,MAAMzE,EAAWJ,EAAU,IAAI,GAAGC,CAAO,IAAIkF,CAAS,GAAI,KAAK,MAAM,UAAWV,EAAWU,CAAS,CAAC,EACrG7D,EAAS,KAAK,KAAK,wBAAwB,cAAclB,EAAS,KAAK,GAAIA,CAAQ,EAAE,KAAMpJ,GAAS,CAChGoO,EAAQP,EAAqB7N,CAAI,CACpC,EAAC,CACd,EACQ,OAAA2N,EAAc,WAAYxM,EAAa,aAAc,CAAC0M,EAAqB7N,IAAS,CAChF,MAAM+C,EAAY,IAAI,aAAa/C,EAAK,MAAM,EAC9C6N,EAAoB,QAAQ7N,EAAK,OAAQ,CAACrG,EAAO/B,IAAU,CACvDmL,EAAUnL,CAAK,EAAIoI,EAAKpI,CAAK,EAAI+B,CACjD,CAAa,EACDuU,EAAmB,aAAanL,CAAS,CACrD,CAAS,EACD4K,EAAc,SAAUxM,EAAa,WAAY,CAAC0M,EAAqB7N,IAAS,CAC5E,MAAMgD,EAAU,IAAI,aAAahD,EAAK,MAAM,EAC5C6N,EAAoB,QAAQ7K,EAAQ,OAAQ,CAACrJ,EAAO/B,IAAU,CAC1DoL,EAAQpL,CAAK,EAAIoI,EAAKpI,CAAK,EAAI+B,CAC/C,CAAa,EACDuU,EAAmB,WAAWlL,CAAO,CACjD,CAAS,EACD2K,EAAc,UAAWxM,EAAa,YAAa,CAAC0M,EAAqB7N,IAAS,CAC9E,MAAMkD,EAAW,IAAI,aAAclD,EAAK,OAAS,EAAK,CAAC,EACvD,IAAIqO,EAAY,EAChBR,EAAoB,QAAS7N,EAAK,OAAS,EAAK,EAAG,CAACrG,EAAO/B,IAAU,EAI5DA,EAAQ,GAAK,IAAM,IACpBsL,EAASmL,CAAS,EAAIrO,EAAKqO,CAAS,EAAI1U,EACxC0U,IAEpB,CAAa,EACDH,EAAmB,YAAYhL,CAAQ,CACnD,CAAS,EACDyK,EAAc,aAAcxM,EAAa,OAAQ,CAAC0M,EAAqB7N,IAAS,CAC5E,MAAMiD,EAAM,IAAI,aAAajD,EAAK,MAAM,EACxC6N,EAAoB,QAAQ7N,EAAK,OAAQ,CAACrG,EAAO/B,IAAU,CACvDqL,EAAIrL,CAAK,EAAIoI,EAAKpI,CAAK,EAAI+B,CAC3C,CAAa,EACDuU,EAAmB,OAAOjL,CAAG,CACzC,CAAS,EACD0K,EAAc,aAAcxM,EAAa,QAAS,CAAC0M,EAAqB7N,IAAS,CAC7E,MAAMiD,EAAM,IAAI,aAAajD,EAAK,MAAM,EACxC6N,EAAoB,QAAQ7N,EAAK,OAAQ,CAACrG,EAAO/B,IAAU,CACvDqL,EAAIrL,CAAK,EAAIoI,EAAKpI,CAAK,EAAI+B,CAC3C,CAAa,EACDuU,EAAmB,QAAQjL,CAAG,CAC1C,CAAS,EACD0K,EAAc,UAAWxM,EAAa,UAAW,CAAC0M,EAAqB7N,IAAS,CAC5E,IAAIoD,EAAS,KACb,MAAMkL,EAAgBT,EAAoB,UAC1C,GAAIS,IAAkB,EAAG,CACrBlL,EAAS,IAAI,aAAcpD,EAAK,OAAS,EAAK,CAAC,EAC/C6N,EAAoB,QAAQ7N,EAAK,OAAQ,CAACrG,EAAO/B,IAAU,CACvD,MAAM2W,EAAQ,KAAK,MAAM3W,EAAQ,CAAC,EAC5B4W,EAAU5W,EAAQ,EACxBwL,EAAO,EAAImL,EAAQC,CAAO,EAAIxO,EAAK,EAAIuO,EAAQC,CAAO,EAAI7U,CAC9E,CAAiB,EACD,QAAS8C,EAAI,EAAGA,EAAIuD,EAAK,OAAS,EAAG,EAAEvD,EACnC2G,EAAO,EAAI3G,EAAI,CAAC,EAAI,CAE3B,SACQ6R,IAAkB,EACvBlL,EAAS,IAAI,aAAapD,EAAK,MAAM,EACrC6N,EAAoB,QAAQ7N,EAAK,OAAQ,CAACrG,EAAO/B,IAAU,CACvDwL,EAAOxL,CAAK,EAAIoI,EAAKpI,CAAK,EAAI+B,CAClD,CAAiB,MAGD,OAAM,IAAI,MAAM,GAAGsP,CAAO,mCAAmCqF,CAAa,yBAAyB,EAEvGJ,EAAmB,UAAU9K,CAAM,CAC/C,CAAS,EACM,QAAQ,IAAIkH,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CACD,OAAO,eAAe1L,EAAM6P,EAAa,CAGrC,GAAI7P,EAAK,MAAQ,KACb,OAEJ,IAAIjE,EAAW3C,EAAQ,OACnB0W,EAAWC,EAAW,WACtBC,EAAU5W,EAAQ,MAClB4G,EAAK,OACUnF,EAAO,UAAUmF,EAAK,MAAM,EACpC,UAAUgQ,EAASF,EAAU/T,CAAQ,GAGxCiE,EAAK,cACLjE,EAAW3C,EAAQ,UAAU4G,EAAK,WAAW,GAE7CA,EAAK,WACL8P,EAAWC,EAAW,UAAU/P,EAAK,QAAQ,GAE7CA,EAAK,QACLgQ,EAAU5W,EAAQ,UAAU4G,EAAK,KAAK,IAG9C6P,EAAY,SAAW9T,EACvB8T,EAAY,mBAAqBC,EACjCD,EAAY,QAAUG,CACzB,CACD,eAAe3F,EAASrK,EAAMkN,EAAMI,EAAQ,CACxC,GAAI,CAAC,KAAK,QAAQ,UACd,OAAO,QAAQ,UAEnB,MAAMb,EAAmB,KAAK,yBAAyBpC,EAASrK,EAAMkN,CAAI,EAC1E,GAAIT,EACA,OAAOA,EAEX,GAAIS,EAAK,MACL,OAAAI,EAAOJ,EAAK,MAAM,eAAe,EAC1BA,EAAK,MAAM,QAEtB,MAAM+C,EAAa,WAAW/C,EAAK,KAAK,GACxC,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAMiB,EAAkB,IAAIpR,GAASmQ,EAAK,MAAQ+C,EAAYA,EAAY,KAAK,aAAa,EAC5F9B,EAAgB,iBAAmB,KAAK,gBACxC,KAAK,cAAc,uBAAyB,GAC5C,KAAK,WAAW9D,EAAS6C,EAAMiB,CAAe,EAC9C,MAAMO,EAAU,KAAK,sCAAsCrE,EAAS6C,CAAI,EAAE,KAAMgD,GAA4B,CACxG,KAAK,oBAAoB/B,EAAiB+B,CAAuB,CAC7E,CAAS,EACD,OAAAhD,EAAK,MAAQ,CACT,gBAAiBiB,EACjB,QAASO,CACrB,EACQpB,EAAOa,CAAe,EACfO,CACV,CACD,WAAWrE,EAAS6C,EAAMiB,EAAiB,CACvC,GAAIjB,EAAK,UAAY,MAAa,KAAK,QAAQ,8BAA+B,CAC1E,MAAMf,EAAW,KAAK,sBAAsB,GAAG9B,CAAO,UAAW6C,EAAK,MAAM,EAC5E,GAAIf,EACA,GAAIe,EAAK,WAAa,OAClBA,EAAK,SAAWf,EAAS,UAExB,CACD,MAAMgE,EAAW,CAAC,EAAGhT,IAAM,CACvB,KAAOA,EAAE,OAAQA,EAAIA,EAAE,OACnB,GAAIA,EAAE,SAAW,EACb,MAAO,GAGf,MAAO,EAC/B,EAC0BiT,EAAehG,EAAU,IAAI,GAAGC,CAAO,YAAa,KAAK,MAAM,MAAO6C,EAAK,QAAQ,EACrFkD,IAAiBjE,GAAY,CAACgE,EAASC,EAAcjE,CAAQ,IAC7D7N,EAAO,KAAK,GAAG+L,CAAO,0FAA0F,EAChH6C,EAAK,SAAWf,EAAS,MAEhC,MAGD7N,EAAO,KAAK,GAAG+L,CAAO,8BAA8B,CAE3D,CACD,MAAMgG,EAAe,GACrB,UAAWrX,KAASkU,EAAK,OAAQ,CAC7B,MAAMlN,EAAOoK,EAAU,IAAI,GAAGC,CAAO,WAAWrR,CAAK,GAAI,KAAK,MAAM,MAAOA,CAAK,EAChF,KAAK,UAAUgH,EAAMkN,EAAMiB,EAAiBkC,CAAY,CAC3D,CACJ,CACD,sBAAsBhG,EAASiG,EAAQ,CACnC,GAAIA,EAAO,SAAW,EAClB,OAAO,KAEX,MAAMC,EAAQ,GACd,UAAWvX,KAASsX,EAAQ,CACxB,MAAME,EAAO,GACb,IAAIxQ,EAAOoK,EAAU,IAAI,GAAGC,CAAO,IAAIrR,CAAK,GAAI,KAAK,MAAM,MAAOA,CAAK,EACvE,KAAOgH,EAAK,QAAU,IAClBwQ,EAAK,QAAQxQ,CAAI,EACjBA,EAAOA,EAAK,OAEhBuQ,EAAMvX,CAAK,EAAIwX,CAClB,CACD,IAAIrE,EAAW,KACf,QAAS,EAAI,GAAI,EAAE,EAAG,CAClB,IAAIqE,EAAOD,EAAMD,EAAO,CAAC,CAAC,EAC1B,GAAI,GAAKE,EAAK,OACV,OAAOrE,EAEX,MAAMnM,EAAOwQ,EAAK,CAAC,EACnB,QAASC,EAAI,EAAGA,EAAIH,EAAO,OAAQ,EAAEG,EAEjC,GADAD,EAAOD,EAAMD,EAAOG,CAAC,CAAC,EAClB,GAAKD,EAAK,QAAUxQ,IAASwQ,EAAK,CAAC,EACnC,OAAOrE,EAGfA,EAAWnM,CACd,CACJ,CACD,UAAUA,EAAMkN,EAAMiB,EAAiBkC,EAAc,CACjDrQ,EAAK,SAAW,GAChB,IAAI0Q,EAAcL,EAAarQ,EAAK,KAAK,EACzC,GAAI0Q,EACA,OAAOA,EAEX,IAAIC,EAAoB,KACpB3Q,EAAK,QAAUkN,EAAK,WAChBlN,EAAK,QAAUA,EAAK,OAAO,QAAU,GACrC2Q,EAAoB,KAAK,UAAU3Q,EAAK,OAAQkN,EAAMiB,EAAiBkC,CAAY,EAE9EnD,EAAK,WAAa,QACvB5O,EAAO,KAAK,UAAU4O,EAAK,KAAK,+CAA+C,GAGvF,MAAM1P,EAAY0P,EAAK,OAAO,QAAQlN,EAAK,KAAK,EAChD,OAAA0Q,EAAc,IAAIjQ,GAAKT,EAAK,MAAQ,QAAQA,EAAK,KAAK,GAAImO,EAAiBwC,EAAmB,KAAK,eAAe3Q,CAAI,EAAG,KAAM,KAAMxC,CAAS,EAC9I6S,EAAarQ,EAAK,KAAK,EAAI0Q,EAE3B,KAAK,sBAAsB,KAAK,IAAM,CAGlCA,EAAY,kBAAkB1Q,EAAK,qBAAqB,CACpE,CAAS,EACM0Q,CACV,CACD,sCAAsCrG,EAAS6C,EAAM,CACjD,GAAIA,EAAK,qBAAuB,KAC5B,OAAO,QAAQ,QAAQ,IAAI,EAE/B,MAAM1C,EAAWJ,EAAU,IAAI,GAAGC,CAAO,uBAAwB,KAAK,MAAM,UAAW6C,EAAK,mBAAmB,EAC/G,OAAO,KAAK,wBAAwB,cAAc1C,EAAS,KAAK,GAAIA,CAAQ,CAC/E,CACD,oBAAoB2D,EAAiB+B,EAAyB,CAC1D,UAAWQ,KAAevC,EAAgB,MAAO,CAC7C,MAAMyC,EAAa/V,EAAO,WACpB2C,EAAYkT,EAAY,OAC1BR,GAA2B1S,IAAc,KACzC3C,EAAO,eAAeqV,EAAyB1S,EAAY,GAAIoT,CAAU,EACzEA,EAAW,YAAYA,CAAU,GAErC,MAAMC,EAAoBH,EAAY,YAClCG,GACAD,EAAW,cAAcC,EAAkB,6BAA8B,EAAED,CAAU,EAEzFF,EAAY,aAAaE,EAAY,GAAO,EAAK,EACjDF,EAAY,4BAA4B,OAAW,EAAK,CAC3D,CACJ,CACD,eAAe1Q,EAAM,CACjB,OAAOA,EAAK,OACNnF,EAAO,UAAUmF,EAAK,MAAM,EAC5BnF,EAAO,QAAQmF,EAAK,MAAQ5G,EAAQ,UAAU4G,EAAK,KAAK,EAAI5G,EAAQ,IAAG,EAAI4G,EAAK,SAAW+P,EAAW,UAAU/P,EAAK,QAAQ,EAAI+P,EAAW,SAAU,EAAE/P,EAAK,YAAc5G,EAAQ,UAAU4G,EAAK,WAAW,EAAI5G,EAAQ,KAAM,EACxO,CAQD,gBAAgBiR,EAASpR,EAAQqU,EAAS,IAAM,GAAK,CACjD,MAAMb,EAAmB,KAAK,2BAA2BpC,EAASpR,EAAQqU,CAAM,EAChF,GAAIb,EACA,OAAOA,EAEX,MAAMf,EAAW,IAAI,MACrB,KAAK,QAAQ,GAAGrB,CAAO,IAAIpR,EAAO,MAAQ,EAAE,EAAE,EAC9C,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAMwU,EAAgB,IAAI/R,EAAWzC,EAAO,MAAQ,SAASA,EAAO,KAAK,GAAIG,EAAQ,KAAI,EAAI,KAAK,cAAe,EAAK,EAOtH,OANAqU,EAAc,iBAAmB,KAAK,gBACtC,KAAK,cAAc,uBAAyB,GAC5CA,EAAc,oBAAsB,GACpCxU,EAAO,eAAiBwU,EAExBA,EAAc,SAAS,IAAI,EAAG,KAAK,GAAI,CAAC,EAChCxU,EAAO,KAAI,CACf,IAAK,cAA4C,CAC7C,MAAM6X,EAAc7X,EAAO,YAC3B,GAAI,CAAC6X,EACD,MAAM,IAAI,MAAM,GAAGzG,CAAO,6CAA6C,EAE3EoD,EAAc,IAAMqD,EAAY,KAChCrD,EAAc,KAAOqD,EAAY,MACjCrD,EAAc,KAAOqD,EAAY,MAAQ,EACzC,KACH,CACD,IAAK,eAA8C,CAC/C,GAAI,CAAC7X,EAAO,aACR,MAAM,IAAI,MAAM,GAAGoR,CAAO,8CAA8C,EAE5EoD,EAAc,KAAOsD,GAAO,oBAC5BtD,EAAc,UAAY,CAACxU,EAAO,aAAa,KAC/CwU,EAAc,WAAaxU,EAAO,aAAa,KAC/CwU,EAAc,YAAc,CAACxU,EAAO,aAAa,KACjDwU,EAAc,SAAWxU,EAAO,aAAa,KAC7CwU,EAAc,KAAOxU,EAAO,aAAa,MACzCwU,EAAc,KAAOxU,EAAO,aAAa,KACzC,KACH,CACD,QACI,MAAM,IAAI,MAAM,GAAGoR,CAAO,0BAA0BpR,EAAO,IAAI,GAAG,CAEzE,CACD,OAAA+R,EAAW,mBAAmByC,EAAepD,CAAO,EACpD,KAAK,QAAQ,yBAAyB,gBAAgBoD,CAAa,EACnEH,EAAOG,CAAa,EACpB,KAAK,SAAQ,EACN,QAAQ,IAAI/B,CAAQ,EAAE,KAAK,IACvB+B,CACV,CACJ,CACD,sBAAuB,CACnB,MAAMlO,EAAa,KAAK,MAAM,WAC9B,GAAI,CAACA,EACD,OAAO,QAAQ,UAEnB,MAAMmM,EAAW,IAAI,MACrB,QAAS1S,EAAQ,EAAGA,EAAQuG,EAAW,OAAQvG,IAAS,CACpD,MAAM6H,EAAYtB,EAAWvG,CAAK,EAClC0S,EAAS,KAAK,KAAK,mBAAmB,eAAe7K,EAAU,KAAK,GAAIA,CAAS,EAAE,KAAMmQ,GAAmB,CAEpGA,EAAe,mBAAmB,SAAW,GAC7CA,EAAe,QAAO,CAE7B,EAAC,CACL,CACD,OAAO,QAAQ,IAAItF,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CAOD,mBAAmBrB,EAASxJ,EAAW,CACnC,MAAM6N,EAAU,KAAK,8BAA8BrE,EAASxJ,CAAS,EACrE,OAAI6N,GAIGuC,GAAA,WAAO,8BAA8C,gDAAE,KAAK,CAAC,CAAE,eAAAC,CAAc,IAAO,CACvF,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAM7D,EAAwB,IAAI6D,EAAerQ,EAAU,MAAQ,YAAYA,EAAU,KAAK,GAAI,KAAK,aAAa,EACpHwM,EAAsB,iBAAmB,KAAK,gBAC9C,KAAK,cAAc,uBAAyB,GAC5CxM,EAAU,uBAAyBwM,EACnC,MAAM3B,EAAW,IAAI,MACrBtB,EAAU,OAAOvJ,EAAU,QAAQ,EACnCuJ,EAAU,OAAOvJ,EAAU,QAAQ,EACnC,UAAW+O,KAAW/O,EAAU,SAC5B6K,EAAS,KAAK,KAAK,2BAA2B,GAAGrB,CAAO,aAAauF,EAAQ,KAAK,GAAIvF,EAASxJ,EAAW+O,EAAS,CAACuB,EAAeC,IAAqB,CACpJD,EAAc,WAAaA,EAAc,YAAc,GACvDA,EAAc,WAAW,KAAKC,CAAgB,EAC9C/D,EAAsB,qBAAqB+D,EAAkBD,CAAa,CAC7E,EAAC,EAEN,OAAO,QAAQ,IAAIzF,CAAQ,EAAE,KAAK,KAC9B2B,EAAsB,UAAU,CAAC,EAC1BA,EACV,CACb,CAAS,CACJ,CAWD,MAAM,2BAA2BhD,EAASgH,EAAkBxQ,EAAW+O,EAAS0B,EAAQ,CACpF,MAAM5C,EAAU,KAAK,qCAAqCrE,EAASgH,EAAkBxQ,EAAW+O,EAAS0B,CAAM,EAC/G,GAAI5C,EACA,OAAOA,EAEX,GAAIkB,EAAQ,OAAO,MAAQ,KACvB,OAAO,QAAQ,UAEnB,MAAM2B,EAAanH,EAAU,IAAI,GAAGC,CAAO,eAAgB,KAAK,MAAM,MAAOuF,EAAQ,OAAO,IAAI,EAC1F4B,EAAoB5B,EAAQ,OAAO,KACnC6B,EAAgBD,IAAsB,UAM5C,GAJKC,GAAiB,CAACF,EAAW,kBAAsB,CAACE,GAAiB,CAACF,EAAW,uBAIlF,CAAC,KAAK,QAAQ,oBAAsB,CAACE,GAAiB,CAACF,EAAW,SAClE,OAAO,QAAQ,UAGnB,MAAKN,GAAA,IAAC,OAAO,mCAA0B,gDACvC,IAAIS,EACJ,OAAQF,EAAiB,CACrB,IAAK,cAA4D,CAC7DE,EAAaC,EAAiB,uBAAuB,GAAG,cACxD,KACH,CACD,IAAK,WAAsD,CACvDD,EAAaC,EAAiB,oBAAoB,GAAG,cACrD,KACH,CACD,IAAK,QAAgD,CACjDD,EAAaC,EAAiB,iBAAiB,GAAG,cAClD,KACH,CACD,IAAK,UAAoD,CACrDD,EAAaC,EAAiB,mBAAmB,GAAG,cACpD,KACH,CACD,QACI,MAAM,IAAI,MAAM,GAAGtH,CAAO,gCAAgCuF,EAAQ,OAAO,IAAI,GAAG,CAEvF,CAED,GAAI,CAAC8B,EACD,MAAM,IAAI,MAAM,GAAGrH,CAAO,0EAA0EuF,EAAQ,OAAO,IAAI,GAAG,EAE9H,MAAMgC,EAAa,CACf,OAAQL,EACR,KAAMG,CAClB,EACQ,OAAO,KAAK,yCAAyCrH,EAASgH,EAAkBxQ,EAAW+O,EAASgC,EAAYN,CAAM,CACzH,CAYD,yCAAyCjH,EAASgH,EAAkBxQ,EAAW+O,EAASgC,EAAYN,EAAQ,CACxG,MAAMO,EAAM,KAAK,OAAO,UAClBC,EAAS,EAAID,EACbE,EAAU3H,EAAU,IAAI,GAAGC,CAAO,WAAYxJ,EAAU,SAAU+O,EAAQ,OAAO,EACvF,OAAO,KAAK,2BAA2B,GAAGyB,CAAgB,aAAazB,EAAQ,OAAO,GAAImC,CAAO,EAAE,KAAM3Q,GAAS,CAC9G,IAAI4Q,EAAgB,EACpB,MAAMzO,EAASqO,EAAW,OACpBK,EAAgBL,EAAW,KAMjC,UAAWM,KAAgBD,EAAe,CACtC,MAAME,EAASD,EAAa,UAAU3O,CAAM,EACtC6O,EAAQhR,EAAK,MACbiR,EAASjR,EAAK,OACdkR,EAAO,IAAI,MAAMF,EAAM,MAAM,EACnC,IAAIG,EAAe,EACnB,OAAQnR,EAAK,cAAa,CACtB,IAAK,OAAiD,CAClD,QAASpI,EAAQ,EAAGA,EAAQoZ,EAAM,OAAQpZ,IAAS,CAC/C,MAAM+B,EAAQmX,EAAa,SAAS3O,EAAQ8O,EAAQE,EAAc,CAAC,EACnEA,GAAgBJ,EAChBG,EAAKtZ,CAAK,EAAI,CACV,MAAOoZ,EAAMpZ,CAAK,EAAI6Y,EACtB,MAAO9W,EACP,cAAe,CAC/C,CACyB,CACD,KACH,CACD,IAAK,cAA+D,CAChE,QAAS/B,EAAQ,EAAGA,EAAQoZ,EAAM,OAAQpZ,IAAS,CAC/C,MAAMwZ,EAAYN,EAAa,SAAS3O,EAAQ8O,EAAQE,EAAcT,CAAM,EAC5ES,GAAgBJ,EAChB,MAAMpX,EAAQmX,EAAa,SAAS3O,EAAQ8O,EAAQE,EAAc,CAAC,EACnEA,GAAgBJ,EAChB,MAAMM,GAAaP,EAAa,SAAS3O,EAAQ8O,EAAQE,EAAcT,CAAM,EAC7ES,GAAgBJ,EAChBG,EAAKtZ,CAAK,EAAI,CACV,MAAOoZ,EAAMpZ,CAAK,EAAI6Y,EACtB,UAAWW,EACX,MAAOzX,EACP,WAAY0X,EAC5C,CACyB,CACD,KACH,CACD,IAAK,SAAqD,CACtD,QAASzZ,EAAQ,EAAGA,EAAQoZ,EAAM,OAAQpZ,IAAS,CAC/C,MAAM+B,EAAQmX,EAAa,SAAS3O,EAAQ8O,EAAQE,EAAc,CAAC,EACnEA,GAAgBJ,EAChBG,EAAKtZ,CAAK,EAAI,CACV,MAAOoZ,EAAMpZ,CAAK,EAAI6Y,EACtB,MAAO9W,CACvC,CACyB,CACD,KACH,CACJ,CACD,GAAIwX,EAAe,EAAG,CAClB,MAAMzW,EAAO,GAAG+E,EAAU,MAAQ,YAAYA,EAAU,KAAK,EAAE,WAAW+O,EAAQ,KAAK,IAAIoC,CAAa,GAClGU,EAAoBR,EAAa,gBAAgB3O,EAAQzH,EAAM+V,EAAKS,CAAI,EAC9E,UAAWlB,KAAoBsB,EAC3BV,IACAV,EAAOF,EAAiB,kBAAmBA,EAAiB,gBAAgB,CAEnF,CACJ,CACb,CAAS,CACJ,CACD,2BAA2B/G,EAAS0H,EAAS,CACzC,GAAIA,EAAQ,MACR,OAAOA,EAAQ,MAEnB,MAAMY,EAAgBZ,EAAQ,eAAiB,SAC/C,OAAQY,EAAa,CACjB,IAAK,OACL,IAAK,SACL,IAAK,cACD,MAEJ,QACI,MAAM,IAAI,MAAM,GAAGtI,CAAO,kCAAkC0H,EAAQ,aAAa,GAAG,CAE3F,CACD,MAAMa,EAAgBxI,EAAU,IAAI,GAAGC,CAAO,SAAU,KAAK,MAAM,UAAW0H,EAAQ,KAAK,EACrFc,EAAiBzI,EAAU,IAAI,GAAGC,CAAO,UAAW,KAAK,MAAM,UAAW0H,EAAQ,MAAM,EAC9F,OAAAA,EAAQ,MAAQ,QAAQ,IAAI,CACxB,KAAK,wBAAwB,cAAca,EAAc,KAAK,GAAIA,CAAa,EAC/E,KAAK,wBAAwB,cAAcC,EAAe,KAAK,GAAIA,CAAc,CACpF,GAAE,KAAK,CAAC,CAACC,EAAWC,CAAU,KACpB,CACH,MAAOD,EACP,cAAeH,EACf,OAAQI,CACxB,EACS,EACMhB,EAAQ,KAClB,CASD,gBAAgB1H,EAASxF,EAAQyB,EAAYxB,EAAY,CACrD,MAAM2H,EAAmB,KAAK,2BAA2BpC,EAASxF,EAAQyB,EAAYxB,CAAU,EAChG,GAAI2H,EACA,OAAOA,EAEX,GAAI,CAAC5H,EAAO,MACR,GAAIA,EAAO,IACPA,EAAO,MAAQ,KAAK,aAAa,GAAGwF,CAAO,OAAQxF,EAAQA,EAAO,GAAG,MAEpE,CACD,GAAI,CAAC,KAAK,KACN,MAAM,IAAI,MAAM,GAAGwF,CAAO,iEAAiE,EAE/FxF,EAAO,MAAQ,KAAK,KAAK,UAAU,EAAGA,EAAO,UAAU,CAC1D,CAEL,OAAOA,EAAO,MAAM,KAAMzD,GAAS,CAC/B,GAAI,CACA,OAAO,IAAI,WAAWA,EAAK,OAAQA,EAAK,WAAakF,EAAYxB,CAAU,CAC9E,OACMyB,EAAG,CACN,MAAM,IAAI,MAAM,GAAG8D,CAAO,KAAK9D,EAAE,OAAO,EAAE,CAC7C,CACb,CAAS,CACJ,CAOD,oBAAoB8D,EAAS2I,EAAY,CACrC,MAAMvG,EAAmB,KAAK,+BAA+BpC,EAAS2I,CAAU,EAChF,GAAIvG,EACA,OAAOA,EAEX,GAAIuG,EAAW,MACX,OAAOA,EAAW,MAEtB,MAAMnO,EAASuF,EAAU,IAAI,GAAGC,CAAO,UAAW,KAAK,MAAM,QAAS2I,EAAW,MAAM,EACvF,OAAAA,EAAW,MAAQ,KAAK,gBAAgB,YAAYnO,EAAO,KAAK,GAAIA,EAAQmO,EAAW,YAAc,EAAGA,EAAW,UAAU,EACtHA,EAAW,KACrB,CACD,mBAAmB3I,EAASG,EAAUyI,EAAa,CAC/C,GAAIzI,EAAS,MACT,OAAOA,EAAS,MAEpB,MAAM0I,EAAgBlI,EAAW,kBAAkBX,EAASG,EAAS,IAAI,EACnE2I,EAAaD,EAAgB3Q,EAAa,kBAAkBiI,EAAS,aAAa,EAClFlB,EAAS4J,EAAgB1I,EAAS,MACxC,GAAIA,EAAS,YAAc,KACvBA,EAAS,MAAQ,QAAQ,QAAQ,IAAIyI,EAAY3J,CAAM,CAAC,MAEvD,CACD,MAAM0J,EAAa5I,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,MAAM,YAAaG,EAAS,UAAU,EACrGA,EAAS,MAAQ,KAAK,oBAAoB,gBAAgBwI,EAAW,KAAK,GAAIA,CAAU,EAAE,KAAM5R,GAAS,CACrG,GAAIoJ,EAAS,gBAAkB,MAA0C,CAACA,EAAS,aAAe,CAACwI,EAAW,YAAcA,EAAW,aAAeG,GAClJ,OAAOnI,EAAW,eAAeX,EAASG,EAAS,cAAepJ,EAAMoJ,EAAS,WAAYlB,CAAM,EAElG,CACD,MAAM8J,EAAa,IAAIH,EAAY3J,CAAM,EACzC,OAAA/G,EAAa,QAAQnB,EAAMoJ,EAAS,YAAc,EAAGwI,EAAW,YAAcG,EAAYD,EAAe1I,EAAS,cAAe4I,EAAW,OAAQ5I,EAAS,YAAc,GAAO,CAACzP,EAAO/B,IAAU,CAChMoa,EAAWpa,CAAK,EAAI+B,CAC5C,CAAqB,EACMqY,CACV,CACjB,CAAa,CACJ,CACD,GAAI5I,EAAS,OAAQ,CACjB,MAAM6I,EAAS7I,EAAS,OACxBA,EAAS,MAAQA,EAAS,MAAM,KAAMpJ,GAAS,CAC3C,MAAMgS,EAAahS,EACbkS,EAAoBlJ,EAAU,IAAI,GAAGC,CAAO,6BAA8B,KAAK,MAAM,YAAagJ,EAAO,QAAQ,UAAU,EAC3HE,EAAmBnJ,EAAU,IAAI,GAAGC,CAAO,4BAA6B,KAAK,MAAM,YAAagJ,EAAO,OAAO,UAAU,EAC9H,OAAO,QAAQ,IAAI,CACf,KAAK,oBAAoB,gBAAgBC,EAAkB,KAAK,GAAIA,CAAiB,EACrF,KAAK,oBAAoB,gBAAgBC,EAAiB,KAAK,GAAIA,CAAgB,CACtF,GAAE,KAAK,CAAC,CAACC,EAAaC,CAAU,IAAM,CACnC,MAAMC,EAAU1I,EAAW,eAAe,GAAGX,CAAO,kBAAmBgJ,EAAO,QAAQ,cAAeG,EAAaH,EAAO,QAAQ,WAAYA,EAAO,KAAK,EACnJM,EAAeT,EAAgBG,EAAO,MAC5C,IAAIO,EACJ,GAAIpJ,EAAS,gBAAkB,MAA0C,CAACA,EAAS,WAC/EoJ,EAAS5I,EAAW,eAAe,GAAGX,CAAO,iBAAkBG,EAAS,cAAeiJ,EAAYJ,EAAO,OAAO,WAAYM,CAAY,MAExI,CACD,MAAME,EAAa7I,EAAW,eAAe,GAAGX,CAAO,iBAAkBG,EAAS,cAAeiJ,EAAYJ,EAAO,OAAO,WAAYM,CAAY,EACnJC,EAAS,IAAIX,EAAYU,CAAY,EACrCpR,EAAa,QAAQsR,EAAY,EAAGV,EAAYD,EAAe1I,EAAS,cAAeoJ,EAAO,OAAQpJ,EAAS,YAAc,GAAO,CAACzP,EAAO/B,IAAU,CAClJ4a,EAAO5a,CAAK,EAAI+B,CAC5C,CAAyB,CACJ,CACD,IAAI+Y,EAAc,EAClB,QAASC,EAAe,EAAGA,EAAeL,EAAQ,OAAQK,IAAgB,CACtE,IAAItE,EAAYiE,EAAQK,CAAY,EAAIb,EACxC,QAASc,EAAiB,EAAGA,EAAiBd,EAAec,IACzDZ,EAAW3D,GAAW,EAAImE,EAAOE,GAAa,CAErD,CACD,OAAOV,CAC3B,CAAiB,CACjB,CAAa,CACJ,CACD,OAAO5I,EAAS,KACnB,CAID,wBAAwBH,EAASG,EAAU,CACvC,OAAO,KAAK,mBAAmBH,EAASG,EAAU,YAAY,CACjE,CAID,0BAA0BH,EAASG,EAAU,CACzC,GAAIA,EAAS,OAAS,SAClB,MAAM,IAAI,MAAM,GAAGH,CAAO,wBAAwBG,EAAS,IAAI,EAAE,EAErE,GAAIA,EAAS,gBAAkB,MAC3BA,EAAS,gBAAkB,MAC3BA,EAAS,gBAAkB,KAC3B,MAAM,IAAI,MAAM,GAAGH,CAAO,iCAAiCG,EAAS,aAAa,EAAE,EAEvF,GAAIA,EAAS,MACT,OAAOA,EAAS,MAEpB,GAAIA,EAAS,OAAQ,CACjB,MAAMyI,EAAcjI,EAAW,0BAA0B,GAAGX,CAAO,iBAAkBG,EAAS,aAAa,EAC3GA,EAAS,MAAQ,KAAK,mBAAmBH,EAASG,EAAUyI,CAAW,CAC1E,KACI,CACD,MAAMD,EAAa5I,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,MAAM,YAAaG,EAAS,UAAU,EACrGA,EAAS,MAAQ,KAAK,oBAAoB,gBAAgBwI,EAAW,KAAK,GAAIA,CAAU,EAAE,KAAM5R,GACrF4J,EAAW,eAAeX,EAASG,EAAS,cAAepJ,EAAMoJ,EAAS,WAAYA,EAAS,KAAK,CAC9G,CACJ,CACD,OAAOA,EAAS,KACnB,CAID,2BAA2BwI,EAAY,CACnC,GAAIA,EAAW,eACX,OAAOA,EAAW,eAEtB,MAAMpZ,EAAS,KAAK,cAAc,UAAS,EAC3C,OAAAoZ,EAAW,eAAiB,KAAK,oBAAoB,gBAAgBA,EAAW,KAAK,GAAIA,CAAU,EAAE,KAAM5R,GAChG,IAAI6S,GAAOra,EAAQwH,EAAM,EAAK,CACxC,EACM4R,EAAW,cACrB,CAID,yBAAyB3I,EAASG,EAAUwE,EAAM,CAC9C,GAAIxE,EAAS,uBAAuBwE,CAAI,EACpC,OAAOxE,EAAS,qBAAqBwE,CAAI,EAExCxE,EAAS,uBACVA,EAAS,qBAAuB,IAEpC,MAAM5Q,EAAS,KAAK,cAAc,UAAS,EAC3C,GAAI4Q,EAAS,QAAUA,EAAS,YAAc,KAC1CA,EAAS,qBAAqBwE,CAAI,EAAI,KAAK,wBAAwB3E,EAASG,CAAQ,EAAE,KAAMpJ,GACjF,IAAImB,EAAa3I,EAAQwH,EAAM4N,EAAM,EAAK,CACpD,MAEA,CACD,MAAMgE,EAAa5I,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,MAAM,YAAaG,EAAS,UAAU,EACrGA,EAAS,qBAAqBwE,CAAI,EAAI,KAAK,2BAA2BgE,CAAU,EAAE,KAAMkB,GAAkB,CACtG,MAAMhB,EAAgBlI,EAAW,kBAAkBX,EAASG,EAAS,IAAI,EACzE,OAAO,IAAIjI,EAAa3I,EAAQsa,EAAelF,EAAM,GAAO,OAAWgE,EAAW,WAAY,OAAWxI,EAAS,WAAY0I,EAAe1I,EAAS,cAAeA,EAAS,WAAY,GAAM,OAAW,EAAI,CAC/N,CAAa,CACJ,CACD,OAAOA,EAAS,qBAAqBwE,CAAI,CAC5C,CACD,8CAA8C3E,EAASqH,EAAY9C,EAAiB,CAChF,GAAI,EAAEA,aAA2BuF,GAC7B,MAAM,IAAI,MAAM,GAAG9J,CAAO,+BAA+B,EAE7D,MAAMqB,EAAW,IAAI,MACrB,OAAIgG,IACIA,EAAW,iBACX9C,EAAgB,YAAcwF,EAAO,UAAU1C,EAAW,eAAe,EACzE9C,EAAgB,MAAQ8C,EAAW,gBAAgB,CAAC,GAGpD9C,EAAgB,YAAcwF,EAAO,QAEzCxF,EAAgB,SAAW8C,EAAW,gBAAkB,KAAY,EAAIA,EAAW,eACnF9C,EAAgB,UAAY8C,EAAW,iBAAmB,KAAY,EAAIA,EAAW,gBACjFA,EAAW,kBACXhG,EAAS,KAAK,KAAK,qBAAqB,GAAGrB,CAAO,oBAAqBqH,EAAW,iBAAmBzJ,GAAY,CAC7GA,EAAQ,KAAO,GAAG2G,EAAgB,IAAI,gBACtCA,EAAgB,cAAgB3G,CACnC,EAAC,EAEFyJ,EAAW,2BACXA,EAAW,yBAAyB,aAAe,GACnDhG,EAAS,KAAK,KAAK,qBAAqB,GAAGrB,CAAO,4BAA6BqH,EAAW,yBAA2BzJ,GAAY,CAC7HA,EAAQ,KAAO,GAAG2G,EAAgB,IAAI,wBACtCA,EAAgB,gBAAkB3G,CACrC,EAAC,EACF2G,EAAgB,qCAAuC,GACvDA,EAAgB,qCAAuC,GACvDA,EAAgB,qCAAuC,KAGxD,QAAQ,IAAIlD,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CAID,mBAAmBrB,EAAStC,EAAU2E,EAAab,EAAiByB,EAAS,IAAM,GAAK,CACpF,MAAMb,EAAmB,KAAK,6BAA6BpC,EAAStC,EAAU2E,EAAab,EAAiByB,CAAM,EAClH,GAAIb,EACA,OAAOA,EAEX1E,EAAS,MAAQA,EAAS,OAAS,GACnC,IAAIsM,EAActM,EAAS,MAAM8D,CAAe,EAChD,GAAI,CAACwI,EAAa,CACd,KAAK,QAAQ,GAAGhK,CAAO,IAAItC,EAAS,MAAQ,EAAE,EAAE,EAChD,MAAM6G,EAAkB,KAAK,eAAevE,EAAStC,EAAU8D,CAAe,EAC9EwI,EAAc,CACV,gBAAiBzF,EACjB,cAAe,CAAE,EACjB,QAAS,KAAK,4BAA4BvE,EAAStC,EAAU6G,CAAe,CAC5F,EACY7G,EAAS,MAAM8D,CAAe,EAAIwI,EAClCrJ,EAAW,mBAAmB4D,EAAiBvE,CAAO,EACtD,KAAK,QAAQ,2BAA2B,gBAAgBuE,CAAe,EACvE,KAAK,SAAQ,CAChB,CACD,OAAIlC,IACA2H,EAAY,cAAc,KAAK3H,CAAW,EAC1CA,EAAY,oBAAoB,QAAQ,IAAM,CAC1C,MAAM1T,EAAQqb,EAAY,cAAc,QAAQ3H,CAAW,EACvD1T,IAAU,IACVqb,EAAY,cAAc,OAAOrb,EAAO,CAAC,CAE7D,CAAa,GAELsU,EAAO+G,EAAY,eAAe,EAC3BA,EAAY,QAAQ,KAAK,IACrBA,EAAY,eACtB,CACJ,CACD,uBAAuBvY,EAAM+P,EAAiB,CAC1C,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAM+C,EAAkB,IAAIuF,EAAYrY,EAAM,KAAK,aAAa,EAChE,OAAA8S,EAAgB,iBAAmB,KAAK,gBACxC,KAAK,cAAc,uBAAyB,GAE5CA,EAAgB,SAAW/C,EAC3B+C,EAAgB,2BAA6B,GAC7CA,EAAgB,qBAAuB,CAAC,KAAK,QAAQ,uBACrDA,EAAgB,qBAAuB,CAAC,KAAK,QAAQ,uBACrDA,EAAgB,iBAAmBuF,EAAY,mBAC/CvF,EAAgB,SAAW,EAC3BA,EAAgB,UAAY,EACrBA,CACV,CAQD,eAAevE,EAAStC,EAAU8D,EAAiB,CAC/C,MAAMY,EAAmB,KAAK,0BAA0BpC,EAAStC,EAAU8D,CAAe,EAC1F,GAAIY,EACA,OAAOA,EAEX,MAAM3Q,EAAOiM,EAAS,MAAQ,WAAWA,EAAS,KAAK,GAEvD,OADwB,KAAK,uBAAuBjM,EAAM+P,CAAe,CAE5E,CAQD,4BAA4BxB,EAAStC,EAAU6G,EAAiB,CAC5D,MAAMnC,EAAmB,KAAK,uCAAuCpC,EAAStC,EAAU6G,CAAe,EACvG,GAAInC,EACA,OAAOA,EAEX,MAAMf,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,gCAAgCrB,EAAStC,EAAU6G,CAAe,CAAC,EAClF7G,EAAS,sBACT2D,EAAS,KAAK,KAAK,8CAA8C,GAAGrB,CAAO,wBAAyBtC,EAAS,qBAAsB6G,CAAe,CAAC,EAEvJ,KAAK,4BAA4BvE,EAAStC,EAAU6G,CAAe,EAC5D,QAAQ,IAAIlD,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CAQD,gCAAgCrB,EAAStC,EAAU6G,EAAiB,CAChE,GAAI,EAAEA,aAA2BuF,GAC7B,MAAM,IAAI,MAAM,GAAG9J,CAAO,+BAA+B,EAE7D,MAAMqB,EAAW,IAAI,MACrB,OAAAkD,EAAgB,cAAgB7G,EAAS,eAAiBqM,EAAO,UAAUrM,EAAS,cAAc,EAAI,IAAIqM,EAAO,EAAG,EAAG,CAAC,EACpHrM,EAAS,cACT6G,EAAgB,gBAAkB,GAClCA,EAAgB,iBAAmB,IAEnC7G,EAAS,gBACTA,EAAS,cAAc,aAAe,GACtC2D,EAAS,KAAK,KAAK,qBAAqB,GAAGrB,CAAO,iBAAkBtC,EAAS,cAAgBE,GAAY,CACrGA,EAAQ,KAAO,GAAG2G,EAAgB,IAAI,YACtCA,EAAgB,YAAc3G,CACjC,EAAC,EACF2G,EAAgB,iBAAmB,CAAC,KAAK,cAAc,qBACvDA,EAAgB,iBAAmB,KAAK,cAAc,qBAClD7G,EAAS,cAAc,OAAS,MAAa6G,EAAgB,cAC7DA,EAAgB,YAAY,MAAQ7G,EAAS,cAAc,OAE/D6G,EAAgB,0BAA4B,IAE5C7G,EAAS,mBACTA,EAAS,iBAAiB,aAAe,GACzC2D,EAAS,KAAK,KAAK,qBAAqB,GAAGrB,CAAO,oBAAqBtC,EAAS,iBAAmBE,GAAY,CAC3GA,EAAQ,KAAO,GAAG2G,EAAgB,IAAI,eACtCA,EAAgB,eAAiB3G,CACpC,EAAC,EACF2G,EAAgB,sBAAwB,GACpC7G,EAAS,iBAAiB,UAAY,OACtC6G,EAAgB,uBAAyB7G,EAAS,iBAAiB,WAGvEA,EAAS,iBACT2D,EAAS,KAAK,KAAK,qBAAqB,GAAGrB,CAAO,mBAAoBtC,EAAS,gBAAkBE,GAAY,CACzGA,EAAQ,KAAO,GAAG2G,EAAgB,IAAI,cACtCA,EAAgB,gBAAkB3G,CACrC,EAAC,EAEC,QAAQ,IAAIyD,CAAQ,EAAE,KAAK,IAAM,EAAG,CAC9C,CAQD,4BAA4BrB,EAAStC,EAAU6G,EAAiB,CAC5D,GAAI,EAAEA,aAA2BuF,GAC7B,MAAM,IAAI,MAAM,GAAG9J,CAAO,+BAA+B,EAG7D,OADkBtC,EAAS,WAAa,SACvB,CACb,IAAK,SAAyC,CAC1C6G,EAAgB,iBAAmBuF,EAAY,mBAC/CvF,EAAgB,MAAQ,EACxB,KACH,CACD,IAAK,OAAqC,CACtCA,EAAgB,iBAAmBuF,EAAY,sBAC/CvF,EAAgB,YAAc7G,EAAS,aAAe,KAAY,GAAMA,EAAS,YAC7E6G,EAAgB,gBAChBA,EAAgB,cAAc,SAAW,IAE7C,KACH,CACD,IAAK,QAAuC,CACxCA,EAAgB,iBAAmBuF,EAAY,uBAC3CvF,EAAgB,gBAChBA,EAAgB,cAAc,SAAW,GACzCA,EAAgB,0BAA4B,IAEhD,KACH,CACD,QACI,MAAM,IAAI,MAAM,GAAGvE,CAAO,8BAA8BtC,EAAS,SAAS,GAAG,CAEpF,CACJ,CAQD,qBAAqBsC,EAASiK,EAAahH,EAAS,IAAM,GAAK,CAC3D,MAAMb,EAAmB,KAAK,gCAAgCpC,EAASiK,EAAahH,CAAM,EAC1F,GAAIb,EACA,OAAOA,EAGX,GADA,KAAK,QAAQ,GAAGpC,CAAO,EAAE,EACrBiK,EAAY,UAAY,EACxB,MAAM,IAAI,MAAM,GAAGjK,CAAO,6BAA6BiK,EAAY,QAAQ,GAAG,EAElF,MAAMrM,EAAUmC,EAAU,IAAI,GAAGC,CAAO,SAAU,KAAK,MAAM,SAAUiK,EAAY,KAAK,EACxFrM,EAAQ,aAAeqM,EACvB,MAAM5F,EAAU,KAAK,kBAAkB,aAAa4F,EAAY,KAAK,GAAIrM,EAAUsM,GAAmB,CAClGA,EAAe,iBAAmBD,EAAY,UAAY,EAC1DtJ,EAAW,mBAAmBuJ,EAAgBlK,CAAO,EACrD,KAAK,QAAQ,0BAA0B,gBAAgBkK,CAAc,EACrEjH,EAAOiH,CAAc,CACjC,CAAS,EACD,YAAK,SAAQ,EACN7F,CACV,CAID,kBAAkBrE,EAASpC,EAASqF,EAAS,IAAM,GAAK,CACpD,MAAMb,EAAmB,KAAK,4BAA4BpC,EAASpC,EAASqF,CAAM,EAClF,GAAIb,EACA,OAAOA,EAEX,KAAK,QAAQ,GAAGpC,CAAO,IAAIpC,EAAQ,MAAQ,EAAE,EAAE,EAC/C,MAAM8J,EAAU9J,EAAQ,SAAW,KAAY+C,EAAW,eAAiBZ,EAAU,IAAI,GAAGC,CAAO,WAAY,KAAK,MAAM,SAAUpC,EAAQ,OAAO,EAC7IuM,EAAQpK,EAAU,IAAI,GAAGC,CAAO,UAAW,KAAK,MAAM,OAAQpC,EAAQ,MAAM,EAC5EyG,EAAU,KAAK,oBAAoBrE,EAAS0H,EAASyC,EAAOlH,EAAQ,OAAW,CAACrF,EAAQ,aAAa,YAAY,EACvH,YAAK,SAAQ,EACNyG,CACV,CAID,oBAAoBrE,EAAS0H,EAASyC,EAAOlH,EAAS,IAAM,CAAG,EAAEmH,EAAsBC,EAAe,CAClG,MAAMC,EAAc,KAAK,aAAa,aAAa5C,EAAQ,KAAK,GAAIA,CAAO,EACrErG,EAAW,IAAI,MACfkJ,EAAW,IAAIC,GACrB,KAAK,cAAc,uBAAyB,CAAC,CAAC,KAAK,gBACnD,MAAMC,EAAyB,CAC3B,SAAUH,EAAY,UACtB,QAAS,GACT,aAAcA,EAAY,aAC1B,OAAQ,IAAM,CACL,KAAK,WACNC,EAAS,QAAO,CAEvB,EACD,QAAS,CAACrP,EAASwP,IAAc,CACxB,KAAK,WACNH,EAAS,OAAO,IAAI,MAAM,GAAGvK,CAAO,KAAK0K,GAAaA,EAAU,QAAUA,EAAU,QAAUxP,GAAW,wBAAwB,EAAE,CAAC,CAE3I,EACD,SAAUiP,EAAM,UAAYQ,GAAYR,EAAM,KAAO,EAAE,EACvD,cAAeC,EACf,cAAe,CAAC,CAACC,GAAiB,KAAK,QAAQ,cAC3D,EACcH,EAAiB,IAAI9R,EAAQ,KAAM,KAAK,cAAeqS,CAAsB,EACnF,OAAAP,EAAe,iBAAmB,KAAK,gBACvC,KAAK,cAAc,uBAAyB,GAC5C7I,EAAS,KAAKkJ,EAAS,OAAO,EAC9BlJ,EAAS,KAAK,KAAK,eAAe,WAAW8I,EAAM,KAAK,GAAIA,CAAK,EAAE,KAAMpT,GAAS,CAC9E,MAAMtF,EAAO0Y,EAAM,KAAO,GAAG,KAAK,SAAS,SAASA,EAAM,KAAK,GACzDS,EAAU,QAAQ,KAAK,cAAc,GAAGnZ,CAAI,GAClDyY,EAAe,UAAUU,EAAS7T,CAAI,EAEtC,MAAM8T,EAAkBX,EAAe,qBACnCW,IACAA,EAAgB,MAAQV,EAAM,KAErC,EAAC,EACFD,EAAe,MAAQI,EAAY,MACnCJ,EAAe,MAAQI,EAAY,MACnCrH,EAAOiH,CAAc,EACjB,KAAK,QAAQ,sBACbA,EAAe,KAAOC,EAAM,MAAQA,EAAM,KAAO,QAAQA,EAAM,KAAK,IAEjE,QAAQ,IAAI9I,CAAQ,EAAE,KAAK,IACvB6I,CACV,CACJ,CACD,aAAalK,EAAS0H,EAAS,CAC3B,OAAKA,EAAQ,QACTA,EAAQ,MAAQ,CACZ,UAAWA,EAAQ,YAAc,MAAuCA,EAAQ,YAAc,KAC9F,aAAc/G,EAAW,wBAAwBX,EAAS0H,CAAO,EACjE,MAAO/G,EAAW,oBAAoB,GAAGX,CAAO,SAAU0H,EAAQ,KAAK,EACvE,MAAO/G,EAAW,oBAAoB,GAAGX,CAAO,SAAU0H,EAAQ,KAAK,CACvF,GAEeA,EAAQ,KAClB,CAOD,eAAe1H,EAASmK,EAAO,CAC3B,GAAI,CAACA,EAAM,MAAO,CAEd,GADA,KAAK,QAAQ,GAAGnK,CAAO,IAAImK,EAAM,MAAQ,EAAE,EAAE,EACzCA,EAAM,IACNA,EAAM,MAAQ,KAAK,aAAa,GAAGnK,CAAO,OAAQmK,EAAOA,EAAM,GAAG,MAEjE,CACD,MAAMxB,EAAa5I,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,MAAM,YAAamK,EAAM,UAAU,EAClGA,EAAM,MAAQ,KAAK,oBAAoB,gBAAgBxB,EAAW,KAAK,GAAIA,CAAU,CACxF,CACD,KAAK,SAAQ,CAChB,CACD,OAAOwB,EAAM,KAChB,CAQD,aAAanK,EAAS8K,EAAU3P,EAAK,CACjC,MAAMiH,EAAmB,KAAK,wBAAwBpC,EAAS8K,EAAU3P,CAAG,EAC5E,GAAIiH,EACA,OAAOA,EAEX,GAAI,CAACzB,EAAW,aAAaxF,CAAG,EAC5B,MAAM,IAAI,MAAM,GAAG6E,CAAO,MAAM7E,CAAG,cAAc,EAErD,GAAI4P,GAAgB5P,CAAG,EAAG,CACtB,MAAMpE,EAAO,IAAI,WAAWiH,GAAwB7C,CAAG,CAAC,EACxD,YAAK,IAAI,GAAG6E,CAAO,aAAa7E,EAAI,UAAU,EAAG,EAAE,CAAC,QAAQpE,EAAK,MAAM,SAAS,EACzE,QAAQ,QAAQA,CAAI,CAC9B,CACD,YAAK,IAAI,GAAGiJ,CAAO,aAAa7E,CAAG,EAAE,EAC9B,KAAK,QAAQ,mBAAmB,KAAK,SAAWA,CAAG,EAAE,KAAMsB,GACvD,IAAI,QAAQ,CAACrB,EAASC,IAAW,CACpC,KAAK,QAAQ,UAAU,KAAK,cAAeoB,EAAM1F,GAAS,CACjD,KAAK,YACN,KAAK,IAAI,GAAGiJ,CAAO,YAAY7E,CAAG,KAAKpE,EAAK,UAAU,SAAS,EAC/DqE,EAAQ,IAAI,WAAWrE,CAAI,CAAC,EAEpD,EAAmB,GAAO8F,GAAY,CAClBxB,EAAO,IAAI2P,GAAc,GAAGhL,CAAO,qBAAqB7E,CAAG,IAAI0B,EAAU,KAAOA,EAAQ,OAAS,IAAMA,EAAQ,WAAa,EAAE,GAAIA,CAAO,CAAC,CAC9J,CAAiB,CACjB,CAAa,CACJ,CACJ,CAMD,OAAO,mBAAmBoO,EAAejb,EAAS,CAC9Cib,EAAc,SAAWA,EAAc,UAAY,GACnD,MAAMC,EAAYD,EAAc,kBAAoBA,EAAc,mBAAqB,GACjFE,EAAQD,EAAS,KAAOA,EAAS,MAAQ,IAC7BC,EAAK,SAAWA,EAAK,UAAY,IAC1C,KAAKnb,CAAO,CACxB,CACD,OAAO,oBAAoBgQ,EAASoL,EAAM,CAGtC,OADAA,EAAOA,GAAoB,MACnBA,EAAI,CACR,IAAK,OACD,OAAOhT,EAAQ,kBACnB,IAAK,OACD,OAAOA,EAAQ,mBACnB,IAAK,OACD,OAAOA,EAAQ,iBACnB,QACI,OAAAnE,EAAO,KAAK,GAAG+L,CAAO,oBAAoBoL,CAAI,GAAG,EAC1ChT,EAAQ,gBACtB,CACJ,CACD,OAAO,wBAAwB4H,EAAS0H,EAAS,CAE7C,MAAM2D,EAAY3D,EAAQ,WAAa,KAAY,KAAqCA,EAAQ,UAC1F4D,EAAY5D,EAAQ,WAAa,KAAY,KAAmDA,EAAQ,UAC9G,GAAI2D,IAAc,KACd,OAAQC,EAAS,CACb,IAAK,MACD,OAAOlT,EAAQ,eACnB,IAAK,MACD,OAAOA,EAAQ,cACnB,IAAK,MACD,OAAOA,EAAQ,0BACnB,IAAK,MACD,OAAOA,EAAQ,yBACnB,IAAK,MACD,OAAOA,EAAQ,yBACnB,IAAK,MACD,OAAOA,EAAQ,wBACnB,QACI,OAAAnE,EAAO,KAAK,GAAG+L,CAAO,8BAA8BsL,CAAS,GAAG,EACzDlT,EAAQ,uBACtB,KAMD,QAHIiT,IAAc,MACdpX,EAAO,KAAK,GAAG+L,CAAO,8BAA8BqL,CAAS,GAAG,EAE5DC,EAAS,CACb,IAAK,MACD,OAAOlT,EAAQ,gBACnB,IAAK,MACD,OAAOA,EAAQ,eACnB,IAAK,MACD,OAAOA,EAAQ,2BACnB,IAAK,MACD,OAAOA,EAAQ,0BACnB,IAAK,MACD,OAAOA,EAAQ,0BACnB,IAAK,MACD,OAAOA,EAAQ,yBACnB,QACI,OAAAnE,EAAO,KAAK,GAAG+L,CAAO,8BAA8BsL,CAAS,GAAG,EACzDlT,EAAQ,0BACtB,CAER,CACD,OAAO,0BAA0B4H,EAASuL,EAAe,CACrD,GAAI,CACA,OAAOC,GAAyBD,CAAa,CAChD,OACMrP,EAAG,CACN,MAAM,IAAI,MAAM,GAAG8D,CAAO,KAAK9D,EAAE,OAAO,EAAE,CAC7C,CACJ,CACD,OAAO,eAAe8D,EAASuL,EAAe5C,EAAY1M,EAAYgD,EAAQ,CAC1E,MAAMzE,EAASmO,EAAW,OAC1B1M,EAAa0M,EAAW,YAAc1M,GAAc,GACpD,MAAM2M,EAAcjI,EAAW,0BAA0B,GAAGX,CAAO,iBAAkBuL,CAAa,EAC5FE,EAAsBvT,EAAa,kBAAkBqT,CAAa,EACxE,OAAItP,EAAawP,IAAwB,GAErCxX,EAAO,KAAK,GAAG+L,CAAO,oCAAoC/D,CAAU,sDAAsDwP,CAAmB,GAAG,EACzI,IAAI7C,EAAYpO,EAAO,MAAMyB,EAAYA,EAAagD,EAASwM,CAAmB,EAAG,CAAC,GAE1F,IAAI7C,EAAYpO,EAAQyB,EAAYgD,CAAM,CACpD,CACD,OAAO,kBAAkBe,EAASlH,EAAM,CACpC,OAAQA,EAAI,CACR,IAAK,SACD,MAAO,GACX,IAAK,OACD,MAAO,GACX,IAAK,OACD,MAAO,GACX,IAAK,OACD,MAAO,GACX,IAAK,OACD,MAAO,GACX,IAAK,OACD,MAAO,GACX,IAAK,OACD,MAAO,GACd,CACD,MAAM,IAAI,MAAM,GAAGkH,CAAO,mBAAmBlH,CAAI,GAAG,CACvD,CACD,OAAO,aAAaqC,EAAK,CACrB,OAAO5M,EAAM,SAAS4M,CAAG,GAAKA,EAAI,QAAQ,IAAI,IAAM,EACvD,CAID,OAAO,aAAa6E,EAASoL,EAAM,CAI/B,OAHIA,GAAQ,OACRA,EAAO,GAEHA,EAAI,CACR,IAAK,GACD,OAAO3J,EAAS,kBACpB,IAAK,GACD,OAAOA,EAAS,iBACpB,IAAK,GACD,OAAOA,EAAS,iBACpB,IAAK,GACD,OAAOA,EAAS,kBACpB,IAAK,GACD,OAAOA,EAAS,iBACpB,IAAK,GACD,OAAOA,EAAS,sBACpB,IAAK,GACD,OAAOA,EAAS,mBACvB,CACD,MAAM,IAAI,MAAM,GAAGzB,CAAO,kCAAkCoL,CAAI,GAAG,CACtE,CACD,wBAAyB,CACrB,KAAK,QAAQ,yBAAyB,mBAAmB,EACzD,MAAM/J,EAAW,IAAI,MACrB,GAAI,KAAK,MAAM,WACX,UAAW3D,KAAY,KAAK,MAAM,UAC9B,GAAIA,EAAS,MACT,UAAW8D,KAAmB9D,EAAS,MAAO,CAC1C,MAAMsM,EAActM,EAAS,MAAM8D,CAAe,EAClD,UAAWa,KAAe2H,EAAY,cAAe,CAEjD3H,EAAY,mBAAmB,EAAI,EACnC,MAAMkC,EAAkByF,EAAY,gBACpC3I,EAAS,KAAKkD,EAAgB,sBAAsBlC,CAAW,CAAC,EAChEhB,EAAS,KAAKkD,EAAgB,sBAAsBlC,EAAa,CAAE,aAAc,EAAM,EAAC,EACpF,KAAK,QAAQ,eACbhB,EAAS,KAAKkD,EAAgB,sBAAsBlC,EAAa,CAAE,UAAW,EAAM,EAAC,EACrFhB,EAAS,KAAKkD,EAAgB,sBAAsBlC,EAAa,CAAE,UAAW,GAAM,aAAc,EAAI,CAAE,CAAC,EAEhH,CACJ,EAIb,OAAO,QAAQ,IAAIhB,CAAQ,EAAE,KAAK,IAAM,CACpC,KAAK,QAAQ,uBAAuB,mBAAmB,CACnE,CAAS,CACJ,CACD,+BAAgC,CAC5B,KAAK,QAAQ,yBAAyB,2BAA2B,EACjE,MAAMA,EAAW,IAAI,MACfqK,EAAS,KAAK,cAAc,OAClC,UAAWC,KAASD,EAAQ,CACxB,MAAME,EAAYD,EAAM,qBACpBC,GACAvK,EAAS,KAAKuK,EAAU,sBAAuB,EAEtD,CACD,OAAO,QAAQ,IAAIvK,CAAQ,EAAE,KAAK,IAAM,CACpC,KAAK,QAAQ,uBAAuB,2BAA2B,CAC3E,CAAS,CACJ,CACD,mBAAmBxQ,EAAQ,CACvB,UAAWkQ,KAAa,KAAK,YACrBA,EAAU,SACVlQ,EAAOkQ,CAAS,CAG3B,CACD,iBAAiB+J,EAAUe,EAAcC,EAAa,CAClD,UAAW/K,KAAa,KAAK,YACzB,GAAIA,EAAU,QAAS,CACnB,MAAMpO,EAAK,GAAGoO,EAAU,IAAI,IAAI8K,CAAY,GACtCE,EAAiBjB,EACvBiB,EAAe,gCAAkCA,EAAe,iCAAmC,GACnG,MAAMC,EAAiCD,EAAe,gCACtD,GAAI,CAACC,EAA+BrZ,CAAE,EAAG,CACrCqZ,EAA+BrZ,CAAE,EAAI,GACrC,GAAI,CACA,MAAMsD,EAAS6V,EAAY/K,CAAS,EACpC,GAAI9K,EACA,OAAOA,CAEd,QACO,CACJ,OAAO+V,EAA+BrZ,CAAE,CAC3C,CACJ,CACJ,CAEL,OAAO,IACV,CACD,sBAAuB,CACnB,KAAK,mBAAoBoO,GAAcA,EAAU,WAAaA,EAAU,UAAS,CAAE,CACtF,CACD,oBAAqB,CACjB,KAAK,mBAAoBA,GAAcA,EAAU,SAAWA,EAAU,QAAO,CAAE,CAClF,CACD,0BAA0Bf,EAASrO,EAAO,CACtC,OAAO,KAAK,iBAAiBA,EAAO,YAAcoP,GAAcA,EAAU,gBAAkBA,EAAU,eAAef,EAASrO,CAAK,CAAC,CACvI,CACD,yBAAyBqO,EAASrK,EAAMsN,EAAQ,CAC5C,OAAO,KAAK,iBAAiBtN,EAAM,WAAaoL,GAAcA,EAAU,eAAiBA,EAAU,cAAcf,EAASrK,EAAMsN,CAAM,CAAC,CAC1I,CACD,2BAA2BjD,EAASpR,EAAQqU,EAAQ,CAChD,OAAO,KAAK,iBAAiBrU,EAAQ,aAAemS,GAAcA,EAAU,iBAAmBA,EAAU,gBAAgBf,EAASpR,EAAQqU,CAAM,CAAC,CACpJ,CACD,+BAA+BjD,EAASkE,EAAW7B,EAAa,CAC5D,OAAO,KAAK,iBAAiB6B,EAAW,iBAAmBnD,GAAcA,EAAU,sBAAwBA,EAAU,qBAAqBf,EAASkE,EAAW7B,CAAW,CAAC,CAC7K,CACD,kCAAkCrC,EAASvO,EAAMkE,EAAM5C,EAAMmR,EAAWjB,EAAQ,CAC5E,OAAO,KAAK,iBAAiBiB,EAAW,oBAAsBnD,GAAcA,EAAU,yBAA2BA,EAAU,wBAAwBf,EAASvO,EAAMkE,EAAM5C,EAAMmR,EAAWjB,CAAM,CAAC,CACnM,CACD,6BAA6BjD,EAAStC,EAAU2E,EAAab,EAAiByB,EAAQ,CAClF,OAAO,KAAK,iBAAiBvF,EAAU,eAAiBqD,GAAcA,EAAU,oBAAsBA,EAAU,mBAAmBf,EAAStC,EAAU2E,EAAab,EAAiByB,CAAM,CAAC,CAC9L,CACD,0BAA0BjD,EAAStC,EAAU8D,EAAiB,CAC1D,OAAO,KAAK,iBAAiB9D,EAAU,iBAAmBqD,GAAcA,EAAU,gBAAkBA,EAAU,eAAef,EAAStC,EAAU8D,CAAe,CAAC,CACnK,CACD,uCAAuCxB,EAAStC,EAAU6G,EAAiB,CACvE,OAAO,KAAK,iBAAiB7G,EAAU,yBAA2BqD,GAAcA,EAAU,6BAA+BA,EAAU,4BAA4Bf,EAAStC,EAAU6G,CAAe,CAAC,CACrM,CACD,gCAAgCvE,EAASiK,EAAahH,EAAQ,CAC1D,OAAO,KAAK,iBAAiBgH,EAAa,kBAAoBlJ,GAAcA,EAAU,sBAAwBA,EAAU,qBAAqBf,EAASiK,EAAahH,CAAM,CAAC,CAC7K,CACD,4BAA4BjD,EAASpC,EAASqF,EAAQ,CAClD,OAAO,KAAK,iBAAiBrF,EAAS,cAAgBmD,GAAcA,EAAU,mBAAqBA,EAAU,kBAAkBf,EAASpC,EAASqF,CAAM,CAAC,CAC3J,CACD,8BAA8BjD,EAASxJ,EAAW,CAC9C,OAAO,KAAK,iBAAiBA,EAAW,gBAAkBuK,GAAcA,EAAU,oBAAsBA,EAAU,mBAAmBf,EAASxJ,CAAS,CAAC,CAC3J,CACD,qCAAqCwJ,EAASgH,EAAkBxQ,EAAW+O,EAAS0B,EAAQ,CACxF,OAAO,KAAK,iBAAiBzQ,EAAW,uBAAyBuK,GAAcA,EAAU,4BAA8BA,EAAU,2BAA2Bf,EAASgH,EAAkBxQ,EAAW+O,EAAS0B,CAAM,CAAC,CACrN,CACD,yBAAyBjH,EAASrK,EAAMkN,EAAM,CAC1C,OAAO,KAAK,iBAAiBA,EAAM,WAAa9B,GAAcA,EAAU,gBAAkBA,EAAU,eAAef,EAASrK,EAAMkN,CAAI,CAAC,CAC1I,CACD,wBAAwB7C,EAAS8K,EAAU3P,EAAK,CAC5C,OAAO,KAAK,iBAAiB2P,EAAU,UAAY/J,GAAcA,EAAU,eAAiBA,EAAU,cAAcf,EAAS8K,EAAU3P,CAAG,CAAC,CAC9I,CACD,+BAA+B6E,EAAS2I,EAAY,CAChD,OAAO,KAAK,iBAAiBA,EAAY,iBAAmB5H,GAAcA,EAAU,qBAAuBA,EAAU,oBAAoBf,EAAS2I,CAAU,CAAC,CAChK,CACD,2BAA2B3I,EAASxF,EAAQyB,EAAYxB,EAAY,CAChE,OAAO,KAAK,iBAAiBD,EAAQ,aAAeuG,GAAcA,EAAU,iBAAmBA,EAAU,gBAAgBf,EAASxF,EAAQyB,EAAYxB,CAAU,CAAC,CACpK,CASD,OAAO,mBAAmBuF,EAAS8K,EAAUmB,EAAeH,EAAa,CACrE,GAAI,CAAChB,EAAS,WACV,OAAO,KAGX,MAAM/J,EADa+J,EAAS,WACCmB,CAAa,EAC1C,OAAKlL,EAGE+K,EAAY,GAAG9L,CAAO,eAAeiM,CAAa,GAAIlL,CAAS,EAF3D,IAGd,CASD,OAAO,eAAef,EAAS8K,EAAUmB,EAAeH,EAAa,CACjE,GAAI,CAAChB,EAAS,OACV,OAAO,KAGX,MAAMoB,EADSpB,EAAS,OACHmB,CAAa,EAClC,OAAKC,EAGEJ,EAAY,GAAG9L,CAAO,WAAWiM,CAAa,GAAIC,CAAK,EAFnD,IAGd,CAMD,gBAAgBza,EAAM,CAClB,MAAO,CAAC,CAAC,KAAK,MAAM,gBAAkB,KAAK,MAAM,eAAe,QAAQA,CAAI,IAAM,EACrF,CAKD,QAAQyJ,EAAS,CACb,KAAK,QAAQ,SAASA,CAAO,CAChC,CAID,UAAW,CACP,KAAK,QAAQ,WAChB,CAKD,IAAIA,EAAS,CACT,KAAK,QAAQ,KAAKA,CAAO,CAC5B,CAKD,wBAAwB2E,EAAa,CACjC,KAAK,QAAQ,yBAAyBA,CAAW,CACpD,CAKD,sBAAsBA,EAAa,CAC/B,KAAK,QAAQ,uBAAuBA,CAAW,CAClD,CACL,CAIAc,EAAW,eAAiB,CAAE,MAAO,EAAE,EACvCjE,EAAe,mBAAsBxG,GAAW,IAAIyK,EAAWzK,CAAM", "names": ["FreeCameraKeyboardMoveInput", "noPreventDefault", "Tools", "info", "evt", "KeyboardEventTypes", "index", "camera", "keyCode", "speed", "Vector3", "handednessMultiplier", "__decorate", "serialize", "CameraInputTypes", "FreeCameraMouseInput", "touchEnabled", "Observable", "engine", "element", "p", "is<PERSON><PERSON>ch", "PointerEventTypes", "srcElement", "offsetX", "offsetY", "BaseCameraMouseWheelInput", "pointer", "event", "platformScale", "EventConstants", "_CameraProperty", "FreeCameraMouseWheelInput", "axis", "cameraTransformMatrix", "Matrix", "transformedDirection", "value", "cameraProperty", "coordinate", "action", "FreeCameraTouchInput", "allowMouse", "previousPosition", "isMouseEvent", "direction", "FreeCameraInputsManager", "CameraInputsManager", "FreeCamera", "TargetCamera", "mouse", "keyboard", "name", "position", "scene", "setActiveOnSceneIfNoneActive", "collisionId", "newPosition", "<PERSON><PERSON><PERSON>", "AbstractEngine", "ignored", "Vector2", "mask", "displacement", "globalPosition", "coordinator", "actualDisplacement", "serializeAsVector3", "RegisterClass", "Skeleton", "id", "EngineStore", "engineCaps", "b", "mesh", "fullDetails", "ret", "first", "boneIndex", "cache", "from", "to", "AnimationRange", "i", "nBones", "deleteFrames", "animationRanges", "source", "rescaleAsRequired", "frameOffset", "boneDict", "sourceBones", "<PERSON><PERSON>", "skelDimensionsRatio", "boneName", "sourceBone", "range", "bone", "highest", "loop", "speedRatio", "onAnimationEnd", "skeleton", "referenceFrame", "rangeValue", "sceneAnimatables", "rangeAnimatable", "sceneAnimatable", "animatables", "animations", "animIndex", "Animation", "targetMatrix", "initialSkinMatrix", "parentBone", "mappedIndex", "dontCheckFrameId", "currentRenderId", "node", "poseMatrix", "needsUpdate", "TmpVectors", "textureWidth", "RawTexture", "result", "parent", "parentIndex", "Bone", "DeepCopier", "rangeName", "blendingSpeed", "animation", "serializationObject", "serializedBone", "parsedSkeleton", "parsedBone", "parsedBoneIndex", "rest", "data", "forceUpdate", "bones", "visited", "MorphTarget", "influence", "previous", "hadPositions", "hadNormals", "hadTangents", "hadUVs", "hadUV2s", "hadColors", "newOne", "SerializationHelper", "animationIndex", "parsedAnimation", "internalClass", "GetClass", "VertexBuffer", "RawTexture2DArray", "Texture", "width", "height", "depth", "format", "generateMipMaps", "invertY", "samplingMode", "textureType", "creationFlags", "type", "MorphTargetManager", "block", "SmartArray", "target", "needUpdate", "effect", "copy", "wasUsingTextureForTargets", "isUsingTextureForTargets", "influenceCount", "targetIndex", "vertexCount", "maxTextureSize", "targetCount", "offset", "positions", "normals", "uvs", "tangents", "uv2s", "colors", "vertex", "morph", "targetData", "DataReader", "buffer", "byteLength", "Decode", "validateAsync", "rootUrl", "fileName", "getExternalResource", "options", "workerFunc", "pendingExternalResources", "message", "uri", "resolve", "reject", "reason", "GLTFValidation", "workerContent", "workerBlobUrl", "worker", "onError", "error", "onMessage", "slicedData", "readAsync", "arrayBuffer", "byteOffset", "e", "readViewAsync", "arrayBufferView", "GLTFLoaderCoordinateSystemMode", "GLTFLoaderAnimationStartMode", "GLTFLoaderState", "GLTFLoaderOptions", "url", "GLTFFileLoader", "GLTFFileLoaderMetadata", "callback", "request", "fileOrUrl", "onSuccess", "onProgress", "useArrayBuffer", "fileRequest", "dataBuffer", "webRequest", "loaderData", "meshesNames", "container", "<PERSON>setC<PERSON><PERSON>", "materials", "material", "textures", "texture", "cameras", "morphTargetManagers", "GLTFMagicBase64Encoded", "DecodeBase64UrlToBinary", "state", "onOpened", "lengthComputable", "loaded", "total", "asset", "version", "minVersion", "createLoader", "json", "parsed", "dataReader", "Binary", "magic", "RuntimeError", "ErrorCodes", "length", "unpacked", "ContentFormat", "contentLength", "contentFormat", "<PERSON><PERSON><PERSON><PERSON>", "startByteOffset", "ChunkFormat", "chunkLength", "match", "a", "spaces", "counterName", "RegisterSceneLoaderPlugin", "ArrayItem", "context", "array", "LoadBoundingInfoFromPositionAccessor", "accessor", "minArray", "max<PERSON><PERSON><PERSON>", "minVector", "maxVector", "divider", "oneOverDivider", "BoundingInfo", "GLTFLoader", "factory", "registerGLTFExtension", "unregisterGLTFExtension", "extension", "nodes", "nodeMap", "resultFunc", "loadingToReadyCounterName", "loadingToCompleteCounterName", "promises", "oldBlockMaterialDirtyMechanism", "m", "babylonDrawMode", "Material", "mat", "buffers", "binaryBuffer", "nodeParents", "rootNode", "extensionPromises", "registeredGLTFExtensions", "registeredExtension", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "extensionPromise", "<PERSON><PERSON><PERSON><PERSON>", "geometries", "geometry", "meshes", "AbstractMesh", "transformNodes", "skeletons", "skins", "skin", "animationGroups", "babylonAnimationGroups", "babylonAnimationGroup", "assign", "loadNode", "babylonTransformNode", "babylonCamera", "childNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "nodeName", "transformNode", "TransformNode", "babylonTransformNodeForSkin", "deepMerge", "babylonSkeleton", "parentNode", "<PERSON><PERSON><PERSON>", "primitives", "primitive", "shouldInstance", "babylonAbstractMesh", "promise", "babylonGeometry", "babylonMaterial", "attributes", "Geometry", "loadAttribute", "kind", "babylonVertexBuffer", "babylonBoundingInfo", "targetNames", "weight", "morphTargetManager", "babylonMorphTarget", "attribute", "setData", "dataIndex", "componentSize", "pixid", "channel", "babylonNode", "rotation", "Quaternion", "scaling", "skeletonId", "inverseBindMatricesData", "isParent", "skeletonNode", "babylonBones", "joints", "paths", "path", "j", "babylonBone", "parentBabylonBone", "baseMatrix", "babylonParentBone", "perspective", "Camera", "animationGroup", "__vitePreload", "AnimationGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "babylonAnimation", "animationContext", "onLoad", "targetNode", "channelTargetPath", "pathIsWeights", "properties", "GetMappingForKey", "targetInfo", "fps", "invfps", "sampler", "numAnimations", "propertyInfos", "propertyInfo", "stride", "input", "output", "keys", "outputOffset", "inTangent", "outTangent", "babylonAnimations", "interpolation", "inputAccessor", "outputAccessor", "inputData", "outputData", "bufferView", "constructor", "numComponents", "byteStride", "typedArray", "sparse", "indicesBufferView", "valuesBufferView", "indicesData", "valuesData", "indices", "<PERSON><PERSON><PERSON><PERSON>", "values", "sparseData", "valuesIndex", "indicesIndex", "componentIndex", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PBRMaterial", "Color3", "babylonData", "textureInfo", "babylonTexture", "image", "textureLoaderOptions", "useSRGBBuffer", "samplerData", "deferred", "Deferred", "textureCreationOptions", "exception", "GetMimeType", "dataUrl", "internalTexture", "property", "IsBase64DataUrl", "LoadFileError", "babylonObject", "metadata", "gltf", "mode", "magFilter", "minFilter", "componentType", "GetTypedArrayConstructor", "componentTypeLength", "lights", "light", "generator", "functionName", "actionAsync", "loaderProperty", "activeLoaderExtensionFunctions", "extensionName", "extra"], "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/Inputs/freeCameraKeyboardMoveInput.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/Inputs/freeCameraMouseInput.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/Inputs/BaseCameraMouseWheelInput.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/Inputs/freeCameraMouseWheelInput.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/Inputs/freeCameraTouchInput.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/freeCameraInputsManager.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Cameras/freeCamera.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Bones/skeleton.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Morph/morphTarget.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/rawTexture2DArray.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Morph/morphTargetManager.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/dataReader.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/glTFValidation.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/glTFFileLoader.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/glTFLoader.js"], "sourcesContent": ["import { __decorate } from \"../../tslib.es6.js\";\nimport { serialize } from \"../../Misc/decorators.js\";\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager.js\";\nimport { KeyboardEventTypes } from \"../../Events/keyboardEvents.js\";\nimport { Vector3 } from \"../../Maths/math.vector.js\";\nimport { Tools } from \"../../Misc/tools.js\";\n/**\n * Manage the keyboard inputs to control the movement of a free camera.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n */\nexport class FreeCameraKeyboardMoveInput {\n    constructor() {\n        /**\n         * Gets or Set the list of keyboard keys used to control the forward move of the camera.\n         */\n        this.keysUp = [38];\n        /**\n         * Gets or Set the list of keyboard keys used to control the upward move of the camera.\n         */\n        this.keysUpward = [33];\n        /**\n         * Gets or Set the list of keyboard keys used to control the backward move of the camera.\n         */\n        this.keysDown = [40];\n        /**\n         * Gets or Set the list of keyboard keys used to control the downward move of the camera.\n         */\n        this.keysDownward = [34];\n        /**\n         * Gets or Set the list of keyboard keys used to control the left strafe move of the camera.\n         */\n        this.keysLeft = [37];\n        /**\n         * Gets or Set the list of keyboard keys used to control the right strafe move of the camera.\n         */\n        this.keysRight = [39];\n        /**\n         * Defines the pointer angular sensibility  along the X and Y axis or how fast is the camera rotating.\n         */\n        this.rotationSpeed = 0.5;\n        /**\n         * Gets or Set the list of keyboard keys used to control the left rotation move of the camera.\n         */\n        this.keysRotateLeft = [];\n        /**\n         * Gets or Set the list of keyboard keys used to control the right rotation move of the camera.\n         */\n        this.keysRotateRight = [];\n        /**\n         * Gets or Set the list of keyboard keys used to control the up rotation move of the camera.\n         */\n        this.keysRotateUp = [];\n        /**\n         * Gets or Set the list of keyboard keys used to control the down rotation move of the camera.\n         */\n        this.keysRotateDown = [];\n        this._keys = new Array();\n    }\n    /**\n     * Attach the input controls to a specific dom element to get the input from.\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\n     */\n    attachControl(noPreventDefault) {\n        // eslint-disable-next-line prefer-rest-params\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\n        if (this._onCanvasBlurObserver) {\n            return;\n        }\n        this._scene = this.camera.getScene();\n        this._engine = this._scene.getEngine();\n        this._onCanvasBlurObserver = this._engine.onCanvasBlurObservable.add(() => {\n            this._keys.length = 0;\n        });\n        this._onKeyboardObserver = this._scene.onKeyboardObservable.add((info) => {\n            const evt = info.event;\n            if (!evt.metaKey) {\n                if (info.type === KeyboardEventTypes.KEYDOWN) {\n                    if (this.keysUp.indexOf(evt.keyCode) !== -1 ||\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\n                        this.keysUpward.indexOf(evt.keyCode) !== -1 ||\n                        this.keysDownward.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateLeft.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateRight.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateUp.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateDown.indexOf(evt.keyCode) !== -1) {\n                        const index = this._keys.indexOf(evt.keyCode);\n                        if (index === -1) {\n                            this._keys.push(evt.keyCode);\n                        }\n                        if (!noPreventDefault) {\n                            evt.preventDefault();\n                        }\n                    }\n                }\n                else {\n                    if (this.keysUp.indexOf(evt.keyCode) !== -1 ||\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\n                        this.keysUpward.indexOf(evt.keyCode) !== -1 ||\n                        this.keysDownward.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateLeft.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateRight.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateUp.indexOf(evt.keyCode) !== -1 ||\n                        this.keysRotateDown.indexOf(evt.keyCode) !== -1) {\n                        const index = this._keys.indexOf(evt.keyCode);\n                        if (index >= 0) {\n                            this._keys.splice(index, 1);\n                        }\n                        if (!noPreventDefault) {\n                            evt.preventDefault();\n                        }\n                    }\n                }\n            }\n        });\n    }\n    /**\n     * Detach the current controls from the specified dom element.\n     */\n    detachControl() {\n        if (this._scene) {\n            if (this._onKeyboardObserver) {\n                this._scene.onKeyboardObservable.remove(this._onKeyboardObserver);\n            }\n            if (this._onCanvasBlurObserver) {\n                this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver);\n            }\n            this._onKeyboardObserver = null;\n            this._onCanvasBlurObserver = null;\n        }\n        this._keys.length = 0;\n    }\n    /**\n     * Update the current camera state depending on the inputs that have been used this frame.\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\n     */\n    checkInputs() {\n        if (this._onKeyboardObserver) {\n            const camera = this.camera;\n            // Keyboard\n            for (let index = 0; index < this._keys.length; index++) {\n                const keyCode = this._keys[index];\n                const speed = camera._computeLocalCameraSpeed();\n                if (this.keysLeft.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(-speed, 0, 0);\n                }\n                else if (this.keysUp.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, speed);\n                }\n                else if (this.keysRight.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(speed, 0, 0);\n                }\n                else if (this.keysDown.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, -speed);\n                }\n                else if (this.keysUpward.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, speed, 0);\n                }\n                else if (this.keysDownward.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, -speed, 0);\n                }\n                else if (this.keysRotateLeft.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, 0);\n                    camera.cameraRotation.y -= this._getLocalRotation();\n                }\n                else if (this.keysRotateRight.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, 0);\n                    camera.cameraRotation.y += this._getLocalRotation();\n                }\n                else if (this.keysRotateUp.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, 0);\n                    camera.cameraRotation.x -= this._getLocalRotation();\n                }\n                else if (this.keysRotateDown.indexOf(keyCode) !== -1) {\n                    camera._localDirection.copyFromFloats(0, 0, 0);\n                    camera.cameraRotation.x += this._getLocalRotation();\n                }\n                if (camera.getScene().useRightHandedSystem) {\n                    camera._localDirection.z *= -1;\n                }\n                camera.getViewMatrix().invertToRef(camera._cameraTransformMatrix);\n                Vector3.TransformNormalToRef(camera._localDirection, camera._cameraTransformMatrix, camera._transformedDirection);\n                camera.cameraDirection.addInPlace(camera._transformedDirection);\n            }\n        }\n    }\n    /**\n     * Gets the class name of the current input.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FreeCameraKeyboardMoveInput\";\n    }\n    /** @internal */\n    _onLostFocus() {\n        this._keys.length = 0;\n    }\n    /**\n     * Get the friendly name associated with the input class.\n     * @returns the input friendly name\n     */\n    getSimpleName() {\n        return \"keyboard\";\n    }\n    _getLocalRotation() {\n        const handednessMultiplier = this.camera._calculateHandednessMultiplier();\n        const rotation = ((this.rotationSpeed * this._engine.getDeltaTime()) / 1000) * handednessMultiplier;\n        return rotation;\n    }\n}\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysUp\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysUpward\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysDown\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysDownward\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysLeft\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysRight\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"rotationSpeed\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysRotateLeft\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysRotateRight\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysRotateUp\", void 0);\n__decorate([\n    serialize()\n], FreeCameraKeyboardMoveInput.prototype, \"keysRotateDown\", void 0);\nCameraInputTypes[\"FreeCameraKeyboardMoveInput\"] = FreeCameraKeyboardMoveInput;\n//# sourceMappingURL=freeCameraKeyboardMoveInput.js.map", "import { __decorate } from \"../../tslib.es6.js\";\nimport { Observable } from \"../../Misc/observable.js\";\nimport { serialize } from \"../../Misc/decorators.js\";\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager.js\";\nimport { PointerEventTypes } from \"../../Events/pointerEvents.js\";\nimport { Tools } from \"../../Misc/tools.js\";\n/**\n * Manage the mouse inputs to control the movement of a free camera.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n */\nexport class FreeCameraMouseInput {\n    /**\n     * Manage the mouse inputs to control the movement of a free camera.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n     * @param touchEnabled Defines if touch is enabled or not\n     */\n    constructor(\n    /**\n     * [true] Define if touch is enabled in the mouse input\n     */\n    touchEnabled = true) {\n        this.touchEnabled = touchEnabled;\n        /**\n         * Defines the buttons associated with the input to handle camera move.\n         */\n        this.buttons = [0, 1, 2];\n        /**\n         * Defines the pointer angular sensibility  along the X and Y axis or how fast is the camera rotating.\n         */\n        this.angularSensibility = 2000.0;\n        this._previousPosition = null;\n        /**\n         * Observable for when a pointer move event occurs containing the move offset\n         */\n        this.onPointerMovedObservable = new Observable();\n        /**\n         * @internal\n         * If the camera should be rotated automatically based on pointer movement\n         */\n        this._allowCameraRotation = true;\n        this._currentActiveButton = -1;\n        this._activePointerId = -1;\n    }\n    /**\n     * Attach the input controls to a specific dom element to get the input from.\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\n     */\n    attachControl(noPreventDefault) {\n        // eslint-disable-next-line prefer-rest-params\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\n        const engine = this.camera.getEngine();\n        const element = engine.getInputElement();\n        if (!this._pointerInput) {\n            this._pointerInput = (p) => {\n                const evt = p.event;\n                const isTouch = evt.pointerType === \"touch\";\n                if (!this.touchEnabled && isTouch) {\n                    return;\n                }\n                if (p.type !== PointerEventTypes.POINTERMOVE && this.buttons.indexOf(evt.button) === -1) {\n                    return;\n                }\n                const srcElement = evt.target;\n                if (p.type === PointerEventTypes.POINTERDOWN) {\n                    // If the input is touch with more than one touch OR if the input is mouse and there is already an active button, return\n                    if ((isTouch && this._activePointerId !== -1) || (!isTouch && this._currentActiveButton !== -1)) {\n                        return;\n                    }\n                    this._activePointerId = evt.pointerId;\n                    try {\n                        srcElement?.setPointerCapture(evt.pointerId);\n                    }\n                    catch (e) {\n                        //Nothing to do with the error. Execution will continue.\n                    }\n                    if (this._currentActiveButton === -1) {\n                        this._currentActiveButton = evt.button;\n                    }\n                    this._previousPosition = {\n                        x: evt.clientX,\n                        y: evt.clientY,\n                    };\n                    if (!noPreventDefault) {\n                        evt.preventDefault();\n                        element && element.focus();\n                    }\n                    // This is required to move while pointer button is down\n                    if (engine.isPointerLock && this._onMouseMove) {\n                        this._onMouseMove(p.event);\n                    }\n                }\n                else if (p.type === PointerEventTypes.POINTERUP) {\n                    // If input is touch with a different touch id OR if input is mouse with a different button, return\n                    if ((isTouch && this._activePointerId !== evt.pointerId) || (!isTouch && this._currentActiveButton !== evt.button)) {\n                        return;\n                    }\n                    try {\n                        srcElement?.releasePointerCapture(evt.pointerId);\n                    }\n                    catch (e) {\n                        //Nothing to do with the error.\n                    }\n                    this._currentActiveButton = -1;\n                    this._previousPosition = null;\n                    if (!noPreventDefault) {\n                        evt.preventDefault();\n                    }\n                    this._activePointerId = -1;\n                }\n                else if (p.type === PointerEventTypes.POINTERMOVE && (this._activePointerId === evt.pointerId || !isTouch)) {\n                    if (engine.isPointerLock && this._onMouseMove) {\n                        this._onMouseMove(p.event);\n                    }\n                    else if (this._previousPosition) {\n                        const handednessMultiplier = this.camera._calculateHandednessMultiplier();\n                        const offsetX = (evt.clientX - this._previousPosition.x) * handednessMultiplier;\n                        const offsetY = evt.clientY - this._previousPosition.y;\n                        if (this._allowCameraRotation) {\n                            this.camera.cameraRotation.y += offsetX / this.angularSensibility;\n                            this.camera.cameraRotation.x += offsetY / this.angularSensibility;\n                        }\n                        this.onPointerMovedObservable.notifyObservers({ offsetX: offsetX, offsetY: offsetY });\n                        this._previousPosition = {\n                            x: evt.clientX,\n                            y: evt.clientY,\n                        };\n                        if (!noPreventDefault) {\n                            evt.preventDefault();\n                        }\n                    }\n                }\n            };\n        }\n        this._onMouseMove = (evt) => {\n            if (!engine.isPointerLock) {\n                return;\n            }\n            const handednessMultiplier = this.camera._calculateHandednessMultiplier();\n            const offsetX = evt.movementX * handednessMultiplier;\n            this.camera.cameraRotation.y += offsetX / this.angularSensibility;\n            const offsetY = evt.movementY;\n            this.camera.cameraRotation.x += offsetY / this.angularSensibility;\n            this._previousPosition = null;\n            if (!noPreventDefault) {\n                evt.preventDefault();\n            }\n        };\n        this._observer = this.camera\n            .getScene()\n            ._inputManager._addCameraPointerObserver(this._pointerInput, PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE);\n        if (element) {\n            this._contextMenuBind = (evt) => this.onContextMenu(evt);\n            element.addEventListener(\"contextmenu\", this._contextMenuBind, false); // TODO: We need to figure out how to handle this for Native\n        }\n    }\n    /**\n     * Called on JS contextmenu event.\n     * Override this method to provide functionality.\n     * @param evt the context menu event\n     */\n    onContextMenu(evt) {\n        evt.preventDefault();\n    }\n    /**\n     * Detach the current controls from the specified dom element.\n     */\n    detachControl() {\n        if (this._observer) {\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\n            if (this._contextMenuBind) {\n                const engine = this.camera.getEngine();\n                const element = engine.getInputElement();\n                element && element.removeEventListener(\"contextmenu\", this._contextMenuBind);\n            }\n            if (this.onPointerMovedObservable) {\n                this.onPointerMovedObservable.clear();\n            }\n            this._observer = null;\n            this._onMouseMove = null;\n            this._previousPosition = null;\n        }\n        this._activePointerId = -1;\n        this._currentActiveButton = -1;\n    }\n    /**\n     * Gets the class name of the current input.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FreeCameraMouseInput\";\n    }\n    /**\n     * Get the friendly name associated with the input class.\n     * @returns the input friendly name\n     */\n    getSimpleName() {\n        return \"mouse\";\n    }\n}\n__decorate([\n    serialize()\n], FreeCameraMouseInput.prototype, \"buttons\", void 0);\n__decorate([\n    serialize()\n], FreeCameraMouseInput.prototype, \"angularSensibility\", void 0);\nCameraInputTypes[\"FreeCameraMouseInput\"] = FreeCameraMouseInput;\n//# sourceMappingURL=freeCameraMouseInput.js.map", "import { __decorate } from \"../../tslib.es6.js\";\nimport { serialize } from \"../../Misc/decorators.js\";\nimport { Observable } from \"../../Misc/observable.js\";\nimport { PointerEventTypes } from \"../../Events/pointerEvents.js\";\nimport { EventConstants } from \"../../Events/deviceInputEvents.js\";\nimport { Tools } from \"../../Misc/tools.js\";\n/**\n * Base class for mouse wheel input..\n * See FollowCameraMouseWheelInput in src/Cameras/Inputs/freeCameraMouseWheelInput.ts\n * for example usage.\n */\nexport class BaseCameraMouseWheelInput {\n    constructor() {\n        /**\n         * How fast is the camera moves in relation to X axis mouseWheel events.\n         * Use negative value to reverse direction.\n         */\n        this.wheelPrecisionX = 3.0;\n        /**\n         * How fast is the camera moves in relation to Y axis mouseWheel events.\n         * Use negative value to reverse direction.\n         */\n        this.wheelPrecisionY = 3.0;\n        /**\n         * How fast is the camera moves in relation to Z axis mouseWheel events.\n         * Use negative value to reverse direction.\n         */\n        this.wheelPrecisionZ = 3.0;\n        /**\n         * Observable for when a mouse wheel move event occurs.\n         */\n        this.onChangedObservable = new Observable();\n        /**\n         * Incremental value of multiple mouse wheel movements of the X axis.\n         * Should be zero-ed when read.\n         */\n        this._wheelDeltaX = 0;\n        /**\n         * Incremental value of multiple mouse wheel movements of the Y axis.\n         * Should be zero-ed when read.\n         */\n        this._wheelDeltaY = 0;\n        /**\n         * Incremental value of multiple mouse wheel movements of the Z axis.\n         * Should be zero-ed when read.\n         */\n        this._wheelDeltaZ = 0;\n        /**\n         * Firefox uses a different scheme to report scroll distances to other\n         * browsers. Rather than use complicated methods to calculate the exact\n         * multiple we need to apply, let's just cheat and use a constant.\n         * https://developer.mozilla.org/en-US/docs/Web/API/WheelEvent/deltaMode\n         * https://stackoverflow.com/questions/20110224/what-is-the-height-of-a-line-in-a-wheel-event-deltamode-dom-delta-line\n         */\n        this._ffMultiplier = 12;\n        /**\n         * Different event attributes for wheel data fall into a few set ranges.\n         * Some relevant but dated date here:\n         * https://stackoverflow.com/questions/5527601/normalizing-mousewheel-speed-across-browsers\n         */\n        this._normalize = 120;\n    }\n    /**\n     * Attach the input controls to a specific dom element to get the input from.\n     * @param noPreventDefault Defines whether event caught by the controls\n     *   should call preventdefault().\n     *   (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\n     */\n    attachControl(noPreventDefault) {\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\n        this._wheel = (pointer) => {\n            // sanity check - this should be a PointerWheel event.\n            if (pointer.type !== PointerEventTypes.POINTERWHEEL) {\n                return;\n            }\n            const event = pointer.event;\n            const platformScale = event.deltaMode === EventConstants.DOM_DELTA_LINE ? this._ffMultiplier : 1; // If this happens to be set to DOM_DELTA_LINE, adjust accordingly\n            this._wheelDeltaX += (this.wheelPrecisionX * platformScale * event.deltaX) / this._normalize;\n            this._wheelDeltaY -= (this.wheelPrecisionY * platformScale * event.deltaY) / this._normalize;\n            this._wheelDeltaZ += (this.wheelPrecisionZ * platformScale * event.deltaZ) / this._normalize;\n            if (event.preventDefault) {\n                if (!noPreventDefault) {\n                    event.preventDefault();\n                }\n            }\n        };\n        this._observer = this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel, PointerEventTypes.POINTERWHEEL);\n    }\n    /**\n     * Detach the current controls from the specified dom element.\n     */\n    detachControl() {\n        if (this._observer) {\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\n            this._observer = null;\n            this._wheel = null;\n        }\n        if (this.onChangedObservable) {\n            this.onChangedObservable.clear();\n        }\n    }\n    /**\n     * Called for each rendered frame.\n     */\n    checkInputs() {\n        this.onChangedObservable.notifyObservers({\n            wheelDeltaX: this._wheelDeltaX,\n            wheelDeltaY: this._wheelDeltaY,\n            wheelDeltaZ: this._wheelDeltaZ,\n        });\n        // Clear deltas.\n        this._wheelDeltaX = 0;\n        this._wheelDeltaY = 0;\n        this._wheelDeltaZ = 0;\n    }\n    /**\n     * Gets the class name of the current input.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"BaseCameraMouseWheelInput\";\n    }\n    /**\n     * Get the friendly name associated with the input class.\n     * @returns the input friendly name\n     */\n    getSimpleName() {\n        return \"mousewheel\";\n    }\n}\n__decorate([\n    serialize()\n], BaseCameraMouseWheelInput.prototype, \"wheelPrecisionX\", void 0);\n__decorate([\n    serialize()\n], BaseCameraMouseWheelInput.prototype, \"wheelPrecisionY\", void 0);\n__decorate([\n    serialize()\n], BaseCameraMouseWheelInput.prototype, \"wheelPrecisionZ\", void 0);\n//# sourceMappingURL=BaseCameraMouseWheelInput.js.map", "import { __decorate } from \"../../tslib.es6.js\";\nimport { serialize } from \"../../Misc/decorators.js\";\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager.js\";\nimport { BaseCameraMouseWheelInput } from \"../../Cameras/Inputs/BaseCameraMouseWheelInput.js\";\nimport { Matrix, Vector3 } from \"../../Maths/math.vector.js\";\n// eslint-disable-next-line @typescript-eslint/naming-convention\nvar _CameraProperty;\n(function (_CameraProperty) {\n    _CameraProperty[_CameraProperty[\"MoveRelative\"] = 0] = \"MoveRelative\";\n    _CameraProperty[_CameraProperty[\"RotateRelative\"] = 1] = \"RotateRelative\";\n    _CameraProperty[_CameraProperty[\"MoveScene\"] = 2] = \"MoveScene\";\n})(_CameraProperty || (_CameraProperty = {}));\n/**\n * Manage the mouse wheel inputs to control a free camera.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n */\nexport class FreeCameraMouseWheelInput extends BaseCameraMouseWheelInput {\n    constructor() {\n        super(...arguments);\n        this._moveRelative = Vector3.Zero();\n        this._rotateRelative = Vector3.Zero();\n        this._moveScene = Vector3.Zero();\n        /**\n         * These are set to the desired default behaviour.\n         */\n        this._wheelXAction = _CameraProperty.MoveRelative;\n        this._wheelXActionCoordinate = 0 /* Coordinate.X */;\n        this._wheelYAction = _CameraProperty.MoveRelative;\n        this._wheelYActionCoordinate = 2 /* Coordinate.Z */;\n        this._wheelZAction = null;\n        this._wheelZActionCoordinate = null;\n    }\n    /**\n     * Gets the class name of the current input.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FreeCameraMouseWheelInput\";\n    }\n    /**\n     * Set which movement axis (relative to camera's orientation) the mouse\n     * wheel's X axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelXMoveRelative(axis) {\n        if (axis === null && this._wheelXAction !== _CameraProperty.MoveRelative) {\n            // Attempting to clear different _wheelXAction.\n            return;\n        }\n        this._wheelXAction = _CameraProperty.MoveRelative;\n        this._wheelXActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to camera's orientation) the\n     * mouse wheel's X axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelXMoveRelative() {\n        if (this._wheelXAction !== _CameraProperty.MoveRelative) {\n            return null;\n        }\n        return this._wheelXActionCoordinate;\n    }\n    /**\n     * Set which movement axis (relative to camera's orientation) the mouse\n     * wheel's Y axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelYMoveRelative(axis) {\n        if (axis === null && this._wheelYAction !== _CameraProperty.MoveRelative) {\n            // Attempting to clear different _wheelYAction.\n            return;\n        }\n        this._wheelYAction = _CameraProperty.MoveRelative;\n        this._wheelYActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to camera's orientation) the\n     * mouse wheel's Y axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelYMoveRelative() {\n        if (this._wheelYAction !== _CameraProperty.MoveRelative) {\n            return null;\n        }\n        return this._wheelYActionCoordinate;\n    }\n    /**\n     * Set which movement axis (relative to camera's orientation) the mouse\n     * wheel's Z axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelZMoveRelative(axis) {\n        if (axis === null && this._wheelZAction !== _CameraProperty.MoveRelative) {\n            // Attempting to clear different _wheelZAction.\n            return;\n        }\n        this._wheelZAction = _CameraProperty.MoveRelative;\n        this._wheelZActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to camera's orientation) the\n     * mouse wheel's Z axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelZMoveRelative() {\n        if (this._wheelZAction !== _CameraProperty.MoveRelative) {\n            return null;\n        }\n        return this._wheelZActionCoordinate;\n    }\n    /**\n     * Set which rotation axis (relative to camera's orientation) the mouse\n     * wheel's X axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelXRotateRelative(axis) {\n        if (axis === null && this._wheelXAction !== _CameraProperty.RotateRelative) {\n            // Attempting to clear different _wheelXAction.\n            return;\n        }\n        this._wheelXAction = _CameraProperty.RotateRelative;\n        this._wheelXActionCoordinate = axis;\n    }\n    /**\n     * Get the configured rotation axis (relative to camera's orientation) the\n     * mouse wheel's X axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelXRotateRelative() {\n        if (this._wheelXAction !== _CameraProperty.RotateRelative) {\n            return null;\n        }\n        return this._wheelXActionCoordinate;\n    }\n    /**\n     * Set which rotation axis (relative to camera's orientation) the mouse\n     * wheel's Y axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelYRotateRelative(axis) {\n        if (axis === null && this._wheelYAction !== _CameraProperty.RotateRelative) {\n            // Attempting to clear different _wheelYAction.\n            return;\n        }\n        this._wheelYAction = _CameraProperty.RotateRelative;\n        this._wheelYActionCoordinate = axis;\n    }\n    /**\n     * Get the configured rotation axis (relative to camera's orientation) the\n     * mouse wheel's Y axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelYRotateRelative() {\n        if (this._wheelYAction !== _CameraProperty.RotateRelative) {\n            return null;\n        }\n        return this._wheelYActionCoordinate;\n    }\n    /**\n     * Set which rotation axis (relative to camera's orientation) the mouse\n     * wheel's Z axis controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelZRotateRelative(axis) {\n        if (axis === null && this._wheelZAction !== _CameraProperty.RotateRelative) {\n            // Attempting to clear different _wheelZAction.\n            return;\n        }\n        this._wheelZAction = _CameraProperty.RotateRelative;\n        this._wheelZActionCoordinate = axis;\n    }\n    /**\n     * Get the configured rotation axis (relative to camera's orientation) the\n     * mouse wheel's Z axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelZRotateRelative() {\n        if (this._wheelZAction !== _CameraProperty.RotateRelative) {\n            return null;\n        }\n        return this._wheelZActionCoordinate;\n    }\n    /**\n     * Set which movement axis (relative to the scene) the mouse wheel's X axis\n     * controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelXMoveScene(axis) {\n        if (axis === null && this._wheelXAction !== _CameraProperty.MoveScene) {\n            // Attempting to clear different _wheelXAction.\n            return;\n        }\n        this._wheelXAction = _CameraProperty.MoveScene;\n        this._wheelXActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\n     * X axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelXMoveScene() {\n        if (this._wheelXAction !== _CameraProperty.MoveScene) {\n            return null;\n        }\n        return this._wheelXActionCoordinate;\n    }\n    /**\n     * Set which movement axis (relative to the scene) the mouse wheel's Y axis\n     * controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelYMoveScene(axis) {\n        if (axis === null && this._wheelYAction !== _CameraProperty.MoveScene) {\n            // Attempting to clear different _wheelYAction.\n            return;\n        }\n        this._wheelYAction = _CameraProperty.MoveScene;\n        this._wheelYActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\n     * Y axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelYMoveScene() {\n        if (this._wheelYAction !== _CameraProperty.MoveScene) {\n            return null;\n        }\n        return this._wheelYActionCoordinate;\n    }\n    /**\n     * Set which movement axis (relative to the scene) the mouse wheel's Z axis\n     * controls.\n     * @param axis The axis to be moved. Set null to clear.\n     */\n    set wheelZMoveScene(axis) {\n        if (axis === null && this._wheelZAction !== _CameraProperty.MoveScene) {\n            // Attempting to clear different _wheelZAction.\n            return;\n        }\n        this._wheelZAction = _CameraProperty.MoveScene;\n        this._wheelZActionCoordinate = axis;\n    }\n    /**\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\n     * Z axis controls.\n     * @returns The configured axis or null if none.\n     */\n    get wheelZMoveScene() {\n        if (this._wheelZAction !== _CameraProperty.MoveScene) {\n            return null;\n        }\n        return this._wheelZActionCoordinate;\n    }\n    /**\n     * Called for each rendered frame.\n     */\n    checkInputs() {\n        if (this._wheelDeltaX === 0 && this._wheelDeltaY === 0 && this._wheelDeltaZ == 0) {\n            return;\n        }\n        // Clear the camera properties that we might be updating.\n        this._moveRelative.setAll(0);\n        this._rotateRelative.setAll(0);\n        this._moveScene.setAll(0);\n        // Set the camera properties that are to be updated.\n        this._updateCamera();\n        if (this.camera.getScene().useRightHandedSystem) {\n            // TODO: Does this need done for worldUpdate too?\n            this._moveRelative.z *= -1;\n        }\n        // Convert updates relative to camera to world position update.\n        const cameraTransformMatrix = Matrix.Zero();\n        this.camera.getViewMatrix().invertToRef(cameraTransformMatrix);\n        const transformedDirection = Vector3.Zero();\n        Vector3.TransformNormalToRef(this._moveRelative, cameraTransformMatrix, transformedDirection);\n        // Apply updates to camera position.\n        this.camera.cameraRotation.x += this._rotateRelative.x / 200;\n        this.camera.cameraRotation.y += this._rotateRelative.y / 200;\n        this.camera.cameraDirection.addInPlace(transformedDirection);\n        this.camera.cameraDirection.addInPlace(this._moveScene);\n        // Call the base class implementation to handle observers and do cleanup.\n        super.checkInputs();\n    }\n    /**\n     * Update the camera according to any configured properties for the 3\n     * mouse-wheel axis.\n     */\n    _updateCamera() {\n        // Do the camera updates for each of the 3 touch-wheel axis.\n        this._updateCameraProperty(this._wheelDeltaX, this._wheelXAction, this._wheelXActionCoordinate);\n        this._updateCameraProperty(this._wheelDeltaY, this._wheelYAction, this._wheelYActionCoordinate);\n        this._updateCameraProperty(this._wheelDeltaZ, this._wheelZAction, this._wheelZActionCoordinate);\n    }\n    /**\n     * Update one property of the camera.\n     * @param value\n     * @param cameraProperty\n     * @param coordinate\n     */\n    _updateCameraProperty(\n    /* Mouse-wheel delta. */\n    value, \n    /* Camera property to be changed. */\n    cameraProperty, \n    /* Axis of Camera property to be changed. */\n    coordinate) {\n        if (value === 0) {\n            // Mouse wheel has not moved.\n            return;\n        }\n        if (cameraProperty === null || coordinate === null) {\n            // Mouse wheel axis not configured.\n            return;\n        }\n        let action = null;\n        switch (cameraProperty) {\n            case _CameraProperty.MoveRelative:\n                action = this._moveRelative;\n                break;\n            case _CameraProperty.RotateRelative:\n                action = this._rotateRelative;\n                break;\n            case _CameraProperty.MoveScene:\n                action = this._moveScene;\n                break;\n        }\n        switch (coordinate) {\n            case 0 /* Coordinate.X */:\n                action.set(value, 0, 0);\n                break;\n            case 1 /* Coordinate.Y */:\n                action.set(0, value, 0);\n                break;\n            case 2 /* Coordinate.Z */:\n                action.set(0, 0, value);\n                break;\n        }\n    }\n}\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelXMoveRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelYMoveRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelZMoveRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelXRotateRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelYRotateRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelZRotateRelative\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelXMoveScene\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelYMoveScene\", null);\n__decorate([\n    serialize()\n], FreeCameraMouseWheelInput.prototype, \"wheelZMoveScene\", null);\nCameraInputTypes[\"FreeCameraMouseWheelInput\"] = FreeCameraMouseWheelInput;\n//# sourceMappingURL=freeCameraMouseWheelInput.js.map", "import { __decorate } from \"../../tslib.es6.js\";\nimport { serialize } from \"../../Misc/decorators.js\";\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager.js\";\nimport { PointerEventTypes } from \"../../Events/pointerEvents.js\";\nimport { Matrix, Vector3 } from \"../../Maths/math.vector.js\";\nimport { Tools } from \"../../Misc/tools.js\";\n/**\n * Manage the touch inputs to control the movement of a free camera.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n */\nexport class FreeCameraTouchInput {\n    /**\n     * Manage the touch inputs to control the movement of a free camera.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n     * @param allowMouse Defines if mouse events can be treated as touch events\n     */\n    constructor(\n    /**\n     * [false] Define if mouse events can be treated as touch events\n     */\n    allowMouse = false) {\n        this.allowMouse = allowMouse;\n        /**\n         * Defines the touch sensibility for rotation.\n         * The lower the faster.\n         */\n        this.touchAngularSensibility = 200000.0;\n        /**\n         * Defines the touch sensibility for move.\n         * The lower the faster.\n         */\n        this.touchMoveSensibility = 250.0;\n        /**\n         * Swap touch actions so that one touch is used for rotation and multiple for movement\n         */\n        this.singleFingerRotate = false;\n        this._offsetX = null;\n        this._offsetY = null;\n        this._pointerPressed = new Array();\n        this._isSafari = Tools.IsSafari();\n    }\n    /**\n     * Attach the input controls to a specific dom element to get the input from.\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\n     */\n    attachControl(noPreventDefault) {\n        // eslint-disable-next-line prefer-rest-params\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\n        let previousPosition = null;\n        if (this._pointerInput === undefined) {\n            this._onLostFocus = () => {\n                this._offsetX = null;\n                this._offsetY = null;\n            };\n            this._pointerInput = (p) => {\n                const evt = p.event;\n                const isMouseEvent = evt.pointerType === \"mouse\" || (this._isSafari && typeof evt.pointerType === \"undefined\");\n                if (!this.allowMouse && isMouseEvent) {\n                    return;\n                }\n                if (p.type === PointerEventTypes.POINTERDOWN) {\n                    if (!noPreventDefault) {\n                        evt.preventDefault();\n                    }\n                    this._pointerPressed.push(evt.pointerId);\n                    if (this._pointerPressed.length !== 1) {\n                        return;\n                    }\n                    previousPosition = {\n                        x: evt.clientX,\n                        y: evt.clientY,\n                    };\n                }\n                else if (p.type === PointerEventTypes.POINTERUP) {\n                    if (!noPreventDefault) {\n                        evt.preventDefault();\n                    }\n                    const index = this._pointerPressed.indexOf(evt.pointerId);\n                    if (index === -1) {\n                        return;\n                    }\n                    this._pointerPressed.splice(index, 1);\n                    if (index != 0) {\n                        return;\n                    }\n                    previousPosition = null;\n                    this._offsetX = null;\n                    this._offsetY = null;\n                }\n                else if (p.type === PointerEventTypes.POINTERMOVE) {\n                    if (!noPreventDefault) {\n                        evt.preventDefault();\n                    }\n                    if (!previousPosition) {\n                        return;\n                    }\n                    const index = this._pointerPressed.indexOf(evt.pointerId);\n                    if (index != 0) {\n                        return;\n                    }\n                    this._offsetX = evt.clientX - previousPosition.x;\n                    this._offsetY = -(evt.clientY - previousPosition.y);\n                }\n            };\n        }\n        this._observer = this.camera\n            .getScene()\n            ._inputManager._addCameraPointerObserver(this._pointerInput, PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE);\n        if (this._onLostFocus) {\n            const engine = this.camera.getEngine();\n            const element = engine.getInputElement();\n            element && element.addEventListener(\"blur\", this._onLostFocus);\n        }\n    }\n    /**\n     * Detach the current controls from the specified dom element.\n     */\n    detachControl() {\n        if (this._pointerInput) {\n            if (this._observer) {\n                this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\n                this._observer = null;\n            }\n            if (this._onLostFocus) {\n                const engine = this.camera.getEngine();\n                const element = engine.getInputElement();\n                element && element.removeEventListener(\"blur\", this._onLostFocus);\n                this._onLostFocus = null;\n            }\n            this._pointerPressed.length = 0;\n            this._offsetX = null;\n            this._offsetY = null;\n        }\n    }\n    /**\n     * Update the current camera state depending on the inputs that have been used this frame.\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\n     */\n    checkInputs() {\n        if (this._offsetX === null || this._offsetY === null) {\n            return;\n        }\n        if (this._offsetX === 0 && this._offsetY === 0) {\n            return;\n        }\n        const camera = this.camera;\n        const handednessMultiplier = camera._calculateHandednessMultiplier();\n        camera.cameraRotation.y = (handednessMultiplier * this._offsetX) / this.touchAngularSensibility;\n        const rotateCamera = (this.singleFingerRotate && this._pointerPressed.length === 1) || (!this.singleFingerRotate && this._pointerPressed.length > 1);\n        if (rotateCamera) {\n            camera.cameraRotation.x = -this._offsetY / this.touchAngularSensibility;\n        }\n        else {\n            const speed = camera._computeLocalCameraSpeed();\n            const direction = new Vector3(0, 0, this.touchMoveSensibility !== 0 ? (speed * this._offsetY) / this.touchMoveSensibility : 0);\n            Matrix.RotationYawPitchRollToRef(camera.rotation.y, camera.rotation.x, 0, camera._cameraRotationMatrix);\n            camera.cameraDirection.addInPlace(Vector3.TransformCoordinates(direction, camera._cameraRotationMatrix));\n        }\n    }\n    /**\n     * Gets the class name of the current input.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FreeCameraTouchInput\";\n    }\n    /**\n     * Get the friendly name associated with the input class.\n     * @returns the input friendly name\n     */\n    getSimpleName() {\n        return \"touch\";\n    }\n}\n__decorate([\n    serialize()\n], FreeCameraTouchInput.prototype, \"touchAngularSensibility\", void 0);\n__decorate([\n    serialize()\n], FreeCameraTouchInput.prototype, \"touchMoveSensibility\", void 0);\nCameraInputTypes[\"FreeCameraTouchInput\"] = FreeCameraTouchInput;\n//# sourceMappingURL=freeCameraTouchInput.js.map", "import { CameraInputsManager } from \"./cameraInputsManager.js\";\nimport { FreeCameraKeyboardMoveInput } from \"../Cameras/Inputs/freeCameraKeyboardMoveInput.js\";\nimport { FreeCameraMouseInput } from \"../Cameras/Inputs/freeCameraMouseInput.js\";\nimport { FreeCameraMouseWheelInput } from \"../Cameras/Inputs/freeCameraMouseWheelInput.js\";\nimport { FreeCameraTouchInput } from \"../Cameras/Inputs/freeCameraTouchInput.js\";\n/**\n * Default Inputs manager for the FreeCamera.\n * It groups all the default supported inputs for ease of use.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\n */\nexport class FreeCameraInputsManager extends CameraInputsManager {\n    /**\n     * Instantiates a new FreeCameraInputsManager.\n     * @param camera Defines the camera the inputs belong to\n     */\n    constructor(camera) {\n        super(camera);\n        /**\n         * @internal\n         */\n        this._mouseInput = null;\n        /**\n         * @internal\n         */\n        this._mouseWheelInput = null;\n    }\n    /**\n     * Add keyboard input support to the input manager.\n     * @returns the current input manager\n     */\n    addKeyboard() {\n        this.add(new FreeCameraKeyboardMoveInput());\n        return this;\n    }\n    /**\n     * Add mouse input support to the input manager.\n     * @param touchEnabled if the FreeCameraMouseInput should support touch (default: true)\n     * @returns the current input manager\n     */\n    addMouse(touchEnabled = true) {\n        if (!this._mouseInput) {\n            this._mouseInput = new FreeCameraMouseInput(touchEnabled);\n            this.add(this._mouseInput);\n        }\n        return this;\n    }\n    /**\n     * Removes the mouse input support from the manager\n     * @returns the current input manager\n     */\n    removeMouse() {\n        if (this._mouseInput) {\n            this.remove(this._mouseInput);\n        }\n        return this;\n    }\n    /**\n     * Add mouse wheel input support to the input manager.\n     * @returns the current input manager\n     */\n    addMouseWheel() {\n        if (!this._mouseWheelInput) {\n            this._mouseWheelInput = new FreeCameraMouseWheelInput();\n            this.add(this._mouseWheelInput);\n        }\n        return this;\n    }\n    /**\n     * Removes the mouse wheel input support from the manager\n     * @returns the current input manager\n     */\n    removeMouseWheel() {\n        if (this._mouseWheelInput) {\n            this.remove(this._mouseWheelInput);\n        }\n        return this;\n    }\n    /**\n     * Add touch input support to the input manager.\n     * @returns the current input manager\n     */\n    addTouch() {\n        this.add(new FreeCameraTouchInput());\n        return this;\n    }\n    /**\n     * Remove all attached input methods from a camera\n     */\n    clear() {\n        super.clear();\n        this._mouseInput = null;\n    }\n}\n//# sourceMappingURL=freeCameraInputsManager.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { serializeAsVector3, serialize } from \"../Misc/decorators.js\";\nimport { Vector3, Vector2 } from \"../Maths/math.vector.js\";\nimport { TargetCamera } from \"./targetCamera.js\";\nimport { FreeCameraInputsManager } from \"./freeCameraInputsManager.js\";\nimport { Tools } from \"../Misc/tools.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\nimport { AbstractEngine } from \"../Engines/abstractEngine.js\";\n/**\n * This represents a free type of camera. It can be useful in First Person Shooter game for instance.\n * Please consider using the new UniversalCamera instead as it adds more functionality like the gamepad.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\n */\nexport class FreeCamera extends TargetCamera {\n    /**\n     * Gets the input sensibility for a mouse input. (default is 2000.0)\n     * Higher values reduce sensitivity.\n     */\n    get angularSensibility() {\n        const mouse = this.inputs.attached[\"mouse\"];\n        if (mouse) {\n            return mouse.angularSensibility;\n        }\n        return 0;\n    }\n    /**\n     * Sets the input sensibility for a mouse input. (default is 2000.0)\n     * Higher values reduce sensitivity.\n     */\n    set angularSensibility(value) {\n        const mouse = this.inputs.attached[\"mouse\"];\n        if (mouse) {\n            mouse.angularSensibility = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the forward move of the camera.\n     */\n    get keysUp() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysUp;\n        }\n        return [];\n    }\n    set keysUp(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysUp = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the upward move of the camera.\n     */\n    get keysUpward() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysUpward;\n        }\n        return [];\n    }\n    set keysUpward(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysUpward = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the backward move of the camera.\n     */\n    get keysDown() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysDown;\n        }\n        return [];\n    }\n    set keysDown(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysDown = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the downward move of the camera.\n     */\n    get keysDownward() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysDownward;\n        }\n        return [];\n    }\n    set keysDownward(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysDownward = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the left strafe move of the camera.\n     */\n    get keysLeft() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysLeft;\n        }\n        return [];\n    }\n    set keysLeft(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysLeft = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the right strafe move of the camera.\n     */\n    get keysRight() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysRight;\n        }\n        return [];\n    }\n    set keysRight(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysRight = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the left rotation move of the camera.\n     */\n    get keysRotateLeft() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysRotateLeft;\n        }\n        return [];\n    }\n    set keysRotateLeft(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysRotateLeft = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the right rotation move of the camera.\n     */\n    get keysRotateRight() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysRotateRight;\n        }\n        return [];\n    }\n    set keysRotateRight(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysRotateRight = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the up rotation move of the camera.\n     */\n    get keysRotateUp() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysRotateUp;\n        }\n        return [];\n    }\n    set keysRotateUp(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysRotateUp = value;\n        }\n    }\n    /**\n     * Gets or Set the list of keyboard keys used to control the down rotation move of the camera.\n     */\n    get keysRotateDown() {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            return keyboard.keysRotateDown;\n        }\n        return [];\n    }\n    set keysRotateDown(value) {\n        const keyboard = this.inputs.attached[\"keyboard\"];\n        if (keyboard) {\n            keyboard.keysRotateDown = value;\n        }\n    }\n    /**\n     * Instantiates a Free Camera.\n     * This represents a free type of camera. It can be useful in First Person Shooter game for instance.\n     * Please consider using the new UniversalCamera instead as it adds more functionality like touch to this camera.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\n     * @param name Define the name of the camera in the scene\n     * @param position Define the start position of the camera in the scene\n     * @param scene Define the scene the camera belongs to\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\n     */\n    constructor(name, position, scene, setActiveOnSceneIfNoneActive = true) {\n        super(name, position, scene, setActiveOnSceneIfNoneActive);\n        /**\n         * Define the collision ellipsoid of the camera.\n         * This is helpful to simulate a camera body like the player body around the camera\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#arcrotatecamera\n         */\n        this.ellipsoid = new Vector3(0.5, 1, 0.5);\n        /**\n         * Define an offset for the position of the ellipsoid around the camera.\n         * This can be helpful to determine the center of the body near the gravity center of the body\n         * instead of its head.\n         */\n        this.ellipsoidOffset = new Vector3(0, 0, 0);\n        /**\n         * Enable or disable collisions of the camera with the rest of the scene objects.\n         */\n        this.checkCollisions = false;\n        /**\n         * Enable or disable gravity on the camera.\n         */\n        this.applyGravity = false;\n        this._needMoveForGravity = false;\n        this._oldPosition = Vector3.Zero();\n        this._diffPosition = Vector3.Zero();\n        this._newPosition = Vector3.Zero();\n        // Collisions\n        this._collisionMask = -1;\n        this._onCollisionPositionChange = (collisionId, newPosition, collidedMesh = null) => {\n            this._newPosition.copyFrom(newPosition);\n            this._newPosition.subtractToRef(this._oldPosition, this._diffPosition);\n            if (this._diffPosition.length() > AbstractEngine.CollisionsEpsilon) {\n                this.position.addToRef(this._diffPosition, this._deferredPositionUpdate);\n                if (!this._deferOnly) {\n                    this.position.copyFrom(this._deferredPositionUpdate);\n                }\n                else {\n                    this._deferredUpdated = true;\n                }\n                // call onCollide, if defined. Note that in case of deferred update, the actual position change might happen in the next frame.\n                if (this.onCollide && collidedMesh) {\n                    this.onCollide(collidedMesh);\n                }\n            }\n        };\n        this.inputs = new FreeCameraInputsManager(this);\n        this.inputs.addKeyboard().addMouse();\n    }\n    /**\n     * Attached controls to the current camera.\n     * @param ignored defines an ignored parameter kept for backward compatibility.\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\n     */\n    attachControl(ignored, noPreventDefault) {\n        // eslint-disable-next-line prefer-rest-params\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\n        this.inputs.attachElement(noPreventDefault);\n    }\n    /**\n     * Detach the current controls from the specified dom element.\n     */\n    detachControl() {\n        this.inputs.detachElement();\n        this.cameraDirection = new Vector3(0, 0, 0);\n        this.cameraRotation = new Vector2(0, 0);\n    }\n    /**\n     * Define a collision mask to limit the list of object the camera can collide with\n     */\n    get collisionMask() {\n        return this._collisionMask;\n    }\n    set collisionMask(mask) {\n        this._collisionMask = !isNaN(mask) ? mask : -1;\n    }\n    /**\n     * @internal\n     */\n    _collideWithWorld(displacement) {\n        let globalPosition;\n        if (this.parent) {\n            globalPosition = Vector3.TransformCoordinates(this.position, this.parent.getWorldMatrix());\n        }\n        else {\n            globalPosition = this.position;\n        }\n        globalPosition.subtractFromFloatsToRef(0, this.ellipsoid.y, 0, this._oldPosition);\n        this._oldPosition.addInPlace(this.ellipsoidOffset);\n        const coordinator = this.getScene().collisionCoordinator;\n        if (!this._collider) {\n            this._collider = coordinator.createCollider();\n        }\n        this._collider._radius = this.ellipsoid;\n        this._collider.collisionMask = this._collisionMask;\n        //no need for clone, as long as gravity is not on.\n        let actualDisplacement = displacement;\n        //add gravity to the direction to prevent the dual-collision checking\n        if (this.applyGravity) {\n            //this prevents mending with cameraDirection, a global variable of the free camera class.\n            actualDisplacement = displacement.add(this.getScene().gravity);\n        }\n        coordinator.getNewPosition(this._oldPosition, actualDisplacement, this._collider, 3, null, this._onCollisionPositionChange, this.uniqueId);\n    }\n    /** @internal */\n    _checkInputs() {\n        if (!this._localDirection) {\n            this._localDirection = Vector3.Zero();\n            this._transformedDirection = Vector3.Zero();\n        }\n        this.inputs.checkInputs();\n        super._checkInputs();\n    }\n    /**\n     * Enable movement without a user input. This allows gravity to always be applied.\n     */\n    set needMoveForGravity(value) {\n        this._needMoveForGravity = value;\n    }\n    /**\n     * When true, gravity is applied whether there is user input or not.\n     */\n    get needMoveForGravity() {\n        return this._needMoveForGravity;\n    }\n    /** @internal */\n    _decideIfNeedsToMove() {\n        return this._needMoveForGravity || Math.abs(this.cameraDirection.x) > 0 || Math.abs(this.cameraDirection.y) > 0 || Math.abs(this.cameraDirection.z) > 0;\n    }\n    /** @internal */\n    _updatePosition() {\n        if (this.checkCollisions && this.getScene().collisionsEnabled) {\n            this._collideWithWorld(this.cameraDirection);\n        }\n        else {\n            super._updatePosition();\n        }\n    }\n    /**\n     * Destroy the camera and release the current resources hold by it.\n     */\n    dispose() {\n        this.inputs.clear();\n        super.dispose();\n    }\n    /**\n     * Gets the current object class name.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FreeCamera\";\n    }\n}\n__decorate([\n    serializeAsVector3()\n], FreeCamera.prototype, \"ellipsoid\", void 0);\n__decorate([\n    serializeAsVector3()\n], FreeCamera.prototype, \"ellipsoidOffset\", void 0);\n__decorate([\n    serialize()\n], FreeCamera.prototype, \"checkCollisions\", void 0);\n__decorate([\n    serialize()\n], FreeCamera.prototype, \"applyGravity\", void 0);\n// Register Class Name\nRegisterClass(\"BABYLON.FreeCamera\", FreeCamera);\n//# sourceMappingURL=freeCamera.js.map", "import { Bone } from \"./bone.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { Vector3, Matrix, TmpVectors } from \"../Maths/math.vector.js\";\nimport { RawTexture } from \"../Materials/Textures/rawTexture.js\";\nimport { Animation } from \"../Animations/animation.js\";\nimport { AnimationRange } from \"../Animations/animationRange.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\n\nimport { Logger } from \"../Misc/logger.js\";\nimport { DeepCopier } from \"../Misc/deepCopier.js\";\n/**\n * Class used to handle skinning animations\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\n */\nexport class Skeleton {\n    /**\n     * Gets or sets a boolean indicating that bone matrices should be stored as a texture instead of using shader uniforms (default is true).\n     * Please note that this option is not available if the hardware does not support it\n     */\n    get useTextureToStoreBoneMatrices() {\n        return this._useTextureToStoreBoneMatrices;\n    }\n    set useTextureToStoreBoneMatrices(value) {\n        this._useTextureToStoreBoneMatrices = value;\n        this._markAsDirty();\n    }\n    /**\n     * Gets or sets the animation properties override\n     */\n    get animationPropertiesOverride() {\n        if (!this._animationPropertiesOverride) {\n            return this._scene.animationPropertiesOverride;\n        }\n        return this._animationPropertiesOverride;\n    }\n    set animationPropertiesOverride(value) {\n        this._animationPropertiesOverride = value;\n    }\n    /**\n     * Gets a boolean indicating that the skeleton effectively stores matrices into a texture\n     */\n    get isUsingTextureForMatrices() {\n        return this.useTextureToStoreBoneMatrices && this._canUseTextureForBones;\n    }\n    /**\n     * Gets the unique ID of this skeleton\n     */\n    get uniqueId() {\n        return this._uniqueId;\n    }\n    /**\n     * Creates a new skeleton\n     * @param name defines the skeleton name\n     * @param id defines the skeleton Id\n     * @param scene defines the hosting scene\n     */\n    constructor(\n    /** defines the skeleton name */\n    name, \n    /** defines the skeleton Id */\n    id, scene) {\n        this.name = name;\n        this.id = id;\n        /**\n         * Defines the list of child bones\n         */\n        this.bones = [];\n        /**\n         * Defines a boolean indicating if the root matrix is provided by meshes or by the current skeleton (this is the default value)\n         */\n        this.needInitialSkinMatrix = false;\n        this._isDirty = true;\n        this._meshesWithPoseMatrix = new Array();\n        this._identity = Matrix.Identity();\n        this._currentRenderId = -1;\n        this._ranges = {};\n        this._absoluteTransformIsDirty = true;\n        this._canUseTextureForBones = false;\n        this._uniqueId = 0;\n        /** @internal */\n        this._numBonesWithLinkedTransformNode = 0;\n        /** @internal */\n        this._hasWaitingData = null;\n        /** @internal */\n        this._parentContainer = null;\n        /**\n         * Specifies if the skeleton should be serialized\n         */\n        this.doNotSerialize = false;\n        this._useTextureToStoreBoneMatrices = true;\n        this._animationPropertiesOverride = null;\n        // Events\n        /**\n         * An observable triggered before computing the skeleton's matrices\n         */\n        this.onBeforeComputeObservable = new Observable();\n        this.bones = [];\n        this._scene = scene || EngineStore.LastCreatedScene;\n        this._uniqueId = this._scene.getUniqueId();\n        this._scene.addSkeleton(this);\n        //make sure it will recalculate the matrix next time prepare is called.\n        this._isDirty = true;\n        const engineCaps = this._scene.getEngine().getCaps();\n        this._canUseTextureForBones = engineCaps.textureFloat && engineCaps.maxVertexTextureImageUnits > 0;\n    }\n    /**\n     * Gets the current object class name.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"Skeleton\";\n    }\n    /**\n     * Returns an array containing the root bones\n     * @returns an array containing the root bones\n     */\n    getChildren() {\n        return this.bones.filter((b) => !b.getParent());\n    }\n    // Members\n    /**\n     * Gets the list of transform matrices to send to shaders (one matrix per bone)\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\n     * @returns a Float32Array containing matrices data\n     */\n    getTransformMatrices(mesh) {\n        if (this.needInitialSkinMatrix) {\n            if (!mesh) {\n                throw new Error(\"getTransformMatrices: When using the needInitialSkinMatrix flag, a mesh must be provided\");\n            }\n            if (!mesh._bonesTransformMatrices) {\n                this.prepare(true);\n            }\n            return mesh._bonesTransformMatrices;\n        }\n        if (!this._transformMatrices || this._isDirty) {\n            this.prepare(!this._transformMatrices);\n        }\n        return this._transformMatrices;\n    }\n    /**\n     * Gets the list of transform matrices to send to shaders inside a texture (one matrix per bone)\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\n     * @returns a raw texture containing the data\n     */\n    getTransformMatrixTexture(mesh) {\n        if (this.needInitialSkinMatrix && mesh._transformMatrixTexture) {\n            return mesh._transformMatrixTexture;\n        }\n        return this._transformMatrixTexture;\n    }\n    /**\n     * Gets the current hosting scene\n     * @returns a scene object\n     */\n    getScene() {\n        return this._scene;\n    }\n    // Methods\n    /**\n     * Gets a string representing the current skeleton data\n     * @param fullDetails defines a boolean indicating if we want a verbose version\n     * @returns a string representing the current skeleton data\n     */\n    toString(fullDetails) {\n        let ret = `Name: ${this.name}, nBones: ${this.bones.length}`;\n        ret += `, nAnimationRanges: ${this._ranges ? Object.keys(this._ranges).length : \"none\"}`;\n        if (fullDetails) {\n            ret += \", Ranges: {\";\n            let first = true;\n            for (const name in this._ranges) {\n                if (first) {\n                    ret += \", \";\n                    first = false;\n                }\n                ret += name;\n            }\n            ret += \"}\";\n        }\n        return ret;\n    }\n    /**\n     * Get bone's index searching by name\n     * @param name defines bone's name to search for\n     * @returns the indice of the bone. Returns -1 if not found\n     */\n    getBoneIndexByName(name) {\n        for (let boneIndex = 0, cache = this.bones.length; boneIndex < cache; boneIndex++) {\n            if (this.bones[boneIndex].name === name) {\n                return boneIndex;\n            }\n        }\n        return -1;\n    }\n    /**\n     * Create a new animation range\n     * @param name defines the name of the range\n     * @param from defines the start key\n     * @param to defines the end key\n     */\n    createAnimationRange(name, from, to) {\n        // check name not already in use\n        if (!this._ranges[name]) {\n            this._ranges[name] = new AnimationRange(name, from, to);\n            for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\n                if (this.bones[i].animations[0]) {\n                    this.bones[i].animations[0].createRange(name, from, to);\n                }\n            }\n        }\n    }\n    /**\n     * Delete a specific animation range\n     * @param name defines the name of the range\n     * @param deleteFrames defines if frames must be removed as well\n     */\n    deleteAnimationRange(name, deleteFrames = true) {\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\n            if (this.bones[i].animations[0]) {\n                this.bones[i].animations[0].deleteRange(name, deleteFrames);\n            }\n        }\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\n    }\n    /**\n     * Gets a specific animation range\n     * @param name defines the name of the range to look for\n     * @returns the requested animation range or null if not found\n     */\n    getAnimationRange(name) {\n        return this._ranges[name] || null;\n    }\n    /**\n     * Gets the list of all animation ranges defined on this skeleton\n     * @returns an array\n     */\n    getAnimationRanges() {\n        const animationRanges = [];\n        let name;\n        for (name in this._ranges) {\n            animationRanges.push(this._ranges[name]);\n        }\n        return animationRanges;\n    }\n    /**\n     * Copy animation range from a source skeleton.\n     * This is not for a complete retargeting, only between very similar skeleton's with only possible bone length differences\n     * @param source defines the source skeleton\n     * @param name defines the name of the range to copy\n     * @param rescaleAsRequired defines if rescaling must be applied if required\n     * @returns true if operation was successful\n     */\n    copyAnimationRange(source, name, rescaleAsRequired = false) {\n        if (this._ranges[name] || !source.getAnimationRange(name)) {\n            return false;\n        }\n        let ret = true;\n        const frameOffset = this._getHighestAnimationFrame() + 1;\n        // make a dictionary of source skeleton's bones, so exact same order or doubly nested loop is not required\n        const boneDict = {};\n        const sourceBones = source.bones;\n        let nBones;\n        let i;\n        for (i = 0, nBones = sourceBones.length; i < nBones; i++) {\n            boneDict[sourceBones[i].name] = sourceBones[i];\n        }\n        if (this.bones.length !== sourceBones.length) {\n            Logger.Warn(`copyAnimationRange: this rig has ${this.bones.length} bones, while source as ${sourceBones.length}`);\n            ret = false;\n        }\n        const skelDimensionsRatio = rescaleAsRequired && this.dimensionsAtRest && source.dimensionsAtRest ? this.dimensionsAtRest.divide(source.dimensionsAtRest) : null;\n        for (i = 0, nBones = this.bones.length; i < nBones; i++) {\n            const boneName = this.bones[i].name;\n            const sourceBone = boneDict[boneName];\n            if (sourceBone) {\n                ret = ret && this.bones[i].copyAnimationRange(sourceBone, name, frameOffset, rescaleAsRequired, skelDimensionsRatio);\n            }\n            else {\n                Logger.Warn(\"copyAnimationRange: not same rig, missing source bone \" + boneName);\n                ret = false;\n            }\n        }\n        // do not call createAnimationRange(), since it also is done to bones, which was already done\n        const range = source.getAnimationRange(name);\n        if (range) {\n            this._ranges[name] = new AnimationRange(name, range.from + frameOffset, range.to + frameOffset);\n        }\n        return ret;\n    }\n    /**\n     * Forces the skeleton to go to rest pose\n     */\n    returnToRest() {\n        for (const bone of this.bones) {\n            if (bone._index !== -1) {\n                bone.returnToRest();\n            }\n        }\n    }\n    _getHighestAnimationFrame() {\n        let ret = 0;\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\n            if (this.bones[i].animations[0]) {\n                const highest = this.bones[i].animations[0].getHighestFrame();\n                if (ret < highest) {\n                    ret = highest;\n                }\n            }\n        }\n        return ret;\n    }\n    /**\n     * Begin a specific animation range\n     * @param name defines the name of the range to start\n     * @param loop defines if looping must be turned on (false by default)\n     * @param speedRatio defines the speed ratio to apply (1 by default)\n     * @param onAnimationEnd defines a callback which will be called when animation will end\n     * @returns a new animatable\n     */\n    beginAnimation(name, loop, speedRatio, onAnimationEnd) {\n        const range = this.getAnimationRange(name);\n        if (!range) {\n            return null;\n        }\n        return this._scene.beginAnimation(this, range.from, range.to, loop, speedRatio, onAnimationEnd);\n    }\n    /**\n     * Convert the keyframes for a range of animation on a skeleton to be relative to a given reference frame.\n     * @param skeleton defines the Skeleton containing the animation range to convert\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to\n     * @param range defines the name of the AnimationRange belonging to the Skeleton to convert\n     * @returns the original skeleton\n     */\n    static MakeAnimationAdditive(skeleton, referenceFrame = 0, range) {\n        const rangeValue = skeleton.getAnimationRange(range);\n        // We can't make a range additive if it doesn't exist\n        if (!rangeValue) {\n            return null;\n        }\n        // Find any current scene-level animatable belonging to the target that matches the range\n        const sceneAnimatables = skeleton._scene.getAllAnimatablesByTarget(skeleton);\n        let rangeAnimatable = null;\n        for (let index = 0; index < sceneAnimatables.length; index++) {\n            const sceneAnimatable = sceneAnimatables[index];\n            if (sceneAnimatable.fromFrame === rangeValue?.from && sceneAnimatable.toFrame === rangeValue?.to) {\n                rangeAnimatable = sceneAnimatable;\n                break;\n            }\n        }\n        // Convert the animations belonging to the skeleton to additive keyframes\n        const animatables = skeleton.getAnimatables();\n        for (let index = 0; index < animatables.length; index++) {\n            const animatable = animatables[index];\n            const animations = animatable.animations;\n            if (!animations) {\n                continue;\n            }\n            for (let animIndex = 0; animIndex < animations.length; animIndex++) {\n                Animation.MakeAnimationAdditive(animations[animIndex], referenceFrame, range);\n            }\n        }\n        // Mark the scene-level animatable as additive\n        if (rangeAnimatable) {\n            rangeAnimatable.isAdditive = true;\n        }\n        return skeleton;\n    }\n    /** @internal */\n    _markAsDirty() {\n        this._isDirty = true;\n        this._absoluteTransformIsDirty = true;\n    }\n    /**\n     * @internal\n     */\n    _registerMeshWithPoseMatrix(mesh) {\n        this._meshesWithPoseMatrix.push(mesh);\n    }\n    /**\n     * @internal\n     */\n    _unregisterMeshWithPoseMatrix(mesh) {\n        const index = this._meshesWithPoseMatrix.indexOf(mesh);\n        if (index > -1) {\n            this._meshesWithPoseMatrix.splice(index, 1);\n        }\n    }\n    _computeTransformMatrices(targetMatrix, initialSkinMatrix) {\n        this.onBeforeComputeObservable.notifyObservers(this);\n        for (let index = 0; index < this.bones.length; index++) {\n            const bone = this.bones[index];\n            bone._childUpdateId++;\n            const parentBone = bone.getParent();\n            if (parentBone) {\n                bone.getLocalMatrix().multiplyToRef(parentBone.getFinalMatrix(), bone.getFinalMatrix());\n            }\n            else {\n                if (initialSkinMatrix) {\n                    bone.getLocalMatrix().multiplyToRef(initialSkinMatrix, bone.getFinalMatrix());\n                }\n                else {\n                    bone.getFinalMatrix().copyFrom(bone.getLocalMatrix());\n                }\n            }\n            if (bone._index !== -1) {\n                const mappedIndex = bone._index === null ? index : bone._index;\n                bone.getAbsoluteInverseBindMatrix().multiplyToArray(bone.getFinalMatrix(), targetMatrix, mappedIndex * 16);\n            }\n        }\n        this._identity.copyToArray(targetMatrix, this.bones.length * 16);\n    }\n    /**\n     * Build all resources required to render a skeleton\n     * @param dontCheckFrameId defines a boolean indicating if prepare should be run without checking first the current frame id (default: false)\n     */\n    prepare(dontCheckFrameId = false) {\n        if (!dontCheckFrameId) {\n            const currentRenderId = this.getScene().getRenderId();\n            if (this._currentRenderId === currentRenderId) {\n                return;\n            }\n            this._currentRenderId = currentRenderId;\n        }\n        // Update the local matrix of bones with linked transform nodes.\n        if (this._numBonesWithLinkedTransformNode > 0) {\n            for (const bone of this.bones) {\n                if (bone._linkedTransformNode) {\n                    const node = bone._linkedTransformNode;\n                    bone.position = node.position;\n                    if (node.rotationQuaternion) {\n                        bone.rotationQuaternion = node.rotationQuaternion;\n                    }\n                    else {\n                        bone.rotation = node.rotation;\n                    }\n                    bone.scaling = node.scaling;\n                }\n            }\n        }\n        if (this.needInitialSkinMatrix) {\n            for (const mesh of this._meshesWithPoseMatrix) {\n                const poseMatrix = mesh.getPoseMatrix();\n                let needsUpdate = this._isDirty;\n                if (!mesh._bonesTransformMatrices || mesh._bonesTransformMatrices.length !== 16 * (this.bones.length + 1)) {\n                    mesh._bonesTransformMatrices = new Float32Array(16 * (this.bones.length + 1));\n                    needsUpdate = true;\n                }\n                if (!needsUpdate) {\n                    continue;\n                }\n                if (this._synchronizedWithMesh !== mesh) {\n                    this._synchronizedWithMesh = mesh;\n                    // Prepare bones\n                    for (const bone of this.bones) {\n                        if (!bone.getParent()) {\n                            const matrix = bone.getBindMatrix();\n                            matrix.multiplyToRef(poseMatrix, TmpVectors.Matrix[1]);\n                            bone._updateAbsoluteBindMatrices(TmpVectors.Matrix[1]);\n                        }\n                    }\n                    if (this.isUsingTextureForMatrices) {\n                        const textureWidth = (this.bones.length + 1) * 4;\n                        if (!mesh._transformMatrixTexture || mesh._transformMatrixTexture.getSize().width !== textureWidth) {\n                            if (mesh._transformMatrixTexture) {\n                                mesh._transformMatrixTexture.dispose();\n                            }\n                            mesh._transformMatrixTexture = RawTexture.CreateRGBATexture(mesh._bonesTransformMatrices, (this.bones.length + 1) * 4, 1, this._scene, false, false, 1, 1);\n                        }\n                    }\n                }\n                this._computeTransformMatrices(mesh._bonesTransformMatrices, poseMatrix);\n                if (this.isUsingTextureForMatrices && mesh._transformMatrixTexture) {\n                    mesh._transformMatrixTexture.update(mesh._bonesTransformMatrices);\n                }\n            }\n        }\n        else {\n            if (!this._isDirty) {\n                return;\n            }\n            if (!this._transformMatrices || this._transformMatrices.length !== 16 * (this.bones.length + 1)) {\n                this._transformMatrices = new Float32Array(16 * (this.bones.length + 1));\n                if (this.isUsingTextureForMatrices) {\n                    if (this._transformMatrixTexture) {\n                        this._transformMatrixTexture.dispose();\n                    }\n                    this._transformMatrixTexture = RawTexture.CreateRGBATexture(this._transformMatrices, (this.bones.length + 1) * 4, 1, this._scene, false, false, 1, 1);\n                }\n            }\n            this._computeTransformMatrices(this._transformMatrices, null);\n            if (this.isUsingTextureForMatrices && this._transformMatrixTexture) {\n                this._transformMatrixTexture.update(this._transformMatrices);\n            }\n        }\n        this._isDirty = false;\n    }\n    /**\n     * Gets the list of animatables currently running for this skeleton\n     * @returns an array of animatables\n     */\n    getAnimatables() {\n        if (!this._animatables || this._animatables.length !== this.bones.length) {\n            this._animatables = [];\n            for (let index = 0; index < this.bones.length; index++) {\n                this._animatables.push(this.bones[index]);\n            }\n        }\n        return this._animatables;\n    }\n    /**\n     * Clone the current skeleton\n     * @param name defines the name of the new skeleton\n     * @param id defines the id of the new skeleton\n     * @returns the new skeleton\n     */\n    clone(name, id) {\n        const result = new Skeleton(name, id || name, this._scene);\n        result.needInitialSkinMatrix = this.needInitialSkinMatrix;\n        for (let index = 0; index < this.bones.length; index++) {\n            const source = this.bones[index];\n            let parentBone = null;\n            const parent = source.getParent();\n            if (parent) {\n                const parentIndex = this.bones.indexOf(parent);\n                parentBone = result.bones[parentIndex];\n            }\n            const bone = new Bone(source.name, result, parentBone, source.getBindMatrix().clone(), source.getRestMatrix().clone());\n            bone._index = source._index;\n            if (source._linkedTransformNode) {\n                bone.linkTransformNode(source._linkedTransformNode);\n            }\n            DeepCopier.DeepCopy(source.animations, bone.animations);\n        }\n        if (this._ranges) {\n            result._ranges = {};\n            for (const rangeName in this._ranges) {\n                const range = this._ranges[rangeName];\n                if (range) {\n                    result._ranges[rangeName] = range.clone();\n                }\n            }\n        }\n        this._isDirty = true;\n        result.prepare(true);\n        return result;\n    }\n    /**\n     * Enable animation blending for this skeleton\n     * @param blendingSpeed defines the blending speed to apply\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\n     */\n    enableBlending(blendingSpeed = 0.01) {\n        this.bones.forEach((bone) => {\n            bone.animations.forEach((animation) => {\n                animation.enableBlending = true;\n                animation.blendingSpeed = blendingSpeed;\n            });\n        });\n    }\n    /**\n     * Releases all resources associated with the current skeleton\n     */\n    dispose() {\n        this._meshesWithPoseMatrix.length = 0;\n        // Animations\n        this.getScene().stopAnimation(this);\n        // Remove from scene\n        this.getScene().removeSkeleton(this);\n        if (this._parentContainer) {\n            const index = this._parentContainer.skeletons.indexOf(this);\n            if (index > -1) {\n                this._parentContainer.skeletons.splice(index, 1);\n            }\n            this._parentContainer = null;\n        }\n        if (this._transformMatrixTexture) {\n            this._transformMatrixTexture.dispose();\n            this._transformMatrixTexture = null;\n        }\n    }\n    /**\n     * Serialize the skeleton in a JSON object\n     * @returns a JSON object\n     */\n    serialize() {\n        const serializationObject = {};\n        serializationObject.name = this.name;\n        serializationObject.id = this.id;\n        if (this.dimensionsAtRest) {\n            serializationObject.dimensionsAtRest = this.dimensionsAtRest.asArray();\n        }\n        serializationObject.bones = [];\n        serializationObject.needInitialSkinMatrix = this.needInitialSkinMatrix;\n        for (let index = 0; index < this.bones.length; index++) {\n            const bone = this.bones[index];\n            const parent = bone.getParent();\n            const serializedBone = {\n                parentBoneIndex: parent ? this.bones.indexOf(parent) : -1,\n                index: bone.getIndex(),\n                name: bone.name,\n                id: bone.id,\n                matrix: bone.getBindMatrix().asArray(),\n                rest: bone.getRestMatrix().asArray(),\n                linkedTransformNodeId: bone.getTransformNode()?.id,\n            };\n            serializationObject.bones.push(serializedBone);\n            if (bone.length) {\n                serializedBone.length = bone.length;\n            }\n            if (bone.metadata) {\n                serializedBone.metadata = bone.metadata;\n            }\n            if (bone.animations && bone.animations.length > 0) {\n                serializedBone.animation = bone.animations[0].serialize();\n            }\n            serializationObject.ranges = [];\n            for (const name in this._ranges) {\n                const source = this._ranges[name];\n                if (!source) {\n                    continue;\n                }\n                const range = {};\n                range.name = name;\n                range.from = source.from;\n                range.to = source.to;\n                serializationObject.ranges.push(range);\n            }\n        }\n        return serializationObject;\n    }\n    /**\n     * Creates a new skeleton from serialized data\n     * @param parsedSkeleton defines the serialized data\n     * @param scene defines the hosting scene\n     * @returns a new skeleton\n     */\n    static Parse(parsedSkeleton, scene) {\n        const skeleton = new Skeleton(parsedSkeleton.name, parsedSkeleton.id, scene);\n        if (parsedSkeleton.dimensionsAtRest) {\n            skeleton.dimensionsAtRest = Vector3.FromArray(parsedSkeleton.dimensionsAtRest);\n        }\n        skeleton.needInitialSkinMatrix = parsedSkeleton.needInitialSkinMatrix;\n        let index;\n        for (index = 0; index < parsedSkeleton.bones.length; index++) {\n            const parsedBone = parsedSkeleton.bones[index];\n            const parsedBoneIndex = parsedSkeleton.bones[index].index;\n            let parentBone = null;\n            if (parsedBone.parentBoneIndex > -1) {\n                parentBone = skeleton.bones[parsedBone.parentBoneIndex];\n            }\n            const rest = parsedBone.rest ? Matrix.FromArray(parsedBone.rest) : null;\n            const bone = new Bone(parsedBone.name, skeleton, parentBone, Matrix.FromArray(parsedBone.matrix), rest, null, parsedBoneIndex);\n            if (parsedBone.id !== undefined && parsedBone.id !== null) {\n                bone.id = parsedBone.id;\n            }\n            if (parsedBone.length) {\n                bone.length = parsedBone.length;\n            }\n            if (parsedBone.metadata) {\n                bone.metadata = parsedBone.metadata;\n            }\n            if (parsedBone.animation) {\n                bone.animations.push(Animation.Parse(parsedBone.animation));\n            }\n            if (parsedBone.linkedTransformNodeId !== undefined && parsedBone.linkedTransformNodeId !== null) {\n                skeleton._hasWaitingData = true;\n                bone._waitingTransformNodeId = parsedBone.linkedTransformNodeId;\n            }\n        }\n        // placed after bones, so createAnimationRange can cascade down\n        if (parsedSkeleton.ranges) {\n            for (index = 0; index < parsedSkeleton.ranges.length; index++) {\n                const data = parsedSkeleton.ranges[index];\n                skeleton.createAnimationRange(data.name, data.from, data.to);\n            }\n        }\n        return skeleton;\n    }\n    /**\n     * Compute all node absolute matrices\n     * @param forceUpdate defines if computation must be done even if cache is up to date\n     */\n    computeAbsoluteMatrices(forceUpdate = false) {\n        if (this._absoluteTransformIsDirty || forceUpdate) {\n            this.bones[0].computeAbsoluteMatrices();\n            this._absoluteTransformIsDirty = false;\n        }\n    }\n    /**\n     * Compute all node absolute matrices\n     * @param forceUpdate defines if computation must be done even if cache is up to date\n     * @deprecated Please use computeAbsoluteMatrices instead\n     */\n    computeAbsoluteTransforms(forceUpdate = false) {\n        this.computeAbsoluteMatrices(forceUpdate);\n    }\n    /**\n     * Gets the root pose matrix\n     * @returns a matrix\n     */\n    getPoseMatrix() {\n        let poseMatrix = null;\n        if (this._meshesWithPoseMatrix.length > 0) {\n            poseMatrix = this._meshesWithPoseMatrix[0].getPoseMatrix();\n        }\n        return poseMatrix;\n    }\n    /**\n     * Sorts bones per internal index\n     */\n    sortBones() {\n        const bones = [];\n        const visited = new Array(this.bones.length);\n        for (let index = 0; index < this.bones.length; index++) {\n            this._sortBones(index, bones, visited);\n        }\n        this.bones = bones;\n    }\n    _sortBones(index, bones, visited) {\n        if (visited[index]) {\n            return;\n        }\n        visited[index] = true;\n        const bone = this.bones[index];\n        if (!bone)\n            return;\n        if (bone._index === undefined) {\n            bone._index = index;\n        }\n        const parentBone = bone.getParent();\n        if (parentBone) {\n            this._sortBones(this.bones.indexOf(parentBone), bones, visited);\n        }\n        bones.push(bone);\n    }\n    /**\n     * Set the current local matrix as the restPose for all bones in the skeleton.\n     */\n    setCurrentPoseAsRest() {\n        this.bones.forEach((b) => {\n            b.setCurrentPoseAsRest();\n        });\n    }\n}\n//# sourceMappingURL=skeleton.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { VertexBuffer } from \"../Buffers/buffer.js\";\nimport { serialize } from \"../Misc/decorators.js\";\nimport { SerializationHelper } from \"../Misc/decorators.serialization.js\";\nimport { GetClass } from \"../Misc/typeStore.js\";\n/**\n * Defines a target to use with MorphTargetManager\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/morphTargets\n */\nexport class MorphTarget {\n    /**\n     * Gets or sets the influence of this target (ie. its weight in the overall morphing)\n     */\n    get influence() {\n        return this._influence;\n    }\n    set influence(influence) {\n        if (this._influence === influence) {\n            return;\n        }\n        const previous = this._influence;\n        this._influence = influence;\n        if (this.onInfluenceChanged.hasObservers()) {\n            this.onInfluenceChanged.notifyObservers(previous === 0 || influence === 0);\n        }\n    }\n    /**\n     * Gets or sets the animation properties override\n     */\n    get animationPropertiesOverride() {\n        if (!this._animationPropertiesOverride && this._scene) {\n            return this._scene.animationPropertiesOverride;\n        }\n        return this._animationPropertiesOverride;\n    }\n    set animationPropertiesOverride(value) {\n        this._animationPropertiesOverride = value;\n    }\n    /**\n     * Creates a new MorphTarget\n     * @param name defines the name of the target\n     * @param influence defines the influence to use\n     * @param scene defines the scene the morphtarget belongs to\n     */\n    constructor(\n    /** defines the name of the target */\n    name, influence = 0, scene = null) {\n        this.name = name;\n        /**\n         * Gets or sets the list of animations\n         */\n        this.animations = [];\n        this._positions = null;\n        this._normals = null;\n        this._tangents = null;\n        this._uvs = null;\n        this._uv2s = null;\n        this._colors = null;\n        this._uniqueId = 0;\n        /**\n         * Observable raised when the influence changes\n         */\n        this.onInfluenceChanged = new Observable();\n        /** @internal */\n        this._onDataLayoutChanged = new Observable();\n        this._animationPropertiesOverride = null;\n        this.id = name;\n        this._scene = scene || EngineStore.LastCreatedScene;\n        this.influence = influence;\n        if (this._scene) {\n            this._uniqueId = this._scene.getUniqueId();\n        }\n    }\n    /**\n     * Gets the unique ID of this manager\n     */\n    get uniqueId() {\n        return this._uniqueId;\n    }\n    /**\n     * Gets a boolean defining if the target contains position data\n     */\n    get hasPositions() {\n        return !!this._positions;\n    }\n    /**\n     * Gets a boolean defining if the target contains normal data\n     */\n    get hasNormals() {\n        return !!this._normals;\n    }\n    /**\n     * Gets a boolean defining if the target contains tangent data\n     */\n    get hasTangents() {\n        return !!this._tangents;\n    }\n    /**\n     * Gets a boolean defining if the target contains texture coordinates data\n     */\n    get hasUVs() {\n        return !!this._uvs;\n    }\n    /**\n     * Gets a boolean defining if the target contains texture coordinates 2 data\n     */\n    get hasUV2s() {\n        return !!this._uv2s;\n    }\n    get hasColors() {\n        return !!this._colors;\n    }\n    /**\n     * Gets the number of vertices stored in this target\n     */\n    get vertexCount() {\n        return this._positions\n            ? this._positions.length / 3\n            : this._normals\n                ? this._normals.length / 3\n                : this._tangents\n                    ? this._tangents.length / 3\n                    : this._uvs\n                        ? this._uvs.length / 2\n                        : this._uv2s\n                            ? this._uv2s.length / 2\n                            : this._colors\n                                ? this._colors.length / 4\n                                : 0;\n    }\n    /**\n     * Affects position data to this target\n     * @param data defines the position data to use\n     */\n    setPositions(data) {\n        const hadPositions = this.hasPositions;\n        this._positions = data;\n        if (hadPositions !== this.hasPositions) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the position data stored in this target\n     * @returns a FloatArray containing the position data (or null if not present)\n     */\n    getPositions() {\n        return this._positions;\n    }\n    /**\n     * Affects normal data to this target\n     * @param data defines the normal data to use\n     */\n    setNormals(data) {\n        const hadNormals = this.hasNormals;\n        this._normals = data;\n        if (hadNormals !== this.hasNormals) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the normal data stored in this target\n     * @returns a FloatArray containing the normal data (or null if not present)\n     */\n    getNormals() {\n        return this._normals;\n    }\n    /**\n     * Affects tangent data to this target\n     * @param data defines the tangent data to use\n     */\n    setTangents(data) {\n        const hadTangents = this.hasTangents;\n        this._tangents = data;\n        if (hadTangents !== this.hasTangents) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the tangent data stored in this target\n     * @returns a FloatArray containing the tangent data (or null if not present)\n     */\n    getTangents() {\n        return this._tangents;\n    }\n    /**\n     * Affects texture coordinates data to this target\n     * @param data defines the texture coordinates data to use\n     */\n    setUVs(data) {\n        const hadUVs = this.hasUVs;\n        this._uvs = data;\n        if (hadUVs !== this.hasUVs) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the texture coordinates data stored in this target\n     * @returns a FloatArray containing the texture coordinates data (or null if not present)\n     */\n    getUVs() {\n        return this._uvs;\n    }\n    /**\n     * Affects texture coordinates 2 data to this target\n     * @param data defines the texture coordinates 2 data to use\n     */\n    setUV2s(data) {\n        const hadUV2s = this.hasUV2s;\n        this._uv2s = data;\n        if (hadUV2s !== this.hasUV2s) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the texture coordinates 2 data stored in this target\n     * @returns a FloatArray containing the texture coordinates 2 data (or null if not present)\n     */\n    getUV2s() {\n        return this._uv2s;\n    }\n    /**\n     * Affects color data to this target\n     * @param data defines the color data to use\n     */\n    setColors(data) {\n        const hadColors = this.hasColors;\n        this._colors = data;\n        if (hadColors !== this.hasColors) {\n            this._onDataLayoutChanged.notifyObservers(undefined);\n        }\n    }\n    /**\n     * Gets the color data stored in this target\n     * @returns a FloatArray containing the color data (or null if not present)\n     */\n    getColors() {\n        return this._colors;\n    }\n    /**\n     * Clone the current target\n     * @returns a new MorphTarget\n     */\n    clone() {\n        const newOne = SerializationHelper.Clone(() => new MorphTarget(this.name, this.influence, this._scene), this);\n        newOne._positions = this._positions;\n        newOne._normals = this._normals;\n        newOne._tangents = this._tangents;\n        newOne._uvs = this._uvs;\n        newOne._uv2s = this._uv2s;\n        newOne._colors = this._colors;\n        return newOne;\n    }\n    /**\n     * Serializes the current target into a Serialization object\n     * @returns the serialized object\n     */\n    serialize() {\n        const serializationObject = {};\n        serializationObject.name = this.name;\n        serializationObject.influence = this.influence;\n        serializationObject.positions = Array.prototype.slice.call(this.getPositions());\n        if (this.id != null) {\n            serializationObject.id = this.id;\n        }\n        if (this.hasNormals) {\n            serializationObject.normals = Array.prototype.slice.call(this.getNormals());\n        }\n        if (this.hasTangents) {\n            serializationObject.tangents = Array.prototype.slice.call(this.getTangents());\n        }\n        if (this.hasUVs) {\n            serializationObject.uvs = Array.prototype.slice.call(this.getUVs());\n        }\n        if (this.hasUV2s) {\n            serializationObject.uv2s = Array.prototype.slice.call(this.getUV2s());\n        }\n        if (this.hasColors) {\n            serializationObject.colors = Array.prototype.slice.call(this.getColors());\n        }\n        // Animations\n        SerializationHelper.AppendSerializedAnimations(this, serializationObject);\n        return serializationObject;\n    }\n    /**\n     * Returns the string \"MorphTarget\"\n     * @returns \"MorphTarget\"\n     */\n    getClassName() {\n        return \"MorphTarget\";\n    }\n    // Statics\n    /**\n     * Creates a new target from serialized data\n     * @param serializationObject defines the serialized data to use\n     * @param scene defines the hosting scene\n     * @returns a new MorphTarget\n     */\n    static Parse(serializationObject, scene) {\n        const result = new MorphTarget(serializationObject.name, serializationObject.influence);\n        result.setPositions(serializationObject.positions);\n        if (serializationObject.id != null) {\n            result.id = serializationObject.id;\n        }\n        if (serializationObject.normals) {\n            result.setNormals(serializationObject.normals);\n        }\n        if (serializationObject.tangents) {\n            result.setTangents(serializationObject.tangents);\n        }\n        if (serializationObject.uvs) {\n            result.setUVs(serializationObject.uvs);\n        }\n        if (serializationObject.uv2s) {\n            result.setUV2s(serializationObject.uv2s);\n        }\n        if (serializationObject.colors) {\n            result.setColors(serializationObject.colors);\n        }\n        // Animations\n        if (serializationObject.animations) {\n            for (let animationIndex = 0; animationIndex < serializationObject.animations.length; animationIndex++) {\n                const parsedAnimation = serializationObject.animations[animationIndex];\n                const internalClass = GetClass(\"BABYLON.Animation\");\n                if (internalClass) {\n                    result.animations.push(internalClass.Parse(parsedAnimation));\n                }\n            }\n            if (serializationObject.autoAnimate && scene) {\n                scene.beginAnimation(result, serializationObject.autoAnimateFrom, serializationObject.autoAnimateTo, serializationObject.autoAnimateLoop, serializationObject.autoAnimateSpeed || 1.0);\n            }\n        }\n        return result;\n    }\n    /**\n     * Creates a MorphTarget from mesh data\n     * @param mesh defines the source mesh\n     * @param name defines the name to use for the new target\n     * @param influence defines the influence to attach to the target\n     * @returns a new MorphTarget\n     */\n    static FromMesh(mesh, name, influence) {\n        if (!name) {\n            name = mesh.name;\n        }\n        const result = new MorphTarget(name, influence, mesh.getScene());\n        result.setPositions(mesh.getVerticesData(VertexBuffer.PositionKind));\n        if (mesh.isVerticesDataPresent(VertexBuffer.NormalKind)) {\n            result.setNormals(mesh.getVerticesData(VertexBuffer.NormalKind));\n        }\n        if (mesh.isVerticesDataPresent(VertexBuffer.TangentKind)) {\n            result.setTangents(mesh.getVerticesData(VertexBuffer.TangentKind));\n        }\n        if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\n            result.setUVs(mesh.getVerticesData(VertexBuffer.UVKind));\n        }\n        if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind)) {\n            result.setUV2s(mesh.getVerticesData(VertexBuffer.UV2Kind));\n        }\n        if (mesh.isVerticesDataPresent(VertexBuffer.ColorKind)) {\n            result.setColors(mesh.getVerticesData(VertexBuffer.ColorKind));\n        }\n        return result;\n    }\n}\n__decorate([\n    serialize()\n], MorphTarget.prototype, \"id\", void 0);\n//# sourceMappingURL=morphTarget.js.map", "import { Texture } from \"./texture.js\";\n\n/**\n * Class used to store 2D array textures containing user data\n */\nexport class RawTexture2DArray extends Texture {\n    /**\n     * Gets the number of layers of the texture\n     */\n    get depth() {\n        return this._depth;\n    }\n    /**\n     * Create a new RawTexture2DArray\n     * @param data defines the data of the texture\n     * @param width defines the width of the texture\n     * @param height defines the height of the texture\n     * @param depth defines the number of layers of the texture\n     * @param format defines the texture format to use\n     * @param scene defines the hosting scene\n     * @param generateMipMaps defines a boolean indicating if mip levels should be generated (true by default)\n     * @param invertY defines if texture must be stored with Y axis inverted\n     * @param samplingMode defines the sampling mode to use (Texture.TRILINEAR_SAMPLINGMODE by default)\n     * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\n     * @param creationFlags specific flags to use when creating the texture (1 for storage textures, for eg)\n     */\n    constructor(data, width, height, depth, \n    /** Gets or sets the texture format to use */\n    format, scene, generateMipMaps = true, invertY = false, samplingMode = Texture.TRILINEAR_SAMPLINGMODE, textureType = 0, creationFlags) {\n        super(null, scene, !generateMipMaps, invertY);\n        this.format = format;\n        this._texture = scene.getEngine().createRawTexture2DArray(data, width, height, depth, format, generateMipMaps, invertY, samplingMode, null, textureType, creationFlags);\n        this._depth = depth;\n        this.is2DArray = true;\n    }\n    /**\n     * Update the texture with new data\n     * @param data defines the data to store in the texture\n     */\n    update(data) {\n        if (!this._texture) {\n            return;\n        }\n        this._getEngine().updateRawTexture2DArray(this._texture, data, this._texture.format, this._texture.invertY, null, this._texture.type);\n    }\n    /**\n     * Creates a RGBA texture from some data.\n     * @param data Define the texture data\n     * @param width Define the width of the texture\n     * @param height Define the height of the texture\n     * @param depth defines the number of layers of the texture\n     * @param scene defines the scene the texture will belong to\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\n     * @returns the RGBA texture\n     */\n    static CreateRGBATexture(data, width, height, depth, scene, generateMipMaps = true, invertY = false, samplingMode = 3, type = 0) {\n        return new RawTexture2DArray(data, width, height, depth, 5, scene, generateMipMaps, invertY, samplingMode, type);\n    }\n}\n//# sourceMappingURL=rawTexture2DArray.js.map", "import { SmartArray } from \"../Misc/smartArray.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { MorphTarget } from \"./morphTarget.js\";\n\nimport { RawTexture2DArray } from \"../Materials/Textures/rawTexture2DArray.js\";\n/**\n * This class is used to deform meshes using morphing between different targets\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/morphTargets\n */\nexport class MorphTargetManager {\n    /**\n     * Sets a boolean indicating that adding new target or updating an existing target will not update the underlying data buffers\n     */\n    set areUpdatesFrozen(block) {\n        if (block) {\n            this._blockCounter++;\n        }\n        else {\n            this._blockCounter--;\n            if (this._blockCounter <= 0) {\n                this._blockCounter = 0;\n                this._syncActiveTargets(this._forceUpdateWhenUnfrozen);\n                this._forceUpdateWhenUnfrozen = false;\n            }\n        }\n    }\n    get areUpdatesFrozen() {\n        return this._blockCounter > 0;\n    }\n    /**\n     * Creates a new MorphTargetManager\n     * @param scene defines the current scene\n     */\n    constructor(scene = null) {\n        this._targets = new Array();\n        this._targetInfluenceChangedObservers = new Array();\n        this._targetDataLayoutChangedObservers = new Array();\n        this._activeTargets = new SmartArray(16);\n        this._supportsPositions = false;\n        this._supportsNormals = false;\n        this._supportsTangents = false;\n        this._supportsUVs = false;\n        this._supportsUV2s = false;\n        this._supportsColors = false;\n        this._vertexCount = 0;\n        this._uniqueId = 0;\n        this._tempInfluences = new Array();\n        this._canUseTextureForTargets = false;\n        this._blockCounter = 0;\n        this._mustSynchronize = true;\n        this._forceUpdateWhenUnfrozen = false;\n        /** @internal */\n        this._textureVertexStride = 0;\n        /** @internal */\n        this._textureWidth = 0;\n        /** @internal */\n        this._textureHeight = 1;\n        /** @internal */\n        this._parentContainer = null;\n        /**\n         * Gets or sets a boolean indicating if influencers must be optimized (eg. recompiling the shader if less influencers are used)\n         */\n        this.optimizeInfluencers = true;\n        /**\n         * Gets or sets a boolean indicating if positions must be morphed\n         */\n        this.enablePositionMorphing = true;\n        /**\n         * Gets or sets a boolean indicating if normals must be morphed\n         */\n        this.enableNormalMorphing = true;\n        /**\n         * Gets or sets a boolean indicating if tangents must be morphed\n         */\n        this.enableTangentMorphing = true;\n        /**\n         * Gets or sets a boolean indicating if UV must be morphed\n         */\n        this.enableUVMorphing = true;\n        /**\n         * Gets or sets a boolean indicating if UV2 must be morphed\n         */\n        this.enableUV2Morphing = true;\n        /**\n         * Gets or sets a boolean indicating if colors must be morphed\n         */\n        this.enableColorMorphing = true;\n        this._numMaxInfluencers = 0;\n        this._useTextureToStoreTargets = true;\n        if (!scene) {\n            scene = EngineStore.LastCreatedScene;\n        }\n        this._scene = scene;\n        if (this._scene) {\n            this._scene.addMorphTargetManager(this);\n            this._uniqueId = this._scene.getUniqueId();\n            const engineCaps = this._scene.getEngine().getCaps();\n            this._canUseTextureForTargets =\n                engineCaps.canUseGLVertexID && engineCaps.textureFloat && engineCaps.maxVertexTextureImageUnits > 0 && engineCaps.texture2DArrayMaxLayerCount > 1;\n        }\n    }\n    /**\n     * Gets or sets the maximum number of influencers (targets) (default value: 0).\n     * Setting a value for this property can lead to a smoother experience, as only one shader will be compiled, which will use this value as the maximum number of influencers.\n     * If you leave the value at 0 (default), a new shader will be compiled every time the number of active influencers changes. This can cause problems, as compiling a shader takes time.\n     * If you assign a non-zero value to this property, you need to ensure that this value is greater than the maximum number of (active) influencers you'll need for this morph manager.\n     * Otherwise, the number of active influencers will be truncated at the value you set for this property, which can lead to unexpected results.\n     * Note that this property has no effect if \"useTextureToStoreTargets\" is false.\n     */\n    get numMaxInfluencers() {\n        return this._numMaxInfluencers;\n    }\n    set numMaxInfluencers(value) {\n        if (this._numMaxInfluencers === value) {\n            return;\n        }\n        this._numMaxInfluencers = value;\n        this._mustSynchronize = true;\n        this._syncActiveTargets();\n    }\n    /**\n     * Gets the unique ID of this manager\n     */\n    get uniqueId() {\n        return this._uniqueId;\n    }\n    /**\n     * Gets the number of vertices handled by this manager\n     */\n    get vertexCount() {\n        return this._vertexCount;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of positions\n     */\n    get supportsPositions() {\n        return this._supportsPositions && this.enablePositionMorphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of normals\n     */\n    get supportsNormals() {\n        return this._supportsNormals && this.enableNormalMorphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of tangents\n     */\n    get supportsTangents() {\n        return this._supportsTangents && this.enableTangentMorphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of texture coordinates\n     */\n    get supportsUVs() {\n        return this._supportsUVs && this.enableUVMorphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of texture coordinates 2\n     */\n    get supportsUV2s() {\n        return this._supportsUV2s && this.enableUV2Morphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager supports morphing of colors\n     */\n    get supportsColors() {\n        return this._supportsColors && this.enableColorMorphing;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing positions\n     */\n    get hasPositions() {\n        return this._supportsPositions;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing normals\n     */\n    get hasNormals() {\n        return this._supportsNormals;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing tangents\n     */\n    get hasTangents() {\n        return this._supportsTangents;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing texture coordinates\n     */\n    get hasUVs() {\n        return this._supportsUVs;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing texture coordinates 2\n     */\n    get hasUV2s() {\n        return this._supportsUV2s;\n    }\n    /**\n     * Gets a boolean indicating if this manager has data for morphing colors\n     */\n    get hasColors() {\n        return this._supportsColors;\n    }\n    /**\n     * Gets the number of targets stored in this manager\n     */\n    get numTargets() {\n        return this._targets.length;\n    }\n    /**\n     * Gets the number of influencers (ie. the number of targets with influences > 0)\n     */\n    get numInfluencers() {\n        return this._activeTargets.length;\n    }\n    /**\n     * Gets the list of influences (one per target)\n     */\n    get influences() {\n        return this._influences;\n    }\n    /**\n     * Gets or sets a boolean indicating that targets should be stored as a texture instead of using vertex attributes (default is true).\n     * Please note that this option is not available if the hardware does not support it\n     */\n    get useTextureToStoreTargets() {\n        return this._useTextureToStoreTargets;\n    }\n    set useTextureToStoreTargets(value) {\n        if (this._useTextureToStoreTargets === value) {\n            return;\n        }\n        this._useTextureToStoreTargets = value;\n        this._mustSynchronize = true;\n        this._syncActiveTargets();\n    }\n    /**\n     * Gets a boolean indicating that the targets are stored into a texture (instead of as attributes)\n     */\n    get isUsingTextureForTargets() {\n        return (MorphTargetManager.EnableTextureStorage &&\n            this.useTextureToStoreTargets &&\n            this._canUseTextureForTargets &&\n            !this._scene?.getEngine().getCaps().disableMorphTargetTexture);\n    }\n    /**\n     * Gets the active target at specified index. An active target is a target with an influence > 0\n     * @param index defines the index to check\n     * @returns the requested target\n     */\n    getActiveTarget(index) {\n        return this._activeTargets.data[index];\n    }\n    /**\n     * Gets the target at specified index\n     * @param index defines the index to check\n     * @returns the requested target\n     */\n    getTarget(index) {\n        return this._targets[index];\n    }\n    /**\n     * Gets the first target with the specified name\n     * @param name defines the name to check\n     * @returns the requested target\n     */\n    getTargetByName(name) {\n        for (const target of this._targets) {\n            if (target.name === name) {\n                return target;\n            }\n        }\n        return null;\n    }\n    /**\n     * Add a new target to this manager\n     * @param target defines the target to add\n     */\n    addTarget(target) {\n        this._targets.push(target);\n        this._targetInfluenceChangedObservers.push(target.onInfluenceChanged.add((needUpdate) => {\n            if (this.areUpdatesFrozen && needUpdate) {\n                this._forceUpdateWhenUnfrozen = true;\n            }\n            this._syncActiveTargets(needUpdate);\n        }));\n        this._targetDataLayoutChangedObservers.push(target._onDataLayoutChanged.add(() => {\n            this._mustSynchronize = true;\n            this._syncActiveTargets();\n        }));\n        this._mustSynchronize = true;\n        this._syncActiveTargets();\n    }\n    /**\n     * Removes a target from the manager\n     * @param target defines the target to remove\n     */\n    removeTarget(target) {\n        const index = this._targets.indexOf(target);\n        if (index >= 0) {\n            this._targets.splice(index, 1);\n            target.onInfluenceChanged.remove(this._targetInfluenceChangedObservers.splice(index, 1)[0]);\n            target._onDataLayoutChanged.remove(this._targetDataLayoutChangedObservers.splice(index, 1)[0]);\n            this._mustSynchronize = true;\n            this._syncActiveTargets();\n        }\n        if (this._scene) {\n            this._scene.stopAnimation(target);\n        }\n    }\n    /**\n     * @internal\n     */\n    _bind(effect) {\n        effect.setFloat3(\"morphTargetTextureInfo\", this._textureVertexStride, this._textureWidth, this._textureHeight);\n        effect.setFloatArray(\"morphTargetTextureIndices\", this._morphTargetTextureIndices);\n        effect.setTexture(\"morphTargets\", this._targetStoreTexture);\n        effect.setInt(\"morphTargetCount\", this.numInfluencers);\n    }\n    /**\n     * Clone the current manager\n     * @returns a new MorphTargetManager\n     */\n    clone() {\n        const copy = new MorphTargetManager(this._scene);\n        for (const target of this._targets) {\n            copy.addTarget(target.clone());\n        }\n        copy.enablePositionMorphing = this.enablePositionMorphing;\n        copy.enableNormalMorphing = this.enableNormalMorphing;\n        copy.enableTangentMorphing = this.enableTangentMorphing;\n        copy.enableUVMorphing = this.enableUVMorphing;\n        copy.enableUV2Morphing = this.enableUV2Morphing;\n        copy.enableColorMorphing = this.enableColorMorphing;\n        return copy;\n    }\n    /**\n     * Serializes the current manager into a Serialization object\n     * @returns the serialized object\n     */\n    serialize() {\n        const serializationObject = {};\n        serializationObject.id = this.uniqueId;\n        serializationObject.targets = [];\n        for (const target of this._targets) {\n            serializationObject.targets.push(target.serialize());\n        }\n        return serializationObject;\n    }\n    _syncActiveTargets(needUpdate = false) {\n        if (this.areUpdatesFrozen) {\n            return;\n        }\n        const wasUsingTextureForTargets = !!this._targetStoreTexture;\n        const isUsingTextureForTargets = this.isUsingTextureForTargets;\n        if (this._mustSynchronize || wasUsingTextureForTargets !== isUsingTextureForTargets) {\n            this._mustSynchronize = false;\n            this.synchronize();\n        }\n        let influenceCount = 0;\n        this._activeTargets.reset();\n        if (!this._morphTargetTextureIndices || this._morphTargetTextureIndices.length !== this._targets.length) {\n            this._morphTargetTextureIndices = new Float32Array(this._targets.length);\n        }\n        let targetIndex = -1;\n        for (const target of this._targets) {\n            targetIndex++;\n            if (target.influence === 0 && this.optimizeInfluencers) {\n                continue;\n            }\n            if (this._activeTargets.length >= MorphTargetManager.MaxActiveMorphTargetsInVertexAttributeMode && !this.isUsingTextureForTargets) {\n                break;\n            }\n            this._activeTargets.push(target);\n            this._morphTargetTextureIndices[influenceCount] = targetIndex;\n            this._tempInfluences[influenceCount++] = target.influence;\n        }\n        if (this._morphTargetTextureIndices.length !== influenceCount) {\n            this._morphTargetTextureIndices = this._morphTargetTextureIndices.slice(0, influenceCount);\n        }\n        if (!this._influences || this._influences.length !== influenceCount) {\n            this._influences = new Float32Array(influenceCount);\n        }\n        for (let index = 0; index < influenceCount; index++) {\n            this._influences[index] = this._tempInfluences[index];\n        }\n        if (needUpdate && this._scene) {\n            for (const mesh of this._scene.meshes) {\n                if (mesh.morphTargetManager === this) {\n                    if (isUsingTextureForTargets) {\n                        mesh._markSubMeshesAsAttributesDirty();\n                    }\n                    else {\n                        mesh._syncGeometryWithMorphTargetManager();\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Synchronize the targets with all the meshes using this morph target manager\n     */\n    synchronize() {\n        if (!this._scene || this.areUpdatesFrozen) {\n            return;\n        }\n        const engine = this._scene.getEngine();\n        this._supportsPositions = true;\n        this._supportsNormals = true;\n        this._supportsTangents = true;\n        this._supportsUVs = true;\n        this._supportsUV2s = true;\n        this._supportsColors = true;\n        this._vertexCount = 0;\n        this._targetStoreTexture?.dispose();\n        this._targetStoreTexture = null;\n        if (this.isUsingTextureForTargets && this._targets.length > engine.getCaps().texture2DArrayMaxLayerCount) {\n            this.useTextureToStoreTargets = false;\n        }\n        for (const target of this._targets) {\n            this._supportsPositions = this._supportsPositions && target.hasPositions;\n            this._supportsNormals = this._supportsNormals && target.hasNormals;\n            this._supportsTangents = this._supportsTangents && target.hasTangents;\n            this._supportsUVs = this._supportsUVs && target.hasUVs;\n            this._supportsUV2s = this._supportsUV2s && target.hasUV2s;\n            this._supportsColors = this._supportsColors && target.hasColors;\n            const vertexCount = target.vertexCount;\n            if (this._vertexCount === 0) {\n                this._vertexCount = vertexCount;\n            }\n            else if (this._vertexCount !== vertexCount) {\n                Logger.Error(`Incompatible target. Targets must all have the same vertices count. Current vertex count: ${this._vertexCount}, vertex count for target \"${target.name}\": ${vertexCount}`);\n                return;\n            }\n        }\n        if (this.isUsingTextureForTargets) {\n            this._textureVertexStride = 0;\n            this._supportsPositions && this._textureVertexStride++;\n            this._supportsNormals && this._textureVertexStride++;\n            this._supportsTangents && this._textureVertexStride++;\n            this._supportsUVs && this._textureVertexStride++;\n            this._supportsUV2s && this._textureVertexStride++;\n            this._supportsColors && this._textureVertexStride++;\n            this._textureWidth = this._vertexCount * this._textureVertexStride || 1;\n            this._textureHeight = 1;\n            const maxTextureSize = engine.getCaps().maxTextureSize;\n            if (this._textureWidth > maxTextureSize) {\n                this._textureHeight = Math.ceil(this._textureWidth / maxTextureSize);\n                this._textureWidth = maxTextureSize;\n            }\n            const targetCount = this._targets.length;\n            const data = new Float32Array(targetCount * this._textureWidth * this._textureHeight * 4);\n            let offset = 0;\n            for (let index = 0; index < targetCount; index++) {\n                const target = this._targets[index];\n                const positions = target.getPositions();\n                const normals = target.getNormals();\n                const uvs = target.getUVs();\n                const tangents = target.getTangents();\n                const uv2s = target.getUV2s();\n                const colors = target.getColors();\n                offset = index * this._textureWidth * this._textureHeight * 4;\n                for (let vertex = 0; vertex < this._vertexCount; vertex++) {\n                    if (this._supportsPositions && positions) {\n                        data[offset] = positions[vertex * 3];\n                        data[offset + 1] = positions[vertex * 3 + 1];\n                        data[offset + 2] = positions[vertex * 3 + 2];\n                        offset += 4;\n                    }\n                    if (this._supportsNormals && normals) {\n                        data[offset] = normals[vertex * 3];\n                        data[offset + 1] = normals[vertex * 3 + 1];\n                        data[offset + 2] = normals[vertex * 3 + 2];\n                        offset += 4;\n                    }\n                    if (this._supportsUVs && uvs) {\n                        data[offset] = uvs[vertex * 2];\n                        data[offset + 1] = uvs[vertex * 2 + 1];\n                        offset += 4;\n                    }\n                    if (this._supportsTangents && tangents) {\n                        data[offset] = tangents[vertex * 3];\n                        data[offset + 1] = tangents[vertex * 3 + 1];\n                        data[offset + 2] = tangents[vertex * 3 + 2];\n                        offset += 4;\n                    }\n                    if (this._supportsUV2s && uv2s) {\n                        data[offset] = uv2s[vertex * 2];\n                        data[offset + 1] = uv2s[vertex * 2 + 1];\n                        offset += 4;\n                    }\n                    if (this._supportsColors && colors) {\n                        data[offset] = colors[vertex * 4];\n                        data[offset + 1] = colors[vertex * 4 + 1];\n                        data[offset + 2] = colors[vertex * 4 + 2];\n                        data[offset + 3] = colors[vertex * 4 + 3];\n                        offset += 4;\n                    }\n                }\n            }\n            this._targetStoreTexture = RawTexture2DArray.CreateRGBATexture(data, this._textureWidth, this._textureHeight, targetCount, this._scene, false, false, 1, 1);\n            this._targetStoreTexture.name = `Morph texture_${this.uniqueId}`;\n        }\n        // Flag meshes as dirty to resync with the active targets\n        for (const mesh of this._scene.meshes) {\n            if (mesh.morphTargetManager === this) {\n                mesh._syncGeometryWithMorphTargetManager();\n            }\n        }\n    }\n    /**\n     * Release all resources\n     */\n    dispose() {\n        if (this._targetStoreTexture) {\n            this._targetStoreTexture.dispose();\n        }\n        this._targetStoreTexture = null;\n        // Remove from scene\n        if (this._scene) {\n            this._scene.removeMorphTargetManager(this);\n            if (this._parentContainer) {\n                const index = this._parentContainer.morphTargetManagers.indexOf(this);\n                if (index > -1) {\n                    this._parentContainer.morphTargetManagers.splice(index, 1);\n                }\n                this._parentContainer = null;\n            }\n            for (const morph of this._targets) {\n                this._scene.stopAnimation(morph);\n            }\n        }\n    }\n    // Statics\n    /**\n     * Creates a new MorphTargetManager from serialized data\n     * @param serializationObject defines the serialized data\n     * @param scene defines the hosting scene\n     * @returns the new MorphTargetManager\n     */\n    static Parse(serializationObject, scene) {\n        const result = new MorphTargetManager(scene);\n        for (const targetData of serializationObject.targets) {\n            result.addTarget(MorphTarget.Parse(targetData, scene));\n        }\n        return result;\n    }\n}\n/** Enable storing morph target data into textures when set to true (true by default) */\nMorphTargetManager.EnableTextureStorage = true;\n/** Maximum number of active morph targets supported in the \"vertex attribute\" mode (i.e., not the \"texture\" mode) */\nMorphTargetManager.MaxActiveMorphTargetsInVertexAttributeMode = 8;\n//# sourceMappingURL=morphTargetManager.js.map", "import { Decode } from \"./stringTools.js\";\n/**\n * Utility class for reading from a data buffer\n */\nexport class DataReader {\n    /**\n     * Constructor\n     * @param buffer The buffer to read\n     */\n    constructor(buffer) {\n        /**\n         * The current byte offset from the beginning of the data buffer.\n         */\n        this.byteOffset = 0;\n        this.buffer = buffer;\n    }\n    /**\n     * Loads the given byte length.\n     * @param byteLength The byte length to load\n     * @returns A promise that resolves when the load is complete\n     */\n    loadAsync(byteLength) {\n        return this.buffer.readAsync(this.byteOffset, byteLength).then((data) => {\n            this._dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);\n            this._dataByteOffset = 0;\n        });\n    }\n    /**\n     * Read a unsigned 32-bit integer from the currently loaded data range.\n     * @returns The 32-bit integer read\n     */\n    readUint32() {\n        const value = this._dataView.getUint32(this._dataByteOffset, true);\n        this._dataByteOffset += 4;\n        this.byteOffset += 4;\n        return value;\n    }\n    /**\n     * Read a byte array from the currently loaded data range.\n     * @param byteLength The byte length to read\n     * @returns The byte array read\n     */\n    readUint8Array(byteLength) {\n        const value = new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + this._dataByteOffset, byteLength);\n        this._dataByteOffset += byteLength;\n        this.byteOffset += byteLength;\n        return value;\n    }\n    /**\n     * Read a string from the currently loaded data range.\n     * @param byteLength The byte length to read\n     * @returns The string read\n     */\n    readString(byteLength) {\n        return Decode(this.readUint8Array(byteLength));\n    }\n    /**\n     * Skips the given byte length the currently loaded data range.\n     * @param byteLength The byte length to skip\n     */\n    skipBytes(byteLength) {\n        this._dataByteOffset += byteLength;\n        this.byteOffset += byteLength;\n    }\n}\n//# sourceMappingURL=dataReader.js.map", "import { Tools } from \"@babylonjs/core/Misc/tools.js\";\nfunction validateAsync(data, rootUrl, fileName, getExternalResource) {\n    const options = {\n        externalResourceFunction: getExternalResource,\n    };\n    if (fileName) {\n        options.uri = rootUrl === \"file:\" ? fileName : rootUrl + fileName;\n    }\n    return ArrayBuffer.isView(data) ? GLTFValidator.validateBytes(data, options) : GLTFValidator.validateString(data, options);\n}\n/**\n * The worker function that gets converted to a blob url to pass into a worker.\n */\nfunction workerFunc() {\n    const pendingExternalResources = [];\n    onmessage = (message) => {\n        const data = message.data;\n        switch (data.id) {\n            case \"init\": {\n                importScripts(data.url);\n                break;\n            }\n            case \"validate\": {\n                validateAsync(data.data, data.rootUrl, data.fileName, (uri) => new Promise((resolve, reject) => {\n                    const index = pendingExternalResources.length;\n                    pendingExternalResources.push({ resolve, reject });\n                    postMessage({ id: \"getExternalResource\", index: index, uri: uri });\n                })).then((value) => {\n                    postMessage({ id: \"validate.resolve\", value: value });\n                }, (reason) => {\n                    postMessage({ id: \"validate.reject\", reason: reason });\n                });\n                break;\n            }\n            case \"getExternalResource.resolve\": {\n                pendingExternalResources[data.index].resolve(data.value);\n                break;\n            }\n            case \"getExternalResource.reject\": {\n                pendingExternalResources[data.index].reject(data.reason);\n                break;\n            }\n        }\n    };\n}\n/**\n * glTF validation\n */\nexport class GLTFValidation {\n    /**\n     * Validate a glTF asset using the glTF-Validator.\n     * @param data The JSON of a glTF or the array buffer of a binary glTF\n     * @param rootUrl The root url for the glTF\n     * @param fileName The file name for the glTF\n     * @param getExternalResource The callback to get external resources for the glTF validator\n     * @returns A promise that resolves with the glTF validation results once complete\n     */\n    static ValidateAsync(data, rootUrl, fileName, getExternalResource) {\n        if (typeof Worker === \"function\") {\n            return new Promise((resolve, reject) => {\n                const workerContent = `${validateAsync}(${workerFunc})()`;\n                const workerBlobUrl = URL.createObjectURL(new Blob([workerContent], { type: \"application/javascript\" }));\n                const worker = new Worker(workerBlobUrl);\n                const onError = (error) => {\n                    worker.removeEventListener(\"error\", onError);\n                    worker.removeEventListener(\"message\", onMessage);\n                    reject(error);\n                };\n                const onMessage = (message) => {\n                    const data = message.data;\n                    switch (data.id) {\n                        case \"getExternalResource\": {\n                            getExternalResource(data.uri).then((value) => {\n                                worker.postMessage({ id: \"getExternalResource.resolve\", index: data.index, value: value }, [value.buffer]);\n                            }, (reason) => {\n                                worker.postMessage({ id: \"getExternalResource.reject\", index: data.index, reason: reason });\n                            });\n                            break;\n                        }\n                        case \"validate.resolve\": {\n                            worker.removeEventListener(\"error\", onError);\n                            worker.removeEventListener(\"message\", onMessage);\n                            resolve(data.value);\n                            worker.terminate();\n                            break;\n                        }\n                        case \"validate.reject\": {\n                            worker.removeEventListener(\"error\", onError);\n                            worker.removeEventListener(\"message\", onMessage);\n                            reject(data.reason);\n                            worker.terminate();\n                        }\n                    }\n                };\n                worker.addEventListener(\"error\", onError);\n                worker.addEventListener(\"message\", onMessage);\n                worker.postMessage({ id: \"init\", url: Tools.GetBabylonScriptURL(this.Configuration.url) });\n                if (ArrayBuffer.isView(data)) {\n                    // Slice the data to avoid copying the whole array buffer.\n                    const slicedData = data.slice();\n                    worker.postMessage({ id: \"validate\", data: slicedData, rootUrl: rootUrl, fileName: fileName }, [slicedData.buffer]);\n                }\n                else {\n                    worker.postMessage({ id: \"validate\", data: data, rootUrl: rootUrl, fileName: fileName });\n                }\n            });\n        }\n        else {\n            if (!this._LoadScriptPromise) {\n                this._LoadScriptPromise = Tools.LoadBabylonScriptAsync(this.Configuration.url);\n            }\n            return this._LoadScriptPromise.then(() => {\n                return validateAsync(data, rootUrl, fileName, getExternalResource);\n            });\n        }\n    }\n}\n/**\n * The configuration. Defaults to `{ url: \"https://cdn.babylonjs.com/gltf_validator.js\" }`.\n */\nGLTFValidation.Configuration = {\n    url: `${Tools._DefaultCdnUrl}/gltf_validator.js`,\n};\n//# sourceMappingURL=glTFValidation.js.map", "import { Observable } from \"@babylonjs/core/Misc/observable.js\";\nimport { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { RegisterSceneLoaderPlugin } from \"@babylonjs/core/Loading/sceneLoader.js\";\nimport { AssetContainer } from \"@babylonjs/core/assetContainer.js\";\nimport { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { DataReader } from \"@babylonjs/core/Misc/dataReader.js\";\nimport { GLTFValidation } from \"./glTFValidation.js\";\nimport { GLTFFileLoaderMetadata, GLTFMagicBase64Encoded } from \"./glTFFileLoader.metadata.js\";\nimport { DecodeBase64UrlToBinary } from \"@babylonjs/core/Misc/fileTools.js\";\nimport { RuntimeError, ErrorCodes } from \"@babylonjs/core/Misc/error.js\";\nfunction readAsync(arrayBuffer, byteOffset, byteLength) {\n    try {\n        return Promise.resolve(new Uint8Array(arrayBuffer, byteOffset, byteLength));\n    }\n    catch (e) {\n        return Promise.reject(e);\n    }\n}\nfunction readViewAsync(arrayBufferView, byteOffset, byteLength) {\n    try {\n        if (byteOffset < 0 || byteOffset >= arrayBufferView.byteLength) {\n            throw new RangeError(\"Offset is out of range.\");\n        }\n        if (byteOffset + byteLength > arrayBufferView.byteLength) {\n            throw new RangeError(\"Length is out of range.\");\n        }\n        return Promise.resolve(new Uint8Array(arrayBufferView.buffer, arrayBufferView.byteOffset + byteOffset, byteLength));\n    }\n    catch (e) {\n        return Promise.reject(e);\n    }\n}\n/**\n * Mode that determines the coordinate system to use.\n */\nexport var GLTFLoaderCoordinateSystemMode;\n(function (GLTFLoaderCoordinateSystemMode) {\n    /**\n     * Automatically convert the glTF right-handed data to the appropriate system based on the current coordinate system mode of the scene.\n     */\n    GLTFLoaderCoordinateSystemMode[GLTFLoaderCoordinateSystemMode[\"AUTO\"] = 0] = \"AUTO\";\n    /**\n     * Sets the useRightHandedSystem flag on the scene.\n     */\n    GLTFLoaderCoordinateSystemMode[GLTFLoaderCoordinateSystemMode[\"FORCE_RIGHT_HANDED\"] = 1] = \"FORCE_RIGHT_HANDED\";\n})(GLTFLoaderCoordinateSystemMode || (GLTFLoaderCoordinateSystemMode = {}));\n/**\n * Mode that determines what animations will start.\n */\nexport var GLTFLoaderAnimationStartMode;\n(function (GLTFLoaderAnimationStartMode) {\n    /**\n     * No animation will start.\n     */\n    GLTFLoaderAnimationStartMode[GLTFLoaderAnimationStartMode[\"NONE\"] = 0] = \"NONE\";\n    /**\n     * The first animation will start.\n     */\n    GLTFLoaderAnimationStartMode[GLTFLoaderAnimationStartMode[\"FIRST\"] = 1] = \"FIRST\";\n    /**\n     * All animations will start.\n     */\n    GLTFLoaderAnimationStartMode[GLTFLoaderAnimationStartMode[\"ALL\"] = 2] = \"ALL\";\n})(GLTFLoaderAnimationStartMode || (GLTFLoaderAnimationStartMode = {}));\n/**\n * Loader state.\n */\nexport var GLTFLoaderState;\n(function (GLTFLoaderState) {\n    /**\n     * The asset is loading.\n     */\n    GLTFLoaderState[GLTFLoaderState[\"LOADING\"] = 0] = \"LOADING\";\n    /**\n     * The asset is ready for rendering.\n     */\n    GLTFLoaderState[GLTFLoaderState[\"READY\"] = 1] = \"READY\";\n    /**\n     * The asset is completely loaded.\n     */\n    GLTFLoaderState[GLTFLoaderState[\"COMPLETE\"] = 2] = \"COMPLETE\";\n})(GLTFLoaderState || (GLTFLoaderState = {}));\nclass GLTFLoaderOptions {\n    constructor() {\n        // ----------\n        // V2 options\n        // ----------\n        /**\n         * The coordinate system mode. Defaults to AUTO.\n         */\n        this.coordinateSystemMode = GLTFLoaderCoordinateSystemMode.AUTO;\n        /**\n         * The animation start mode. Defaults to FIRST.\n         */\n        this.animationStartMode = GLTFLoaderAnimationStartMode.FIRST;\n        /**\n         * Defines if the loader should load node animations. Defaults to true.\n         * NOTE: The animation of this node will still load if the node is also a joint of a skin and `loadSkins` is true.\n         */\n        this.loadNodeAnimations = true;\n        /**\n         * Defines if the loader should load skins. Defaults to true.\n         */\n        this.loadSkins = true;\n        /**\n         * Defines if the loader should load morph targets. Defaults to true.\n         */\n        this.loadMorphTargets = true;\n        /**\n         * Defines if the loader should compile materials before raising the success callback. Defaults to false.\n         */\n        this.compileMaterials = false;\n        /**\n         * Defines if the loader should also compile materials with clip planes. Defaults to false.\n         */\n        this.useClipPlane = false;\n        /**\n         * Defines if the loader should compile shadow generators before raising the success callback. Defaults to false.\n         */\n        this.compileShadowGenerators = false;\n        /**\n         * Defines if the Alpha blended materials are only applied as coverage.\n         * If false, (default) The luminance of each pixel will reduce its opacity to simulate the behaviour of most physical materials.\n         * If true, no extra effects are applied to transparent pixels.\n         */\n        this.transparencyAsCoverage = false;\n        /**\n         * Defines if the loader should use range requests when load binary glTF files from HTTP.\n         * Enabling will disable offline support and glTF validator.\n         * Defaults to false.\n         */\n        this.useRangeRequests = false;\n        /**\n         * Defines if the loader should create instances when multiple glTF nodes point to the same glTF mesh. Defaults to true.\n         */\n        this.createInstances = true;\n        /**\n         * Defines if the loader should always compute the bounding boxes of meshes and not use the min/max values from the position accessor. Defaults to false.\n         */\n        this.alwaysComputeBoundingBox = false;\n        /**\n         * If true, load all materials defined in the file, even if not used by any mesh. Defaults to false.\n         */\n        this.loadAllMaterials = false;\n        /**\n         * If true, load only the materials defined in the file. Defaults to false.\n         */\n        this.loadOnlyMaterials = false;\n        /**\n         * If true, do not load any materials defined in the file. Defaults to false.\n         */\n        this.skipMaterials = false;\n        /**\n         * If true, load the color (gamma encoded) textures into sRGB buffers (if supported by the GPU), which will yield more accurate results when sampling the texture. Defaults to true.\n         */\n        this.useSRGBBuffers = true;\n        /**\n         * When loading glTF animations, which are defined in seconds, target them to this FPS. Defaults to 60.\n         */\n        this.targetFps = 60;\n        /**\n         * Defines if the loader should always compute the nearest common ancestor of the skeleton joints instead of using `skin.skeleton`. Defaults to false.\n         * Set this to true if loading assets with invalid `skin.skeleton` values.\n         */\n        this.alwaysComputeSkeletonRootNode = false;\n        /**\n         * If true, the loader will derive the name for Babylon textures from the glTF texture name, image name, or image url. Defaults to false.\n         * Note that it is possible for multiple Babylon textures to share the same name when the Babylon textures load from the same glTF texture or image.\n         */\n        this.useGltfTextureNames = false;\n        /**\n         * Function called before loading a url referenced by the asset.\n         * @param url url referenced by the asset\n         * @returns Async url to load\n         */\n        this.preprocessUrlAsync = (url) => Promise.resolve(url);\n        /**\n         * Defines options for glTF extensions.\n         */\n        this.extensionOptions = {};\n    }\n    // eslint-disable-next-line babylonjs/available\n    copyFrom(options) {\n        if (options) {\n            this.onParsed = options.onParsed;\n            this.coordinateSystemMode = options.coordinateSystemMode ?? this.coordinateSystemMode;\n            this.animationStartMode = options.animationStartMode ?? this.animationStartMode;\n            this.loadNodeAnimations = options.loadNodeAnimations ?? this.loadNodeAnimations;\n            this.loadSkins = options.loadSkins ?? this.loadSkins;\n            this.loadMorphTargets = options.loadMorphTargets ?? this.loadMorphTargets;\n            this.compileMaterials = options.compileMaterials ?? this.compileMaterials;\n            this.useClipPlane = options.useClipPlane ?? this.useClipPlane;\n            this.compileShadowGenerators = options.compileShadowGenerators ?? this.compileShadowGenerators;\n            this.transparencyAsCoverage = options.transparencyAsCoverage ?? this.transparencyAsCoverage;\n            this.useRangeRequests = options.useRangeRequests ?? this.useRangeRequests;\n            this.createInstances = options.createInstances ?? this.createInstances;\n            this.alwaysComputeBoundingBox = options.alwaysComputeBoundingBox ?? this.alwaysComputeBoundingBox;\n            this.loadAllMaterials = options.loadAllMaterials ?? this.loadAllMaterials;\n            this.loadOnlyMaterials = options.loadOnlyMaterials ?? this.loadOnlyMaterials;\n            this.skipMaterials = options.skipMaterials ?? this.skipMaterials;\n            this.useSRGBBuffers = options.useSRGBBuffers ?? this.useSRGBBuffers;\n            this.targetFps = options.targetFps ?? this.targetFps;\n            this.alwaysComputeSkeletonRootNode = options.alwaysComputeSkeletonRootNode ?? this.alwaysComputeSkeletonRootNode;\n            this.useGltfTextureNames = options.useGltfTextureNames ?? this.useGltfTextureNames;\n            this.preprocessUrlAsync = options.preprocessUrlAsync ?? this.preprocessUrlAsync;\n            this.customRootNode = options.customRootNode;\n            this.onMeshLoaded = options.onMeshLoaded;\n            this.onSkinLoaded = options.onSkinLoaded;\n            this.onTextureLoaded = options.onTextureLoaded;\n            this.onMaterialLoaded = options.onMaterialLoaded;\n            this.onCameraLoaded = options.onCameraLoaded;\n            this.extensionOptions = options.extensionOptions ?? this.extensionOptions;\n        }\n    }\n}\n/**\n * File loader for loading glTF files into a scene.\n */\nexport class GLTFFileLoader extends GLTFLoaderOptions {\n    /**\n     * Creates a new glTF file loader.\n     * @param options The options for the loader\n     */\n    constructor(options) {\n        super();\n        // --------------------\n        // Begin Common options\n        // --------------------\n        /**\n         * Raised when the asset has been parsed\n         */\n        this.onParsedObservable = new Observable();\n        // --------------\n        // End V1 options\n        // --------------\n        /**\n         * Observable raised when the loader creates a mesh after parsing the glTF properties of the mesh.\n         * Note that the observable is raised as soon as the mesh object is created, meaning some data may not have been setup yet for this mesh (vertex data, morph targets, material, ...)\n         */\n        this.onMeshLoadedObservable = new Observable();\n        /**\n         * Observable raised when the loader creates a skin after parsing the glTF properties of the skin node.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/glTF/glTFSkinning#ignoring-the-transform-of-the-skinned-mesh\n         * @param node - the transform node that corresponds to the original glTF skin node used for animations\n         * @param skinnedNode - the transform node that is the skinned mesh itself or the parent of the skinned meshes\n         */\n        this.onSkinLoadedObservable = new Observable();\n        /**\n         * Observable raised when the loader creates a texture after parsing the glTF properties of the texture.\n         */\n        this.onTextureLoadedObservable = new Observable();\n        /**\n         * Observable raised when the loader creates a material after parsing the glTF properties of the material.\n         */\n        this.onMaterialLoadedObservable = new Observable();\n        /**\n         * Observable raised when the loader creates a camera after parsing the glTF properties of the camera.\n         */\n        this.onCameraLoadedObservable = new Observable();\n        /**\n         * Observable raised when the asset is completely loaded, immediately before the loader is disposed.\n         * For assets with LODs, raised when all of the LODs are complete.\n         * For assets without LODs, raised when the model is complete, immediately after the loader resolves the returned promise.\n         */\n        this.onCompleteObservable = new Observable();\n        /**\n         * Observable raised when an error occurs.\n         */\n        this.onErrorObservable = new Observable();\n        /**\n         * Observable raised after the loader is disposed.\n         */\n        this.onDisposeObservable = new Observable();\n        /**\n         * Observable raised after a loader extension is created.\n         * Set additional options for a loader extension in this event.\n         */\n        this.onExtensionLoadedObservable = new Observable();\n        /**\n         * Defines if the loader should validate the asset.\n         */\n        this.validate = false;\n        /**\n         * Observable raised after validation when validate is set to true. The event data is the result of the validation.\n         */\n        this.onValidatedObservable = new Observable();\n        this._loader = null;\n        this._state = null;\n        this._requests = new Array();\n        /**\n         * Name of the loader (\"gltf\")\n         */\n        this.name = GLTFFileLoaderMetadata.name;\n        /** @internal */\n        this.extensions = GLTFFileLoaderMetadata.extensions;\n        /**\n         * Observable raised when the loader state changes.\n         */\n        this.onLoaderStateChangedObservable = new Observable();\n        this._logIndentLevel = 0;\n        this._loggingEnabled = false;\n        /** @internal */\n        this._log = this._logDisabled;\n        this._capturePerformanceCounters = false;\n        /** @internal */\n        this._startPerformanceCounter = this._startPerformanceCounterDisabled;\n        /** @internal */\n        this._endPerformanceCounter = this._endPerformanceCounterDisabled;\n        this.copyFrom(options);\n    }\n    /**\n     * Raised when the asset has been parsed\n     */\n    set onParsed(callback) {\n        if (this._onParsedObserver) {\n            this.onParsedObservable.remove(this._onParsedObserver);\n        }\n        if (callback) {\n            this._onParsedObserver = this.onParsedObservable.add(callback);\n        }\n    }\n    /**\n     * Callback raised when the loader creates a mesh after parsing the glTF properties of the mesh.\n     * Note that the callback is called as soon as the mesh object is created, meaning some data may not have been setup yet for this mesh (vertex data, morph targets, material, ...)\n     */\n    set onMeshLoaded(callback) {\n        if (this._onMeshLoadedObserver) {\n            this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver);\n        }\n        if (callback) {\n            this._onMeshLoadedObserver = this.onMeshLoadedObservable.add(callback);\n        }\n    }\n    /**\n     * Callback raised when the loader creates a skin after parsing the glTF properties of the skin node.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/glTF/glTFSkinning#ignoring-the-transform-of-the-skinned-mesh\n     */\n    set onSkinLoaded(callback) {\n        if (this._onSkinLoadedObserver) {\n            this.onSkinLoadedObservable.remove(this._onSkinLoadedObserver);\n        }\n        if (callback) {\n            this._onSkinLoadedObserver = this.onSkinLoadedObservable.add((data) => callback(data.node, data.skinnedNode));\n        }\n    }\n    /**\n     * Callback raised when the loader creates a texture after parsing the glTF properties of the texture.\n     */\n    set onTextureLoaded(callback) {\n        if (this._onTextureLoadedObserver) {\n            this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver);\n        }\n        if (callback) {\n            this._onTextureLoadedObserver = this.onTextureLoadedObservable.add(callback);\n        }\n    }\n    /**\n     * Callback raised when the loader creates a material after parsing the glTF properties of the material.\n     */\n    set onMaterialLoaded(callback) {\n        if (this._onMaterialLoadedObserver) {\n            this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver);\n        }\n        if (callback) {\n            this._onMaterialLoadedObserver = this.onMaterialLoadedObservable.add(callback);\n        }\n    }\n    /**\n     * Callback raised when the loader creates a camera after parsing the glTF properties of the camera.\n     */\n    set onCameraLoaded(callback) {\n        if (this._onCameraLoadedObserver) {\n            this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver);\n        }\n        if (callback) {\n            this._onCameraLoadedObserver = this.onCameraLoadedObservable.add(callback);\n        }\n    }\n    /**\n     * Callback raised when the asset is completely loaded, immediately before the loader is disposed.\n     * For assets with LODs, raised when all of the LODs are complete.\n     * For assets without LODs, raised when the model is complete, immediately after the loader resolves the returned promise.\n     */\n    set onComplete(callback) {\n        if (this._onCompleteObserver) {\n            this.onCompleteObservable.remove(this._onCompleteObserver);\n        }\n        this._onCompleteObserver = this.onCompleteObservable.add(callback);\n    }\n    /**\n     * Callback raised when an error occurs.\n     */\n    set onError(callback) {\n        if (this._onErrorObserver) {\n            this.onErrorObservable.remove(this._onErrorObserver);\n        }\n        this._onErrorObserver = this.onErrorObservable.add(callback);\n    }\n    /**\n     * Callback raised after the loader is disposed.\n     */\n    set onDispose(callback) {\n        if (this._onDisposeObserver) {\n            this.onDisposeObservable.remove(this._onDisposeObserver);\n        }\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\n    }\n    /**\n     * Callback raised after a loader extension is created.\n     */\n    set onExtensionLoaded(callback) {\n        if (this._onExtensionLoadedObserver) {\n            this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver);\n        }\n        this._onExtensionLoadedObserver = this.onExtensionLoadedObservable.add(callback);\n    }\n    /**\n     * Defines if the loader logging is enabled.\n     */\n    get loggingEnabled() {\n        return this._loggingEnabled;\n    }\n    set loggingEnabled(value) {\n        if (this._loggingEnabled === value) {\n            return;\n        }\n        this._loggingEnabled = value;\n        if (this._loggingEnabled) {\n            this._log = this._logEnabled;\n        }\n        else {\n            this._log = this._logDisabled;\n        }\n    }\n    /**\n     * Defines if the loader should capture performance counters.\n     */\n    get capturePerformanceCounters() {\n        return this._capturePerformanceCounters;\n    }\n    set capturePerformanceCounters(value) {\n        if (this._capturePerformanceCounters === value) {\n            return;\n        }\n        this._capturePerformanceCounters = value;\n        if (this._capturePerformanceCounters) {\n            this._startPerformanceCounter = this._startPerformanceCounterEnabled;\n            this._endPerformanceCounter = this._endPerformanceCounterEnabled;\n        }\n        else {\n            this._startPerformanceCounter = this._startPerformanceCounterDisabled;\n            this._endPerformanceCounter = this._endPerformanceCounterDisabled;\n        }\n    }\n    /**\n     * Callback raised after a loader extension is created.\n     */\n    set onValidated(callback) {\n        if (this._onValidatedObserver) {\n            this.onValidatedObservable.remove(this._onValidatedObserver);\n        }\n        this._onValidatedObserver = this.onValidatedObservable.add(callback);\n    }\n    /**\n     * Disposes the loader, releases resources during load, and cancels any outstanding requests.\n     */\n    dispose() {\n        if (this._loader) {\n            this._loader.dispose();\n            this._loader = null;\n        }\n        for (const request of this._requests) {\n            request.abort();\n        }\n        this._requests.length = 0;\n        delete this._progressCallback;\n        this.preprocessUrlAsync = (url) => Promise.resolve(url);\n        this.onMeshLoadedObservable.clear();\n        this.onSkinLoadedObservable.clear();\n        this.onTextureLoadedObservable.clear();\n        this.onMaterialLoadedObservable.clear();\n        this.onCameraLoadedObservable.clear();\n        this.onCompleteObservable.clear();\n        this.onExtensionLoadedObservable.clear();\n        this.onDisposeObservable.notifyObservers(undefined);\n        this.onDisposeObservable.clear();\n    }\n    /**\n     * @internal\n     */\n    loadFile(scene, fileOrUrl, rootUrl, onSuccess, onProgress, useArrayBuffer, onError, name) {\n        if (ArrayBuffer.isView(fileOrUrl)) {\n            this._loadBinary(scene, fileOrUrl, rootUrl, onSuccess, onError, name);\n            return null;\n        }\n        this._progressCallback = onProgress;\n        const fileName = fileOrUrl.name || Tools.GetFilename(fileOrUrl);\n        if (useArrayBuffer) {\n            if (this.useRangeRequests) {\n                if (this.validate) {\n                    Logger.Warn(\"glTF validation is not supported when range requests are enabled\");\n                }\n                const fileRequest = {\n                    abort: () => { },\n                    onCompleteObservable: new Observable(),\n                };\n                const dataBuffer = {\n                    readAsync: (byteOffset, byteLength) => {\n                        return new Promise((resolve, reject) => {\n                            this._loadFile(scene, fileOrUrl, (data) => {\n                                resolve(new Uint8Array(data));\n                            }, true, (error) => {\n                                reject(error);\n                            }, (webRequest) => {\n                                webRequest.setRequestHeader(\"Range\", `bytes=${byteOffset}-${byteOffset + byteLength - 1}`);\n                            });\n                        });\n                    },\n                    byteLength: 0,\n                };\n                this._unpackBinaryAsync(new DataReader(dataBuffer)).then((loaderData) => {\n                    fileRequest.onCompleteObservable.notifyObservers(fileRequest);\n                    onSuccess(loaderData);\n                }, onError ? (error) => onError(undefined, error) : undefined);\n                return fileRequest;\n            }\n            return this._loadFile(scene, fileOrUrl, (data) => {\n                this._validate(scene, new Uint8Array(data, 0, data.byteLength), rootUrl, fileName);\n                this._unpackBinaryAsync(new DataReader({\n                    readAsync: (byteOffset, byteLength) => readAsync(data, byteOffset, byteLength),\n                    byteLength: data.byteLength,\n                })).then((loaderData) => {\n                    onSuccess(loaderData);\n                }, onError ? (error) => onError(undefined, error) : undefined);\n            }, true, onError);\n        }\n        else {\n            return this._loadFile(scene, fileOrUrl, (data) => {\n                try {\n                    this._validate(scene, data, rootUrl, fileName);\n                    onSuccess({ json: this._parseJson(data) });\n                }\n                catch {\n                    if (onError) {\n                        onError();\n                    }\n                }\n            }, false, onError);\n        }\n    }\n    _loadBinary(scene, data, rootUrl, onSuccess, onError, fileName) {\n        this._validate(scene, new Uint8Array(data.buffer, data.byteOffset, data.byteLength), rootUrl, fileName);\n        this._unpackBinaryAsync(new DataReader({\n            readAsync: (byteOffset, byteLength) => readViewAsync(data, byteOffset, byteLength),\n            byteLength: data.byteLength,\n        })).then((loaderData) => {\n            onSuccess(loaderData);\n        }, onError ? (error) => onError(undefined, error) : undefined);\n    }\n    /**\n     * @internal\n     */\n    importMeshAsync(meshesNames, scene, data, rootUrl, onProgress, fileName) {\n        return Promise.resolve().then(() => {\n            this.onParsedObservable.notifyObservers(data);\n            this.onParsedObservable.clear();\n            this._log(`Loading ${fileName || \"\"}`);\n            this._loader = this._getLoader(data);\n            return this._loader.importMeshAsync(meshesNames, scene, null, data, rootUrl, onProgress, fileName);\n        });\n    }\n    /**\n     * @internal\n     */\n    loadAsync(scene, data, rootUrl, onProgress, fileName) {\n        return Promise.resolve().then(() => {\n            this.onParsedObservable.notifyObservers(data);\n            this.onParsedObservable.clear();\n            this._log(`Loading ${fileName || \"\"}`);\n            this._loader = this._getLoader(data);\n            return this._loader.loadAsync(scene, data, rootUrl, onProgress, fileName);\n        });\n    }\n    /**\n     * @internal\n     */\n    loadAssetContainerAsync(scene, data, rootUrl, onProgress, fileName) {\n        return Promise.resolve().then(() => {\n            this.onParsedObservable.notifyObservers(data);\n            this.onParsedObservable.clear();\n            this._log(`Loading ${fileName || \"\"}`);\n            this._loader = this._getLoader(data);\n            // Prepare the asset container.\n            const container = new AssetContainer(scene);\n            // Get materials/textures when loading to add to container\n            const materials = [];\n            this.onMaterialLoadedObservable.add((material) => {\n                materials.push(material);\n            });\n            const textures = [];\n            this.onTextureLoadedObservable.add((texture) => {\n                textures.push(texture);\n            });\n            const cameras = [];\n            this.onCameraLoadedObservable.add((camera) => {\n                cameras.push(camera);\n            });\n            const morphTargetManagers = [];\n            this.onMeshLoadedObservable.add((mesh) => {\n                if (mesh.morphTargetManager) {\n                    morphTargetManagers.push(mesh.morphTargetManager);\n                }\n            });\n            return this._loader.importMeshAsync(null, scene, container, data, rootUrl, onProgress, fileName).then((result) => {\n                Array.prototype.push.apply(container.geometries, result.geometries);\n                Array.prototype.push.apply(container.meshes, result.meshes);\n                Array.prototype.push.apply(container.particleSystems, result.particleSystems);\n                Array.prototype.push.apply(container.skeletons, result.skeletons);\n                Array.prototype.push.apply(container.animationGroups, result.animationGroups);\n                Array.prototype.push.apply(container.materials, materials);\n                Array.prototype.push.apply(container.textures, textures);\n                Array.prototype.push.apply(container.lights, result.lights);\n                Array.prototype.push.apply(container.transformNodes, result.transformNodes);\n                Array.prototype.push.apply(container.cameras, cameras);\n                Array.prototype.push.apply(container.morphTargetManagers, morphTargetManagers);\n                return container;\n            });\n        });\n    }\n    /**\n     * @internal\n     */\n    canDirectLoad(data) {\n        return GLTFFileLoaderMetadata.canDirectLoad(data);\n    }\n    /**\n     * @internal\n     */\n    directLoad(scene, data) {\n        if (data.startsWith(\"base64,\" + GLTFMagicBase64Encoded) || // this is technically incorrect, but will continue to support for backcompat.\n            data.startsWith(\";base64,\" + GLTFMagicBase64Encoded) ||\n            data.startsWith(\"application/octet-stream;base64,\" + GLTFMagicBase64Encoded) ||\n            data.startsWith(\"model/gltf-binary;base64,\" + GLTFMagicBase64Encoded)) {\n            const arrayBuffer = DecodeBase64UrlToBinary(data);\n            this._validate(scene, new Uint8Array(arrayBuffer, 0, arrayBuffer.byteLength));\n            return this._unpackBinaryAsync(new DataReader({\n                readAsync: (byteOffset, byteLength) => readAsync(arrayBuffer, byteOffset, byteLength),\n                byteLength: arrayBuffer.byteLength,\n            }));\n        }\n        this._validate(scene, data);\n        return Promise.resolve({ json: this._parseJson(data) });\n    }\n    /** @internal */\n    createPlugin(options) {\n        return new GLTFFileLoader(options[GLTFFileLoaderMetadata.name]);\n    }\n    /**\n     * The loader state or null if the loader is not active.\n     */\n    get loaderState() {\n        return this._state;\n    }\n    /**\n     * Returns a promise that resolves when the asset is completely loaded.\n     * @returns a promise that resolves when the asset is completely loaded.\n     */\n    whenCompleteAsync() {\n        return new Promise((resolve, reject) => {\n            this.onCompleteObservable.addOnce(() => {\n                resolve();\n            });\n            this.onErrorObservable.addOnce((reason) => {\n                reject(reason);\n            });\n        });\n    }\n    /**\n     * @internal\n     */\n    _setState(state) {\n        if (this._state === state) {\n            return;\n        }\n        this._state = state;\n        this.onLoaderStateChangedObservable.notifyObservers(this._state);\n        this._log(GLTFLoaderState[this._state]);\n    }\n    /**\n     * @internal\n     */\n    _loadFile(scene, fileOrUrl, onSuccess, useArrayBuffer, onError, onOpened) {\n        const request = scene._loadFile(fileOrUrl, onSuccess, (event) => {\n            this._onProgress(event, request);\n        }, true, useArrayBuffer, onError, onOpened);\n        request.onCompleteObservable.add(() => {\n            // Force the length computable to be true since we can guarantee the data is loaded.\n            request._lengthComputable = true;\n            request._total = request._loaded;\n        });\n        this._requests.push(request);\n        return request;\n    }\n    _onProgress(event, request) {\n        if (!this._progressCallback) {\n            return;\n        }\n        request._lengthComputable = event.lengthComputable;\n        request._loaded = event.loaded;\n        request._total = event.total;\n        let lengthComputable = true;\n        let loaded = 0;\n        let total = 0;\n        for (const request of this._requests) {\n            if (request._lengthComputable === undefined || request._loaded === undefined || request._total === undefined) {\n                return;\n            }\n            lengthComputable = lengthComputable && request._lengthComputable;\n            loaded += request._loaded;\n            total += request._total;\n        }\n        this._progressCallback({\n            lengthComputable: lengthComputable,\n            loaded: loaded,\n            total: lengthComputable ? total : 0,\n        });\n    }\n    _validate(scene, data, rootUrl = \"\", fileName = \"\") {\n        if (!this.validate) {\n            return;\n        }\n        this._startPerformanceCounter(\"Validate JSON\");\n        GLTFValidation.ValidateAsync(data, rootUrl, fileName, (uri) => {\n            return this.preprocessUrlAsync(rootUrl + uri).then((url) => {\n                return scene._loadFileAsync(url, undefined, true, true).then((data) => {\n                    return new Uint8Array(data, 0, data.byteLength);\n                });\n            });\n        }).then((result) => {\n            this._endPerformanceCounter(\"Validate JSON\");\n            this.onValidatedObservable.notifyObservers(result);\n            this.onValidatedObservable.clear();\n        }, (reason) => {\n            this._endPerformanceCounter(\"Validate JSON\");\n            Tools.Warn(`Failed to validate: ${reason.message}`);\n            this.onValidatedObservable.clear();\n        });\n    }\n    _getLoader(loaderData) {\n        const asset = loaderData.json.asset || {};\n        this._log(`Asset version: ${asset.version}`);\n        asset.minVersion && this._log(`Asset minimum version: ${asset.minVersion}`);\n        asset.generator && this._log(`Asset generator: ${asset.generator}`);\n        const version = GLTFFileLoader._parseVersion(asset.version);\n        if (!version) {\n            throw new Error(\"Invalid version: \" + asset.version);\n        }\n        if (asset.minVersion !== undefined) {\n            const minVersion = GLTFFileLoader._parseVersion(asset.minVersion);\n            if (!minVersion) {\n                throw new Error(\"Invalid minimum version: \" + asset.minVersion);\n            }\n            if (GLTFFileLoader._compareVersion(minVersion, { major: 2, minor: 0 }) > 0) {\n                throw new Error(\"Incompatible minimum version: \" + asset.minVersion);\n            }\n        }\n        const createLoaders = {\n            1: GLTFFileLoader._CreateGLTF1Loader,\n            2: GLTFFileLoader._CreateGLTF2Loader,\n        };\n        const createLoader = createLoaders[version.major];\n        if (!createLoader) {\n            throw new Error(\"Unsupported version: \" + asset.version);\n        }\n        return createLoader(this);\n    }\n    _parseJson(json) {\n        this._startPerformanceCounter(\"Parse JSON\");\n        this._log(`JSON length: ${json.length}`);\n        const parsed = JSON.parse(json);\n        this._endPerformanceCounter(\"Parse JSON\");\n        return parsed;\n    }\n    _unpackBinaryAsync(dataReader) {\n        this._startPerformanceCounter(\"Unpack Binary\");\n        // Read magic + version + length + json length + json format\n        return dataReader.loadAsync(20).then(() => {\n            const Binary = {\n                Magic: 0x46546c67,\n            };\n            const magic = dataReader.readUint32();\n            if (magic !== Binary.Magic) {\n                throw new RuntimeError(\"Unexpected magic: \" + magic, ErrorCodes.GLTFLoaderUnexpectedMagicError);\n            }\n            const version = dataReader.readUint32();\n            if (this.loggingEnabled) {\n                this._log(`Binary version: ${version}`);\n            }\n            const length = dataReader.readUint32();\n            if (!this.useRangeRequests && length !== dataReader.buffer.byteLength) {\n                Logger.Warn(`Length in header does not match actual data length: ${length} != ${dataReader.buffer.byteLength}`);\n            }\n            let unpacked;\n            switch (version) {\n                case 1: {\n                    unpacked = this._unpackBinaryV1Async(dataReader, length);\n                    break;\n                }\n                case 2: {\n                    unpacked = this._unpackBinaryV2Async(dataReader, length);\n                    break;\n                }\n                default: {\n                    throw new Error(\"Unsupported version: \" + version);\n                }\n            }\n            this._endPerformanceCounter(\"Unpack Binary\");\n            return unpacked;\n        });\n    }\n    _unpackBinaryV1Async(dataReader, length) {\n        const ContentFormat = {\n            JSON: 0,\n        };\n        const contentLength = dataReader.readUint32();\n        const contentFormat = dataReader.readUint32();\n        if (contentFormat !== ContentFormat.JSON) {\n            throw new Error(`Unexpected content format: ${contentFormat}`);\n        }\n        const bodyLength = length - dataReader.byteOffset;\n        const data = { json: this._parseJson(dataReader.readString(contentLength)), bin: null };\n        if (bodyLength !== 0) {\n            const startByteOffset = dataReader.byteOffset;\n            data.bin = {\n                readAsync: (byteOffset, byteLength) => dataReader.buffer.readAsync(startByteOffset + byteOffset, byteLength),\n                byteLength: bodyLength,\n            };\n        }\n        return Promise.resolve(data);\n    }\n    _unpackBinaryV2Async(dataReader, length) {\n        const ChunkFormat = {\n            JSON: 0x4e4f534a,\n            BIN: 0x004e4942,\n        };\n        // Read the JSON chunk header.\n        const chunkLength = dataReader.readUint32();\n        const chunkFormat = dataReader.readUint32();\n        if (chunkFormat !== ChunkFormat.JSON) {\n            throw new Error(\"First chunk format is not JSON\");\n        }\n        // Bail if there are no other chunks.\n        if (dataReader.byteOffset + chunkLength === length) {\n            return dataReader.loadAsync(chunkLength).then(() => {\n                return { json: this._parseJson(dataReader.readString(chunkLength)), bin: null };\n            });\n        }\n        // Read the JSON chunk and the length and type of the next chunk.\n        return dataReader.loadAsync(chunkLength + 8).then(() => {\n            const data = { json: this._parseJson(dataReader.readString(chunkLength)), bin: null };\n            const readAsync = () => {\n                const chunkLength = dataReader.readUint32();\n                const chunkFormat = dataReader.readUint32();\n                switch (chunkFormat) {\n                    case ChunkFormat.JSON: {\n                        throw new Error(\"Unexpected JSON chunk\");\n                    }\n                    case ChunkFormat.BIN: {\n                        const startByteOffset = dataReader.byteOffset;\n                        data.bin = {\n                            readAsync: (byteOffset, byteLength) => dataReader.buffer.readAsync(startByteOffset + byteOffset, byteLength),\n                            byteLength: chunkLength,\n                        };\n                        dataReader.skipBytes(chunkLength);\n                        break;\n                    }\n                    default: {\n                        // ignore unrecognized chunkFormat\n                        dataReader.skipBytes(chunkLength);\n                        break;\n                    }\n                }\n                if (dataReader.byteOffset !== length) {\n                    return dataReader.loadAsync(8).then(readAsync);\n                }\n                return Promise.resolve(data);\n            };\n            return readAsync();\n        });\n    }\n    static _parseVersion(version) {\n        if (version === \"1.0\" || version === \"1.0.1\") {\n            return {\n                major: 1,\n                minor: 0,\n            };\n        }\n        const match = (version + \"\").match(/^(\\d+)\\.(\\d+)/);\n        if (!match) {\n            return null;\n        }\n        return {\n            major: parseInt(match[1]),\n            minor: parseInt(match[2]),\n        };\n    }\n    static _compareVersion(a, b) {\n        if (a.major > b.major) {\n            return 1;\n        }\n        if (a.major < b.major) {\n            return -1;\n        }\n        if (a.minor > b.minor) {\n            return 1;\n        }\n        if (a.minor < b.minor) {\n            return -1;\n        }\n        return 0;\n    }\n    /**\n     * @internal\n     */\n    _logOpen(message) {\n        this._log(message);\n        this._logIndentLevel++;\n    }\n    /** @internal */\n    _logClose() {\n        --this._logIndentLevel;\n    }\n    _logEnabled(message) {\n        const spaces = GLTFFileLoader._logSpaces.substring(0, this._logIndentLevel * 2);\n        Logger.Log(`${spaces}${message}`);\n    }\n    _logDisabled(message) { }\n    _startPerformanceCounterEnabled(counterName) {\n        Tools.StartPerformanceCounter(counterName);\n    }\n    _startPerformanceCounterDisabled(counterName) { }\n    _endPerformanceCounterEnabled(counterName) {\n        Tools.EndPerformanceCounter(counterName);\n    }\n    _endPerformanceCounterDisabled(counterName) { }\n}\n// ------------------\n// End Common options\n// ------------------\n// ----------------\n// Begin V1 options\n// ----------------\n/**\n * Set this property to false to disable incremental loading which delays the loader from calling the success callback until after loading the meshes and shaders.\n * Textures always loads asynchronously. For example, the success callback can compute the bounding information of the loaded meshes when incremental loading is disabled.\n * Defaults to true.\n * @internal\n */\nGLTFFileLoader.IncrementalLoading = true;\n/**\n * Set this property to true in order to work with homogeneous coordinates, available with some converters and exporters.\n * Defaults to false. See https://en.wikipedia.org/wiki/Homogeneous_coordinates.\n * @internal\n */\nGLTFFileLoader.HomogeneousCoordinates = false;\nGLTFFileLoader._logSpaces = \"                                \";\nRegisterSceneLoaderPlugin(new GLTFFileLoader());\n//# sourceMappingURL=glTFFileLoader.js.map", "import { Deferred } from \"@babylonjs/core/Misc/deferred.js\";\nimport { Quaternion, Vector3, Matrix, TmpVectors } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { Camera } from \"@babylonjs/core/Cameras/camera.js\";\nimport { FreeCamera } from \"@babylonjs/core/Cameras/freeCamera.js\";\nimport { Bone } from \"@babylonjs/core/Bones/bone.js\";\nimport { Skeleton } from \"@babylonjs/core/Bones/skeleton.js\";\nimport { Material } from \"@babylonjs/core/Materials/material.js\";\nimport { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { Texture } from \"@babylonjs/core/Materials/Textures/texture.js\";\nimport { TransformNode } from \"@babylonjs/core/Meshes/transformNode.js\";\nimport { Buffer, VertexBuffer } from \"@babylonjs/core/Buffers/buffer.js\";\nimport { Geometry } from \"@babylonjs/core/Meshes/geometry.js\";\nimport { AbstractMesh } from \"@babylonjs/core/Meshes/abstractMesh.js\";\nimport { Mesh } from \"@babylonjs/core/Meshes/mesh.js\";\nimport { MorphTarget } from \"@babylonjs/core/Morph/morphTarget.js\";\nimport { MorphTargetManager } from \"@babylonjs/core/Morph/morphTargetManager.js\";\nimport { GLTFFileLoader, GLTFLoaderState, GLTFLoaderCoordinateSystemMode, GLTFLoaderAnimationStartMode } from \"../glTFFileLoader.js\";\nimport { DecodeBase64UrlToBinary, GetMimeType, IsBase64DataUrl, LoadFileError } from \"@babylonjs/core/Misc/fileTools.js\";\nimport { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { BoundingInfo } from \"@babylonjs/core/Culling/boundingInfo.js\";\nimport { registeredGLTFExtensions, registerGLTFExtension, unregisterGLTFExtension } from \"./glTFLoaderExtensionRegistry.js\";\nimport { GetMappingForKey } from \"./Extensions/objectModelMapping.js\";\nimport { deepMerge } from \"@babylonjs/core/Misc/deepMerger.js\";\nimport { GetTypedArrayConstructor } from \"@babylonjs/core/Buffers/bufferUtils.js\";\nexport { GLTFFileLoader };\n/**\n * Helper class for working with arrays when loading the glTF asset\n */\nexport class ArrayItem {\n    /**\n     * Gets an item from the given array.\n     * @param context The context when loading the asset\n     * @param array The array to get the item from\n     * @param index The index to the array\n     * @returns The array item\n     */\n    static Get(context, array, index) {\n        if (!array || index == undefined || !array[index]) {\n            throw new Error(`${context}: Failed to find index (${index})`);\n        }\n        return array[index];\n    }\n    /**\n     * Gets an item from the given array or returns null if not available.\n     * @param array The array to get the item from\n     * @param index The index to the array\n     * @returns The array item or null\n     */\n    static TryGet(array, index) {\n        if (!array || index == undefined || !array[index]) {\n            return null;\n        }\n        return array[index];\n    }\n    /**\n     * Assign an `index` field to each item of the given array.\n     * @param array The array of items\n     */\n    static Assign(array) {\n        if (array) {\n            for (let index = 0; index < array.length; index++) {\n                array[index].index = index;\n            }\n        }\n    }\n}\n/** @internal */\nexport function LoadBoundingInfoFromPositionAccessor(accessor) {\n    if (accessor.min && accessor.max) {\n        const minArray = accessor.min;\n        const maxArray = accessor.max;\n        const minVector = TmpVectors.Vector3[0].copyFromFloats(minArray[0], minArray[1], minArray[2]);\n        const maxVector = TmpVectors.Vector3[1].copyFromFloats(maxArray[0], maxArray[1], maxArray[2]);\n        if (accessor.normalized && accessor.componentType !== 5126 /* AccessorComponentType.FLOAT */) {\n            let divider = 1;\n            switch (accessor.componentType) {\n                case 5120 /* AccessorComponentType.BYTE */:\n                    divider = 127.0;\n                    break;\n                case 5121 /* AccessorComponentType.UNSIGNED_BYTE */:\n                    divider = 255.0;\n                    break;\n                case 5122 /* AccessorComponentType.SHORT */:\n                    divider = 32767.0;\n                    break;\n                case 5123 /* AccessorComponentType.UNSIGNED_SHORT */:\n                    divider = 65535.0;\n                    break;\n            }\n            const oneOverDivider = 1 / divider;\n            minVector.scaleInPlace(oneOverDivider);\n            maxVector.scaleInPlace(oneOverDivider);\n        }\n        return new BoundingInfo(minVector, maxVector);\n    }\n    return null;\n}\n/**\n * The glTF 2.0 loader\n */\nexport class GLTFLoader {\n    /**\n     * Registers a loader extension.\n     * @param name The name of the loader extension.\n     * @param factory The factory function that creates the loader extension.\n     * @deprecated Please use registerGLTFExtension instead.\n     */\n    static RegisterExtension(name, factory) {\n        registerGLTFExtension(name, false, factory);\n    }\n    /**\n     * Unregisters a loader extension.\n     * @param name The name of the loader extension.\n     * @returns A boolean indicating whether the extension has been unregistered\n     * @deprecated Please use unregisterGLTFExtension instead.\n     */\n    static UnregisterExtension(name) {\n        return unregisterGLTFExtension(name);\n    }\n    /**\n     * The object that represents the glTF JSON.\n     */\n    get gltf() {\n        if (!this._gltf) {\n            throw new Error(\"glTF JSON is not available\");\n        }\n        return this._gltf;\n    }\n    /**\n     * The BIN chunk of a binary glTF.\n     */\n    get bin() {\n        return this._bin;\n    }\n    /**\n     * The parent file loader.\n     */\n    get parent() {\n        return this._parent;\n    }\n    /**\n     * The Babylon scene when loading the asset.\n     */\n    get babylonScene() {\n        if (!this._babylonScene) {\n            throw new Error(\"Scene is not available\");\n        }\n        return this._babylonScene;\n    }\n    /**\n     * The root Babylon node when loading the asset.\n     */\n    get rootBabylonMesh() {\n        return this._rootBabylonMesh;\n    }\n    /**\n     * The root url when loading the asset.\n     */\n    get rootUrl() {\n        return this._rootUrl;\n    }\n    /**\n     * @internal\n     */\n    constructor(parent) {\n        /** @internal */\n        this._completePromises = new Array();\n        /** @internal */\n        this._assetContainer = null;\n        /** Storage */\n        this._babylonLights = [];\n        /** @internal */\n        this._disableInstancedMesh = 0;\n        /** @internal */\n        this._allMaterialsDirtyRequired = false;\n        /** @internal */\n        this._skipStartAnimationStep = false;\n        this._extensions = new Array();\n        this._disposed = false;\n        this._rootUrl = null;\n        this._fileName = null;\n        this._uniqueRootUrl = null;\n        this._bin = null;\n        this._rootBabylonMesh = null;\n        this._defaultBabylonMaterialData = {};\n        this._postSceneLoadActions = new Array();\n        this._parent = parent;\n    }\n    /** @internal */\n    dispose() {\n        if (this._disposed) {\n            return;\n        }\n        this._disposed = true;\n        this._completePromises.length = 0;\n        this._extensions.forEach((extension) => extension.dispose && extension.dispose());\n        this._extensions.length = 0;\n        this._gltf = null; // TODO\n        this._bin = null;\n        this._babylonScene = null; // TODO\n        this._rootBabylonMesh = null;\n        this._defaultBabylonMaterialData = {};\n        this._postSceneLoadActions.length = 0;\n        this._parent.dispose();\n    }\n    /**\n     * @internal\n     */\n    importMeshAsync(meshesNames, scene, container, data, rootUrl, onProgress, fileName = \"\") {\n        return Promise.resolve().then(() => {\n            this._babylonScene = scene;\n            this._assetContainer = container;\n            this._loadData(data);\n            let nodes = null;\n            if (meshesNames) {\n                const nodeMap = {};\n                if (this._gltf.nodes) {\n                    for (const node of this._gltf.nodes) {\n                        if (node.name) {\n                            nodeMap[node.name] = node.index;\n                        }\n                    }\n                }\n                const names = meshesNames instanceof Array ? meshesNames : [meshesNames];\n                nodes = names.map((name) => {\n                    const node = nodeMap[name];\n                    if (node === undefined) {\n                        throw new Error(`Failed to find node '${name}'`);\n                    }\n                    return node;\n                });\n            }\n            return this._loadAsync(rootUrl, fileName, nodes, () => {\n                return {\n                    meshes: this._getMeshes(),\n                    particleSystems: [],\n                    skeletons: this._getSkeletons(),\n                    animationGroups: this._getAnimationGroups(),\n                    lights: this._babylonLights,\n                    transformNodes: this._getTransformNodes(),\n                    geometries: this._getGeometries(),\n                    spriteManagers: [],\n                };\n            });\n        });\n    }\n    /**\n     * @internal\n     */\n    loadAsync(scene, data, rootUrl, onProgress, fileName = \"\") {\n        return Promise.resolve().then(() => {\n            this._babylonScene = scene;\n            this._loadData(data);\n            return this._loadAsync(rootUrl, fileName, null, () => undefined);\n        });\n    }\n    _loadAsync(rootUrl, fileName, nodes, resultFunc) {\n        return Promise.resolve()\n            .then(async () => {\n            this._rootUrl = rootUrl;\n            this._uniqueRootUrl = !rootUrl.startsWith(\"file:\") && fileName ? rootUrl : `${rootUrl}${Date.now()}/`;\n            this._fileName = fileName;\n            this._allMaterialsDirtyRequired = false;\n            await this._loadExtensionsAsync();\n            const loadingToReadyCounterName = `${GLTFLoaderState[GLTFLoaderState.LOADING]} => ${GLTFLoaderState[GLTFLoaderState.READY]}`;\n            const loadingToCompleteCounterName = `${GLTFLoaderState[GLTFLoaderState.LOADING]} => ${GLTFLoaderState[GLTFLoaderState.COMPLETE]}`;\n            this._parent._startPerformanceCounter(loadingToReadyCounterName);\n            this._parent._startPerformanceCounter(loadingToCompleteCounterName);\n            this._parent._setState(GLTFLoaderState.LOADING);\n            this._extensionsOnLoading();\n            const promises = new Array();\n            // Block the marking of materials dirty until the scene is loaded.\n            const oldBlockMaterialDirtyMechanism = this._babylonScene.blockMaterialDirtyMechanism;\n            this._babylonScene.blockMaterialDirtyMechanism = true;\n            if (!this.parent.loadOnlyMaterials) {\n                if (nodes) {\n                    promises.push(this.loadSceneAsync(\"/nodes\", { nodes: nodes, index: -1 }));\n                }\n                else if (this._gltf.scene != undefined || (this._gltf.scenes && this._gltf.scenes[0])) {\n                    const scene = ArrayItem.Get(`/scene`, this._gltf.scenes, this._gltf.scene || 0);\n                    promises.push(this.loadSceneAsync(`/scenes/${scene.index}`, scene));\n                }\n            }\n            if (!this.parent.skipMaterials && this.parent.loadAllMaterials && this._gltf.materials) {\n                for (let m = 0; m < this._gltf.materials.length; ++m) {\n                    const material = this._gltf.materials[m];\n                    const context = \"/materials/\" + m;\n                    const babylonDrawMode = Material.TriangleFillMode;\n                    promises.push(this._loadMaterialAsync(context, material, null, babylonDrawMode, () => { }));\n                }\n            }\n            // Restore the blocking of material dirty.\n            if (this._allMaterialsDirtyRequired) {\n                // This can happen if we add a light for instance as it will impact the whole scene.\n                // This automatically resets everything if needed.\n                this._babylonScene.blockMaterialDirtyMechanism = oldBlockMaterialDirtyMechanism;\n            }\n            else {\n                // By default a newly created material is dirty so there is no need to flag the full scene as dirty.\n                // For perf reasons, we then bypass blockMaterialDirtyMechanism as this would \"dirty\" the entire scene.\n                this._babylonScene._forceBlockMaterialDirtyMechanism(oldBlockMaterialDirtyMechanism);\n            }\n            if (this._parent.compileMaterials) {\n                promises.push(this._compileMaterialsAsync());\n            }\n            if (this._parent.compileShadowGenerators) {\n                promises.push(this._compileShadowGeneratorsAsync());\n            }\n            const resultPromise = Promise.all(promises).then(() => {\n                if (this._rootBabylonMesh && this._rootBabylonMesh !== this._parent.customRootNode) {\n                    this._rootBabylonMesh.setEnabled(true);\n                }\n                // Making sure we enable enough lights to have all lights together\n                for (const material of this._babylonScene.materials) {\n                    const mat = material;\n                    if (mat.maxSimultaneousLights !== undefined) {\n                        mat.maxSimultaneousLights = Math.max(mat.maxSimultaneousLights, this._babylonScene.lights.length);\n                    }\n                }\n                this._extensionsOnReady();\n                this._parent._setState(GLTFLoaderState.READY);\n                if (!this._skipStartAnimationStep) {\n                    this._startAnimations();\n                }\n                return resultFunc();\n            });\n            return resultPromise.then((result) => {\n                this._parent._endPerformanceCounter(loadingToReadyCounterName);\n                Tools.SetImmediate(() => {\n                    if (!this._disposed) {\n                        Promise.all(this._completePromises).then(() => {\n                            this._parent._endPerformanceCounter(loadingToCompleteCounterName);\n                            this._parent._setState(GLTFLoaderState.COMPLETE);\n                            this._parent.onCompleteObservable.notifyObservers(undefined);\n                            this._parent.onCompleteObservable.clear();\n                            this.dispose();\n                        }, (error) => {\n                            this._parent.onErrorObservable.notifyObservers(error);\n                            this._parent.onErrorObservable.clear();\n                            this.dispose();\n                        });\n                    }\n                });\n                return result;\n            });\n        })\n            .catch((error) => {\n            if (!this._disposed) {\n                this._parent.onErrorObservable.notifyObservers(error);\n                this._parent.onErrorObservable.clear();\n                this.dispose();\n            }\n            throw error;\n        });\n    }\n    _loadData(data) {\n        this._gltf = data.json;\n        this._setupData();\n        if (data.bin) {\n            const buffers = this._gltf.buffers;\n            if (buffers && buffers[0] && !buffers[0].uri) {\n                const binaryBuffer = buffers[0];\n                if (binaryBuffer.byteLength < data.bin.byteLength - 3 || binaryBuffer.byteLength > data.bin.byteLength) {\n                    Logger.Warn(`Binary buffer length (${binaryBuffer.byteLength}) from JSON does not match chunk length (${data.bin.byteLength})`);\n                }\n                this._bin = data.bin;\n            }\n            else {\n                Logger.Warn(\"Unexpected BIN chunk\");\n            }\n        }\n    }\n    _setupData() {\n        ArrayItem.Assign(this._gltf.accessors);\n        ArrayItem.Assign(this._gltf.animations);\n        ArrayItem.Assign(this._gltf.buffers);\n        ArrayItem.Assign(this._gltf.bufferViews);\n        ArrayItem.Assign(this._gltf.cameras);\n        ArrayItem.Assign(this._gltf.images);\n        ArrayItem.Assign(this._gltf.materials);\n        ArrayItem.Assign(this._gltf.meshes);\n        ArrayItem.Assign(this._gltf.nodes);\n        ArrayItem.Assign(this._gltf.samplers);\n        ArrayItem.Assign(this._gltf.scenes);\n        ArrayItem.Assign(this._gltf.skins);\n        ArrayItem.Assign(this._gltf.textures);\n        if (this._gltf.nodes) {\n            const nodeParents = {};\n            for (const node of this._gltf.nodes) {\n                if (node.children) {\n                    for (const index of node.children) {\n                        nodeParents[index] = node.index;\n                    }\n                }\n            }\n            const rootNode = this._createRootNode();\n            for (const node of this._gltf.nodes) {\n                const parentIndex = nodeParents[node.index];\n                node.parent = parentIndex === undefined ? rootNode : this._gltf.nodes[parentIndex];\n            }\n        }\n    }\n    async _loadExtensionsAsync() {\n        const extensionPromises = [];\n        registeredGLTFExtensions.forEach((registeredExtension, name) => {\n            // Don't load explicitly disabled extensions.\n            if (this.parent.extensionOptions[name]?.enabled === false) {\n                // But warn if the disabled extension is used by the model.\n                if (registeredExtension.isGLTFExtension && this.isExtensionUsed(name)) {\n                    Logger.Warn(`Extension ${name} is used but has been explicitly disabled.`);\n                }\n            }\n            // Load loader extensions that are not a glTF extension, as well as extensions that are glTF extensions and are used by the model.\n            else if (!registeredExtension.isGLTFExtension || this.isExtensionUsed(name)) {\n                extensionPromises.push((async () => {\n                    const extension = await registeredExtension.factory(this);\n                    if (extension.name !== name) {\n                        Logger.Warn(`The name of the glTF loader extension instance does not match the registered name: ${extension.name} !== ${name}`);\n                    }\n                    this._parent.onExtensionLoadedObservable.notifyObservers(extension);\n                    return extension;\n                })());\n            }\n        });\n        this._extensions.push(...(await Promise.all(extensionPromises)));\n        this._extensions.sort((a, b) => (a.order || Number.MAX_VALUE) - (b.order || Number.MAX_VALUE));\n        this._parent.onExtensionLoadedObservable.clear();\n        if (this._gltf.extensionsRequired) {\n            for (const name of this._gltf.extensionsRequired) {\n                const available = this._extensions.some((extension) => extension.name === name && extension.enabled);\n                if (!available) {\n                    if (this.parent.extensionOptions[name]?.enabled === false) {\n                        throw new Error(`Required extension ${name} is disabled`);\n                    }\n                    throw new Error(`Required extension ${name} is not available`);\n                }\n            }\n        }\n    }\n    _createRootNode() {\n        if (this._parent.customRootNode !== undefined) {\n            this._rootBabylonMesh = this._parent.customRootNode;\n            return {\n                // eslint-disable-next-line @typescript-eslint/naming-convention\n                _babylonTransformNode: this._rootBabylonMesh === null ? undefined : this._rootBabylonMesh,\n                index: -1,\n            };\n        }\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        const rootMesh = new Mesh(\"__root__\", this._babylonScene);\n        this._rootBabylonMesh = rootMesh;\n        this._rootBabylonMesh._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        this._rootBabylonMesh.setEnabled(false);\n        const rootNode = {\n            // eslint-disable-next-line @typescript-eslint/naming-convention\n            _babylonTransformNode: this._rootBabylonMesh,\n            index: -1,\n        };\n        switch (this._parent.coordinateSystemMode) {\n            case GLTFLoaderCoordinateSystemMode.AUTO: {\n                if (!this._babylonScene.useRightHandedSystem) {\n                    rootNode.rotation = [0, 1, 0, 0];\n                    rootNode.scale = [1, 1, -1];\n                    GLTFLoader._LoadTransform(rootNode, this._rootBabylonMesh);\n                }\n                break;\n            }\n            case GLTFLoaderCoordinateSystemMode.FORCE_RIGHT_HANDED: {\n                this._babylonScene.useRightHandedSystem = true;\n                break;\n            }\n            default: {\n                throw new Error(`Invalid coordinate system mode (${this._parent.coordinateSystemMode})`);\n            }\n        }\n        this._parent.onMeshLoadedObservable.notifyObservers(rootMesh);\n        return rootNode;\n    }\n    /**\n     * Loads a glTF scene.\n     * @param context The context when loading the asset\n     * @param scene The glTF scene property\n     * @returns A promise that resolves when the load is complete\n     */\n    loadSceneAsync(context, scene) {\n        const extensionPromise = this._extensionsLoadSceneAsync(context, scene);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        const promises = new Array();\n        this.logOpen(`${context} ${scene.name || \"\"}`);\n        if (scene.nodes) {\n            for (const index of scene.nodes) {\n                const node = ArrayItem.Get(`${context}/nodes/${index}`, this._gltf.nodes, index);\n                promises.push(this.loadNodeAsync(`/nodes/${node.index}`, node, (babylonMesh) => {\n                    babylonMesh.parent = this._rootBabylonMesh;\n                }));\n            }\n        }\n        for (const action of this._postSceneLoadActions) {\n            action();\n        }\n        promises.push(this._loadAnimationsAsync());\n        this.logClose();\n        return Promise.all(promises).then(() => { });\n    }\n    _forEachPrimitive(node, callback) {\n        if (node._primitiveBabylonMeshes) {\n            for (const babylonMesh of node._primitiveBabylonMeshes) {\n                callback(babylonMesh);\n            }\n        }\n    }\n    _getGeometries() {\n        const geometries = [];\n        const nodes = this._gltf.nodes;\n        if (nodes) {\n            for (const node of nodes) {\n                this._forEachPrimitive(node, (babylonMesh) => {\n                    const geometry = babylonMesh.geometry;\n                    if (geometry && geometries.indexOf(geometry) === -1) {\n                        geometries.push(geometry);\n                    }\n                });\n            }\n        }\n        return geometries;\n    }\n    _getMeshes() {\n        const meshes = [];\n        // Root mesh is always first, if available.\n        if (this._rootBabylonMesh instanceof AbstractMesh) {\n            meshes.push(this._rootBabylonMesh);\n        }\n        const nodes = this._gltf.nodes;\n        if (nodes) {\n            for (const node of nodes) {\n                this._forEachPrimitive(node, (babylonMesh) => {\n                    meshes.push(babylonMesh);\n                });\n            }\n        }\n        return meshes;\n    }\n    _getTransformNodes() {\n        const transformNodes = [];\n        const nodes = this._gltf.nodes;\n        if (nodes) {\n            for (const node of nodes) {\n                if (node._babylonTransformNode && node._babylonTransformNode.getClassName() === \"TransformNode\") {\n                    transformNodes.push(node._babylonTransformNode);\n                }\n                if (node._babylonTransformNodeForSkin) {\n                    transformNodes.push(node._babylonTransformNodeForSkin);\n                }\n            }\n        }\n        return transformNodes;\n    }\n    _getSkeletons() {\n        const skeletons = [];\n        const skins = this._gltf.skins;\n        if (skins) {\n            for (const skin of skins) {\n                if (skin._data) {\n                    skeletons.push(skin._data.babylonSkeleton);\n                }\n            }\n        }\n        return skeletons;\n    }\n    _getAnimationGroups() {\n        const animationGroups = [];\n        const animations = this._gltf.animations;\n        if (animations) {\n            for (const animation of animations) {\n                if (animation._babylonAnimationGroup) {\n                    animationGroups.push(animation._babylonAnimationGroup);\n                }\n            }\n        }\n        return animationGroups;\n    }\n    _startAnimations() {\n        switch (this._parent.animationStartMode) {\n            case GLTFLoaderAnimationStartMode.NONE: {\n                // do nothing\n                break;\n            }\n            case GLTFLoaderAnimationStartMode.FIRST: {\n                const babylonAnimationGroups = this._getAnimationGroups();\n                if (babylonAnimationGroups.length !== 0) {\n                    babylonAnimationGroups[0].start(true);\n                }\n                break;\n            }\n            case GLTFLoaderAnimationStartMode.ALL: {\n                const babylonAnimationGroups = this._getAnimationGroups();\n                for (const babylonAnimationGroup of babylonAnimationGroups) {\n                    babylonAnimationGroup.start(true);\n                }\n                break;\n            }\n            default: {\n                Logger.Error(`Invalid animation start mode (${this._parent.animationStartMode})`);\n                return;\n            }\n        }\n    }\n    /**\n     * Loads a glTF node.\n     * @param context The context when loading the asset\n     * @param node The glTF node property\n     * @param assign A function called synchronously after parsing the glTF properties\n     * @returns A promise that resolves with the loaded Babylon mesh when the load is complete\n     */\n    loadNodeAsync(context, node, assign = () => { }) {\n        const extensionPromise = this._extensionsLoadNodeAsync(context, node, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        if (node._babylonTransformNode) {\n            throw new Error(`${context}: Invalid recursive node hierarchy`);\n        }\n        const promises = new Array();\n        this.logOpen(`${context} ${node.name || \"\"}`);\n        const loadNode = (babylonTransformNode) => {\n            GLTFLoader.AddPointerMetadata(babylonTransformNode, context);\n            GLTFLoader._LoadTransform(node, babylonTransformNode);\n            if (node.camera != undefined) {\n                const camera = ArrayItem.Get(`${context}/camera`, this._gltf.cameras, node.camera);\n                promises.push(this.loadCameraAsync(`/cameras/${camera.index}`, camera, (babylonCamera) => {\n                    babylonCamera.parent = babylonTransformNode;\n                }));\n            }\n            if (node.children) {\n                for (const index of node.children) {\n                    const childNode = ArrayItem.Get(`${context}/children/${index}`, this._gltf.nodes, index);\n                    promises.push(this.loadNodeAsync(`/nodes/${childNode.index}`, childNode, (childBabylonMesh) => {\n                        childBabylonMesh.parent = babylonTransformNode;\n                    }));\n                }\n            }\n            assign(babylonTransformNode);\n        };\n        const hasMesh = node.mesh != undefined;\n        const hasSkin = this._parent.loadSkins && node.skin != undefined;\n        if (!hasMesh || hasSkin) {\n            const nodeName = node.name || `node${node.index}`;\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\n            const transformNode = new TransformNode(nodeName, this._babylonScene);\n            transformNode._parentContainer = this._assetContainer;\n            this._babylonScene._blockEntityCollection = false;\n            if (node.mesh == undefined) {\n                node._babylonTransformNode = transformNode;\n            }\n            else {\n                node._babylonTransformNodeForSkin = transformNode;\n            }\n            loadNode(transformNode);\n        }\n        if (hasMesh) {\n            if (hasSkin) {\n                // See https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins (second implementation note)\n                // This code path will place the skinned mesh as a sibling of the skeleton root node without loading the\n                // transform, which effectively ignores the transform of the skinned mesh, as per spec.\n                const mesh = ArrayItem.Get(`${context}/mesh`, this._gltf.meshes, node.mesh);\n                promises.push(this._loadMeshAsync(`/meshes/${mesh.index}`, node, mesh, (babylonTransformNode) => {\n                    const babylonTransformNodeForSkin = node._babylonTransformNodeForSkin;\n                    // Merge the metadata from the skin node to the skinned mesh in case a loader extension added metadata.\n                    babylonTransformNode.metadata = deepMerge(babylonTransformNodeForSkin.metadata, babylonTransformNode.metadata || {});\n                    const skin = ArrayItem.Get(`${context}/skin`, this._gltf.skins, node.skin);\n                    promises.push(this._loadSkinAsync(`/skins/${skin.index}`, node, skin, (babylonSkeleton) => {\n                        this._forEachPrimitive(node, (babylonMesh) => {\n                            babylonMesh.skeleton = babylonSkeleton;\n                        });\n                        // Wait until all the nodes are parented before parenting the skinned mesh.\n                        this._postSceneLoadActions.push(() => {\n                            if (skin.skeleton != undefined) {\n                                // Place the skinned mesh node as a sibling of the skeleton root node.\n                                // Handle special case when the parent of the skeleton root is the skinned mesh.\n                                const parentNode = ArrayItem.Get(`/skins/${skin.index}/skeleton`, this._gltf.nodes, skin.skeleton).parent;\n                                if (node.index === parentNode.index) {\n                                    babylonTransformNode.parent = babylonTransformNodeForSkin.parent;\n                                }\n                                else {\n                                    babylonTransformNode.parent = parentNode._babylonTransformNode;\n                                }\n                            }\n                            else {\n                                babylonTransformNode.parent = this._rootBabylonMesh;\n                            }\n                            this._parent.onSkinLoadedObservable.notifyObservers({ node: babylonTransformNodeForSkin, skinnedNode: babylonTransformNode });\n                        });\n                    }));\n                }));\n            }\n            else {\n                const mesh = ArrayItem.Get(`${context}/mesh`, this._gltf.meshes, node.mesh);\n                promises.push(this._loadMeshAsync(`/meshes/${mesh.index}`, node, mesh, loadNode));\n            }\n        }\n        this.logClose();\n        return Promise.all(promises).then(() => {\n            this._forEachPrimitive(node, (babylonMesh) => {\n                const asMesh = babylonMesh;\n                if (!asMesh.isAnInstance && asMesh.geometry && asMesh.geometry.useBoundingInfoFromGeometry) {\n                    // simply apply the world matrices to the bounding info - the extends are already ok\n                    babylonMesh._updateBoundingInfo();\n                }\n                else {\n                    babylonMesh.refreshBoundingInfo(true, true);\n                }\n            });\n            return node._babylonTransformNode;\n        });\n    }\n    _loadMeshAsync(context, node, mesh, assign) {\n        const primitives = mesh.primitives;\n        if (!primitives || !primitives.length) {\n            throw new Error(`${context}: Primitives are missing`);\n        }\n        if (primitives[0].index == undefined) {\n            ArrayItem.Assign(primitives);\n        }\n        const promises = new Array();\n        this.logOpen(`${context} ${mesh.name || \"\"}`);\n        const name = node.name || `node${node.index}`;\n        if (primitives.length === 1) {\n            const primitive = mesh.primitives[0];\n            promises.push(this._loadMeshPrimitiveAsync(`${context}/primitives/${primitive.index}`, name, node, mesh, primitive, (babylonMesh) => {\n                node._babylonTransformNode = babylonMesh;\n                node._primitiveBabylonMeshes = [babylonMesh];\n            }));\n        }\n        else {\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\n            node._babylonTransformNode = new TransformNode(name, this._babylonScene);\n            node._babylonTransformNode._parentContainer = this._assetContainer;\n            this._babylonScene._blockEntityCollection = false;\n            node._primitiveBabylonMeshes = [];\n            for (const primitive of primitives) {\n                promises.push(this._loadMeshPrimitiveAsync(`${context}/primitives/${primitive.index}`, `${name}_primitive${primitive.index}`, node, mesh, primitive, (babylonMesh) => {\n                    babylonMesh.parent = node._babylonTransformNode;\n                    node._primitiveBabylonMeshes.push(babylonMesh);\n                }));\n            }\n        }\n        assign(node._babylonTransformNode);\n        this.logClose();\n        return Promise.all(promises).then(() => {\n            return node._babylonTransformNode;\n        });\n    }\n    /**\n     * @internal Define this method to modify the default behavior when loading data for mesh primitives.\n     * @param context The context when loading the asset\n     * @param name The mesh name when loading the asset\n     * @param node The glTF node when loading the asset\n     * @param mesh The glTF mesh when loading the asset\n     * @param primitive The glTF mesh primitive property\n     * @param assign A function called synchronously after parsing the glTF properties\n     * @returns A promise that resolves with the loaded mesh when the load is complete or null if not handled\n     */\n    _loadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign) {\n        const extensionPromise = this._extensionsLoadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        this.logOpen(`${context}`);\n        const shouldInstance = this._disableInstancedMesh === 0 && this._parent.createInstances && node.skin == undefined && !mesh.primitives[0].targets;\n        let babylonAbstractMesh;\n        let promise;\n        if (shouldInstance && primitive._instanceData) {\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\n            babylonAbstractMesh = primitive._instanceData.babylonSourceMesh.createInstance(name);\n            babylonAbstractMesh._parentContainer = this._assetContainer;\n            this._babylonScene._blockEntityCollection = false;\n            promise = primitive._instanceData.promise;\n        }\n        else {\n            const promises = new Array();\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\n            const babylonMesh = new Mesh(name, this._babylonScene);\n            babylonMesh._parentContainer = this._assetContainer;\n            this._babylonScene._blockEntityCollection = false;\n            babylonMesh.sideOrientation = this._babylonScene.useRightHandedSystem ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\n            this._createMorphTargets(context, node, mesh, primitive, babylonMesh);\n            promises.push(this._loadVertexDataAsync(context, primitive, babylonMesh).then((babylonGeometry) => {\n                return this._loadMorphTargetsAsync(context, primitive, babylonMesh, babylonGeometry).then(() => {\n                    if (this._disposed) {\n                        return;\n                    }\n                    this._babylonScene._blockEntityCollection = !!this._assetContainer;\n                    babylonGeometry.applyToMesh(babylonMesh);\n                    babylonGeometry._parentContainer = this._assetContainer;\n                    this._babylonScene._blockEntityCollection = false;\n                });\n            }));\n            const babylonDrawMode = GLTFLoader._GetDrawMode(context, primitive.mode);\n            if (primitive.material == undefined) {\n                let babylonMaterial = this._defaultBabylonMaterialData[babylonDrawMode];\n                if (!babylonMaterial) {\n                    babylonMaterial = this._createDefaultMaterial(\"__GLTFLoader._default\", babylonDrawMode);\n                    this._parent.onMaterialLoadedObservable.notifyObservers(babylonMaterial);\n                    this._defaultBabylonMaterialData[babylonDrawMode] = babylonMaterial;\n                }\n                babylonMesh.material = babylonMaterial;\n            }\n            else if (!this.parent.skipMaterials) {\n                const material = ArrayItem.Get(`${context}/material`, this._gltf.materials, primitive.material);\n                promises.push(this._loadMaterialAsync(`/materials/${material.index}`, material, babylonMesh, babylonDrawMode, (babylonMaterial) => {\n                    babylonMesh.material = babylonMaterial;\n                }));\n            }\n            promise = Promise.all(promises);\n            if (shouldInstance) {\n                primitive._instanceData = {\n                    babylonSourceMesh: babylonMesh,\n                    promise: promise,\n                };\n            }\n            babylonAbstractMesh = babylonMesh;\n        }\n        GLTFLoader.AddPointerMetadata(babylonAbstractMesh, context);\n        this._parent.onMeshLoadedObservable.notifyObservers(babylonAbstractMesh);\n        assign(babylonAbstractMesh);\n        this.logClose();\n        return promise.then(() => {\n            return babylonAbstractMesh;\n        });\n    }\n    _loadVertexDataAsync(context, primitive, babylonMesh) {\n        const extensionPromise = this._extensionsLoadVertexDataAsync(context, primitive, babylonMesh);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        const attributes = primitive.attributes;\n        if (!attributes) {\n            throw new Error(`${context}: Attributes are missing`);\n        }\n        const promises = new Array();\n        const babylonGeometry = new Geometry(babylonMesh.name, this._babylonScene);\n        if (primitive.indices == undefined) {\n            babylonMesh.isUnIndexed = true;\n        }\n        else {\n            const accessor = ArrayItem.Get(`${context}/indices`, this._gltf.accessors, primitive.indices);\n            promises.push(this._loadIndicesAccessorAsync(`/accessors/${accessor.index}`, accessor).then((data) => {\n                babylonGeometry.setIndices(data);\n            }));\n        }\n        const loadAttribute = (name, kind, callback) => {\n            if (attributes[name] == undefined) {\n                return;\n            }\n            babylonMesh._delayInfo = babylonMesh._delayInfo || [];\n            if (babylonMesh._delayInfo.indexOf(kind) === -1) {\n                babylonMesh._delayInfo.push(kind);\n            }\n            const accessor = ArrayItem.Get(`${context}/attributes/${name}`, this._gltf.accessors, attributes[name]);\n            promises.push(this._loadVertexAccessorAsync(`/accessors/${accessor.index}`, accessor, kind).then((babylonVertexBuffer) => {\n                if (babylonVertexBuffer.getKind() === VertexBuffer.PositionKind && !this.parent.alwaysComputeBoundingBox && !babylonMesh.skeleton) {\n                    const babylonBoundingInfo = LoadBoundingInfoFromPositionAccessor(accessor);\n                    if (babylonBoundingInfo) {\n                        babylonGeometry._boundingInfo = babylonBoundingInfo;\n                        babylonGeometry.useBoundingInfoFromGeometry = true;\n                    }\n                }\n                babylonGeometry.setVerticesBuffer(babylonVertexBuffer, accessor.count);\n            }));\n            if (kind == VertexBuffer.MatricesIndicesExtraKind) {\n                babylonMesh.numBoneInfluencers = 8;\n            }\n            if (callback) {\n                callback(accessor);\n            }\n        };\n        loadAttribute(\"POSITION\", VertexBuffer.PositionKind);\n        loadAttribute(\"NORMAL\", VertexBuffer.NormalKind);\n        loadAttribute(\"TANGENT\", VertexBuffer.TangentKind);\n        loadAttribute(\"TEXCOORD_0\", VertexBuffer.UVKind);\n        loadAttribute(\"TEXCOORD_1\", VertexBuffer.UV2Kind);\n        loadAttribute(\"TEXCOORD_2\", VertexBuffer.UV3Kind);\n        loadAttribute(\"TEXCOORD_3\", VertexBuffer.UV4Kind);\n        loadAttribute(\"TEXCOORD_4\", VertexBuffer.UV5Kind);\n        loadAttribute(\"TEXCOORD_5\", VertexBuffer.UV6Kind);\n        loadAttribute(\"JOINTS_0\", VertexBuffer.MatricesIndicesKind);\n        loadAttribute(\"WEIGHTS_0\", VertexBuffer.MatricesWeightsKind);\n        loadAttribute(\"JOINTS_1\", VertexBuffer.MatricesIndicesExtraKind);\n        loadAttribute(\"WEIGHTS_1\", VertexBuffer.MatricesWeightsExtraKind);\n        loadAttribute(\"COLOR_0\", VertexBuffer.ColorKind, (accessor) => {\n            if (accessor.type === \"VEC4\" /* AccessorType.VEC4 */) {\n                babylonMesh.hasVertexAlpha = true;\n            }\n        });\n        return Promise.all(promises).then(() => {\n            return babylonGeometry;\n        });\n    }\n    _createMorphTargets(context, node, mesh, primitive, babylonMesh) {\n        if (!primitive.targets || !this._parent.loadMorphTargets) {\n            return;\n        }\n        if (node._numMorphTargets == undefined) {\n            node._numMorphTargets = primitive.targets.length;\n        }\n        else if (primitive.targets.length !== node._numMorphTargets) {\n            throw new Error(`${context}: Primitives do not have the same number of targets`);\n        }\n        const targetNames = mesh.extras ? mesh.extras.targetNames : null;\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        babylonMesh.morphTargetManager = new MorphTargetManager(this._babylonScene);\n        babylonMesh.morphTargetManager._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        babylonMesh.morphTargetManager.areUpdatesFrozen = true;\n        for (let index = 0; index < primitive.targets.length; index++) {\n            const weight = node.weights ? node.weights[index] : mesh.weights ? mesh.weights[index] : 0;\n            const name = targetNames ? targetNames[index] : `morphTarget${index}`;\n            babylonMesh.morphTargetManager.addTarget(new MorphTarget(name, weight, babylonMesh.getScene()));\n            // TODO: tell the target whether it has positions, normals, tangents\n        }\n    }\n    _loadMorphTargetsAsync(context, primitive, babylonMesh, babylonGeometry) {\n        if (!primitive.targets || !this._parent.loadMorphTargets) {\n            return Promise.resolve();\n        }\n        const promises = new Array();\n        const morphTargetManager = babylonMesh.morphTargetManager;\n        for (let index = 0; index < morphTargetManager.numTargets; index++) {\n            const babylonMorphTarget = morphTargetManager.getTarget(index);\n            promises.push(this._loadMorphTargetVertexDataAsync(`${context}/targets/${index}`, babylonGeometry, primitive.targets[index], babylonMorphTarget));\n        }\n        return Promise.all(promises).then(() => {\n            morphTargetManager.areUpdatesFrozen = false;\n        });\n    }\n    _loadMorphTargetVertexDataAsync(context, babylonGeometry, attributes, babylonMorphTarget) {\n        const promises = new Array();\n        const loadAttribute = (attribute, kind, setData) => {\n            if (attributes[attribute] == undefined) {\n                return;\n            }\n            const babylonVertexBuffer = babylonGeometry.getVertexBuffer(kind);\n            if (!babylonVertexBuffer) {\n                return;\n            }\n            const accessor = ArrayItem.Get(`${context}/${attribute}`, this._gltf.accessors, attributes[attribute]);\n            promises.push(this._loadFloatAccessorAsync(`/accessors/${accessor.index}`, accessor).then((data) => {\n                setData(babylonVertexBuffer, data);\n            }));\n        };\n        loadAttribute(\"POSITION\", VertexBuffer.PositionKind, (babylonVertexBuffer, data) => {\n            const positions = new Float32Array(data.length);\n            babylonVertexBuffer.forEach(data.length, (value, index) => {\n                positions[index] = data[index] + value;\n            });\n            babylonMorphTarget.setPositions(positions);\n        });\n        loadAttribute(\"NORMAL\", VertexBuffer.NormalKind, (babylonVertexBuffer, data) => {\n            const normals = new Float32Array(data.length);\n            babylonVertexBuffer.forEach(normals.length, (value, index) => {\n                normals[index] = data[index] + value;\n            });\n            babylonMorphTarget.setNormals(normals);\n        });\n        loadAttribute(\"TANGENT\", VertexBuffer.TangentKind, (babylonVertexBuffer, data) => {\n            const tangents = new Float32Array((data.length / 3) * 4);\n            let dataIndex = 0;\n            babylonVertexBuffer.forEach((data.length / 3) * 4, (value, index) => {\n                // Tangent data for morph targets is stored as xyz delta.\n                // The vertexData.tangent is stored as xyzw.\n                // So we need to skip every fourth vertexData.tangent.\n                if ((index + 1) % 4 !== 0) {\n                    tangents[dataIndex] = data[dataIndex] + value;\n                    dataIndex++;\n                }\n            });\n            babylonMorphTarget.setTangents(tangents);\n        });\n        loadAttribute(\"TEXCOORD_0\", VertexBuffer.UVKind, (babylonVertexBuffer, data) => {\n            const uvs = new Float32Array(data.length);\n            babylonVertexBuffer.forEach(data.length, (value, index) => {\n                uvs[index] = data[index] + value;\n            });\n            babylonMorphTarget.setUVs(uvs);\n        });\n        loadAttribute(\"TEXCOORD_1\", VertexBuffer.UV2Kind, (babylonVertexBuffer, data) => {\n            const uvs = new Float32Array(data.length);\n            babylonVertexBuffer.forEach(data.length, (value, index) => {\n                uvs[index] = data[index] + value;\n            });\n            babylonMorphTarget.setUV2s(uvs);\n        });\n        loadAttribute(\"COLOR_0\", VertexBuffer.ColorKind, (babylonVertexBuffer, data) => {\n            let colors = null;\n            const componentSize = babylonVertexBuffer.getSize();\n            if (componentSize === 3) {\n                colors = new Float32Array((data.length / 3) * 4);\n                babylonVertexBuffer.forEach(data.length, (value, index) => {\n                    const pixid = Math.floor(index / 3);\n                    const channel = index % 3;\n                    colors[4 * pixid + channel] = data[3 * pixid + channel] + value;\n                });\n                for (let i = 0; i < data.length / 3; ++i) {\n                    colors[4 * i + 3] = 1;\n                }\n            }\n            else if (componentSize === 4) {\n                colors = new Float32Array(data.length);\n                babylonVertexBuffer.forEach(data.length, (value, index) => {\n                    colors[index] = data[index] + value;\n                });\n            }\n            else {\n                throw new Error(`${context}: Invalid number of components (${componentSize}) for COLOR_0 attribute`);\n            }\n            babylonMorphTarget.setColors(colors);\n        });\n        return Promise.all(promises).then(() => { });\n    }\n    static _LoadTransform(node, babylonNode) {\n        // Ignore the TRS of skinned nodes.\n        // See https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins (second implementation note)\n        if (node.skin != undefined) {\n            return;\n        }\n        let position = Vector3.Zero();\n        let rotation = Quaternion.Identity();\n        let scaling = Vector3.One();\n        if (node.matrix) {\n            const matrix = Matrix.FromArray(node.matrix);\n            matrix.decompose(scaling, rotation, position);\n        }\n        else {\n            if (node.translation) {\n                position = Vector3.FromArray(node.translation);\n            }\n            if (node.rotation) {\n                rotation = Quaternion.FromArray(node.rotation);\n            }\n            if (node.scale) {\n                scaling = Vector3.FromArray(node.scale);\n            }\n        }\n        babylonNode.position = position;\n        babylonNode.rotationQuaternion = rotation;\n        babylonNode.scaling = scaling;\n    }\n    _loadSkinAsync(context, node, skin, assign) {\n        if (!this._parent.loadSkins) {\n            return Promise.resolve();\n        }\n        const extensionPromise = this._extensionsLoadSkinAsync(context, node, skin);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        if (skin._data) {\n            assign(skin._data.babylonSkeleton);\n            return skin._data.promise;\n        }\n        const skeletonId = `skeleton${skin.index}`;\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        const babylonSkeleton = new Skeleton(skin.name || skeletonId, skeletonId, this._babylonScene);\n        babylonSkeleton._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        this._loadBones(context, skin, babylonSkeleton);\n        const promise = this._loadSkinInverseBindMatricesDataAsync(context, skin).then((inverseBindMatricesData) => {\n            this._updateBoneMatrices(babylonSkeleton, inverseBindMatricesData);\n        });\n        skin._data = {\n            babylonSkeleton: babylonSkeleton,\n            promise: promise,\n        };\n        assign(babylonSkeleton);\n        return promise;\n    }\n    _loadBones(context, skin, babylonSkeleton) {\n        if (skin.skeleton == undefined || this._parent.alwaysComputeSkeletonRootNode) {\n            const rootNode = this._findSkeletonRootNode(`${context}/joints`, skin.joints);\n            if (rootNode) {\n                if (skin.skeleton === undefined) {\n                    skin.skeleton = rootNode.index;\n                }\n                else {\n                    const isParent = (a, b) => {\n                        for (; b.parent; b = b.parent) {\n                            if (b.parent === a) {\n                                return true;\n                            }\n                        }\n                        return false;\n                    };\n                    const skeletonNode = ArrayItem.Get(`${context}/skeleton`, this._gltf.nodes, skin.skeleton);\n                    if (skeletonNode !== rootNode && !isParent(skeletonNode, rootNode)) {\n                        Logger.Warn(`${context}/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root`);\n                        skin.skeleton = rootNode.index;\n                    }\n                }\n            }\n            else {\n                Logger.Warn(`${context}: Failed to find common root`);\n            }\n        }\n        const babylonBones = {};\n        for (const index of skin.joints) {\n            const node = ArrayItem.Get(`${context}/joints/${index}`, this._gltf.nodes, index);\n            this._loadBone(node, skin, babylonSkeleton, babylonBones);\n        }\n    }\n    _findSkeletonRootNode(context, joints) {\n        if (joints.length === 0) {\n            return null;\n        }\n        const paths = {};\n        for (const index of joints) {\n            const path = [];\n            let node = ArrayItem.Get(`${context}/${index}`, this._gltf.nodes, index);\n            while (node.index !== -1) {\n                path.unshift(node);\n                node = node.parent;\n            }\n            paths[index] = path;\n        }\n        let rootNode = null;\n        for (let i = 0;; ++i) {\n            let path = paths[joints[0]];\n            if (i >= path.length) {\n                return rootNode;\n            }\n            const node = path[i];\n            for (let j = 1; j < joints.length; ++j) {\n                path = paths[joints[j]];\n                if (i >= path.length || node !== path[i]) {\n                    return rootNode;\n                }\n            }\n            rootNode = node;\n        }\n    }\n    _loadBone(node, skin, babylonSkeleton, babylonBones) {\n        node._isJoint = true;\n        let babylonBone = babylonBones[node.index];\n        if (babylonBone) {\n            return babylonBone;\n        }\n        let parentBabylonBone = null;\n        if (node.index !== skin.skeleton) {\n            if (node.parent && node.parent.index !== -1) {\n                parentBabylonBone = this._loadBone(node.parent, skin, babylonSkeleton, babylonBones);\n            }\n            else if (skin.skeleton !== undefined) {\n                Logger.Warn(`/skins/${skin.index}/skeleton: Skeleton node is not a common root`);\n            }\n        }\n        const boneIndex = skin.joints.indexOf(node.index);\n        babylonBone = new Bone(node.name || `joint${node.index}`, babylonSkeleton, parentBabylonBone, this._getNodeMatrix(node), null, null, boneIndex);\n        babylonBones[node.index] = babylonBone;\n        // Wait until the scene is loaded to ensure the transform nodes are loaded.\n        this._postSceneLoadActions.push(() => {\n            // Link the Babylon bone with the corresponding Babylon transform node.\n            // A glTF joint is a pointer to a glTF node in the glTF node hierarchy similar to Unity3D.\n            babylonBone.linkTransformNode(node._babylonTransformNode);\n        });\n        return babylonBone;\n    }\n    _loadSkinInverseBindMatricesDataAsync(context, skin) {\n        if (skin.inverseBindMatrices == undefined) {\n            return Promise.resolve(null);\n        }\n        const accessor = ArrayItem.Get(`${context}/inverseBindMatrices`, this._gltf.accessors, skin.inverseBindMatrices);\n        return this._loadFloatAccessorAsync(`/accessors/${accessor.index}`, accessor);\n    }\n    _updateBoneMatrices(babylonSkeleton, inverseBindMatricesData) {\n        for (const babylonBone of babylonSkeleton.bones) {\n            const baseMatrix = Matrix.Identity();\n            const boneIndex = babylonBone._index;\n            if (inverseBindMatricesData && boneIndex !== -1) {\n                Matrix.FromArrayToRef(inverseBindMatricesData, boneIndex * 16, baseMatrix);\n                baseMatrix.invertToRef(baseMatrix);\n            }\n            const babylonParentBone = babylonBone.getParent();\n            if (babylonParentBone) {\n                baseMatrix.multiplyToRef(babylonParentBone.getAbsoluteInverseBindMatrix(), baseMatrix);\n            }\n            babylonBone.updateMatrix(baseMatrix, false, false);\n            babylonBone._updateAbsoluteBindMatrices(undefined, false);\n        }\n    }\n    _getNodeMatrix(node) {\n        return node.matrix\n            ? Matrix.FromArray(node.matrix)\n            : Matrix.Compose(node.scale ? Vector3.FromArray(node.scale) : Vector3.One(), node.rotation ? Quaternion.FromArray(node.rotation) : Quaternion.Identity(), node.translation ? Vector3.FromArray(node.translation) : Vector3.Zero());\n    }\n    /**\n     * Loads a glTF camera.\n     * @param context The context when loading the asset\n     * @param camera The glTF camera property\n     * @param assign A function called synchronously after parsing the glTF properties\n     * @returns A promise that resolves with the loaded Babylon camera when the load is complete\n     */\n    loadCameraAsync(context, camera, assign = () => { }) {\n        const extensionPromise = this._extensionsLoadCameraAsync(context, camera, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        const promises = new Array();\n        this.logOpen(`${context} ${camera.name || \"\"}`);\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        const babylonCamera = new FreeCamera(camera.name || `camera${camera.index}`, Vector3.Zero(), this._babylonScene, false);\n        babylonCamera._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        babylonCamera.ignoreParentScaling = true;\n        camera._babylonCamera = babylonCamera;\n        // Rotation by 180 as glTF has a different convention than Babylon.\n        babylonCamera.rotation.set(0, Math.PI, 0);\n        switch (camera.type) {\n            case \"perspective\" /* CameraType.PERSPECTIVE */: {\n                const perspective = camera.perspective;\n                if (!perspective) {\n                    throw new Error(`${context}: Camera perspective properties are missing`);\n                }\n                babylonCamera.fov = perspective.yfov;\n                babylonCamera.minZ = perspective.znear;\n                babylonCamera.maxZ = perspective.zfar || 0;\n                break;\n            }\n            case \"orthographic\" /* CameraType.ORTHOGRAPHIC */: {\n                if (!camera.orthographic) {\n                    throw new Error(`${context}: Camera orthographic properties are missing`);\n                }\n                babylonCamera.mode = Camera.ORTHOGRAPHIC_CAMERA;\n                babylonCamera.orthoLeft = -camera.orthographic.xmag;\n                babylonCamera.orthoRight = camera.orthographic.xmag;\n                babylonCamera.orthoBottom = -camera.orthographic.ymag;\n                babylonCamera.orthoTop = camera.orthographic.ymag;\n                babylonCamera.minZ = camera.orthographic.znear;\n                babylonCamera.maxZ = camera.orthographic.zfar;\n                break;\n            }\n            default: {\n                throw new Error(`${context}: Invalid camera type (${camera.type})`);\n            }\n        }\n        GLTFLoader.AddPointerMetadata(babylonCamera, context);\n        this._parent.onCameraLoadedObservable.notifyObservers(babylonCamera);\n        assign(babylonCamera);\n        this.logClose();\n        return Promise.all(promises).then(() => {\n            return babylonCamera;\n        });\n    }\n    _loadAnimationsAsync() {\n        const animations = this._gltf.animations;\n        if (!animations) {\n            return Promise.resolve();\n        }\n        const promises = new Array();\n        for (let index = 0; index < animations.length; index++) {\n            const animation = animations[index];\n            promises.push(this.loadAnimationAsync(`/animations/${animation.index}`, animation).then((animationGroup) => {\n                // Delete the animation group if it ended up not having any animations in it.\n                if (animationGroup.targetedAnimations.length === 0) {\n                    animationGroup.dispose();\n                }\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n    /**\n     * Loads a glTF animation.\n     * @param context The context when loading the asset\n     * @param animation The glTF animation property\n     * @returns A promise that resolves with the loaded Babylon animation group when the load is complete\n     */\n    loadAnimationAsync(context, animation) {\n        const promise = this._extensionsLoadAnimationAsync(context, animation);\n        if (promise) {\n            return promise;\n        }\n        // eslint-disable-next-line @typescript-eslint/naming-convention\n        return import(\"@babylonjs/core/Animations/animationGroup.js\").then(({ AnimationGroup }) => {\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\n            const babylonAnimationGroup = new AnimationGroup(animation.name || `animation${animation.index}`, this._babylonScene);\n            babylonAnimationGroup._parentContainer = this._assetContainer;\n            this._babylonScene._blockEntityCollection = false;\n            animation._babylonAnimationGroup = babylonAnimationGroup;\n            const promises = new Array();\n            ArrayItem.Assign(animation.channels);\n            ArrayItem.Assign(animation.samplers);\n            for (const channel of animation.channels) {\n                promises.push(this._loadAnimationChannelAsync(`${context}/channels/${channel.index}`, context, animation, channel, (babylonTarget, babylonAnimation) => {\n                    babylonTarget.animations = babylonTarget.animations || [];\n                    babylonTarget.animations.push(babylonAnimation);\n                    babylonAnimationGroup.addTargetedAnimation(babylonAnimation, babylonTarget);\n                }));\n            }\n            return Promise.all(promises).then(() => {\n                babylonAnimationGroup.normalize(0);\n                return babylonAnimationGroup;\n            });\n        });\n    }\n    /**\n     * @hidden\n     * Loads a glTF animation channel.\n     * @param context The context when loading the asset\n     * @param animationContext The context of the animation when loading the asset\n     * @param animation The glTF animation property\n     * @param channel The glTF animation channel property\n     * @param onLoad Called for each animation loaded\n     * @returns A void promise that resolves when the load is complete\n     */\n    async _loadAnimationChannelAsync(context, animationContext, animation, channel, onLoad) {\n        const promise = this._extensionsLoadAnimationChannelAsync(context, animationContext, animation, channel, onLoad);\n        if (promise) {\n            return promise;\n        }\n        if (channel.target.node == undefined) {\n            return Promise.resolve();\n        }\n        const targetNode = ArrayItem.Get(`${context}/target/node`, this._gltf.nodes, channel.target.node);\n        const channelTargetPath = channel.target.path;\n        const pathIsWeights = channelTargetPath === \"weights\" /* AnimationChannelTargetPath.WEIGHTS */;\n        // Ignore animations that have no animation targets.\n        if ((pathIsWeights && !targetNode._numMorphTargets) || (!pathIsWeights && !targetNode._babylonTransformNode)) {\n            return Promise.resolve();\n        }\n        // Don't load node animations if disabled.\n        if (!this._parent.loadNodeAnimations && !pathIsWeights && !targetNode._isJoint) {\n            return Promise.resolve();\n        }\n        // async-load the animation sampler to provide the interpolation of the channelTargetPath\n        await import(\"./glTFLoaderAnimation.js\");\n        let properties;\n        switch (channelTargetPath) {\n            case \"translation\" /* AnimationChannelTargetPath.TRANSLATION */: {\n                properties = GetMappingForKey(\"/nodes/{}/translation\")?.interpolation;\n                break;\n            }\n            case \"rotation\" /* AnimationChannelTargetPath.ROTATION */: {\n                properties = GetMappingForKey(\"/nodes/{}/rotation\")?.interpolation;\n                break;\n            }\n            case \"scale\" /* AnimationChannelTargetPath.SCALE */: {\n                properties = GetMappingForKey(\"/nodes/{}/scale\")?.interpolation;\n                break;\n            }\n            case \"weights\" /* AnimationChannelTargetPath.WEIGHTS */: {\n                properties = GetMappingForKey(\"/nodes/{}/weights\")?.interpolation;\n                break;\n            }\n            default: {\n                throw new Error(`${context}/target/path: Invalid value (${channel.target.path})`);\n            }\n        }\n        // stay safe\n        if (!properties) {\n            throw new Error(`${context}/target/path: Could not find interpolation properties for target path (${channel.target.path})`);\n        }\n        const targetInfo = {\n            object: targetNode,\n            info: properties,\n        };\n        return this._loadAnimationChannelFromTargetInfoAsync(context, animationContext, animation, channel, targetInfo, onLoad);\n    }\n    /**\n     * @hidden\n     * Loads a glTF animation channel.\n     * @param context The context when loading the asset\n     * @param animationContext The context of the animation when loading the asset\n     * @param animation The glTF animation property\n     * @param channel The glTF animation channel property\n     * @param targetInfo The glTF target and properties\n     * @param onLoad Called for each animation loaded\n     * @returns A void promise that resolves when the load is complete\n     */\n    _loadAnimationChannelFromTargetInfoAsync(context, animationContext, animation, channel, targetInfo, onLoad) {\n        const fps = this.parent.targetFps;\n        const invfps = 1 / fps;\n        const sampler = ArrayItem.Get(`${context}/sampler`, animation.samplers, channel.sampler);\n        return this._loadAnimationSamplerAsync(`${animationContext}/samplers/${channel.sampler}`, sampler).then((data) => {\n            let numAnimations = 0;\n            const target = targetInfo.object;\n            const propertyInfos = targetInfo.info;\n            // Extract the corresponding values from the read value.\n            // GLTF values may be dispatched to several Babylon properties.\n            // For example, baseColorFactor [`r`, `g`, `b`, `a`] is dispatched to\n            // - albedoColor as Color3(`r`, `g`, `b`)\n            // - alpha as `a`\n            for (const propertyInfo of propertyInfos) {\n                const stride = propertyInfo.getStride(target);\n                const input = data.input;\n                const output = data.output;\n                const keys = new Array(input.length);\n                let outputOffset = 0;\n                switch (data.interpolation) {\n                    case \"STEP\" /* AnimationSamplerInterpolation.STEP */: {\n                        for (let index = 0; index < input.length; index++) {\n                            const value = propertyInfo.getValue(target, output, outputOffset, 1);\n                            outputOffset += stride;\n                            keys[index] = {\n                                frame: input[index] * fps,\n                                value: value,\n                                interpolation: 1 /* AnimationKeyInterpolation.STEP */,\n                            };\n                        }\n                        break;\n                    }\n                    case \"CUBICSPLINE\" /* AnimationSamplerInterpolation.CUBICSPLINE */: {\n                        for (let index = 0; index < input.length; index++) {\n                            const inTangent = propertyInfo.getValue(target, output, outputOffset, invfps);\n                            outputOffset += stride;\n                            const value = propertyInfo.getValue(target, output, outputOffset, 1);\n                            outputOffset += stride;\n                            const outTangent = propertyInfo.getValue(target, output, outputOffset, invfps);\n                            outputOffset += stride;\n                            keys[index] = {\n                                frame: input[index] * fps,\n                                inTangent: inTangent,\n                                value: value,\n                                outTangent: outTangent,\n                            };\n                        }\n                        break;\n                    }\n                    case \"LINEAR\" /* AnimationSamplerInterpolation.LINEAR */: {\n                        for (let index = 0; index < input.length; index++) {\n                            const value = propertyInfo.getValue(target, output, outputOffset, 1);\n                            outputOffset += stride;\n                            keys[index] = {\n                                frame: input[index] * fps,\n                                value: value,\n                            };\n                        }\n                        break;\n                    }\n                }\n                if (outputOffset > 0) {\n                    const name = `${animation.name || `animation${animation.index}`}_channel${channel.index}_${numAnimations}`;\n                    const babylonAnimations = propertyInfo.buildAnimations(target, name, fps, keys);\n                    for (const babylonAnimation of babylonAnimations) {\n                        numAnimations++;\n                        onLoad(babylonAnimation.babylonAnimatable, babylonAnimation.babylonAnimation);\n                    }\n                }\n            }\n        });\n    }\n    _loadAnimationSamplerAsync(context, sampler) {\n        if (sampler._data) {\n            return sampler._data;\n        }\n        const interpolation = sampler.interpolation || \"LINEAR\" /* AnimationSamplerInterpolation.LINEAR */;\n        switch (interpolation) {\n            case \"STEP\" /* AnimationSamplerInterpolation.STEP */:\n            case \"LINEAR\" /* AnimationSamplerInterpolation.LINEAR */:\n            case \"CUBICSPLINE\" /* AnimationSamplerInterpolation.CUBICSPLINE */: {\n                break;\n            }\n            default: {\n                throw new Error(`${context}/interpolation: Invalid value (${sampler.interpolation})`);\n            }\n        }\n        const inputAccessor = ArrayItem.Get(`${context}/input`, this._gltf.accessors, sampler.input);\n        const outputAccessor = ArrayItem.Get(`${context}/output`, this._gltf.accessors, sampler.output);\n        sampler._data = Promise.all([\n            this._loadFloatAccessorAsync(`/accessors/${inputAccessor.index}`, inputAccessor),\n            this._loadFloatAccessorAsync(`/accessors/${outputAccessor.index}`, outputAccessor),\n        ]).then(([inputData, outputData]) => {\n            return {\n                input: inputData,\n                interpolation: interpolation,\n                output: outputData,\n            };\n        });\n        return sampler._data;\n    }\n    /**\n     * Loads a glTF buffer.\n     * @param context The context when loading the asset\n     * @param buffer The glTF buffer property\n     * @param byteOffset The byte offset to use\n     * @param byteLength The byte length to use\n     * @returns A promise that resolves with the loaded data when the load is complete\n     */\n    loadBufferAsync(context, buffer, byteOffset, byteLength) {\n        const extensionPromise = this._extensionsLoadBufferAsync(context, buffer, byteOffset, byteLength);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        if (!buffer._data) {\n            if (buffer.uri) {\n                buffer._data = this.loadUriAsync(`${context}/uri`, buffer, buffer.uri);\n            }\n            else {\n                if (!this._bin) {\n                    throw new Error(`${context}: Uri is missing or the binary glTF is missing its binary chunk`);\n                }\n                buffer._data = this._bin.readAsync(0, buffer.byteLength);\n            }\n        }\n        return buffer._data.then((data) => {\n            try {\n                return new Uint8Array(data.buffer, data.byteOffset + byteOffset, byteLength);\n            }\n            catch (e) {\n                throw new Error(`${context}: ${e.message}`);\n            }\n        });\n    }\n    /**\n     * Loads a glTF buffer view.\n     * @param context The context when loading the asset\n     * @param bufferView The glTF buffer view property\n     * @returns A promise that resolves with the loaded data when the load is complete\n     */\n    loadBufferViewAsync(context, bufferView) {\n        const extensionPromise = this._extensionsLoadBufferViewAsync(context, bufferView);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        if (bufferView._data) {\n            return bufferView._data;\n        }\n        const buffer = ArrayItem.Get(`${context}/buffer`, this._gltf.buffers, bufferView.buffer);\n        bufferView._data = this.loadBufferAsync(`/buffers/${buffer.index}`, buffer, bufferView.byteOffset || 0, bufferView.byteLength);\n        return bufferView._data;\n    }\n    _loadAccessorAsync(context, accessor, constructor) {\n        if (accessor._data) {\n            return accessor._data;\n        }\n        const numComponents = GLTFLoader._GetNumComponents(context, accessor.type);\n        const byteStride = numComponents * VertexBuffer.GetTypeByteLength(accessor.componentType);\n        const length = numComponents * accessor.count;\n        if (accessor.bufferView == undefined) {\n            accessor._data = Promise.resolve(new constructor(length));\n        }\n        else {\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\n            accessor._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\n                if (accessor.componentType === 5126 /* AccessorComponentType.FLOAT */ && !accessor.normalized && (!bufferView.byteStride || bufferView.byteStride === byteStride)) {\n                    return GLTFLoader._GetTypedArray(context, accessor.componentType, data, accessor.byteOffset, length);\n                }\n                else {\n                    const typedArray = new constructor(length);\n                    VertexBuffer.ForEach(data, accessor.byteOffset || 0, bufferView.byteStride || byteStride, numComponents, accessor.componentType, typedArray.length, accessor.normalized || false, (value, index) => {\n                        typedArray[index] = value;\n                    });\n                    return typedArray;\n                }\n            });\n        }\n        if (accessor.sparse) {\n            const sparse = accessor.sparse;\n            accessor._data = accessor._data.then((data) => {\n                const typedArray = data;\n                const indicesBufferView = ArrayItem.Get(`${context}/sparse/indices/bufferView`, this._gltf.bufferViews, sparse.indices.bufferView);\n                const valuesBufferView = ArrayItem.Get(`${context}/sparse/values/bufferView`, this._gltf.bufferViews, sparse.values.bufferView);\n                return Promise.all([\n                    this.loadBufferViewAsync(`/bufferViews/${indicesBufferView.index}`, indicesBufferView),\n                    this.loadBufferViewAsync(`/bufferViews/${valuesBufferView.index}`, valuesBufferView),\n                ]).then(([indicesData, valuesData]) => {\n                    const indices = GLTFLoader._GetTypedArray(`${context}/sparse/indices`, sparse.indices.componentType, indicesData, sparse.indices.byteOffset, sparse.count);\n                    const sparseLength = numComponents * sparse.count;\n                    let values;\n                    if (accessor.componentType === 5126 /* AccessorComponentType.FLOAT */ && !accessor.normalized) {\n                        values = GLTFLoader._GetTypedArray(`${context}/sparse/values`, accessor.componentType, valuesData, sparse.values.byteOffset, sparseLength);\n                    }\n                    else {\n                        const sparseData = GLTFLoader._GetTypedArray(`${context}/sparse/values`, accessor.componentType, valuesData, sparse.values.byteOffset, sparseLength);\n                        values = new constructor(sparseLength);\n                        VertexBuffer.ForEach(sparseData, 0, byteStride, numComponents, accessor.componentType, values.length, accessor.normalized || false, (value, index) => {\n                            values[index] = value;\n                        });\n                    }\n                    let valuesIndex = 0;\n                    for (let indicesIndex = 0; indicesIndex < indices.length; indicesIndex++) {\n                        let dataIndex = indices[indicesIndex] * numComponents;\n                        for (let componentIndex = 0; componentIndex < numComponents; componentIndex++) {\n                            typedArray[dataIndex++] = values[valuesIndex++];\n                        }\n                    }\n                    return typedArray;\n                });\n            });\n        }\n        return accessor._data;\n    }\n    /**\n     * @internal\n     */\n    _loadFloatAccessorAsync(context, accessor) {\n        return this._loadAccessorAsync(context, accessor, Float32Array);\n    }\n    /**\n     * @internal\n     */\n    _loadIndicesAccessorAsync(context, accessor) {\n        if (accessor.type !== \"SCALAR\" /* AccessorType.SCALAR */) {\n            throw new Error(`${context}/type: Invalid value ${accessor.type}`);\n        }\n        if (accessor.componentType !== 5121 /* AccessorComponentType.UNSIGNED_BYTE */ &&\n            accessor.componentType !== 5123 /* AccessorComponentType.UNSIGNED_SHORT */ &&\n            accessor.componentType !== 5125 /* AccessorComponentType.UNSIGNED_INT */) {\n            throw new Error(`${context}/componentType: Invalid value ${accessor.componentType}`);\n        }\n        if (accessor._data) {\n            return accessor._data;\n        }\n        if (accessor.sparse) {\n            const constructor = GLTFLoader._GetTypedArrayConstructor(`${context}/componentType`, accessor.componentType);\n            accessor._data = this._loadAccessorAsync(context, accessor, constructor);\n        }\n        else {\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\n            accessor._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\n                return GLTFLoader._GetTypedArray(context, accessor.componentType, data, accessor.byteOffset, accessor.count);\n            });\n        }\n        return accessor._data;\n    }\n    /**\n     * @internal\n     */\n    _loadVertexBufferViewAsync(bufferView) {\n        if (bufferView._babylonBuffer) {\n            return bufferView._babylonBuffer;\n        }\n        const engine = this._babylonScene.getEngine();\n        bufferView._babylonBuffer = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\n            return new Buffer(engine, data, false);\n        });\n        return bufferView._babylonBuffer;\n    }\n    /**\n     * @internal\n     */\n    _loadVertexAccessorAsync(context, accessor, kind) {\n        if (accessor._babylonVertexBuffer?.[kind]) {\n            return accessor._babylonVertexBuffer[kind];\n        }\n        if (!accessor._babylonVertexBuffer) {\n            accessor._babylonVertexBuffer = {};\n        }\n        const engine = this._babylonScene.getEngine();\n        if (accessor.sparse || accessor.bufferView == undefined) {\n            accessor._babylonVertexBuffer[kind] = this._loadFloatAccessorAsync(context, accessor).then((data) => {\n                return new VertexBuffer(engine, data, kind, false);\n            });\n        }\n        else {\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\n            accessor._babylonVertexBuffer[kind] = this._loadVertexBufferViewAsync(bufferView).then((babylonBuffer) => {\n                const numComponents = GLTFLoader._GetNumComponents(context, accessor.type);\n                return new VertexBuffer(engine, babylonBuffer, kind, false, undefined, bufferView.byteStride, undefined, accessor.byteOffset, numComponents, accessor.componentType, accessor.normalized, true, undefined, true);\n            });\n        }\n        return accessor._babylonVertexBuffer[kind];\n    }\n    _loadMaterialMetallicRoughnessPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        if (properties) {\n            if (properties.baseColorFactor) {\n                babylonMaterial.albedoColor = Color3.FromArray(properties.baseColorFactor);\n                babylonMaterial.alpha = properties.baseColorFactor[3];\n            }\n            else {\n                babylonMaterial.albedoColor = Color3.White();\n            }\n            babylonMaterial.metallic = properties.metallicFactor == undefined ? 1 : properties.metallicFactor;\n            babylonMaterial.roughness = properties.roughnessFactor == undefined ? 1 : properties.roughnessFactor;\n            if (properties.baseColorTexture) {\n                promises.push(this.loadTextureInfoAsync(`${context}/baseColorTexture`, properties.baseColorTexture, (texture) => {\n                    texture.name = `${babylonMaterial.name} (Base Color)`;\n                    babylonMaterial.albedoTexture = texture;\n                }));\n            }\n            if (properties.metallicRoughnessTexture) {\n                properties.metallicRoughnessTexture.nonColorData = true;\n                promises.push(this.loadTextureInfoAsync(`${context}/metallicRoughnessTexture`, properties.metallicRoughnessTexture, (texture) => {\n                    texture.name = `${babylonMaterial.name} (Metallic Roughness)`;\n                    babylonMaterial.metallicTexture = texture;\n                }));\n                babylonMaterial.useMetallnessFromMetallicTextureBlue = true;\n                babylonMaterial.useRoughnessFromMetallicTextureGreen = true;\n                babylonMaterial.useRoughnessFromMetallicTextureAlpha = false;\n            }\n        }\n        return Promise.all(promises).then(() => { });\n    }\n    /**\n     * @internal\n     */\n    _loadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign = () => { }) {\n        const extensionPromise = this._extensionsLoadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        material._data = material._data || {};\n        let babylonData = material._data[babylonDrawMode];\n        if (!babylonData) {\n            this.logOpen(`${context} ${material.name || \"\"}`);\n            const babylonMaterial = this.createMaterial(context, material, babylonDrawMode);\n            babylonData = {\n                babylonMaterial: babylonMaterial,\n                babylonMeshes: [],\n                promise: this.loadMaterialPropertiesAsync(context, material, babylonMaterial),\n            };\n            material._data[babylonDrawMode] = babylonData;\n            GLTFLoader.AddPointerMetadata(babylonMaterial, context);\n            this._parent.onMaterialLoadedObservable.notifyObservers(babylonMaterial);\n            this.logClose();\n        }\n        if (babylonMesh) {\n            babylonData.babylonMeshes.push(babylonMesh);\n            babylonMesh.onDisposeObservable.addOnce(() => {\n                const index = babylonData.babylonMeshes.indexOf(babylonMesh);\n                if (index !== -1) {\n                    babylonData.babylonMeshes.splice(index, 1);\n                }\n            });\n        }\n        assign(babylonData.babylonMaterial);\n        return babylonData.promise.then(() => {\n            return babylonData.babylonMaterial;\n        });\n    }\n    _createDefaultMaterial(name, babylonDrawMode) {\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        const babylonMaterial = new PBRMaterial(name, this._babylonScene);\n        babylonMaterial._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        // Moved to mesh so user can change materials on gltf meshes: babylonMaterial.sideOrientation = this._babylonScene.useRightHandedSystem ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\n        babylonMaterial.fillMode = babylonDrawMode;\n        babylonMaterial.enableSpecularAntiAliasing = true;\n        babylonMaterial.useRadianceOverAlpha = !this._parent.transparencyAsCoverage;\n        babylonMaterial.useSpecularOverAlpha = !this._parent.transparencyAsCoverage;\n        babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_OPAQUE;\n        babylonMaterial.metallic = 1;\n        babylonMaterial.roughness = 1;\n        return babylonMaterial;\n    }\n    /**\n     * Creates a Babylon material from a glTF material.\n     * @param context The context when loading the asset\n     * @param material The glTF material property\n     * @param babylonDrawMode The draw mode for the Babylon material\n     * @returns The Babylon material\n     */\n    createMaterial(context, material, babylonDrawMode) {\n        const extensionPromise = this._extensionsCreateMaterial(context, material, babylonDrawMode);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        const name = material.name || `material${material.index}`;\n        const babylonMaterial = this._createDefaultMaterial(name, babylonDrawMode);\n        return babylonMaterial;\n    }\n    /**\n     * Loads properties from a glTF material into a Babylon material.\n     * @param context The context when loading the asset\n     * @param material The glTF material property\n     * @param babylonMaterial The Babylon material\n     * @returns A promise that resolves when the load is complete\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        const extensionPromise = this._extensionsLoadMaterialPropertiesAsync(context, material, babylonMaterial);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        const promises = new Array();\n        promises.push(this.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\n        if (material.pbrMetallicRoughness) {\n            promises.push(this._loadMaterialMetallicRoughnessPropertiesAsync(`${context}/pbrMetallicRoughness`, material.pbrMetallicRoughness, babylonMaterial));\n        }\n        this.loadMaterialAlphaProperties(context, material, babylonMaterial);\n        return Promise.all(promises).then(() => { });\n    }\n    /**\n     * Loads the normal, occlusion, and emissive properties from a glTF material into a Babylon material.\n     * @param context The context when loading the asset\n     * @param material The glTF material property\n     * @param babylonMaterial The Babylon material\n     * @returns A promise that resolves when the load is complete\n     */\n    loadMaterialBasePropertiesAsync(context, material, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.emissiveColor = material.emissiveFactor ? Color3.FromArray(material.emissiveFactor) : new Color3(0, 0, 0);\n        if (material.doubleSided) {\n            babylonMaterial.backFaceCulling = false;\n            babylonMaterial.twoSidedLighting = true;\n        }\n        if (material.normalTexture) {\n            material.normalTexture.nonColorData = true;\n            promises.push(this.loadTextureInfoAsync(`${context}/normalTexture`, material.normalTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Normal)`;\n                babylonMaterial.bumpTexture = texture;\n            }));\n            babylonMaterial.invertNormalMapX = !this._babylonScene.useRightHandedSystem;\n            babylonMaterial.invertNormalMapY = this._babylonScene.useRightHandedSystem;\n            if (material.normalTexture.scale != undefined && babylonMaterial.bumpTexture) {\n                babylonMaterial.bumpTexture.level = material.normalTexture.scale;\n            }\n            babylonMaterial.forceIrradianceInFragment = true;\n        }\n        if (material.occlusionTexture) {\n            material.occlusionTexture.nonColorData = true;\n            promises.push(this.loadTextureInfoAsync(`${context}/occlusionTexture`, material.occlusionTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Occlusion)`;\n                babylonMaterial.ambientTexture = texture;\n            }));\n            babylonMaterial.useAmbientInGrayScale = true;\n            if (material.occlusionTexture.strength != undefined) {\n                babylonMaterial.ambientTextureStrength = material.occlusionTexture.strength;\n            }\n        }\n        if (material.emissiveTexture) {\n            promises.push(this.loadTextureInfoAsync(`${context}/emissiveTexture`, material.emissiveTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Emissive)`;\n                babylonMaterial.emissiveTexture = texture;\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n    /**\n     * Loads the alpha properties from a glTF material into a Babylon material.\n     * Must be called after the setting the albedo texture of the Babylon material when the material has an albedo texture.\n     * @param context The context when loading the asset\n     * @param material The glTF material property\n     * @param babylonMaterial The Babylon material\n     */\n    loadMaterialAlphaProperties(context, material, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const alphaMode = material.alphaMode || \"OPAQUE\" /* MaterialAlphaMode.OPAQUE */;\n        switch (alphaMode) {\n            case \"OPAQUE\" /* MaterialAlphaMode.OPAQUE */: {\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_OPAQUE;\n                babylonMaterial.alpha = 1.0; // Force alpha to 1.0 for opaque mode.\n                break;\n            }\n            case \"MASK\" /* MaterialAlphaMode.MASK */: {\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_ALPHATEST;\n                babylonMaterial.alphaCutOff = material.alphaCutoff == undefined ? 0.5 : material.alphaCutoff;\n                if (babylonMaterial.albedoTexture) {\n                    babylonMaterial.albedoTexture.hasAlpha = true;\n                }\n                break;\n            }\n            case \"BLEND\" /* MaterialAlphaMode.BLEND */: {\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_ALPHABLEND;\n                if (babylonMaterial.albedoTexture) {\n                    babylonMaterial.albedoTexture.hasAlpha = true;\n                    babylonMaterial.useAlphaFromAlbedoTexture = true;\n                }\n                break;\n            }\n            default: {\n                throw new Error(`${context}/alphaMode: Invalid value (${material.alphaMode})`);\n            }\n        }\n    }\n    /**\n     * Loads a glTF texture info.\n     * @param context The context when loading the asset\n     * @param textureInfo The glTF texture info property\n     * @param assign A function called synchronously after parsing the glTF properties\n     * @returns A promise that resolves with the loaded Babylon texture when the load is complete\n     */\n    loadTextureInfoAsync(context, textureInfo, assign = () => { }) {\n        const extensionPromise = this._extensionsLoadTextureInfoAsync(context, textureInfo, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        this.logOpen(`${context}`);\n        if (textureInfo.texCoord >= 6) {\n            throw new Error(`${context}/texCoord: Invalid value (${textureInfo.texCoord})`);\n        }\n        const texture = ArrayItem.Get(`${context}/index`, this._gltf.textures, textureInfo.index);\n        texture._textureInfo = textureInfo;\n        const promise = this._loadTextureAsync(`/textures/${textureInfo.index}`, texture, (babylonTexture) => {\n            babylonTexture.coordinatesIndex = textureInfo.texCoord || 0;\n            GLTFLoader.AddPointerMetadata(babylonTexture, context);\n            this._parent.onTextureLoadedObservable.notifyObservers(babylonTexture);\n            assign(babylonTexture);\n        });\n        this.logClose();\n        return promise;\n    }\n    /**\n     * @internal\n     */\n    _loadTextureAsync(context, texture, assign = () => { }) {\n        const extensionPromise = this._extensionsLoadTextureAsync(context, texture, assign);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        this.logOpen(`${context} ${texture.name || \"\"}`);\n        const sampler = texture.sampler == undefined ? GLTFLoader.DefaultSampler : ArrayItem.Get(`${context}/sampler`, this._gltf.samplers, texture.sampler);\n        const image = ArrayItem.Get(`${context}/source`, this._gltf.images, texture.source);\n        const promise = this._createTextureAsync(context, sampler, image, assign, undefined, !texture._textureInfo.nonColorData);\n        this.logClose();\n        return promise;\n    }\n    /**\n     * @internal\n     */\n    _createTextureAsync(context, sampler, image, assign = () => { }, textureLoaderOptions, useSRGBBuffer) {\n        const samplerData = this._loadSampler(`/samplers/${sampler.index}`, sampler);\n        const promises = new Array();\n        const deferred = new Deferred();\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\n        const textureCreationOptions = {\n            noMipmap: samplerData.noMipMaps,\n            invertY: false,\n            samplingMode: samplerData.samplingMode,\n            onLoad: () => {\n                if (!this._disposed) {\n                    deferred.resolve();\n                }\n            },\n            onError: (message, exception) => {\n                if (!this._disposed) {\n                    deferred.reject(new Error(`${context}: ${exception && exception.message ? exception.message : message || \"Failed to load texture\"}`));\n                }\n            },\n            mimeType: image.mimeType ?? GetMimeType(image.uri ?? \"\"),\n            loaderOptions: textureLoaderOptions,\n            useSRGBBuffer: !!useSRGBBuffer && this._parent.useSRGBBuffers,\n        };\n        const babylonTexture = new Texture(null, this._babylonScene, textureCreationOptions);\n        babylonTexture._parentContainer = this._assetContainer;\n        this._babylonScene._blockEntityCollection = false;\n        promises.push(deferred.promise);\n        promises.push(this.loadImageAsync(`/images/${image.index}`, image).then((data) => {\n            const name = image.uri || `${this._fileName}#image${image.index}`;\n            const dataUrl = `data:${this._uniqueRootUrl}${name}`;\n            babylonTexture.updateURL(dataUrl, data);\n            // Set the internal texture label.\n            const internalTexture = babylonTexture.getInternalTexture();\n            if (internalTexture) {\n                internalTexture.label = image.name;\n            }\n        }));\n        babylonTexture.wrapU = samplerData.wrapU;\n        babylonTexture.wrapV = samplerData.wrapV;\n        assign(babylonTexture);\n        if (this._parent.useGltfTextureNames) {\n            babylonTexture.name = image.name || image.uri || `image${image.index}`;\n        }\n        return Promise.all(promises).then(() => {\n            return babylonTexture;\n        });\n    }\n    _loadSampler(context, sampler) {\n        if (!sampler._data) {\n            sampler._data = {\n                noMipMaps: sampler.minFilter === 9728 /* TextureMinFilter.NEAREST */ || sampler.minFilter === 9729 /* TextureMinFilter.LINEAR */,\n                samplingMode: GLTFLoader._GetTextureSamplingMode(context, sampler),\n                wrapU: GLTFLoader._GetTextureWrapMode(`${context}/wrapS`, sampler.wrapS),\n                wrapV: GLTFLoader._GetTextureWrapMode(`${context}/wrapT`, sampler.wrapT),\n            };\n        }\n        return sampler._data;\n    }\n    /**\n     * Loads a glTF image.\n     * @param context The context when loading the asset\n     * @param image The glTF image property\n     * @returns A promise that resolves with the loaded data when the load is complete\n     */\n    loadImageAsync(context, image) {\n        if (!image._data) {\n            this.logOpen(`${context} ${image.name || \"\"}`);\n            if (image.uri) {\n                image._data = this.loadUriAsync(`${context}/uri`, image, image.uri);\n            }\n            else {\n                const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, image.bufferView);\n                image._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView);\n            }\n            this.logClose();\n        }\n        return image._data;\n    }\n    /**\n     * Loads a glTF uri.\n     * @param context The context when loading the asset\n     * @param property The glTF property associated with the uri\n     * @param uri The base64 or relative uri\n     * @returns A promise that resolves with the loaded data when the load is complete\n     */\n    loadUriAsync(context, property, uri) {\n        const extensionPromise = this._extensionsLoadUriAsync(context, property, uri);\n        if (extensionPromise) {\n            return extensionPromise;\n        }\n        if (!GLTFLoader._ValidateUri(uri)) {\n            throw new Error(`${context}: '${uri}' is invalid`);\n        }\n        if (IsBase64DataUrl(uri)) {\n            const data = new Uint8Array(DecodeBase64UrlToBinary(uri));\n            this.log(`${context}: Decoded ${uri.substring(0, 64)}... (${data.length} bytes)`);\n            return Promise.resolve(data);\n        }\n        this.log(`${context}: Loading ${uri}`);\n        return this._parent.preprocessUrlAsync(this._rootUrl + uri).then((url) => {\n            return new Promise((resolve, reject) => {\n                this._parent._loadFile(this._babylonScene, url, (data) => {\n                    if (!this._disposed) {\n                        this.log(`${context}: Loaded ${uri} (${data.byteLength} bytes)`);\n                        resolve(new Uint8Array(data));\n                    }\n                }, true, (request) => {\n                    reject(new LoadFileError(`${context}: Failed to load '${uri}'${request ? \": \" + request.status + \" \" + request.statusText : \"\"}`, request));\n                });\n            });\n        });\n    }\n    /**\n     * Adds a JSON pointer to the _internalMetadata of the Babylon object at `<object>._internalMetadata.gltf.pointers`.\n     * @param babylonObject the Babylon object with _internalMetadata\n     * @param pointer the JSON pointer\n     */\n    static AddPointerMetadata(babylonObject, pointer) {\n        babylonObject.metadata = babylonObject.metadata || {};\n        const metadata = (babylonObject._internalMetadata = babylonObject._internalMetadata || {});\n        const gltf = (metadata.gltf = metadata.gltf || {});\n        const pointers = (gltf.pointers = gltf.pointers || []);\n        pointers.push(pointer);\n    }\n    static _GetTextureWrapMode(context, mode) {\n        // Set defaults if undefined\n        mode = mode == undefined ? 10497 /* TextureWrapMode.REPEAT */ : mode;\n        switch (mode) {\n            case 33071 /* TextureWrapMode.CLAMP_TO_EDGE */:\n                return Texture.CLAMP_ADDRESSMODE;\n            case 33648 /* TextureWrapMode.MIRRORED_REPEAT */:\n                return Texture.MIRROR_ADDRESSMODE;\n            case 10497 /* TextureWrapMode.REPEAT */:\n                return Texture.WRAP_ADDRESSMODE;\n            default:\n                Logger.Warn(`${context}: Invalid value (${mode})`);\n                return Texture.WRAP_ADDRESSMODE;\n        }\n    }\n    static _GetTextureSamplingMode(context, sampler) {\n        // Set defaults if undefined\n        const magFilter = sampler.magFilter == undefined ? 9729 /* TextureMagFilter.LINEAR */ : sampler.magFilter;\n        const minFilter = sampler.minFilter == undefined ? 9987 /* TextureMinFilter.LINEAR_MIPMAP_LINEAR */ : sampler.minFilter;\n        if (magFilter === 9729 /* TextureMagFilter.LINEAR */) {\n            switch (minFilter) {\n                case 9728 /* TextureMinFilter.NEAREST */:\n                    return Texture.LINEAR_NEAREST;\n                case 9729 /* TextureMinFilter.LINEAR */:\n                    return Texture.LINEAR_LINEAR;\n                case 9984 /* TextureMinFilter.NEAREST_MIPMAP_NEAREST */:\n                    return Texture.LINEAR_NEAREST_MIPNEAREST;\n                case 9985 /* TextureMinFilter.LINEAR_MIPMAP_NEAREST */:\n                    return Texture.LINEAR_LINEAR_MIPNEAREST;\n                case 9986 /* TextureMinFilter.NEAREST_MIPMAP_LINEAR */:\n                    return Texture.LINEAR_NEAREST_MIPLINEAR;\n                case 9987 /* TextureMinFilter.LINEAR_MIPMAP_LINEAR */:\n                    return Texture.LINEAR_LINEAR_MIPLINEAR;\n                default:\n                    Logger.Warn(`${context}/minFilter: Invalid value (${minFilter})`);\n                    return Texture.LINEAR_LINEAR_MIPLINEAR;\n            }\n        }\n        else {\n            if (magFilter !== 9728 /* TextureMagFilter.NEAREST */) {\n                Logger.Warn(`${context}/magFilter: Invalid value (${magFilter})`);\n            }\n            switch (minFilter) {\n                case 9728 /* TextureMinFilter.NEAREST */:\n                    return Texture.NEAREST_NEAREST;\n                case 9729 /* TextureMinFilter.LINEAR */:\n                    return Texture.NEAREST_LINEAR;\n                case 9984 /* TextureMinFilter.NEAREST_MIPMAP_NEAREST */:\n                    return Texture.NEAREST_NEAREST_MIPNEAREST;\n                case 9985 /* TextureMinFilter.LINEAR_MIPMAP_NEAREST */:\n                    return Texture.NEAREST_LINEAR_MIPNEAREST;\n                case 9986 /* TextureMinFilter.NEAREST_MIPMAP_LINEAR */:\n                    return Texture.NEAREST_NEAREST_MIPLINEAR;\n                case 9987 /* TextureMinFilter.LINEAR_MIPMAP_LINEAR */:\n                    return Texture.NEAREST_LINEAR_MIPLINEAR;\n                default:\n                    Logger.Warn(`${context}/minFilter: Invalid value (${minFilter})`);\n                    return Texture.NEAREST_NEAREST_MIPNEAREST;\n            }\n        }\n    }\n    static _GetTypedArrayConstructor(context, componentType) {\n        try {\n            return GetTypedArrayConstructor(componentType);\n        }\n        catch (e) {\n            throw new Error(`${context}: ${e.message}`);\n        }\n    }\n    static _GetTypedArray(context, componentType, bufferView, byteOffset, length) {\n        const buffer = bufferView.buffer;\n        byteOffset = bufferView.byteOffset + (byteOffset || 0);\n        const constructor = GLTFLoader._GetTypedArrayConstructor(`${context}/componentType`, componentType);\n        const componentTypeLength = VertexBuffer.GetTypeByteLength(componentType);\n        if (byteOffset % componentTypeLength !== 0) {\n            // HACK: Copy the buffer if byte offset is not a multiple of component type byte length.\n            Logger.Warn(`${context}: Copying buffer as byte offset (${byteOffset}) is not a multiple of component type byte length (${componentTypeLength})`);\n            return new constructor(buffer.slice(byteOffset, byteOffset + length * componentTypeLength), 0);\n        }\n        return new constructor(buffer, byteOffset, length);\n    }\n    static _GetNumComponents(context, type) {\n        switch (type) {\n            case \"SCALAR\":\n                return 1;\n            case \"VEC2\":\n                return 2;\n            case \"VEC3\":\n                return 3;\n            case \"VEC4\":\n                return 4;\n            case \"MAT2\":\n                return 4;\n            case \"MAT3\":\n                return 9;\n            case \"MAT4\":\n                return 16;\n        }\n        throw new Error(`${context}: Invalid type (${type})`);\n    }\n    static _ValidateUri(uri) {\n        return Tools.IsBase64(uri) || uri.indexOf(\"..\") === -1;\n    }\n    /**\n     * @internal\n     */\n    static _GetDrawMode(context, mode) {\n        if (mode == undefined) {\n            mode = 4 /* MeshPrimitiveMode.TRIANGLES */;\n        }\n        switch (mode) {\n            case 0 /* MeshPrimitiveMode.POINTS */:\n                return Material.PointListDrawMode;\n            case 1 /* MeshPrimitiveMode.LINES */:\n                return Material.LineListDrawMode;\n            case 2 /* MeshPrimitiveMode.LINE_LOOP */:\n                return Material.LineLoopDrawMode;\n            case 3 /* MeshPrimitiveMode.LINE_STRIP */:\n                return Material.LineStripDrawMode;\n            case 4 /* MeshPrimitiveMode.TRIANGLES */:\n                return Material.TriangleFillMode;\n            case 5 /* MeshPrimitiveMode.TRIANGLE_STRIP */:\n                return Material.TriangleStripDrawMode;\n            case 6 /* MeshPrimitiveMode.TRIANGLE_FAN */:\n                return Material.TriangleFanDrawMode;\n        }\n        throw new Error(`${context}: Invalid mesh primitive mode (${mode})`);\n    }\n    _compileMaterialsAsync() {\n        this._parent._startPerformanceCounter(\"Compile materials\");\n        const promises = new Array();\n        if (this._gltf.materials) {\n            for (const material of this._gltf.materials) {\n                if (material._data) {\n                    for (const babylonDrawMode in material._data) {\n                        const babylonData = material._data[babylonDrawMode];\n                        for (const babylonMesh of babylonData.babylonMeshes) {\n                            // Ensure nonUniformScaling is set if necessary.\n                            babylonMesh.computeWorldMatrix(true);\n                            const babylonMaterial = babylonData.babylonMaterial;\n                            promises.push(babylonMaterial.forceCompilationAsync(babylonMesh));\n                            promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { useInstances: true }));\n                            if (this._parent.useClipPlane) {\n                                promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { clipPlane: true }));\n                                promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { clipPlane: true, useInstances: true }));\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        return Promise.all(promises).then(() => {\n            this._parent._endPerformanceCounter(\"Compile materials\");\n        });\n    }\n    _compileShadowGeneratorsAsync() {\n        this._parent._startPerformanceCounter(\"Compile shadow generators\");\n        const promises = new Array();\n        const lights = this._babylonScene.lights;\n        for (const light of lights) {\n            const generator = light.getShadowGenerator();\n            if (generator) {\n                promises.push(generator.forceCompilationAsync());\n            }\n        }\n        return Promise.all(promises).then(() => {\n            this._parent._endPerformanceCounter(\"Compile shadow generators\");\n        });\n    }\n    _forEachExtensions(action) {\n        for (const extension of this._extensions) {\n            if (extension.enabled) {\n                action(extension);\n            }\n        }\n    }\n    _applyExtensions(property, functionName, actionAsync) {\n        for (const extension of this._extensions) {\n            if (extension.enabled) {\n                const id = `${extension.name}.${functionName}`;\n                const loaderProperty = property;\n                loaderProperty._activeLoaderExtensionFunctions = loaderProperty._activeLoaderExtensionFunctions || {};\n                const activeLoaderExtensionFunctions = loaderProperty._activeLoaderExtensionFunctions;\n                if (!activeLoaderExtensionFunctions[id]) {\n                    activeLoaderExtensionFunctions[id] = true;\n                    try {\n                        const result = actionAsync(extension);\n                        if (result) {\n                            return result;\n                        }\n                    }\n                    finally {\n                        delete activeLoaderExtensionFunctions[id];\n                    }\n                }\n            }\n        }\n        return null;\n    }\n    _extensionsOnLoading() {\n        this._forEachExtensions((extension) => extension.onLoading && extension.onLoading());\n    }\n    _extensionsOnReady() {\n        this._forEachExtensions((extension) => extension.onReady && extension.onReady());\n    }\n    _extensionsLoadSceneAsync(context, scene) {\n        return this._applyExtensions(scene, \"loadScene\", (extension) => extension.loadSceneAsync && extension.loadSceneAsync(context, scene));\n    }\n    _extensionsLoadNodeAsync(context, node, assign) {\n        return this._applyExtensions(node, \"loadNode\", (extension) => extension.loadNodeAsync && extension.loadNodeAsync(context, node, assign));\n    }\n    _extensionsLoadCameraAsync(context, camera, assign) {\n        return this._applyExtensions(camera, \"loadCamera\", (extension) => extension.loadCameraAsync && extension.loadCameraAsync(context, camera, assign));\n    }\n    _extensionsLoadVertexDataAsync(context, primitive, babylonMesh) {\n        return this._applyExtensions(primitive, \"loadVertexData\", (extension) => extension._loadVertexDataAsync && extension._loadVertexDataAsync(context, primitive, babylonMesh));\n    }\n    _extensionsLoadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign) {\n        return this._applyExtensions(primitive, \"loadMeshPrimitive\", (extension) => extension._loadMeshPrimitiveAsync && extension._loadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign));\n    }\n    _extensionsLoadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign) {\n        return this._applyExtensions(material, \"loadMaterial\", (extension) => extension._loadMaterialAsync && extension._loadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign));\n    }\n    _extensionsCreateMaterial(context, material, babylonDrawMode) {\n        return this._applyExtensions(material, \"createMaterial\", (extension) => extension.createMaterial && extension.createMaterial(context, material, babylonDrawMode));\n    }\n    _extensionsLoadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return this._applyExtensions(material, \"loadMaterialProperties\", (extension) => extension.loadMaterialPropertiesAsync && extension.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n    }\n    _extensionsLoadTextureInfoAsync(context, textureInfo, assign) {\n        return this._applyExtensions(textureInfo, \"loadTextureInfo\", (extension) => extension.loadTextureInfoAsync && extension.loadTextureInfoAsync(context, textureInfo, assign));\n    }\n    _extensionsLoadTextureAsync(context, texture, assign) {\n        return this._applyExtensions(texture, \"loadTexture\", (extension) => extension._loadTextureAsync && extension._loadTextureAsync(context, texture, assign));\n    }\n    _extensionsLoadAnimationAsync(context, animation) {\n        return this._applyExtensions(animation, \"loadAnimation\", (extension) => extension.loadAnimationAsync && extension.loadAnimationAsync(context, animation));\n    }\n    _extensionsLoadAnimationChannelAsync(context, animationContext, animation, channel, onLoad) {\n        return this._applyExtensions(animation, \"loadAnimationChannel\", (extension) => extension._loadAnimationChannelAsync && extension._loadAnimationChannelAsync(context, animationContext, animation, channel, onLoad));\n    }\n    _extensionsLoadSkinAsync(context, node, skin) {\n        return this._applyExtensions(skin, \"loadSkin\", (extension) => extension._loadSkinAsync && extension._loadSkinAsync(context, node, skin));\n    }\n    _extensionsLoadUriAsync(context, property, uri) {\n        return this._applyExtensions(property, \"loadUri\", (extension) => extension._loadUriAsync && extension._loadUriAsync(context, property, uri));\n    }\n    _extensionsLoadBufferViewAsync(context, bufferView) {\n        return this._applyExtensions(bufferView, \"loadBufferView\", (extension) => extension.loadBufferViewAsync && extension.loadBufferViewAsync(context, bufferView));\n    }\n    _extensionsLoadBufferAsync(context, buffer, byteOffset, byteLength) {\n        return this._applyExtensions(buffer, \"loadBuffer\", (extension) => extension.loadBufferAsync && extension.loadBufferAsync(context, buffer, byteOffset, byteLength));\n    }\n    /**\n     * Helper method called by a loader extension to load an glTF extension.\n     * @param context The context when loading the asset\n     * @param property The glTF property to load the extension from\n     * @param extensionName The name of the extension to load\n     * @param actionAsync The action to run\n     * @returns The promise returned by actionAsync or null if the extension does not exist\n     */\n    static LoadExtensionAsync(context, property, extensionName, actionAsync) {\n        if (!property.extensions) {\n            return null;\n        }\n        const extensions = property.extensions;\n        const extension = extensions[extensionName];\n        if (!extension) {\n            return null;\n        }\n        return actionAsync(`${context}/extensions/${extensionName}`, extension);\n    }\n    /**\n     * Helper method called by a loader extension to load a glTF extra.\n     * @param context The context when loading the asset\n     * @param property The glTF property to load the extra from\n     * @param extensionName The name of the extension to load\n     * @param actionAsync The action to run\n     * @returns The promise returned by actionAsync or null if the extra does not exist\n     */\n    static LoadExtraAsync(context, property, extensionName, actionAsync) {\n        if (!property.extras) {\n            return null;\n        }\n        const extras = property.extras;\n        const extra = extras[extensionName];\n        if (!extra) {\n            return null;\n        }\n        return actionAsync(`${context}/extras/${extensionName}`, extra);\n    }\n    /**\n     * Checks for presence of an extension.\n     * @param name The name of the extension to check\n     * @returns A boolean indicating the presence of the given extension name in `extensionsUsed`\n     */\n    isExtensionUsed(name) {\n        return !!this._gltf.extensionsUsed && this._gltf.extensionsUsed.indexOf(name) !== -1;\n    }\n    /**\n     * Increments the indentation level and logs a message.\n     * @param message The message to log\n     */\n    logOpen(message) {\n        this._parent._logOpen(message);\n    }\n    /**\n     * Decrements the indentation level.\n     */\n    logClose() {\n        this._parent._logClose();\n    }\n    /**\n     * Logs a message\n     * @param message The message to log\n     */\n    log(message) {\n        this._parent._log(message);\n    }\n    /**\n     * Starts a performance counter.\n     * @param counterName The name of the performance counter\n     */\n    startPerformanceCounter(counterName) {\n        this._parent._startPerformanceCounter(counterName);\n    }\n    /**\n     * Ends a performance counter.\n     * @param counterName The name of the performance counter\n     */\n    endPerformanceCounter(counterName) {\n        this._parent._endPerformanceCounter(counterName);\n    }\n}\n/**\n * The default glTF sampler.\n */\nGLTFLoader.DefaultSampler = { index: -1 };\nGLTFFileLoader._CreateGLTF2Loader = (parent) => new GLTFLoader(parent);\n//# sourceMappingURL=glTFLoader.js.map"], "file": "assets/glTFLoader-D-NF4fEj.js"}