import{F as a}from"./flowGraphUnaryOperationBlock-BsZVallq.js";import{c as n,b as s,j as p,F as e}from"./declarationMapper-r-RREw_K.js";import{R as l}from"./index-Cb4A4-Xi.js";import"./flowGraphCachedOperationBlock-D--7yusk.js";import"./KHR_interactivity-DVSiPm30.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class F extends a{constructor(r){super(n,s,o=>+o,"FlowGraphBooleanToFloat",r)}}l("FlowGraphBooleanToFloat",F);class u extends a{constructor(r){super(n,p,o=>e.FromValue(+o),"FlowGraphBooleanToInt",r)}}l("FlowGraphBooleanToInt",u);class c extends a{constructor(r){super(s,n,o=>!!o,"FlowGraphFloatToBoolean",r)}}l("FlowGraphFloatToBoolean",c);class h extends a{constructor(r){super(p,n,o=>!!o.value,"FlowGraphIntToBoolean",r)}}l("FlowGraphIntToBoolean",h);class w extends a{constructor(r){super(p,s,o=>o.value,"FlowGraphIntToFloat",r)}}l("FlowGraphIntToFloat",w);class G extends a{constructor(r){super(s,p,o=>{switch(r?.roundingMode){case"floor":return e.FromValue(Math.floor(o));case"ceil":return e.FromValue(Math.ceil(o));case"round":return e.FromValue(Math.round(o));default:return e.FromValue(o)}},"FlowGraphFloatToInt",r)}}l("FlowGraphFloatToInt",G);export{F as FlowGraphBooleanToFloat,u as FlowGraphBooleanToInt,c as FlowGraphFloatToBoolean,G as FlowGraphFloatToInt,h as FlowGraphIntToBoolean,w as FlowGraphIntToFloat};
//# sourceMappingURL=flowGraphTypeToTypeBlocks-UXTyliE6.js.map
