import{F as o}from"./flowGraphCachedOperationBlock-D--7yusk.js";class p extends o{constructor(t,a,s,e,r,i){super(s,i),this._operation=e,this._className=r,this.a=this.registerDataInput("a",t),this.b=this.registerDataInput("b",a)}_doOperation(t){const a=this.a.getValue(t),s=this.b.getValue(t);return this._operation(a,s)}getClassName(){return this._className}}export{p as F};
//# sourceMappingURL=flowGraphBinaryOperationBlock-DaBPP43z.js.map
