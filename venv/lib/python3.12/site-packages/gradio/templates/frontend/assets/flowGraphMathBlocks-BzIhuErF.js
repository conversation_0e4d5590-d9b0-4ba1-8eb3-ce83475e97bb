import{R as s,M as y,Q as N,az as I,V as R,ay as S}from"./index-Cb4A4-Xi.js";import{g as i,n as k,p as d,b as n,R as l,c as B,F as G,j as F}from"./declarationMapper-r-RREw_K.js";import{F as u}from"./flowGraphBinaryOperationBlock-DaBPP43z.js";import{F as E}from"./flowGraphCachedOperationBlock-D--7yusk.js";import{F as p}from"./flowGraphUnaryOperationBlock-BsZVallq.js";import{F as v}from"./flowGraphTernaryOperationBlock-DqI69tK1.js";import{e as m,j as x,k as _,l as M,i as C,g}from"./KHR_interactivity-DVSiPm30.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class f extends E{constructor(r,o,t,a){super(r,a),this._operation=o,this._className=t}_doOperation(r){return this._operation(r)}getClassName(){return this._className}}class L extends u{constructor(r){super(i(r?.type),i(r?.type),i(r?.type),(o,t)=>this._polymorphicAdd(o,t),"FlowGraphAddBlock",r)}_polymorphicAdd(r,o){const t=m(r),a=m(o);return x(t,a)||_(t,a)||M(t,a)||t==="Quaternion"||a==="Quaternion"?r.add(o):r+o}}s("FlowGraphAddBlock",L);class O extends u{constructor(r){super(i(r?.type),i(r?.type),i(r?.type),(o,t)=>this._polymorphicSubtract(o,t),"FlowGraphSubtractBlock",r)}_polymorphicSubtract(r,o){const t=m(r),a=m(o);return x(t,a)||M(t,a)||_(t,a)||t==="Quaternion"||a==="Quaternion"?r.subtract(o):r-o}}s("FlowGraphSubtractBlock",O);class z extends u{constructor(r){super(i(r?.type),i(r?.type),i(r?.type),(o,t)=>this._polymorphicMultiply(o,t),"FlowGraphMultiplyBlock",r)}_polymorphicMultiply(r,o){const t=m(r),a=m(o);if(x(t,a)||M(t,a))return r.multiply(o);if(t==="Quaternion"||a==="Quaternion"){const c=r.clone();return c.x*=o.x,c.y*=o.y,c.z*=o.z,c.w*=o.w,c}else if(_(t,a))if(this.config?.useMatrixPerComponent){const c=r.m;for(let w=0;w<c.length;w++)c[w]*=o.m[w];return t==="Matrix2D"?new k(c):t==="Matrix3D"?new d(c):y.FromArray(c)}else return r=r,o=o,o.multiply(r);else return r*o}}s("FlowGraphMultiplyBlock",z);class q extends u{constructor(r){super(i(r?.type),i(r?.type),i(r?.type),(o,t)=>this._polymorphicDivide(o,t),"FlowGraphDivideBlock",r)}_polymorphicDivide(r,o){const t=m(r),a=m(o);if(x(t,a)||M(t,a))return r.divide(o);if(t==="Quaternion"||a==="Quaternion"){const c=r.clone();return c.x/=o.x,c.y/=o.y,c.z/=o.z,c.w/=o.w,c}else if(_(t,a))if(this.config?.useMatrixPerComponent){const c=r.m;for(let w=0;w<c.length;w++)c[w]/=o.m[w];return t==="Matrix2D"?new k(c):t==="Matrix3D"?new d(c):y.FromArray(c)}else return r=r,o=o,r.divide(o);else return r/o}}s("FlowGraphDivideBlock",q);class V extends f{constructor(r){super(n,o=>this._random(o),"FlowGraphRandomBlock",r),this.min=this.registerDataInput("min",n,r?.min??0),this.max=this.registerDataInput("max",n,r?.max??1),r?.seed&&(this._seed=r.seed)}_isSeed(r=this._seed){return r!==void 0}_getRandomValue(){if(this._isSeed(this._seed)){const r=Math.sin(this._seed++)*1e4;return r-Math.floor(r)}return Math.random()}_random(r){const o=this.min.getValue(r),t=this.max.getValue(r);return this._getRandomValue()*(t-o)+o}}s("FlowGraphRandomBlock",V);class P extends f{constructor(r){super(n,()=>Math.E,"FlowGraphEBlock",r)}}s("FlowGraphEBlock",P);class Q extends f{constructor(r){super(n,()=>Math.PI,"FlowGraphPIBlock",r)}}s("FlowGraphPIBlock",Q);class $ extends f{constructor(r){super(n,()=>Number.POSITIVE_INFINITY,"FlowGraphInfBlock",r)}}s("FlowGraphInfBlock",$);class j extends f{constructor(r){super(n,()=>Number.NaN,"FlowGraphNaNBlock",r)}}s("FlowGraphNaNBlock",j);function h(e,r){switch(m(e)){case"FlowGraphInteger":return e=e,new G(r(e.value));case"Vector2":return e=e,new S(r(e.x),r(e.y));case"Vector3":return e=e,new R(r(e.x),r(e.y),r(e.z));case"Vector4":return e=e,new I(r(e.x),r(e.y),r(e.z),r(e.w));case"Quaternion":return e=e,new N(r(e.x),r(e.y),r(e.z),r(e.w));case"Matrix":return e=e,y.FromArray(e.m.map(r));case"Matrix2D":return e=e,new k(e.m.map(r));case"Matrix3D":return e=e,new d(e.m.map(r));default:return e=e,r(e)}}class Z extends p{constructor(r){super(n,n,o=>this._polymorphicAbs(o),"FlowGraphAbsBlock",r)}_polymorphicAbs(r){return h(r,Math.abs)}}s("FlowGraphAbsBlock",Z);class X extends p{constructor(r){super(n,n,o=>this._polymorphicSign(o),"FlowGraphSignBlock",r)}_polymorphicSign(r){return h(r,Math.sign)}}s("FlowGraphSignBlock",X);class W extends p{constructor(r){super(n,n,o=>this._polymorphicTrunc(o),"FlowGraphTruncBlock",r)}_polymorphicTrunc(r){return h(r,Math.trunc)}}s("FlowGraphTruncBlock",W);class U extends p{constructor(r){super(n,n,o=>this._polymorphicFloor(o),"FlowGraphFloorBlock",r)}_polymorphicFloor(r){return h(r,Math.floor)}}s("FlowGraphFloorBlock",U);class H extends p{constructor(r){super(l,l,o=>this._polymorphicCeiling(o),"FlowGraphCeilBlock",r)}_polymorphicCeiling(r){return h(r,Math.ceil)}}s("FlowGraphCeilBlock",H);class Y extends p{constructor(r){super(l,l,o=>this._polymorphicRound(o),"FlowGraphRoundBlock",r)}_polymorphicRound(r){return h(r,o=>o<0&&this.config?.roundHalfAwayFromZero?-Math.round(-o):Math.round(o))}}s("FlowGraphRoundBlock",Y);class J extends p{constructor(r){super(l,l,o=>this._polymorphicFraction(o),"FlowGraphFractBlock",r)}_polymorphicFraction(r){return h(r,o=>o-Math.floor(o))}}s("FlowGraphFractBlock",J);class K extends p{constructor(r){super(l,l,o=>this._polymorphicNeg(o),"FlowGraphNegationBlock",r)}_polymorphicNeg(r){return h(r,o=>-o)}}s("FlowGraphNegationBlock",K);function T(e,r,o){switch(m(e)){case"FlowGraphInteger":return e=e,r=r,new G(o(e.value,r.value));case"Vector2":return e=e,r=r,new S(o(e.x,r.x),o(e.y,r.y));case"Vector3":return e=e,r=r,new R(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z));case"Vector4":return e=e,r=r,new I(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z),o(e.w,r.w));case"Quaternion":return e=e,r=r,new N(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z),o(e.w,r.w));case"Matrix":return e=e,y.FromArray(e.m.map((a,c)=>o(a,r.m[c])));case"Matrix2D":return e=e,new k(e.m.map((a,c)=>o(a,r.m[c])));case"Matrix3D":return e=e,new d(e.m.map((a,c)=>o(a,r.m[c])));default:return o(e,r)}}class b extends u{constructor(r){super(l,l,l,(o,t)=>this._polymorphicRemainder(o,t),"FlowGraphModuloBlock",r)}_polymorphicRemainder(r,o){return T(r,o,(t,a)=>t%a)}}s("FlowGraphModuloBlock",b);class rr extends u{constructor(r){super(l,l,l,(o,t)=>this._polymorphicMin(o,t),"FlowGraphMinBlock",r)}_polymorphicMin(r,o){return T(r,o,Math.min)}}s("FlowGraphMinBlock",rr);class or extends u{constructor(r){super(l,l,l,(o,t)=>this._polymorphicMax(o,t),"FlowGraphMaxBlock",r)}_polymorphicMax(r,o){return T(r,o,Math.max)}}s("FlowGraphMaxBlock",or);function er(e,r,o){return Math.min(Math.max(e,Math.min(r,o)),Math.max(r,o))}function D(e,r,o,t){switch(m(e)){case"FlowGraphInteger":return e=e,r=r,o=o,new G(t(e.value,r.value,o.value));case"Vector2":return e=e,r=r,o=o,new S(t(e.x,r.x,o.x),t(e.y,r.y,o.y));case"Vector3":return e=e,r=r,o=o,new R(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z));case"Vector4":return e=e,r=r,o=o,new I(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z),t(e.w,r.w,o.w));case"Quaternion":return e=e,r=r,o=o,new N(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z),t(e.w,r.w,o.w));case"Matrix":return y.FromArray(e.m.map((c,w)=>t(c,r.m[w],o.m[w])));case"Matrix2D":return new k(e.m.map((c,w)=>t(c,r.m[w],o.m[w])));case"Matrix3D":return new d(e.m.map((c,w)=>t(c,r.m[w],o.m[w])));default:return t(e,r,o)}}class tr extends v{constructor(r){super(l,l,l,l,(o,t,a)=>this._polymorphicClamp(o,t,a),"FlowGraphClampBlock",r)}_polymorphicClamp(r,o,t){return D(r,o,t,er)}}s("FlowGraphClampBlock",tr);function lr(e){return Math.min(Math.max(e,0),1)}class sr extends p{constructor(r){super(l,l,o=>this._polymorphicSaturate(o),"FlowGraphSaturateBlock",r)}_polymorphicSaturate(r){return h(r,lr)}}s("FlowGraphSaturateBlock",sr);function ar(e,r,o){return(1-o)*e+o*r}class nr extends v{constructor(r){super(l,l,l,l,(o,t,a)=>this._polymorphicInterpolate(o,t,a),"FlowGraphMathInterpolationBlock",r)}_polymorphicInterpolate(r,o,t){return D(r,o,t,ar)}}s("FlowGraphMathInterpolationBlock",nr);class cr extends u{constructor(r){super(l,l,B,(o,t)=>this._polymorphicEq(o,t),"FlowGraphEqualityBlock",r)}_polymorphicEq(r,o){const t=m(r),a=m(o);return x(t,a)||_(t,a)||M(t,a)?r.equals(o):r===o}}s("FlowGraphEqualityBlock",cr);function A(e,r,o){if(C(e)&&C(r))return o(g(e),g(r));throw new Error(`Cannot compare ${e} and ${r}`)}class pr extends u{constructor(r){super(l,l,B,(o,t)=>this._polymorphicLessThan(o,t),"FlowGraphLessThanBlock",r)}_polymorphicLessThan(r,o){return A(r,o,(t,a)=>t<a)}}s("FlowGraphLessThanBlock",pr);class hr extends u{constructor(r){super(l,l,B,(o,t)=>this._polymorphicLessThanOrEqual(o,t),"FlowGraphLessThanOrEqualBlock",r)}_polymorphicLessThanOrEqual(r,o){return A(r,o,(t,a)=>t<=a)}}s("FlowGraphLessThanOrEqualBlock",hr);class ir extends u{constructor(r){super(l,l,B,(o,t)=>this._polymorphicGreaterThan(o,t),"FlowGraphGreaterThanBlock",r)}_polymorphicGreaterThan(r,o){return A(r,o,(t,a)=>t>a)}}s("FlowGraphGreaterThanBlock",ir);class ur extends u{constructor(r){super(l,l,B,(o,t)=>this._polymorphicGreaterThanOrEqual(o,t),"FlowGraphGreaterThanOrEqualBlock",r)}_polymorphicGreaterThanOrEqual(r,o){return A(r,o,(t,a)=>t>=a)}}s("FlowGraphGreaterThanOrEqualBlock",ur);class wr extends p{constructor(r){super(l,B,o=>this._polymorphicIsNan(o),"FlowGraphIsNaNBlock",r)}_polymorphicIsNan(r){if(C(r))return isNaN(g(r));throw new Error(`Cannot get NaN of ${r}`)}}s("FlowGraphIsNaNBlock",wr);class mr extends p{constructor(r){super(l,B,o=>this._polymorphicIsInf(o),"FlowGraphIsInfBlock",r)}_polymorphicIsInf(r){if(C(r))return!isFinite(g(r));throw new Error(`Cannot get isInf of ${r}`)}}s("FlowGraphIsInfBlock",mr);class Fr extends p{constructor(r){super(l,l,o=>this._polymorphicDegToRad(o),"FlowGraphDegToRadBlock",r)}_degToRad(r){return r*Math.PI/180}_polymorphicDegToRad(r){return h(r,this._degToRad)}}s("FlowGraphDegToRadBlock",Fr);class Gr extends p{constructor(r){super(l,l,o=>this._polymorphicRadToDeg(o),"FlowGraphRadToDegBlock",r)}_radToDeg(r){return r*180/Math.PI}_polymorphicRadToDeg(r){return h(r,this._radToDeg)}}s("FlowGraphRadToDegBlock",Gr);class ro extends p{constructor(r){super(n,n,o=>this._polymorphicSin(o),"FlowGraphSinBlock",r)}_polymorphicSin(r){return h(r,Math.sin)}}class oo extends p{constructor(r){super(n,n,o=>this._polymorphicCos(o),"FlowGraphCosBlock",r)}_polymorphicCos(r){return h(r,Math.cos)}}class eo extends p{constructor(r){super(n,n,o=>this._polymorphicTan(o),"FlowGraphTanBlock",r)}_polymorphicTan(r){return h(r,Math.tan)}}class Br extends p{constructor(r){super(n,n,o=>this._polymorphicAsin(o),"FlowGraphASinBlock",r)}_polymorphicAsin(r){return h(r,Math.asin)}}s("FlowGraphASinBlock",Br);class yr extends p{constructor(r){super(n,n,o=>this._polymorphicAcos(o),"FlowGraphACosBlock",r)}_polymorphicAcos(r){return h(r,Math.acos)}}s("FlowGraphACosBlock",yr);class kr extends p{constructor(r){super(n,n,o=>this._polymorphicAtan(o),"FlowGraphATanBlock",r)}_polymorphicAtan(r){return h(r,Math.atan)}}s("FlowGraphATanBlock",kr);class dr extends u{constructor(r){super(l,l,l,(o,t)=>this._polymorphicAtan2(o,t),"FlowGraphATan2Block",r)}_polymorphicAtan2(r,o){return T(r,o,Math.atan2)}}s("FlowGraphATan2Block",dr);class xr extends p{constructor(r){super(l,l,o=>this._polymorphicSinh(o),"FlowGraphSinhBlock",r)}_polymorphicSinh(r){return h(r,Math.sinh)}}s("FlowGraphSinhBlock",xr);class _r extends p{constructor(r){super(l,l,o=>this._polymorphicCosh(o),"FlowGraphCoshBlock",r)}_polymorphicCosh(r){return h(r,Math.cosh)}}s("FlowGraphCoshBlock",_r);class Mr extends p{constructor(r){super(l,l,o=>this._polymorphicTanh(o),"FlowGraphTanhBlock",r)}_polymorphicTanh(r){return h(r,Math.tanh)}}s("FlowGraphTanhBlock",Mr);class fr extends p{constructor(r){super(l,n,o=>this._polymorphicAsinh(o),"FlowGraphASinhBlock",r)}_polymorphicAsinh(r){return h(r,Math.asinh)}}s("FlowGraphASinhBlock",fr);class Tr extends p{constructor(r){super(l,n,o=>this._polymorphicAcosh(o),"FlowGraphACoshBlock",r)}_polymorphicAcosh(r){return h(r,Math.acosh)}}s("FlowGraphACoshBlock",Tr);class Cr extends p{constructor(r){super(l,n,o=>this._polymorphicAtanh(o),"FlowGraphATanhBlock",r)}_polymorphicAtanh(r){return h(r,Math.atanh)}}s("FlowGraphATanhBlock",Cr);class gr extends p{constructor(r){super(l,n,o=>this._polymorphicExp(o),"FlowGraphExponentialBlock",r)}_polymorphicExp(r){return h(r,Math.exp)}}s("FlowGraphExponentialBlock",gr);class Ar extends p{constructor(r){super(l,n,o=>this._polymorphicLog(o),"FlowGraphLogBlock",r)}_polymorphicLog(r){return h(r,Math.log)}}s("FlowGraphLogBlock",Ar);class Nr extends p{constructor(r){super(l,n,o=>this._polymorphicLog2(o),"FlowGraphLog2Block",r)}_polymorphicLog2(r){return h(r,Math.log2)}}s("FlowGraphLog2Block",Nr);class Ir extends p{constructor(r){super(l,n,o=>this._polymorphicLog10(o),"FlowGraphLog10Block",r)}_polymorphicLog10(r){return h(r,Math.log10)}}s("FlowGraphLog10Block",Ir);class Rr extends p{constructor(r){super(l,n,o=>this._polymorphicSqrt(o),"FlowGraphSquareRootBlock",r)}_polymorphicSqrt(r){return h(r,Math.sqrt)}}s("FlowGraphSquareRootBlock",Rr);class Sr extends p{constructor(r){super(l,n,o=>this._polymorphicCubeRoot(o),"FlowGraphCubeRootBlock",r)}_polymorphicCubeRoot(r){return h(r,Math.cbrt)}}s("FlowGraphCubeRootBlock",Sr);class vr extends u{constructor(r){super(l,n,n,(o,t)=>this._polymorphicPow(o,t),"FlowGraphPowerBlock",r)}_polymorphicPow(r,o){return T(r,o,Math.pow)}}s("FlowGraphPowerBlock",vr);class Dr extends p{constructor(r){super(i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),o=>typeof o=="boolean"?!o:typeof o=="number"?~o:new G(~o.value),"FlowGraphBitwiseNotBlock",r)}}s("FlowGraphBitwiseNotBlock",Dr);class Er extends u{constructor(r){super(i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o&&t;if(typeof o=="number"&&typeof t=="number")return o&t;if(typeof o=="object"&&typeof t=="object")return new G(o.value&t.value);throw new Error(`Cannot perform bitwise AND on ${o} and ${t}`)},"FlowGraphBitwiseAndBlock",r)}}s("FlowGraphBitwiseAndBlock",Er);class Lr extends u{constructor(r){super(i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o||t;if(typeof o=="number"&&typeof t=="number")return o|t;if(typeof o=="object"&&typeof t=="object")return new G(o.value|t.value);throw new Error(`Cannot perform bitwise OR on ${o} and ${t}`)},"FlowGraphBitwiseOrBlock",r)}}s("FlowGraphBitwiseOrBlock",Lr);class Or extends u{constructor(r){super(i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),i(r?.valueType||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o!==t;if(typeof o=="number"&&typeof t=="number")return o^t;if(typeof o=="object"&&typeof t=="object")return new G(o.value^t.value);throw new Error(`Cannot perform bitwise XOR on ${o} and ${t}`)},"FlowGraphBitwiseXorBlock",r)}}s("FlowGraphBitwiseXorBlock",Or);class zr extends u{constructor(r){super(F,F,F,(o,t)=>new G(o.value<<t.value),"FlowGraphBitwiseLeftShiftBlock",r)}}s("FlowGraphBitwiseLeftShiftBlock",zr);class qr extends u{constructor(r){super(F,F,F,(o,t)=>new G(o.value>>t.value),"FlowGraphBitwiseRightShiftBlock",r)}}s("FlowGraphBitwiseRightShiftBlock",qr);class Vr extends p{constructor(r){super(F,F,o=>new G(Math.clz32(o.value)),"FlowGraphLeadingZerosBlock",r)}}s("FlowGraphLeadingZerosBlock",Vr);class Pr extends p{constructor(r){super(F,F,o=>new G(o.value?31-Math.clz32(o.value&-o.value):32),"FlowGraphTrailingZerosBlock",r)}}s("FlowGraphTrailingZerosBlock",Pr);function Qr(e){let r=0;for(;e;)r+=e&1,e>>=1;return r}class $r extends p{constructor(r){super(F,F,o=>new G(Qr(o.value)),"FlowGraphOneBitsCounterBlock",r)}}s("FlowGraphOneBitsCounterBlock",$r);export{Z as FlowGraphAbsBlock,yr as FlowGraphAcosBlock,Tr as FlowGraphAcoshBlock,L as FlowGraphAddBlock,Br as FlowGraphAsinBlock,fr as FlowGraphAsinhBlock,dr as FlowGraphAtan2Block,kr as FlowGraphAtanBlock,Cr as FlowGraphAtanhBlock,Er as FlowGraphBitwiseAndBlock,zr as FlowGraphBitwiseLeftShiftBlock,Dr as FlowGraphBitwiseNotBlock,Lr as FlowGraphBitwiseOrBlock,qr as FlowGraphBitwiseRightShiftBlock,Or as FlowGraphBitwiseXorBlock,H as FlowGraphCeilBlock,tr as FlowGraphClampBlock,oo as FlowGraphCosBlock,_r as FlowGraphCoshBlock,Sr as FlowGraphCubeRootBlock,Fr as FlowGraphDegToRadBlock,q as FlowGraphDivideBlock,P as FlowGraphEBlock,cr as FlowGraphEqualityBlock,gr as FlowGraphExpBlock,U as FlowGraphFloorBlock,J as FlowGraphFractionBlock,ir as FlowGraphGreaterThanBlock,ur as FlowGraphGreaterThanOrEqualBlock,$ as FlowGraphInfBlock,mr as FlowGraphIsInfinityBlock,wr as FlowGraphIsNanBlock,Vr as FlowGraphLeadingZerosBlock,pr as FlowGraphLessThanBlock,hr as FlowGraphLessThanOrEqualBlock,Ir as FlowGraphLog10Block,Nr as FlowGraphLog2Block,Ar as FlowGraphLogBlock,nr as FlowGraphMathInterpolationBlock,or as FlowGraphMaxBlock,rr as FlowGraphMinBlock,b as FlowGraphModuloBlock,z as FlowGraphMultiplyBlock,j as FlowGraphNaNBlock,K as FlowGraphNegationBlock,$r as FlowGraphOneBitsCounterBlock,Q as FlowGraphPiBlock,vr as FlowGraphPowerBlock,Gr as FlowGraphRadToDegBlock,V as FlowGraphRandomBlock,Y as FlowGraphRoundBlock,sr as FlowGraphSaturateBlock,X as FlowGraphSignBlock,ro as FlowGraphSinBlock,xr as FlowGraphSinhBlock,Rr as FlowGraphSquareRootBlock,O as FlowGraphSubtractBlock,eo as FlowGraphTanBlock,Mr as FlowGraphTanhBlock,Pr as FlowGraphTrailingZerosBlock,W as FlowGraphTruncBlock};
//# sourceMappingURL=flowGraphMathBlocks-BzIhuErF.js.map
