import{F as r}from"./KHR_interactivity-DVSiPm30.js";import{R as i}from"./declarationMapper-r-RREw_K.js";import{R as l}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class t extends r{constructor(a){super(a),this.config=a,this.value=this.registerDataOutput("value",i,a.initialValue)}_updateOutputs(a){const e=this.config.variable;a.hasVariable(e)&&this.value.setValue(a.getVariable(e),a)}serialize(a){super.serialize(a),a.config.variable=this.config.variable}getClassName(){return"FlowGraphGetVariableBlock"}}l("FlowGraphGetVariableBlock",t);export{t as FlowGraphGetVariableBlock};
//# sourceMappingURL=flowGraphGetVariableBlock-BAu4mxNU.js.map
