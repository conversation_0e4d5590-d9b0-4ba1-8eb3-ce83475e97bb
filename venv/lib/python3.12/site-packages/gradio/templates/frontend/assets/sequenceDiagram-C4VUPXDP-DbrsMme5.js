import{g as Pt,a as Vt,d as fe,b as xe,c as Te,e as ye}from"./chunk-PLTTB2RT-4f9nCmH_.js";import{I as Ee}from"./chunk-66XRIAFR-Cs7RKspm.js";import{_ as u,s as Ut,c as be,n as me,g as we,b as ve,o as Ie,d as $,t as Le,l as G,i as It,x as _e,e as v,M as ot,N as Et,u as Y,a as Pe,j as Ae,k as Jt,O as Zt,F as Dt,P as Qt,Z as ke}from"./mermaid.core-DGK6UhOk.js";import{s as Lt}from"./select-BigU4G0v.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";var Ct=function(){var t=u(function(ht,w,L,P){for(L=L||{},P=ht.length;P--;L[ht[P]]=w);return L},"o"),e=[1,2],c=[1,3],s=[1,4],r=[2,4],i=[1,9],o=[1,11],h=[1,13],d=[1,14],a=[1,16],f=[1,17],E=[1,18],g=[1,24],T=[1,25],m=[1,26],I=[1,27],A=[1,28],O=[1,29],S=[1,30],B=[1,31],D=[1,32],W=[1,33],q=[1,34],X=[1,35],tt=[1,36],z=[1,37],H=[1,38],F=[1,39],M=[1,41],J=[1,42],K=[1,43],Z=[1,44],et=[1,45],N=[1,46],y=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],_=[4,5,16,50,52,53],Q=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],at=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],k=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],Wt=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],it=[68,69,70],ct=[1,122],kt={trace:u(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:u(function(w,L,P,b,R,l,yt){var p=l.length-1;switch(R){case 3:return b.apply(l[p]),l[p];case 4:case 9:this.$=[];break;case 5:case 10:l[p-1].push(l[p]),this.$=l[p-1];break;case 6:case 7:case 11:case 12:this.$=l[p];break;case 8:case 13:this.$=[];break;case 15:l[p].type="createParticipant",this.$=l[p];break;case 16:l[p-1].unshift({type:"boxStart",boxData:b.parseBoxData(l[p-2])}),l[p-1].push({type:"boxEnd",boxText:l[p-2]}),this.$=l[p-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-2]),sequenceIndexStep:Number(l[p-1]),sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:b.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:b.LINETYPE.ACTIVE_START,actor:l[p-1].actor};break;case 23:this.$={type:"activeEnd",signalType:b.LINETYPE.ACTIVE_END,actor:l[p-1].actor};break;case 29:b.setDiagramTitle(l[p].substring(6)),this.$=l[p].substring(6);break;case 30:b.setDiagramTitle(l[p].substring(7)),this.$=l[p].substring(7);break;case 31:this.$=l[p].trim(),b.setAccTitle(this.$);break;case 32:case 33:this.$=l[p].trim(),b.setAccDescription(this.$);break;case 34:l[p-1].unshift({type:"loopStart",loopText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.LOOP_START}),l[p-1].push({type:"loopEnd",loopText:l[p-2],signalType:b.LINETYPE.LOOP_END}),this.$=l[p-1];break;case 35:l[p-1].unshift({type:"rectStart",color:b.parseMessage(l[p-2]),signalType:b.LINETYPE.RECT_START}),l[p-1].push({type:"rectEnd",color:b.parseMessage(l[p-2]),signalType:b.LINETYPE.RECT_END}),this.$=l[p-1];break;case 36:l[p-1].unshift({type:"optStart",optText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.OPT_START}),l[p-1].push({type:"optEnd",optText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.OPT_END}),this.$=l[p-1];break;case 37:l[p-1].unshift({type:"altStart",altText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.ALT_START}),l[p-1].push({type:"altEnd",signalType:b.LINETYPE.ALT_END}),this.$=l[p-1];break;case 38:l[p-1].unshift({type:"parStart",parText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.PAR_START}),l[p-1].push({type:"parEnd",signalType:b.LINETYPE.PAR_END}),this.$=l[p-1];break;case 39:l[p-1].unshift({type:"parStart",parText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.PAR_OVER_START}),l[p-1].push({type:"parEnd",signalType:b.LINETYPE.PAR_END}),this.$=l[p-1];break;case 40:l[p-1].unshift({type:"criticalStart",criticalText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.CRITICAL_START}),l[p-1].push({type:"criticalEnd",signalType:b.LINETYPE.CRITICAL_END}),this.$=l[p-1];break;case 41:l[p-1].unshift({type:"breakStart",breakText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.BREAK_START}),l[p-1].push({type:"breakEnd",optText:b.parseMessage(l[p-2]),signalType:b.LINETYPE.BREAK_END}),this.$=l[p-1];break;case 43:this.$=l[p-3].concat([{type:"option",optionText:b.parseMessage(l[p-1]),signalType:b.LINETYPE.CRITICAL_OPTION},l[p]]);break;case 45:this.$=l[p-3].concat([{type:"and",parText:b.parseMessage(l[p-1]),signalType:b.LINETYPE.PAR_AND},l[p]]);break;case 47:this.$=l[p-3].concat([{type:"else",altText:b.parseMessage(l[p-1]),signalType:b.LINETYPE.ALT_ELSE},l[p]]);break;case 48:l[p-3].draw="participant",l[p-3].type="addParticipant",l[p-3].description=b.parseMessage(l[p-1]),this.$=l[p-3];break;case 49:l[p-1].draw="participant",l[p-1].type="addParticipant",this.$=l[p-1];break;case 50:l[p-3].draw="actor",l[p-3].type="addParticipant",l[p-3].description=b.parseMessage(l[p-1]),this.$=l[p-3];break;case 51:l[p-1].draw="actor",l[p-1].type="addParticipant",this.$=l[p-1];break;case 52:l[p-1].type="destroyParticipant",this.$=l[p-1];break;case 53:this.$=[l[p-1],{type:"addNote",placement:l[p-2],actor:l[p-1].actor,text:l[p]}];break;case 54:l[p-2]=[].concat(l[p-1],l[p-1]).slice(0,2),l[p-2][0]=l[p-2][0].actor,l[p-2][1]=l[p-2][1].actor,this.$=[l[p-1],{type:"addNote",placement:b.PLACEMENT.OVER,actor:l[p-2].slice(0,2),text:l[p]}];break;case 55:this.$=[l[p-1],{type:"addLinks",actor:l[p-1].actor,text:l[p]}];break;case 56:this.$=[l[p-1],{type:"addALink",actor:l[p-1].actor,text:l[p]}];break;case 57:this.$=[l[p-1],{type:"addProperties",actor:l[p-1].actor,text:l[p]}];break;case 58:this.$=[l[p-1],{type:"addDetails",actor:l[p-1].actor,text:l[p]}];break;case 61:this.$=[l[p-2],l[p]];break;case 62:this.$=l[p];break;case 63:this.$=b.PLACEMENT.LEFTOF;break;case 64:this.$=b.PLACEMENT.RIGHTOF;break;case 65:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p],activate:!0},{type:"activeStart",signalType:b.LINETYPE.ACTIVE_START,actor:l[p-1].actor}];break;case 66:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p]},{type:"activeEnd",signalType:b.LINETYPE.ACTIVE_END,actor:l[p-4].actor}];break;case 67:this.$=[l[p-3],l[p-1],{type:"addMessage",from:l[p-3].actor,to:l[p-1].actor,signalType:l[p-2],msg:l[p]}];break;case 68:this.$={type:"addParticipant",actor:l[p]};break;case 69:this.$=b.LINETYPE.SOLID_OPEN;break;case 70:this.$=b.LINETYPE.DOTTED_OPEN;break;case 71:this.$=b.LINETYPE.SOLID;break;case 72:this.$=b.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=b.LINETYPE.DOTTED;break;case 74:this.$=b.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=b.LINETYPE.SOLID_CROSS;break;case 76:this.$=b.LINETYPE.DOTTED_CROSS;break;case 77:this.$=b.LINETYPE.SOLID_POINT;break;case 78:this.$=b.LINETYPE.DOTTED_POINT;break;case 79:this.$=b.parseMessage(l[p].trim().substring(1));break}},"anonymous"),table:[{3:1,4:e,5:c,6:s},{1:[3]},{3:5,4:e,5:c,6:s},{3:6,4:e,5:c,6:s},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:o,8:8,9:10,12:12,13:h,14:d,17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},t(y,[2,5]),{9:47,12:12,13:h,14:d,17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},t(y,[2,7]),t(y,[2,8]),t(y,[2,14]),{12:48,50:z,52:H,53:F},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:N},{22:55,70:N},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(y,[2,29]),t(y,[2,30]),{32:[1,61]},{34:[1,62]},t(y,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:N},{22:72,70:N},{22:73,70:N},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:N},{22:90,70:N},{22:91,70:N},{22:92,70:N},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(y,[2,6]),t(y,[2,15]),t(_,[2,9],{10:93}),t(y,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(y,[2,21]),{5:[1,97]},{5:[1,98]},t(y,[2,24]),t(y,[2,25]),t(y,[2,26]),t(y,[2,27]),t(y,[2,28]),t(y,[2,31]),t(y,[2,32]),t(Q,r,{7:99}),t(Q,r,{7:100}),t(Q,r,{7:101}),t(at,r,{40:102,7:103}),t(k,r,{42:104,7:105}),t(k,r,{7:105,42:106}),t(Wt,r,{45:107,7:108}),t(Q,r,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:N},t(it,[2,69]),t(it,[2,70]),t(it,[2,71]),t(it,[2,72]),t(it,[2,73]),t(it,[2,74]),t(it,[2,75]),t(it,[2,76]),t(it,[2,77]),t(it,[2,78]),{22:118,70:N},{22:120,58:119,70:N},{70:[2,63]},{70:[2,64]},{56:121,81:ct},{56:123,81:ct},{56:124,81:ct},{56:125,81:ct},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:z,52:H,53:F},{5:[1,131]},t(y,[2,19]),t(y,[2,20]),t(y,[2,22]),t(y,[2,23]),{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[1,132],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[1,133],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[1,134],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{16:[1,135]},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[2,46],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,49:[1,136],50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{16:[1,137]},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[2,44],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,48:[1,138],50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{16:[1,139]},{16:[1,140]},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[2,42],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,47:[1,141],50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{4:i,5:o,8:8,9:10,12:12,13:h,14:d,16:[1,142],17:15,18:a,21:f,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:I,35:A,36:O,37:S,38:B,39:D,41:W,43:q,44:X,46:tt,50:z,52:H,53:F,54:M,59:J,60:K,61:Z,62:et,70:N},{15:[1,143]},t(y,[2,49]),{15:[1,144]},t(y,[2,51]),t(y,[2,52]),{22:145,70:N},{22:146,70:N},{56:147,81:ct},{56:148,81:ct},{56:149,81:ct},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(y,[2,16]),t(_,[2,10]),{12:151,50:z,52:H,53:F},t(_,[2,12]),t(_,[2,13]),t(y,[2,18]),t(y,[2,34]),t(y,[2,35]),t(y,[2,36]),t(y,[2,37]),{15:[1,152]},t(y,[2,38]),{15:[1,153]},t(y,[2,39]),t(y,[2,40]),{15:[1,154]},t(y,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:ct},{56:158,81:ct},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:N},t(_,[2,11]),t(at,r,{7:103,40:160}),t(k,r,{7:105,42:161}),t(Wt,r,{7:108,45:162}),t(y,[2,48]),t(y,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:u(function(w,L){if(L.recoverable)this.trace(w);else{var P=new Error(w);throw P.hash=L,P}},"parseError"),parse:u(function(w){var L=this,P=[0],b=[],R=[null],l=[],yt=this.table,p="",mt=0,qt=0,de=2,zt=1,pe=l.slice.call(arguments,1),V=Object.create(this.lexer),dt={yy:{}};for(var Nt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Nt)&&(dt.yy[Nt]=this.yy[Nt]);V.setInput(w,dt.yy),dt.yy.lexer=V,dt.yy.parser=this,typeof V.yylloc>"u"&&(V.yylloc={});var St=V.yylloc;l.push(St);var ue=V.options&&V.options.ranges;typeof dt.yy.parseError=="function"?this.parseError=dt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ge(j){P.length=P.length-2*j,R.length=R.length-j,l.length=l.length-j}u(ge,"popStack");function Ht(){var j;return j=b.pop()||V.lex()||zt,typeof j!="number"&&(j instanceof Array&&(b=j,j=b.pop()),j=L.symbols_[j]||j),j}u(Ht,"lex");for(var U,pt,st,Mt,ft={},wt,lt,Kt,vt;;){if(pt=P[P.length-1],this.defaultActions[pt]?st=this.defaultActions[pt]:((U===null||typeof U>"u")&&(U=Ht()),st=yt[pt]&&yt[pt][U]),typeof st>"u"||!st.length||!st[0]){var Rt="";vt=[];for(wt in yt[pt])this.terminals_[wt]&&wt>de&&vt.push("'"+this.terminals_[wt]+"'");V.showPosition?Rt="Parse error on line "+(mt+1)+`:
`+V.showPosition()+`
Expecting `+vt.join(", ")+", got '"+(this.terminals_[U]||U)+"'":Rt="Parse error on line "+(mt+1)+": Unexpected "+(U==zt?"end of input":"'"+(this.terminals_[U]||U)+"'"),this.parseError(Rt,{text:V.match,token:this.terminals_[U]||U,line:V.yylineno,loc:St,expected:vt})}if(st[0]instanceof Array&&st.length>1)throw new Error("Parse Error: multiple actions possible at state: "+pt+", token: "+U);switch(st[0]){case 1:P.push(U),R.push(V.yytext),l.push(V.yylloc),P.push(st[1]),U=null,qt=V.yyleng,p=V.yytext,mt=V.yylineno,St=V.yylloc;break;case 2:if(lt=this.productions_[st[1]][1],ft.$=R[R.length-lt],ft._$={first_line:l[l.length-(lt||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(lt||1)].first_column,last_column:l[l.length-1].last_column},ue&&(ft._$.range=[l[l.length-(lt||1)].range[0],l[l.length-1].range[1]]),Mt=this.performAction.apply(ft,[p,qt,mt,dt.yy,st[1],R,l].concat(pe)),typeof Mt<"u")return Mt;lt&&(P=P.slice(0,-1*lt*2),R=R.slice(0,-1*lt),l=l.slice(0,-1*lt)),P.push(this.productions_[st[1]][0]),R.push(ft.$),l.push(ft._$),Kt=yt[P[P.length-2]][P[P.length-1]],P.push(Kt);break;case 3:return!0}}return!0},"parse")},he=function(){var ht={EOF:1,parseError:u(function(L,P){if(this.yy.parser)this.yy.parser.parseError(L,P);else throw new Error(L)},"parseError"),setInput:u(function(w,L){return this.yy=L||this.yy||{},this._input=w,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:u(function(){var w=this._input[0];this.yytext+=w,this.yyleng++,this.offset++,this.match+=w,this.matched+=w;var L=w.match(/(?:\r\n?|\n).*/g);return L?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),w},"input"),unput:u(function(w){var L=w.length,P=w.split(/(?:\r\n?|\n)/g);this._input=w+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-L),this.offset-=L;var b=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),P.length-1&&(this.yylineno-=P.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:P?(P.length===b.length?this.yylloc.first_column:0)+b[b.length-P.length].length-P[0].length:this.yylloc.first_column-L},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-L]),this.yyleng=this.yytext.length,this},"unput"),more:u(function(){return this._more=!0,this},"more"),reject:u(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:u(function(w){this.unput(this.match.slice(w))},"less"),pastInput:u(function(){var w=this.matched.substr(0,this.matched.length-this.match.length);return(w.length>20?"...":"")+w.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:u(function(){var w=this.match;return w.length<20&&(w+=this._input.substr(0,20-w.length)),(w.substr(0,20)+(w.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:u(function(){var w=this.pastInput(),L=new Array(w.length+1).join("-");return w+this.upcomingInput()+`
`+L+"^"},"showPosition"),test_match:u(function(w,L){var P,b,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),b=w[0].match(/(?:\r\n?|\n).*/g),b&&(this.yylineno+=b.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:b?b[b.length-1].length-b[b.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+w[0].length},this.yytext+=w[0],this.match+=w[0],this.matches=w,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(w[0].length),this.matched+=w[0],P=this.performAction.call(this,this.yy,this,L,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),P)return P;if(this._backtrack){for(var l in R)this[l]=R[l];return!1}return!1},"test_match"),next:u(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var w,L,P,b;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),l=0;l<R.length;l++)if(P=this._input.match(this.rules[R[l]]),P&&(!L||P[0].length>L[0].length)){if(L=P,b=l,this.options.backtrack_lexer){if(w=this.test_match(P,R[l]),w!==!1)return w;if(this._backtrack){L=!1;continue}else return!1}else if(!this.options.flex)break}return L?(w=this.test_match(L,R[b]),w!==!1?w:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:u(function(){var L=this.next();return L||this.lex()},"lex"),begin:u(function(L){this.conditionStack.push(L)},"begin"),popState:u(function(){var L=this.conditionStack.length-1;return L>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:u(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:u(function(L){return L=this.conditionStack.length-1-Math.abs(L||0),L>=0?this.conditionStack[L]:"INITIAL"},"topState"),pushState:u(function(L){this.begin(L)},"pushState"),stateStackSize:u(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:u(function(L,P,b,R){switch(b){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return P.yytext=P.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:return P.yytext=P.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 66:return 5;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}};return ht}();kt.lexer=he;function bt(){this.yy={}}return u(bt,"Parser"),bt.prototype=kt,kt.Parser=bt,new bt}();Ct.parser=Ct;var Ne=Ct,Se={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},Me={FILLED:0,OPEN:1},Re={LEFTOF:0,RIGHTOF:1,OVER:2},De=class{constructor(){this.state=new Ee(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),this.setAccTitle=Ut,this.setAccDescription=be,this.setDiagramTitle=me,this.getAccTitle=we,this.getAccDescription=ve,this.getDiagramTitle=Ie,this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap($().wrap),this.LINETYPE=Se,this.ARROWTYPE=Me,this.PLACEMENT=Re}static{u(this,"SequenceDB")}addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,e,c,s){let r=this.state.records.currentBox;const i=this.state.records.actors.get(t);if(i){if(this.state.records.currentBox&&i.box&&this.state.records.currentBox!==i.box)throw new Error(`A same participant should only be defined in one Box: ${i.name} can't be in '${i.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(r=i.box?i.box:this.state.records.currentBox,i.box=r,i&&e===i.name&&c==null)return}if(c?.text==null&&(c={text:e,type:s}),(s==null||c.text==null)&&(c={text:e,type:s}),this.state.records.actors.set(t,{box:r,name:e,description:c.text,wrap:c.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:s??"participant"}),this.state.records.prevActor){const o=this.state.records.actors.get(this.state.records.prevActor);o&&(o.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let e,c=0;if(!t)return 0;for(e=0;e<this.state.records.messages.length;e++)this.state.records.messages[e].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[e].from===t&&c++,this.state.records.messages[e].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[e].from===t&&c--;return c}addMessage(t,e,c,s){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:c.text,wrap:c.wrap??this.autoWrap(),answer:s})}addSignal(t,e,c,s,r=!1){if(s===this.LINETYPE.ACTIVE_END&&this.activationCount(t??"")<1){const o=new Error("Trying to inactivate an inactive participant ("+t+")");throw o.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},o}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:c?.text??"",wrap:c?.wrap??this.autoWrap(),type:s,activate:r}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some(t=>t.name)}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(t===void 0)return{};t=t.trim();const e=/^:?wrap:/.exec(t)!==null?!0:/^:?nowrap:/.exec(t)!==null?!1:void 0;return{cleanedText:(e===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:e}}autoWrap(){return this.state.records.wrapEnabled!==void 0?this.state.records.wrapEnabled:$().sequence?.wrap??!1}clear(){this.state.reset(),Le()}parseMessage(t){const e=t.trim(),{wrap:c,cleanedText:s}=this.extractWrap(e),r={text:s,wrap:c};return G.debug(`parseMessage: ${JSON.stringify(r)}`),r}parseBoxData(t){const e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let c=e?.[1]?e[1].trim():"transparent",s=e?.[2]?e[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",c)||(c="transparent",s=t.trim());else{const o=new Option().style;o.color=c,o.color!==c&&(c="transparent",s=t.trim())}const{wrap:r,cleanedText:i}=this.extractWrap(s);return{text:i?It(i,$()):void 0,color:c,wrap:r}}addNote(t,e,c){const s={actor:t,placement:e,message:c.text,wrap:c.wrap??this.autoWrap()},r=[].concat(t,t);this.state.records.notes.push(s),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:r[0],to:r[1],message:c.text,wrap:c.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:e})}addLinks(t,e){const c=this.getActor(t);try{let s=It(e.text,$());s=s.replace(/&equals;/g,"="),s=s.replace(/&amp;/g,"&");const r=JSON.parse(s);this.insertLinks(c,r)}catch(s){G.error("error while parsing actor link text",s)}}addALink(t,e){const c=this.getActor(t);try{const s={};let r=It(e.text,$());const i=r.indexOf("@");r=r.replace(/&equals;/g,"="),r=r.replace(/&amp;/g,"&");const o=r.slice(0,i-1).trim(),h=r.slice(i+1).trim();s[o]=h,this.insertLinks(c,s)}catch(s){G.error("error while parsing actor link text",s)}}insertLinks(t,e){if(t.links==null)t.links=e;else for(const c in e)t.links[c]=e[c]}addProperties(t,e){const c=this.getActor(t);try{const s=It(e.text,$()),r=JSON.parse(s);this.insertProperties(c,r)}catch(s){G.error("error while parsing actor properties text",s)}}insertProperties(t,e){if(t.properties==null)t.properties=e;else for(const c in e)t.properties[c]=e[c]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,e){const c=this.getActor(t),s=document.getElementById(e.text);try{const r=s.innerHTML,i=JSON.parse(r);i.properties&&this.insertProperties(c,i.properties),i.links&&this.insertLinks(c,i.links)}catch(r){G.error("error while parsing actor details text",r)}}getActorProperty(t,e){if(t?.properties!==void 0)return t.properties[e]}apply(t){if(Array.isArray(t))t.forEach(e=>{this.apply(e)});else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"rectEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"optEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"altStart":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"altEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":Ut(t.text);break;case"parStart":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"parEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break}}getConfig(){return $().sequence}},Ce=u(t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),Oe=Ce,ut=18*2,jt="actor-top",$t="actor-bottom",Be="actor-box",Gt="actor-man",Yt=u(function(t,e){return fe(t,e)},"drawRect"),Ve=u(function(t,e,c,s,r){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0)return{height:0,width:0};const i=e.links,o=e.actorCnt,h=e.rectData;var d="none";r&&(d="block !important");const a=t.append("g");a.attr("id","actor"+o+"_popup"),a.attr("class","actorPopupMenu"),a.attr("display",d);var f="";h.class!==void 0&&(f=" "+h.class);let E=h.width>c?h.width:c;const g=a.append("rect");if(g.attr("class","actorPopupMenuPanel"+f),g.attr("x",h.x),g.attr("y",h.height),g.attr("fill",h.fill),g.attr("stroke",h.stroke),g.attr("width",E),g.attr("height",h.height),g.attr("rx",h.rx),g.attr("ry",h.ry),i!=null){var T=20;for(let A in i){var m=a.append("a"),I=Jt(i[A]);m.attr("xlink:href",I),m.attr("target","_blank"),es(s)(A,m,h.x+10,h.height+T,E,20,{class:"actor"},s),T+=30}}return g.attr("height",T),{height:h.height+T,width:E}},"drawPopup"),Ye=u(function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),_t=u(async function(t,e,c=null){let s=t.append("foreignObject");const r=await Zt(e.text,Dt()),o=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(r).node().getBoundingClientRect();if(s.attr("height",Math.round(o.height)).attr("width",Math.round(o.width)),e.class==="noteText"){const h=t.node().firstChild;h.setAttribute("height",o.height+2*e.textMargin);const d=h.getBBox();s.attr("x",Math.round(d.x+d.width/2-o.width/2)).attr("y",Math.round(d.y+d.height/2-o.height/2))}else if(c){let{startx:h,stopx:d,starty:a}=c;if(h>d){const f=h;h=d,d=f}s.attr("x",Math.round(h+Math.abs(h-d)/2-o.width/2)),e.class==="loopText"?s.attr("y",Math.round(a)):s.attr("y",Math.round(a-o.height))}return[s]},"drawKatex"),Tt=u(function(t,e){let c=0,s=0;const r=e.text.split(v.lineBreakRegex),[i,o]=Qt(e.fontSize);let h=[],d=0,a=u(()=>e.y,"yfunc");if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0)switch(e.valign){case"top":case"start":a=u(()=>Math.round(e.y+e.textMargin),"yfunc");break;case"middle":case"center":a=u(()=>Math.round(e.y+(c+s+e.textMargin)/2),"yfunc");break;case"bottom":case"end":a=u(()=>Math.round(e.y+(c+s+2*e.textMargin)-e.textMargin),"yfunc");break}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle";break}for(let[f,E]of r.entries()){e.textMargin!==void 0&&e.textMargin===0&&i!==void 0&&(d=f*i);const g=t.append("text");g.attr("x",e.x),g.attr("y",a()),e.anchor!==void 0&&g.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),e.fontFamily!==void 0&&g.style("font-family",e.fontFamily),o!==void 0&&g.style("font-size",o),e.fontWeight!==void 0&&g.style("font-weight",e.fontWeight),e.fill!==void 0&&g.attr("fill",e.fill),e.class!==void 0&&g.attr("class",e.class),e.dy!==void 0?g.attr("dy",e.dy):d!==0&&g.attr("dy",d);const T=E||ke;if(e.tspan){const m=g.append("tspan");m.attr("x",e.x),e.fill!==void 0&&m.attr("fill",e.fill),m.text(T)}else g.text(T);e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0&&(s+=(g._groups||g)[0][0].getBBox().height,c=s),h.push(g)}return h},"drawText"),te=u(function(t,e){function c(r,i,o,h,d){return r+","+i+" "+(r+o)+","+i+" "+(r+o)+","+(i+h-d)+" "+(r+o-d*1.2)+","+(i+h)+" "+r+","+(i+h)}u(c,"genPoints");const s=t.append("polygon");return s.attr("points",c(e.x,e.y,e.width,e.height,7)),s.attr("class","labelBox"),e.y=e.y+e.height/2,Tt(t,e),s},"drawLabel"),nt=-1,ee=u((t,e,c,s)=>{t.select&&c.forEach(r=>{const i=e.get(r),o=t.select("#actor"+i.actorCnt);!s.mirrorActors&&i.stopy?o.attr("y2",i.stopy+i.height/2):s.mirrorActors&&o.attr("y2",i.stopy)})},"fixLifeLineHeights"),Fe=u(function(t,e,c,s){const r=s?e.stopy:e.starty,i=e.x+e.width/2,o=r+e.height,h=t.append("g").lower();var d=h;s||(nt++,Object.keys(e.links||{}).length&&!c.forceMenus&&d.attr("onclick",Ye(`actor${nt}_popup`)).attr("cursor","pointer"),d.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),d=h.append("g"),e.actorCnt=nt,e.links!=null&&d.attr("id","root-"+nt));const a=Pt();var f="actor";e.properties?.class?f=e.properties.class:a.fill="#eaeaea",s?f+=` ${$t}`:f+=` ${jt}`,a.x=e.x,a.y=r,a.width=e.width,a.height=e.height,a.class=f,a.rx=3,a.ry=3,a.name=e.name;const E=Yt(d,a);if(e.rectData=a,e.properties?.icon){const T=e.properties.icon.trim();T.charAt(0)==="@"?Te(d,a.x+a.width-20,a.y+10,T.substr(1)):ye(d,a.x+a.width-20,a.y+10,T)}Ft(c,ot(e.description))(e.description,d,a.x,a.y,a.width,a.height,{class:`actor ${Be}`},c);let g=e.height;if(E.node){const T=E.node().getBBox();e.height=T.height,g=T.height}return g},"drawActorTypeParticipant"),We=u(function(t,e,c,s){const r=s?e.stopy:e.starty,i=e.x+e.width/2,o=r+80,h=t.append("g").lower();s||(nt++,h.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=nt);const d=t.append("g");let a=Gt;s?a+=` ${$t}`:a+=` ${jt}`,d.attr("class",a),d.attr("name",e.name);const f=Pt();f.x=e.x,f.y=r,f.fill="#eaeaea",f.width=e.width,f.height=e.height,f.class="actor",f.rx=3,f.ry=3,d.append("line").attr("id","actor-man-torso"+nt).attr("x1",i).attr("y1",r+25).attr("x2",i).attr("y2",r+45),d.append("line").attr("id","actor-man-arms"+nt).attr("x1",i-ut/2).attr("y1",r+33).attr("x2",i+ut/2).attr("y2",r+33),d.append("line").attr("x1",i-ut/2).attr("y1",r+60).attr("x2",i).attr("y2",r+45),d.append("line").attr("x1",i).attr("y1",r+45).attr("x2",i+ut/2-2).attr("y2",r+60);const E=d.append("circle");E.attr("cx",e.x+e.width/2),E.attr("cy",r+10),E.attr("r",15),E.attr("width",e.width),E.attr("height",e.height);const g=d.node().getBBox();return e.height=g.height,Ft(c,ot(e.description))(e.description,d,f.x,f.y+35,f.width,f.height,{class:`actor ${Gt}`},c),e.height},"drawActorTypeActor"),qe=u(async function(t,e,c,s){switch(e.type){case"actor":return await We(t,e,c,s);case"participant":return await Fe(t,e,c,s)}},"drawActor"),ze=u(function(t,e,c){const r=t.append("g");se(r,e),e.name&&Ft(c)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},c),r.lower()},"drawBox"),He=u(function(t){return t.append("g")},"anchorElement"),Ke=u(function(t,e,c,s,r){const i=Pt(),o=e.anchored;i.x=e.startx,i.y=e.starty,i.class="activation"+r%3,i.width=e.stopx-e.startx,i.height=c-e.starty,Yt(o,i)},"drawActivation"),Ue=u(async function(t,e,c,s){const{boxMargin:r,boxTextMargin:i,labelBoxHeight:o,labelBoxWidth:h,messageFontFamily:d,messageFontSize:a,messageFontWeight:f}=s,E=t.append("g"),g=u(function(I,A,O,S){return E.append("line").attr("x1",I).attr("y1",A).attr("x2",O).attr("y2",S).attr("class","loopLine")},"drawLoopLine");g(e.startx,e.starty,e.stopx,e.starty),g(e.stopx,e.starty,e.stopx,e.stopy),g(e.startx,e.stopy,e.stopx,e.stopy),g(e.startx,e.starty,e.startx,e.stopy),e.sections!==void 0&&e.sections.forEach(function(I){g(e.startx,I.y,e.stopx,I.y).style("stroke-dasharray","3, 3")});let T=Vt();T.text=c,T.x=e.startx,T.y=e.starty,T.fontFamily=d,T.fontSize=a,T.fontWeight=f,T.anchor="middle",T.valign="middle",T.tspan=!1,T.width=h||50,T.height=o||20,T.textMargin=i,T.class="labelText",te(E,T),T=ae(),T.text=e.title,T.x=e.startx+h/2+(e.stopx-e.startx)/2,T.y=e.starty+r+i,T.anchor="middle",T.valign="middle",T.textMargin=i,T.class="loopText",T.fontFamily=d,T.fontSize=a,T.fontWeight=f,T.wrap=!0;let m=ot(T.text)?await _t(E,T,e):Tt(E,T);if(e.sectionTitles!==void 0){for(const[I,A]of Object.entries(e.sectionTitles))if(A.message){T.text=A.message,T.x=e.startx+(e.stopx-e.startx)/2,T.y=e.sections[I].y+r+i,T.class="loopText",T.anchor="middle",T.valign="middle",T.tspan=!1,T.fontFamily=d,T.fontSize=a,T.fontWeight=f,T.wrap=e.wrap,ot(T.text)?(e.starty=e.sections[I].y,await _t(E,T,e)):Tt(E,T);let O=Math.round(m.map(S=>(S._groups||S)[0][0].getBBox().height).reduce((S,B)=>S+B));e.sections[I].height+=O-(r+i)}}return e.height=Math.round(e.stopy-e.starty),E},"drawLoop"),se=u(function(t,e){xe(t,e)},"drawBackgroundRect"),Ge=u(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),Xe=u(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),Je=u(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),Ze=u(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),Qe=u(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),je=u(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),$e=u(function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),ae=u(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),ts=u(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),Ft=function(){function t(i,o,h,d,a,f,E){const g=o.append("text").attr("x",h+a/2).attr("y",d+f/2+5).style("text-anchor","middle").text(i);r(g,E)}u(t,"byText");function e(i,o,h,d,a,f,E,g){const{actorFontSize:T,actorFontFamily:m,actorFontWeight:I}=g,[A,O]=Qt(T),S=i.split(v.lineBreakRegex);for(let B=0;B<S.length;B++){const D=B*A-A*(S.length-1)/2,W=o.append("text").attr("x",h+a/2).attr("y",d).style("text-anchor","middle").style("font-size",O).style("font-weight",I).style("font-family",m);W.append("tspan").attr("x",h+a/2).attr("dy",D).text(S[B]),W.attr("y",d+f/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),r(W,E)}}u(e,"byTspan");function c(i,o,h,d,a,f,E,g){const T=o.append("switch"),I=T.append("foreignObject").attr("x",h).attr("y",d).attr("width",a).attr("height",f).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");I.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),e(i,T,h,d,a,f,E,g),r(I,E)}u(c,"byFo");async function s(i,o,h,d,a,f,E,g){const T=await Et(i,Dt()),m=o.append("switch"),A=m.append("foreignObject").attr("x",h+a/2-T.width/2).attr("y",d+f/2-T.height/2).attr("width",T.width).attr("height",T.height).append("xhtml:div").style("height","100%").style("width","100%");A.append("div").style("text-align","center").style("vertical-align","middle").html(await Zt(i,Dt())),e(i,m,h,d,a,f,E,g),r(A,E)}u(s,"byKatex");function r(i,o){for(const h in o)o.hasOwnProperty(h)&&i.attr(h,o[h])}return u(r,"_setTextAttrs"),function(i,o=!1){return o?s:i.textPlacement==="fo"?c:i.textPlacement==="old"?t:e}}(),es=function(){function t(r,i,o,h,d,a,f){const E=i.append("text").attr("x",o).attr("y",h).style("text-anchor","start").text(r);s(E,f)}u(t,"byText");function e(r,i,o,h,d,a,f,E){const{actorFontSize:g,actorFontFamily:T,actorFontWeight:m}=E,I=r.split(v.lineBreakRegex);for(let A=0;A<I.length;A++){const O=A*g-g*(I.length-1)/2,S=i.append("text").attr("x",o).attr("y",h).style("text-anchor","start").style("font-size",g).style("font-weight",m).style("font-family",T);S.append("tspan").attr("x",o).attr("dy",O).text(I[A]),S.attr("y",h+a/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(S,f)}}u(e,"byTspan");function c(r,i,o,h,d,a,f,E){const g=i.append("switch"),m=g.append("foreignObject").attr("x",o).attr("y",h).attr("width",d).attr("height",a).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");m.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(r),e(r,g,o,h,d,a,f,E),s(m,f)}u(c,"byFo");function s(r,i){for(const o in i)i.hasOwnProperty(o)&&r.attr(o,i[o])}return u(s,"_setTextAttrs"),function(r){return r.textPlacement==="fo"?c:r.textPlacement==="old"?t:e}}(),C={drawRect:Yt,drawText:Tt,drawLabel:te,drawActor:qe,drawBox:ze,drawPopup:Ve,anchorElement:He,drawActivation:Ke,drawLoop:Ue,drawBackgroundRect:se,insertArrowHead:Ze,insertArrowFilledHead:Qe,insertSequenceNumber:je,insertArrowCrossHead:$e,insertDatabaseIcon:Ge,insertComputerIcon:Xe,insertClockIcon:Je,getTextObj:ae,getNoteRect:ts,fixLifeLineHeights:ee,sanitizeUrl:Jt},n={},x={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:u(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(t=>t.height||0))+(this.loops.length===0?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.messages.length===0?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.notes.length===0?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},"getHeight"),clear:u(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:u(function(t){this.boxes.push(t)},"addBox"),addActor:u(function(t){this.actors.push(t)},"addActor"),addLoop:u(function(t){this.loops.push(t)},"addLoop"),addMessage:u(function(t){this.messages.push(t)},"addMessage"),addNote:u(function(t){this.notes.push(t)},"addNote"),lastActor:u(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:u(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:u(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:u(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:u(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,ne($())},"init"),updateVal:u(function(t,e,c,s){t[e]===void 0?t[e]=c:t[e]=s(c,t[e])},"updateVal"),updateBounds:u(function(t,e,c,s){const r=this;let i=0;function o(h){return u(function(a){i++;const f=r.sequenceItems.length-i+1;r.updateVal(a,"starty",e-f*n.boxMargin,Math.min),r.updateVal(a,"stopy",s+f*n.boxMargin,Math.max),r.updateVal(x.data,"startx",t-f*n.boxMargin,Math.min),r.updateVal(x.data,"stopx",c+f*n.boxMargin,Math.max),h!=="activation"&&(r.updateVal(a,"startx",t-f*n.boxMargin,Math.min),r.updateVal(a,"stopx",c+f*n.boxMargin,Math.max),r.updateVal(x.data,"starty",e-f*n.boxMargin,Math.min),r.updateVal(x.data,"stopy",s+f*n.boxMargin,Math.max))},"updateItemBounds")}u(o,"updateFn"),this.sequenceItems.forEach(o()),this.activations.forEach(o("activation"))},"updateBounds"),insert:u(function(t,e,c,s){const r=v.getMin(t,c),i=v.getMax(t,c),o=v.getMin(e,s),h=v.getMax(e,s);this.updateVal(x.data,"startx",r,Math.min),this.updateVal(x.data,"starty",o,Math.min),this.updateVal(x.data,"stopx",i,Math.max),this.updateVal(x.data,"stopy",h,Math.max),this.updateBounds(r,o,i,h)},"insert"),newActivation:u(function(t,e,c){const s=c.get(t.from),r=At(t.from).length||0,i=s.x+s.width/2+(r-1)*n.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+n.activationWidth,stopy:void 0,actor:t.from,anchored:C.anchorElement(e)})},"newActivation"),endActivation:u(function(t){const e=this.activations.map(function(c){return c.actor}).lastIndexOf(t.from);return this.activations.splice(e,1)[0]},"endActivation"),createLoop:u(function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},"createLoop"),newLoop:u(function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},"newLoop"),endLoop:u(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:u(function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},"isLoopOverlap"),addSectionToLoop:u(function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:x.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},"addSectionToLoop"),saveVerticalPos:u(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:u(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:u(function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=v.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:u(function(){return this.verticalPos},"getVerticalPos"),getBounds:u(function(){return{bounds:this.data,models:this.models}},"getBounds")},ss=u(async function(t,e){x.bumpVerticalPos(n.boxMargin),e.height=n.boxMargin,e.starty=x.getVerticalPos();const c=Pt();c.x=e.startx,c.y=e.starty,c.width=e.width||n.width,c.class="note";const s=t.append("g"),r=C.drawRect(s,c),i=Vt();i.x=e.startx,i.y=e.starty,i.width=c.width,i.dy="1em",i.text=e.message,i.class="noteText",i.fontFamily=n.noteFontFamily,i.fontSize=n.noteFontSize,i.fontWeight=n.noteFontWeight,i.anchor=n.noteAlign,i.textMargin=n.noteMargin,i.valign="center";const o=ot(i.text)?await _t(s,i):Tt(s,i),h=Math.round(o.map(d=>(d._groups||d)[0][0].getBBox().height).reduce((d,a)=>d+a));r.attr("height",h+2*n.noteMargin),e.height+=h+2*n.noteMargin,x.bumpVerticalPos(h+2*n.noteMargin),e.stopy=e.starty+h+2*n.noteMargin,e.stopx=e.startx+c.width,x.insert(e.startx,e.starty,e.stopx,e.stopy),x.models.addNote(e)},"drawNote"),gt=u(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont"),xt=u(t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),"noteFont"),Ot=u(t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),"actorFont");async function re(t,e){x.bumpVerticalPos(10);const{startx:c,stopx:s,message:r}=e,i=v.splitBreaks(r).length,o=ot(r),h=o?await Et(r,$()):Y.calculateTextDimensions(r,gt(n));if(!o){const E=h.height/i;e.height+=E,x.bumpVerticalPos(E)}let d,a=h.height-10;const f=h.width;if(c===s){d=x.getVerticalPos()+a,n.rightAngles||(a+=n.boxMargin,d=x.getVerticalPos()+a),a+=30;const E=v.getMax(f/2,n.width/2);x.insert(c-E,x.getVerticalPos()-10+a,s+E,x.getVerticalPos()+30+a)}else a+=n.boxMargin,d=x.getVerticalPos()+a,x.insert(c,d-10,s,d);return x.bumpVerticalPos(a),e.height+=a,e.stopy=e.starty+e.height,x.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),d}u(re,"boundMessage");var as=u(async function(t,e,c,s){const{startx:r,stopx:i,starty:o,message:h,type:d,sequenceIndex:a,sequenceVisible:f}=e,E=Y.calculateTextDimensions(h,gt(n)),g=Vt();g.x=r,g.y=o+10,g.width=i-r,g.class="messageText",g.dy="1em",g.text=h,g.fontFamily=n.messageFontFamily,g.fontSize=n.messageFontSize,g.fontWeight=n.messageFontWeight,g.anchor=n.messageAlign,g.valign="center",g.textMargin=n.wrapPadding,g.tspan=!1,ot(g.text)?await _t(t,g,{startx:r,stopx:i,starty:c}):Tt(t,g);const T=E.width;let m;r===i?n.rightAngles?m=t.append("path").attr("d",`M  ${r},${c} H ${r+v.getMax(n.width/2,T/2)} V ${c+25} H ${r}`):m=t.append("path").attr("d","M "+r+","+c+" C "+(r+60)+","+(c-10)+" "+(r+60)+","+(c+30)+" "+r+","+(c+20)):(m=t.append("line"),m.attr("x1",r),m.attr("y1",c),m.attr("x2",i),m.attr("y2",c)),d===s.db.LINETYPE.DOTTED||d===s.db.LINETYPE.DOTTED_CROSS||d===s.db.LINETYPE.DOTTED_POINT||d===s.db.LINETYPE.DOTTED_OPEN||d===s.db.LINETYPE.BIDIRECTIONAL_DOTTED?(m.style("stroke-dasharray","3, 3"),m.attr("class","messageLine1")):m.attr("class","messageLine0");let I="";n.arrowMarkerAbsolute&&(I=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,I=I.replace(/\(/g,"\\("),I=I.replace(/\)/g,"\\)")),m.attr("stroke-width",2),m.attr("stroke","none"),m.style("fill","none"),(d===s.db.LINETYPE.SOLID||d===s.db.LINETYPE.DOTTED)&&m.attr("marker-end","url("+I+"#arrowhead)"),(d===s.db.LINETYPE.BIDIRECTIONAL_SOLID||d===s.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(m.attr("marker-start","url("+I+"#arrowhead)"),m.attr("marker-end","url("+I+"#arrowhead)")),(d===s.db.LINETYPE.SOLID_POINT||d===s.db.LINETYPE.DOTTED_POINT)&&m.attr("marker-end","url("+I+"#filled-head)"),(d===s.db.LINETYPE.SOLID_CROSS||d===s.db.LINETYPE.DOTTED_CROSS)&&m.attr("marker-end","url("+I+"#crosshead)"),(f||n.showSequenceNumbers)&&(m.attr("marker-start","url("+I+"#sequencenumber)"),t.append("text").attr("x",r).attr("y",c+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(a))},"drawMessage"),rs=u(function(t,e,c,s,r,i,o){let h=0,d=0,a,f=0;for(const E of s){const g=e.get(E),T=g.box;a&&a!=T&&(o||x.models.addBox(a),d+=n.boxMargin+a.margin),T&&T!=a&&(o||(T.x=h+d,T.y=r),d+=T.margin),g.width=g.width||n.width,g.height=v.getMax(g.height||n.height,n.height),g.margin=g.margin||n.actorMargin,f=v.getMax(f,g.height),c.get(g.name)&&(d+=g.width/2),g.x=h+d,g.starty=x.getVerticalPos(),x.insert(g.x,r,g.x+g.width,g.height),h+=g.width+d,g.box&&(g.box.width=h+T.margin-g.box.x),d=g.margin,a=g.box,x.models.addActor(g)}a&&!o&&x.models.addBox(a),x.bumpVerticalPos(f)},"addActorRenderingData"),Bt=u(async function(t,e,c,s){if(s){let r=0;x.bumpVerticalPos(n.boxMargin*2);for(const i of c){const o=e.get(i);o.stopy||(o.stopy=x.getVerticalPos());const h=await C.drawActor(t,o,n,!0);r=v.getMax(r,h)}x.bumpVerticalPos(r+n.boxMargin)}else for(const r of c){const i=e.get(r);await C.drawActor(t,i,n,!1)}},"drawActors"),ie=u(function(t,e,c,s){let r=0,i=0;for(const o of c){const h=e.get(o),d=ns(h),a=C.drawPopup(t,h,d,n,n.forceMenus,s);a.height>r&&(r=a.height),a.width+h.x>i&&(i=a.width+h.x)}return{maxHeight:r,maxWidth:i}},"drawActorsPopup"),ne=u(function(t){Pe(n,t),t.fontFamily&&(n.actorFontFamily=n.noteFontFamily=n.messageFontFamily=t.fontFamily),t.fontSize&&(n.actorFontSize=n.noteFontSize=n.messageFontSize=t.fontSize),t.fontWeight&&(n.actorFontWeight=n.noteFontWeight=n.messageFontWeight=t.fontWeight)},"setConf"),At=u(function(t){return x.activations.filter(function(e){return e.actor===t})},"actorActivations"),Xt=u(function(t,e){const c=e.get(t),s=At(t),r=s.reduce(function(o,h){return v.getMin(o,h.startx)},c.x+c.width/2-1),i=s.reduce(function(o,h){return v.getMax(o,h.stopx)},c.x+c.width/2+1);return[r,i]},"activationBounds");function rt(t,e,c,s,r){x.bumpVerticalPos(c);let i=s;if(e.id&&e.message&&t[e.id]){const o=t[e.id].width,h=gt(n);e.message=Y.wrapLabel(`[${e.message}]`,o-2*n.wrapPadding,h),e.width=o,e.wrap=!0;const d=Y.calculateTextDimensions(e.message,h),a=v.getMax(d.height,n.labelBoxHeight);i=s+a,G.debug(`${a} - ${e.message}`)}r(e),x.bumpVerticalPos(i)}u(rt,"adjustLoopHeightForWrap");function oe(t,e,c,s,r,i,o){function h(a,f){a.x<r.get(t.from).x?(x.insert(e.stopx-f,e.starty,e.startx,e.stopy+a.height/2+n.noteMargin),e.stopx=e.stopx+f):(x.insert(e.startx,e.starty,e.stopx+f,e.stopy+a.height/2+n.noteMargin),e.stopx=e.stopx-f)}u(h,"receiverAdjustment");function d(a,f){a.x<r.get(t.to).x?(x.insert(e.startx-f,e.starty,e.stopx,e.stopy+a.height/2+n.noteMargin),e.startx=e.startx+f):(x.insert(e.stopx,e.starty,e.startx+f,e.stopy+a.height/2+n.noteMargin),e.startx=e.startx-f)}if(u(d,"senderAdjustment"),i.get(t.to)==s){const a=r.get(t.to),f=a.type=="actor"?ut/2+3:a.width/2+3;h(a,f),a.starty=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(t.from)==s){const a=r.get(t.from);if(n.mirrorActors){const f=a.type=="actor"?ut/2:a.width/2;d(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(t.to)==s){const a=r.get(t.to);if(n.mirrorActors){const f=a.type=="actor"?ut/2+3:a.width/2+3;h(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}}u(oe,"adjustCreatedDestroyedData");var is=u(async function(t,e,c,s){const{securityLevel:r,sequence:i}=$();n=i;let o;r==="sandbox"&&(o=Lt("#i"+e));const h=r==="sandbox"?Lt(o.nodes()[0].contentDocument.body):Lt("body"),d=r==="sandbox"?o.nodes()[0].contentDocument:document;x.init(),G.debug(s.db);const a=r==="sandbox"?h.select(`[id="${e}"]`):Lt(`[id="${e}"]`),f=s.db.getActors(),E=s.db.getCreatedActors(),g=s.db.getDestroyedActors(),T=s.db.getBoxes();let m=s.db.getActorKeys();const I=s.db.getMessages(),A=s.db.getDiagramTitle(),O=s.db.hasAtLeastOneBox(),S=s.db.hasAtLeastOneBoxWithTitle(),B=await ce(f,I,s);if(n.height=await le(f,B,T),C.insertComputerIcon(a),C.insertDatabaseIcon(a),C.insertClockIcon(a),O&&(x.bumpVerticalPos(n.boxMargin),S&&x.bumpVerticalPos(T[0].textMaxHeight)),n.hideUnusedParticipants===!0){const y=new Set;I.forEach(_=>{y.add(_.from),y.add(_.to)}),m=m.filter(_=>y.has(_))}rs(a,f,E,m,0,I,!1);const D=await ls(I,f,B,s);C.insertArrowHead(a),C.insertArrowCrossHead(a),C.insertArrowFilledHead(a),C.insertSequenceNumber(a);function W(y,_){const Q=x.endActivation(y);Q.starty+18>_&&(Q.starty=_-6,_+=12),C.drawActivation(a,Q,_,n,At(y.from).length),x.insert(Q.startx,_-10,Q.stopx,_)}u(W,"activeEnd");let q=1,X=1;const tt=[],z=[];let H=0;for(const y of I){let _,Q,at;switch(y.type){case s.db.LINETYPE.NOTE:x.resetVerticalPos(),Q=y.noteModel,await ss(a,Q);break;case s.db.LINETYPE.ACTIVE_START:x.newActivation(y,a,f);break;case s.db.LINETYPE.ACTIVE_END:W(y,x.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k));break;case s.db.LINETYPE.LOOP_END:_=x.endLoop(),await C.drawLoop(a,_,"loop",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.RECT_START:rt(D,y,n.boxMargin,n.boxMargin,k=>x.newLoop(void 0,k.message));break;case s.db.LINETYPE.RECT_END:_=x.endLoop(),z.push(_),x.models.addLoop(_),x.bumpVerticalPos(_.stopy-x.getVerticalPos());break;case s.db.LINETYPE.OPT_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k));break;case s.db.LINETYPE.OPT_END:_=x.endLoop(),await C.drawLoop(a,_,"opt",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.ALT_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k));break;case s.db.LINETYPE.ALT_ELSE:rt(D,y,n.boxMargin+n.boxTextMargin,n.boxMargin,k=>x.addSectionToLoop(k));break;case s.db.LINETYPE.ALT_END:_=x.endLoop(),await C.drawLoop(a,_,"alt",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k)),x.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:rt(D,y,n.boxMargin+n.boxTextMargin,n.boxMargin,k=>x.addSectionToLoop(k));break;case s.db.LINETYPE.PAR_END:_=x.endLoop(),await C.drawLoop(a,_,"par",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.AUTONUMBER:q=y.message.start||q,X=y.message.step||X,y.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k));break;case s.db.LINETYPE.CRITICAL_OPTION:rt(D,y,n.boxMargin+n.boxTextMargin,n.boxMargin,k=>x.addSectionToLoop(k));break;case s.db.LINETYPE.CRITICAL_END:_=x.endLoop(),await C.drawLoop(a,_,"critical",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.BREAK_START:rt(D,y,n.boxMargin,n.boxMargin+n.boxTextMargin,k=>x.newLoop(k));break;case s.db.LINETYPE.BREAK_END:_=x.endLoop(),await C.drawLoop(a,_,"break",n),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;default:try{at=y.msgModel,at.starty=x.getVerticalPos(),at.sequenceIndex=q,at.sequenceVisible=s.db.showSequenceNumbers();const k=await re(a,at);oe(y,at,k,H,f,E,g),tt.push({messageModel:at,lineStartY:k}),x.models.addMessage(at)}catch(k){G.error("error while drawing message",k)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(y.type)&&(q=q+X),H++}G.debug("createdActors",E),G.debug("destroyedActors",g),await Bt(a,f,m,!1);for(const y of tt)await as(a,y.messageModel,y.lineStartY,s);n.mirrorActors&&await Bt(a,f,m,!0),z.forEach(y=>C.drawBackgroundRect(a,y)),ee(a,f,m,n);for(const y of x.models.boxes)y.height=x.getVerticalPos()-y.y,x.insert(y.x,y.y,y.x+y.width,y.height),y.startx=y.x,y.starty=y.y,y.stopx=y.startx+y.width,y.stopy=y.starty+y.height,y.stroke="rgb(0,0,0, 0.5)",C.drawBox(a,y,n);O&&x.bumpVerticalPos(n.boxMargin);const F=ie(a,f,m,d),{bounds:M}=x.getBounds();M.startx===void 0&&(M.startx=0),M.starty===void 0&&(M.starty=0),M.stopx===void 0&&(M.stopx=0),M.stopy===void 0&&(M.stopy=0);let J=M.stopy-M.starty;J<F.maxHeight&&(J=F.maxHeight);let K=J+2*n.diagramMarginY;n.mirrorActors&&(K=K-n.boxMargin+n.bottomMarginAdj);let Z=M.stopx-M.startx;Z<F.maxWidth&&(Z=F.maxWidth);const et=Z+2*n.diagramMarginX;A&&a.append("text").text(A).attr("x",(M.stopx-M.startx)/2-2*n.diagramMarginX).attr("y",-25),Ae(a,K,et,n.useMaxWidth);const N=A?40:0;a.attr("viewBox",M.startx-n.diagramMarginX+" -"+(n.diagramMarginY+N)+" "+et+" "+(K+N)),G.debug("models:",x.models)},"draw");async function ce(t,e,c){const s={};for(const r of e)if(t.get(r.to)&&t.get(r.from)){const i=t.get(r.to);if(r.placement===c.db.PLACEMENT.LEFTOF&&!i.prevActor||r.placement===c.db.PLACEMENT.RIGHTOF&&!i.nextActor)continue;const o=r.placement!==void 0,h=!o,d=o?xt(n):gt(n),a=r.wrap?Y.wrapLabel(r.message,n.width-2*n.wrapPadding,d):r.message,E=(ot(a)?await Et(r.message,$()):Y.calculateTextDimensions(a,d)).width+2*n.wrapPadding;h&&r.from===i.nextActor?s[r.to]=v.getMax(s[r.to]||0,E):h&&r.from===i.prevActor?s[r.from]=v.getMax(s[r.from]||0,E):h&&r.from===r.to?(s[r.from]=v.getMax(s[r.from]||0,E/2),s[r.to]=v.getMax(s[r.to]||0,E/2)):r.placement===c.db.PLACEMENT.RIGHTOF?s[r.from]=v.getMax(s[r.from]||0,E):r.placement===c.db.PLACEMENT.LEFTOF?s[i.prevActor]=v.getMax(s[i.prevActor]||0,E):r.placement===c.db.PLACEMENT.OVER&&(i.prevActor&&(s[i.prevActor]=v.getMax(s[i.prevActor]||0,E/2)),i.nextActor&&(s[r.from]=v.getMax(s[r.from]||0,E/2)))}return G.debug("maxMessageWidthPerActor:",s),s}u(ce,"getMaxMessageWidthPerActor");var ns=u(function(t){let e=0;const c=Ot(n);for(const s in t.links){const i=Y.calculateTextDimensions(s,c).width+2*n.wrapPadding+2*n.boxMargin;e<i&&(e=i)}return e},"getRequiredPopupWidth");async function le(t,e,c){let s=0;for(const i of t.keys()){const o=t.get(i);o.wrap&&(o.description=Y.wrapLabel(o.description,n.width-2*n.wrapPadding,Ot(n)));const h=ot(o.description)?await Et(o.description,$()):Y.calculateTextDimensions(o.description,Ot(n));o.width=o.wrap?n.width:v.getMax(n.width,h.width+2*n.wrapPadding),o.height=o.wrap?v.getMax(h.height,n.height):n.height,s=v.getMax(s,o.height)}for(const i in e){const o=t.get(i);if(!o)continue;const h=t.get(o.nextActor);if(!h){const E=e[i]+n.actorMargin-o.width/2;o.margin=v.getMax(E,n.actorMargin);continue}const a=e[i]+n.actorMargin-o.width/2-h.width/2;o.margin=v.getMax(a,n.actorMargin)}let r=0;return c.forEach(i=>{const o=gt(n);let h=i.actorKeys.reduce((f,E)=>f+=t.get(E).width+(t.get(E).margin||0),0);h-=2*n.boxTextMargin,i.wrap&&(i.name=Y.wrapLabel(i.name,h-2*n.wrapPadding,o));const d=Y.calculateTextDimensions(i.name,o);r=v.getMax(d.height,r);const a=v.getMax(h,d.width+2*n.wrapPadding);if(i.margin=n.boxTextMargin,h<a){const f=(a-h)/2;i.margin+=f}}),c.forEach(i=>i.textMaxHeight=r),v.getMax(s,n.height)}u(le,"calculateActorMargins");var os=u(async function(t,e,c){const s=e.get(t.from),r=e.get(t.to),i=s.x,o=r.x,h=t.wrap&&t.message;let d=ot(t.message)?await Et(t.message,$()):Y.calculateTextDimensions(h?Y.wrapLabel(t.message,n.width,xt(n)):t.message,xt(n));const a={width:h?n.width:v.getMax(n.width,d.width+2*n.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===c.db.PLACEMENT.RIGHTOF?(a.width=h?v.getMax(n.width,d.width):v.getMax(s.width/2+r.width/2,d.width+2*n.noteMargin),a.startx=i+(s.width+n.actorMargin)/2):t.placement===c.db.PLACEMENT.LEFTOF?(a.width=h?v.getMax(n.width,d.width+2*n.noteMargin):v.getMax(s.width/2+r.width/2,d.width+2*n.noteMargin),a.startx=i-a.width+(s.width-n.actorMargin)/2):t.to===t.from?(d=Y.calculateTextDimensions(h?Y.wrapLabel(t.message,v.getMax(n.width,s.width),xt(n)):t.message,xt(n)),a.width=h?v.getMax(n.width,s.width):v.getMax(s.width,n.width,d.width+2*n.noteMargin),a.startx=i+(s.width-a.width)/2):(a.width=Math.abs(i+s.width/2-(o+r.width/2))+n.actorMargin,a.startx=i<o?i+s.width/2-n.actorMargin/2:o+r.width/2-n.actorMargin/2),h&&(a.message=Y.wrapLabel(t.message,a.width-2*n.wrapPadding,xt(n))),G.debug(`NM:[${a.startx},${a.stopx},${a.starty},${a.stopy}:${a.width},${a.height}=${t.message}]`),a},"buildNoteModel"),cs=u(function(t,e,c){if(![c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN,c.db.LINETYPE.SOLID,c.db.LINETYPE.DOTTED,c.db.LINETYPE.SOLID_CROSS,c.db.LINETYPE.DOTTED_CROSS,c.db.LINETYPE.SOLID_POINT,c.db.LINETYPE.DOTTED_POINT,c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};const[s,r]=Xt(t.from,e),[i,o]=Xt(t.to,e),h=s<=i;let d=h?r:s,a=h?i:o;const f=Math.abs(i-o)>2,E=u(I=>h?-I:I,"adjustValue");t.from===t.to?a=d:(t.activate&&!f&&(a+=E(n.activationWidth/2-1)),[c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(a+=E(3)),[c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(d-=E(3)));const g=[s,r,i,o],T=Math.abs(d-a);t.wrap&&t.message&&(t.message=Y.wrapLabel(t.message,v.getMax(T+2*n.wrapPadding,n.width),gt(n)));const m=Y.calculateTextDimensions(t.message,gt(n));return{width:v.getMax(t.wrap?0:m.width+2*n.wrapPadding,T+2*n.wrapPadding,n.width),height:0,startx:d,stopx:a,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}},"buildMessageModel"),ls=u(async function(t,e,c,s){const r={},i=[];let o,h,d;for(const a of t){switch(a.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:i.push({id:a.id,msg:a.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:a.message&&(o=i.pop(),r[o.id]=o,r[a.id]=o,i.push(o));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:o=i.pop(),r[o.id]=o;break;case s.db.LINETYPE.ACTIVE_START:{const E=e.get(a.from?a.from:a.to.actor),g=At(a.from?a.from:a.to.actor).length,T=E.x+E.width/2+(g-1)*n.activationWidth/2,m={startx:T,stopx:T+n.activationWidth,actor:a.from,enabled:!0};x.activations.push(m)}break;case s.db.LINETYPE.ACTIVE_END:{const E=x.activations.map(g=>g.actor).lastIndexOf(a.from);x.activations.splice(E,1).splice(0,1)}break}a.placement!==void 0?(h=await os(a,e,s),a.noteModel=h,i.forEach(E=>{o=E,o.from=v.getMin(o.from,h.startx),o.to=v.getMax(o.to,h.startx+h.width),o.width=v.getMax(o.width,Math.abs(o.from-o.to))-n.labelBoxWidth})):(d=cs(a,e,s),a.msgModel=d,d.startx&&d.stopx&&i.length>0&&i.forEach(E=>{if(o=E,d.startx===d.stopx){const g=e.get(a.from),T=e.get(a.to);o.from=v.getMin(g.x-d.width/2,g.x-g.width/2,o.from),o.to=v.getMax(T.x+d.width/2,T.x+g.width/2,o.to),o.width=v.getMax(o.width,Math.abs(o.to-o.from))-n.labelBoxWidth}else o.from=v.getMin(d.startx,o.from),o.to=v.getMax(d.stopx,o.to),o.width=v.getMax(o.width,d.width)-n.labelBoxWidth}))}return x.activations=[],G.debug("Loop type widths:",r),r},"calculateLoopBounds"),hs={bounds:x,drawActors:Bt,drawActorsPopup:ie,setConf:ne,draw:is},Es={parser:Ne,get db(){return new De},renderer:hs,styles:Oe,init:u(t=>{t.sequence||(t.sequence={}),t.wrap&&(t.sequence.wrap=t.wrap,_e({sequence:{wrap:t.wrap}}))},"init")};export{Es as diagram};
//# sourceMappingURL=sequenceDiagram-C4VUPXDP-DbrsMme5.js.map
