import{G as l,U as t,a as r}from"./environmentTextureTools-B2gws8HC.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./index-Cb4A4-Xi.js";import"./dumpTools-BnnULfWw.js";class y{constructor(){this.supportCascades=!1}loadCubeData(o,a,d,s,n){if(Array.isArray(o))return;const e=l(o);if(e){a.width=e.width,a.height=e.width;try{t(a,e),r(a,o,e).then(()=>{a.isReady=!0,a.onLoadedObservable.notifyObservers(a),a.onLoadedObservable.clear(),s&&s()},i=>{n?.("Can not upload environment levels",i)})}catch(i){n?.("Can not upload environment file",i)}}else n&&n("Can not parse the environment file",null)}loadData(){throw".env not supported in 2d."}}export{y as _ENVTextureLoader};
//# sourceMappingURL=envTextureLoader-3N7VcluH.js.map
