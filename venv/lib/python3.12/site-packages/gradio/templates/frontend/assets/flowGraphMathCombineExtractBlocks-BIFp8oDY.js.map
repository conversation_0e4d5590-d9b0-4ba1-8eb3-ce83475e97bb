{"version": 3, "file": "flowGraphMathCombineExtractBlocks-BIFp8oDY.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathCombineExtractBlocks.js"], "sourcesContent": ["import { FlowGraphCachedOperationBlock } from \"../flowGraphCachedOperationBlock.js\";\nimport { RichTypeMatrix, RichTypeMatrix2D, RichTypeMatrix3D, RichTypeNumber, RichTypeVector2, RichTypeVector3, RichTypeVector4, } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { Matrix, Vector2, Vector3, Vector4 } from \"../../../../Maths/math.vector.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphMatrix2D, FlowGraphMatrix3D } from \"../../../CustomTypes/flowGraphMatrix.js\";\nclass FlowGraphMathCombineBlock extends FlowGraphCachedOperationBlock {\n    /**\n     * Base class for blocks that combine multiple numeric inputs into a single result.\n     * <PERSON>les registering data inputs and managing cached outputs.\n     * @param numberOfInputs The number of input values to combine.\n     * @param type The type of the result.\n     * @param config The block configuration.\n     */\n    constructor(numberOfInputs, type, config) {\n        super(type, config);\n        for (let i = 0; i < numberOfInputs; i++) {\n            this.registerDataInput(`input_${i}`, RichTypeNumber, 0);\n        }\n    }\n}\n/**\n * Abstract class representing a flow graph block that extracts multiple outputs from a single input.\n */\nclass FlowGraphMathExtractBlock extends FlowGraphBlock {\n    /**\n     * Creates an instance of FlowGraphMathExtractBlock.\n     *\n     * @param numberOfOutputs - The number of outputs to be extracted from the input.\n     * @param type - The type of the input data.\n     * @param config - Optional configuration for the flow graph block.\n     */\n    constructor(numberOfOutputs, type, config) {\n        super(config);\n        this.registerDataInput(\"input\", type);\n        for (let i = 0; i < numberOfOutputs; i++) {\n            this.registerDataOutput(`output_${i}`, RichTypeNumber, 0);\n        }\n    }\n}\n/**\n * Combines two floats into a new Vector2\n */\nexport class FlowGraphCombineVector2Block extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(2, RichTypeVector2, config);\n    }\n    /**\n     * @internal\n     * Combines two floats into a new Vector2\n     */\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedVector\")) {\n            context._setExecutionVariable(this, \"cachedVector\", new Vector2());\n        }\n        const vector = context._getExecutionVariable(this, \"cachedVector\", null);\n        vector.set(this.getDataInput(\"input_0\").getValue(context), this.getDataInput(\"input_1\").getValue(context));\n        return vector;\n    }\n    getClassName() {\n        return \"FlowGraphCombineVector2Block\" /* FlowGraphBlockNames.CombineVector2 */;\n    }\n}\nRegisterClass(\"FlowGraphCombineVector2Block\" /* FlowGraphBlockNames.CombineVector2 */, FlowGraphCombineVector2Block);\n/**\n * Combines three floats into a new Vector3\n */\nexport class FlowGraphCombineVector3Block extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(3, RichTypeVector3, config);\n    }\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedVector\")) {\n            context._setExecutionVariable(this, \"cachedVector\", new Vector3());\n        }\n        const vector = context._getExecutionVariable(this, \"cachedVector\", null);\n        vector.set(this.getDataInput(\"input_0\").getValue(context), this.getDataInput(\"input_1\").getValue(context), this.getDataInput(\"input_2\").getValue(context));\n        return vector;\n    }\n    getClassName() {\n        return \"FlowGraphCombineVector3Block\" /* FlowGraphBlockNames.CombineVector3 */;\n    }\n}\nRegisterClass(\"FlowGraphCombineVector3Block\" /* FlowGraphBlockNames.CombineVector3 */, FlowGraphCombineVector3Block);\n/**\n * Combines four floats into a new Vector4\n */\nexport class FlowGraphCombineVector4Block extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(4, RichTypeVector4, config);\n    }\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedVector\")) {\n            context._setExecutionVariable(this, \"cachedVector\", new Vector4());\n        }\n        const vector = context._getExecutionVariable(this, \"cachedVector\", null);\n        vector.set(this.getDataInput(\"input_0\").getValue(context), this.getDataInput(\"input_1\").getValue(context), this.getDataInput(\"input_2\").getValue(context), this.getDataInput(\"input_3\").getValue(context));\n        return vector;\n    }\n    getClassName() {\n        return \"FlowGraphCombineVector4Block\" /* FlowGraphBlockNames.CombineVector4 */;\n    }\n}\nRegisterClass(\"FlowGraphCombineVector4Block\" /* FlowGraphBlockNames.CombineVector4 */, FlowGraphCombineVector4Block);\n/**\n * Combines 16 floats into a new Matrix\n *\n * Note that glTF interactivity's combine4x4 uses column-major order, while Babylon.js uses row-major order.\n */\nexport class FlowGraphCombineMatrixBlock extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(16, RichTypeMatrix, config);\n    }\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedMatrix\")) {\n            context._setExecutionVariable(this, \"cachedMatrix\", new Matrix());\n        }\n        const matrix = context._getExecutionVariable(this, \"cachedMatrix\", null);\n        if (this.config?.inputIsColumnMajor) {\n            matrix.set(this.getDataInput(\"input_0\").getValue(context), this.getDataInput(\"input_4\").getValue(context), this.getDataInput(\"input_8\").getValue(context), this.getDataInput(\"input_12\").getValue(context), this.getDataInput(\"input_1\").getValue(context), this.getDataInput(\"input_5\").getValue(context), this.getDataInput(\"input_9\").getValue(context), this.getDataInput(\"input_13\").getValue(context), this.getDataInput(\"input_2\").getValue(context), this.getDataInput(\"input_6\").getValue(context), this.getDataInput(\"input_10\").getValue(context), this.getDataInput(\"input_14\").getValue(context), this.getDataInput(\"input_3\").getValue(context), this.getDataInput(\"input_7\").getValue(context), this.getDataInput(\"input_11\").getValue(context), this.getDataInput(\"input_15\").getValue(context));\n        }\n        else {\n            matrix.set(this.getDataInput(\"input_0\").getValue(context), this.getDataInput(\"input_1\").getValue(context), this.getDataInput(\"input_2\").getValue(context), this.getDataInput(\"input_3\").getValue(context), this.getDataInput(\"input_4\").getValue(context), this.getDataInput(\"input_5\").getValue(context), this.getDataInput(\"input_6\").getValue(context), this.getDataInput(\"input_7\").getValue(context), this.getDataInput(\"input_8\").getValue(context), this.getDataInput(\"input_9\").getValue(context), this.getDataInput(\"input_10\").getValue(context), this.getDataInput(\"input_11\").getValue(context), this.getDataInput(\"input_12\").getValue(context), this.getDataInput(\"input_13\").getValue(context), this.getDataInput(\"input_14\").getValue(context), this.getDataInput(\"input_15\").getValue(context));\n        }\n        return matrix;\n    }\n    getClassName() {\n        return \"FlowGraphCombineMatrixBlock\" /* FlowGraphBlockNames.CombineMatrix */;\n    }\n}\nRegisterClass(\"FlowGraphCombineMatrixBlock\" /* FlowGraphBlockNames.CombineMatrix */, FlowGraphCombineMatrixBlock);\n/**\n * Combines 4 floats into a new Matrix\n */\nexport class FlowGraphCombineMatrix2DBlock extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(4, RichTypeMatrix2D, config);\n    }\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedMatrix\")) {\n            context._setExecutionVariable(this, \"cachedMatrix\", new FlowGraphMatrix2D());\n        }\n        const matrix = context._getExecutionVariable(this, \"cachedMatrix\", null);\n        const array = this.config?.inputIsColumnMajor\n            ? [\n                // column to row-major\n                this.getDataInput(\"input_0\").getValue(context),\n                this.getDataInput(\"input_2\").getValue(context),\n                this.getDataInput(\"input_1\").getValue(context),\n                this.getDataInput(\"input_3\").getValue(context),\n            ]\n            : [\n                this.getDataInput(\"input_0\").getValue(context),\n                this.getDataInput(\"input_1\").getValue(context),\n                this.getDataInput(\"input_2\").getValue(context),\n                this.getDataInput(\"input_3\").getValue(context),\n            ];\n        matrix.fromArray(array);\n        return matrix;\n    }\n    getClassName() {\n        return \"FlowGraphCombineMatrix2DBlock\" /* FlowGraphBlockNames.CombineMatrix2D */;\n    }\n}\nRegisterClass(\"FlowGraphCombineMatrix2DBlock\" /* FlowGraphBlockNames.CombineMatrix2D */, FlowGraphCombineMatrix2DBlock);\n/**\n * Combines 9 floats into a new Matrix3D\n */\nexport class FlowGraphCombineMatrix3DBlock extends FlowGraphMathCombineBlock {\n    constructor(config) {\n        super(9, RichTypeMatrix3D, config);\n    }\n    _doOperation(context) {\n        if (!context._hasExecutionVariable(this, \"cachedMatrix\")) {\n            context._setExecutionVariable(this, \"cachedMatrix\", new FlowGraphMatrix3D());\n        }\n        const matrix = context._getExecutionVariable(this, \"cachedMatrix\", null);\n        const array = this.config?.inputIsColumnMajor\n            ? [\n                // column to row major\n                this.getDataInput(\"input_0\").getValue(context),\n                this.getDataInput(\"input_3\").getValue(context),\n                this.getDataInput(\"input_6\").getValue(context),\n                this.getDataInput(\"input_1\").getValue(context),\n                this.getDataInput(\"input_4\").getValue(context),\n                this.getDataInput(\"input_7\").getValue(context),\n                this.getDataInput(\"input_2\").getValue(context),\n                this.getDataInput(\"input_5\").getValue(context),\n                this.getDataInput(\"input_8\").getValue(context),\n            ]\n            : [\n                this.getDataInput(\"input_0\").getValue(context),\n                this.getDataInput(\"input_1\").getValue(context),\n                this.getDataInput(\"input_2\").getValue(context),\n                this.getDataInput(\"input_3\").getValue(context),\n                this.getDataInput(\"input_4\").getValue(context),\n                this.getDataInput(\"input_5\").getValue(context),\n                this.getDataInput(\"input_6\").getValue(context),\n                this.getDataInput(\"input_7\").getValue(context),\n                this.getDataInput(\"input_8\").getValue(context),\n            ];\n        matrix.fromArray(array);\n        return matrix;\n    }\n    getClassName() {\n        return \"FlowGraphCombineMatrix3DBlock\" /* FlowGraphBlockNames.CombineMatrix3D */;\n    }\n}\nRegisterClass(\"FlowGraphCombineMatrix3DBlock\" /* FlowGraphBlockNames.CombineMatrix3D */, FlowGraphCombineMatrix3DBlock);\n/**\n * Extracts two floats from a Vector2\n */\nexport class FlowGraphExtractVector2Block extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(2, RichTypeVector2, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = Vector2.Zero();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        this.getDataOutput(\"output_0\").setValue(input.x, context);\n        this.getDataOutput(\"output_1\").setValue(input.y, context);\n    }\n    getClassName() {\n        return \"FlowGraphExtractVector2Block\" /* FlowGraphBlockNames.ExtractVector2 */;\n    }\n}\nRegisterClass(\"FlowGraphExtractVector2Block\" /* FlowGraphBlockNames.ExtractVector2 */, FlowGraphExtractVector2Block);\n/**\n * Extracts three floats from a Vector3\n */\nexport class FlowGraphExtractVector3Block extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(3, RichTypeVector3, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = Vector3.Zero();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        this.getDataOutput(\"output_0\").setValue(input.x, context);\n        this.getDataOutput(\"output_1\").setValue(input.y, context);\n        this.getDataOutput(\"output_2\").setValue(input.z, context);\n    }\n    getClassName() {\n        return \"FlowGraphExtractVector3Block\" /* FlowGraphBlockNames.ExtractVector3 */;\n    }\n}\nRegisterClass(\"FlowGraphExtractVector3Block\" /* FlowGraphBlockNames.ExtractVector3 */, FlowGraphExtractVector3Block);\n/**\n * Extracts four floats from a Vector4\n */\nexport class FlowGraphExtractVector4Block extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(4, RichTypeVector4, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = Vector4.Zero();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        this.getDataOutput(\"output_0\").setValue(input.x, context);\n        this.getDataOutput(\"output_1\").setValue(input.y, context);\n        this.getDataOutput(\"output_2\").setValue(input.z, context);\n        this.getDataOutput(\"output_3\").setValue(input.w, context);\n    }\n    getClassName() {\n        return \"FlowGraphExtractVector4Block\" /* FlowGraphBlockNames.ExtractVector4 */;\n    }\n}\nRegisterClass(\"FlowGraphExtractVector4Block\" /* FlowGraphBlockNames.ExtractVector4 */, FlowGraphExtractVector4Block);\n/**\n * Extracts 16 floats from a Matrix\n */\nexport class FlowGraphExtractMatrixBlock extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(16, RichTypeMatrix, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = Matrix.Identity();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        for (let i = 0; i < 16; i++) {\n            this.getDataOutput(`output_${i}`).setValue(input.m[i], context);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphExtractMatrixBlock\" /* FlowGraphBlockNames.ExtractMatrix */;\n    }\n}\nRegisterClass(\"FlowGraphExtractMatrixBlock\" /* FlowGraphBlockNames.ExtractMatrix */, FlowGraphExtractMatrixBlock);\n/**\n * Extracts 4 floats from a Matrix2D\n */\nexport class FlowGraphExtractMatrix2DBlock extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(4, RichTypeMatrix2D, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = new FlowGraphMatrix2D();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        for (let i = 0; i < 4; i++) {\n            this.getDataOutput(`output_${i}`).setValue(input.m[i], context);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphExtractMatrix2DBlock\" /* FlowGraphBlockNames.ExtractMatrix2D */;\n    }\n}\nRegisterClass(\"FlowGraphExtractMatrix2DBlock\" /* FlowGraphBlockNames.ExtractMatrix2D */, FlowGraphExtractMatrix2DBlock);\n/**\n * Extracts 4 floats from a Matrix2D\n */\nexport class FlowGraphExtractMatrix3DBlock extends FlowGraphMathExtractBlock {\n    constructor(config) {\n        super(9, RichTypeMatrix3D, config);\n    }\n    _updateOutputs(context) {\n        let input = this.getDataInput(\"input\")?.getValue(context);\n        if (!input) {\n            input = new FlowGraphMatrix3D();\n            this.getDataInput(\"input\").setValue(input, context);\n        }\n        for (let i = 0; i < 9; i++) {\n            this.getDataOutput(`output_${i}`).setValue(input.m[i], context);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphExtractMatrix3DBlock\" /* FlowGraphBlockNames.ExtractMatrix3D */;\n    }\n}\nRegisterClass(\"FlowGraphExtractMatrix3DBlock\" /* FlowGraphBlockNames.ExtractMatrix3D */, FlowGraphExtractMatrix3DBlock);\n//# sourceMappingURL=flowGraphMathCombineExtractBlocks.js.map"], "names": ["FlowGraphMathCombineBlock", "FlowGraphCachedOperationBlock", "numberOfInputs", "type", "config", "i", "RichTypeNumber", "FlowGraphMathExtractBlock", "FlowGraphBlock", "numberOfOutputs", "FlowGraphCombineVector2Block", "RichTypeVector2", "context", "Vector2", "vector", "RegisterClass", "FlowGraphCombineVector3Block", "RichTypeVector3", "Vector3", "FlowGraphCombineVector4Block", "RichTypeVector4", "Vector4", "FlowGraphCombineMatrixBlock", "RichTypeMatrix", "Matrix", "matrix", "FlowGraphCombineMatrix2DBlock", "RichTypeMatrix2D", "FlowGraphMatrix2D", "array", "FlowGraphCombineMatrix3DBlock", "RichTypeMatrix3D", "FlowGraphMatrix3D", "FlowGraphExtractVector2Block", "input", "FlowGraphExtractVector3Block", "FlowGraphExtractVector4Block", "FlowGraphExtractMatrixBlock", "FlowGraphExtractMatrix2DBlock", "FlowGraphExtractMatrix3DBlock"], "mappings": "iYAMA,MAAMA,UAAkCC,CAA8B,CAQlE,YAAYC,EAAgBC,EAAMC,EAAQ,CACtC,MAAMD,EAAMC,CAAM,EAClB,QAASC,EAAI,EAAGA,EAAIH,EAAgBG,IAChC,KAAK,kBAAkB,SAASA,CAAC,GAAIC,EAAgB,CAAC,CAE7D,CACL,CAIA,MAAMC,UAAkCC,CAAe,CAQnD,YAAYC,EAAiBN,EAAMC,EAAQ,CACvC,MAAMA,CAAM,EACZ,KAAK,kBAAkB,QAASD,CAAI,EACpC,QAASE,EAAI,EAAGA,EAAII,EAAiBJ,IACjC,KAAK,mBAAmB,UAAUA,CAAC,GAAIC,EAAgB,CAAC,CAE/D,CACL,CAIO,MAAMI,UAAqCV,CAA0B,CACxE,YAAYI,EAAQ,CAChB,MAAM,EAAGO,EAAiBP,CAAM,CACnC,CAKD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIC,CAAS,EAErE,MAAMC,EAASF,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACvE,OAAAE,EAAO,IAAI,KAAK,aAAa,SAAS,EAAE,SAASF,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAAC,EAClGE,CACV,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAC,EAAc,+BAAyEL,CAA4B,EAI5G,MAAMM,UAAqChB,CAA0B,CACxE,YAAYI,EAAQ,CAChB,MAAM,EAAGa,EAAiBb,CAAM,CACnC,CACD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIM,CAAS,EAErE,MAAMJ,EAASF,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACvE,OAAAE,EAAO,IAAI,KAAK,aAAa,SAAS,EAAE,SAASF,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAAC,EAClJE,CACV,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAC,EAAc,+BAAyEC,CAA4B,EAI5G,MAAMG,UAAqCnB,CAA0B,CACxE,YAAYI,EAAQ,CAChB,MAAM,EAAGgB,EAAiBhB,CAAM,CACnC,CACD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIS,CAAS,EAErE,MAAMP,EAASF,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACvE,OAAAE,EAAO,IAAI,KAAK,aAAa,SAAS,EAAE,SAASF,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAAC,EAClME,CACV,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAC,EAAc,+BAAyEI,CAA4B,EAM5G,MAAMG,UAAoCtB,CAA0B,CACvE,YAAYI,EAAQ,CAChB,MAAM,GAAImB,EAAgBnB,CAAM,CACnC,CACD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIY,CAAQ,EAEpE,MAAMC,EAASb,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACvE,OAAI,KAAK,QAAQ,mBACba,EAAO,IAAI,KAAK,aAAa,SAAS,EAAE,SAASb,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,CAAC,EAG/wBa,EAAO,IAAI,KAAK,aAAa,SAAS,EAAE,SAASb,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,EAAG,KAAK,aAAa,UAAU,EAAE,SAASA,CAAO,CAAC,EAE5wBa,CACV,CACD,cAAe,CACX,MAAO,6BACV,CACL,CACAV,EAAc,8BAAuEO,CAA2B,EAIzG,MAAMI,UAAsC1B,CAA0B,CACzE,YAAYI,EAAQ,CAChB,MAAM,EAAGuB,EAAkBvB,CAAM,CACpC,CACD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIgB,CAAmB,EAE/E,MAAMH,EAASb,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACjEiB,EAAQ,KAAK,QAAQ,mBACrB,CAEE,KAAK,aAAa,SAAS,EAAE,SAASjB,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAChD,EACC,CACE,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAC7D,EACQ,OAAAa,EAAO,UAAUI,CAAK,EACfJ,CACV,CACD,cAAe,CACX,MAAO,+BACV,CACL,CACAV,EAAc,gCAA2EW,CAA6B,EAI/G,MAAMI,UAAsC9B,CAA0B,CACzE,YAAYI,EAAQ,CAChB,MAAM,EAAG2B,EAAkB3B,CAAM,CACpC,CACD,aAAaQ,EAAS,CACbA,EAAQ,sBAAsB,KAAM,cAAc,GACnDA,EAAQ,sBAAsB,KAAM,eAAgB,IAAIoB,CAAmB,EAE/E,MAAMP,EAASb,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EACjEiB,EAAQ,KAAK,QAAQ,mBACrB,CAEE,KAAK,aAAa,SAAS,EAAE,SAASjB,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAChD,EACC,CACE,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,EAC7C,KAAK,aAAa,SAAS,EAAE,SAASA,CAAO,CAC7D,EACQ,OAAAa,EAAO,UAAUI,CAAK,EACfJ,CACV,CACD,cAAe,CACX,MAAO,+BACV,CACL,CACAV,EAAc,gCAA2Ee,CAA6B,EAI/G,MAAMG,UAAqC1B,CAA0B,CACxE,YAAYH,EAAQ,CAChB,MAAM,EAAGO,EAAiBP,CAAM,CACnC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQrB,EAAQ,OAChB,KAAK,aAAa,OAAO,EAAE,SAASqB,EAAOtB,CAAO,GAEtD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,CAC3D,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAG,EAAc,+BAAyEkB,CAA4B,EAI5G,MAAME,UAAqC5B,CAA0B,CACxE,YAAYH,EAAQ,CAChB,MAAM,EAAGa,EAAiBb,CAAM,CACnC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQhB,EAAQ,OAChB,KAAK,aAAa,OAAO,EAAE,SAASgB,EAAOtB,CAAO,GAEtD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,CAC3D,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAG,EAAc,+BAAyEoB,CAA4B,EAI5G,MAAMC,UAAqC7B,CAA0B,CACxE,YAAYH,EAAQ,CAChB,MAAM,EAAGgB,EAAiBhB,CAAM,CACnC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQb,EAAQ,OAChB,KAAK,aAAa,OAAO,EAAE,SAASa,EAAOtB,CAAO,GAEtD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,EACxD,KAAK,cAAc,UAAU,EAAE,SAASsB,EAAM,EAAGtB,CAAO,CAC3D,CACD,cAAe,CACX,MAAO,8BACV,CACL,CACAG,EAAc,+BAAyEqB,CAA4B,EAI5G,MAAMC,UAAoC9B,CAA0B,CACvE,YAAYH,EAAQ,CAChB,MAAM,GAAImB,EAAgBnB,CAAM,CACnC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQV,EAAO,WACf,KAAK,aAAa,OAAO,EAAE,SAASU,EAAOtB,CAAO,GAEtD,QAASP,EAAI,EAAGA,EAAI,GAAIA,IACpB,KAAK,cAAc,UAAUA,CAAC,EAAE,EAAE,SAAS6B,EAAM,EAAE7B,CAAC,EAAGO,CAAO,CAErE,CACD,cAAe,CACX,MAAO,6BACV,CACL,CACAG,EAAc,8BAAuEsB,CAA2B,EAIzG,MAAMC,UAAsC/B,CAA0B,CACzE,YAAYH,EAAQ,CAChB,MAAM,EAAGuB,EAAkBvB,CAAM,CACpC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQ,IAAIN,EACZ,KAAK,aAAa,OAAO,EAAE,SAASM,EAAOtB,CAAO,GAEtD,QAASP,EAAI,EAAGA,EAAI,EAAGA,IACnB,KAAK,cAAc,UAAUA,CAAC,EAAE,EAAE,SAAS6B,EAAM,EAAE7B,CAAC,EAAGO,CAAO,CAErE,CACD,cAAe,CACX,MAAO,+BACV,CACL,CACAG,EAAc,gCAA2EuB,CAA6B,EAI/G,MAAMC,UAAsChC,CAA0B,CACzE,YAAYH,EAAQ,CAChB,MAAM,EAAG2B,EAAkB3B,CAAM,CACpC,CACD,eAAeQ,EAAS,CACpB,IAAIsB,EAAQ,KAAK,aAAa,OAAO,GAAG,SAAStB,CAAO,EACnDsB,IACDA,EAAQ,IAAIF,EACZ,KAAK,aAAa,OAAO,EAAE,SAASE,EAAOtB,CAAO,GAEtD,QAASP,EAAI,EAAGA,EAAI,EAAGA,IACnB,KAAK,cAAc,UAAUA,CAAC,EAAE,EAAE,SAAS6B,EAAM,EAAE7B,CAAC,EAAGO,CAAO,CAErE,CACD,cAAe,CACX,MAAO,+BACV,CACL,CACAG,EAAc,gCAA2EwB,CAA6B", "x_google_ignoreList": [0]}