import{F as s}from"./KHR_interactivity-DVSiPm30.js";import{c as a,R as o}from"./declarationMapper-r-RREw_K.js";import{R as e}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class r extends s{constructor(t){super(t),this.condition=this.registerDataInput("condition",a),this.onTrue=this.registerDataInput("onTrue",o),this.onFalse=this.registerDataInput("onFalse",o),this.output=this.registerDataOutput("output",o)}_updateOutputs(t){const i=this.condition.getValue(t);this.output.setValue(i?this.onTrue.getValue(t):this.onFalse.getValue(t),t)}getClassName(){return"FlowGraphConditionalBlock"}}e("FlowGraphConditionalBlock",r);export{r as FlowGraphConditionalDataBlock};
//# sourceMappingURL=flowGraphConditionalDataBlock-CQ_HDF-s.js.map
