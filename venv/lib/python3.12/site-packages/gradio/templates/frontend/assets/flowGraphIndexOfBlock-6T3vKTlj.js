import{F as i}from"./KHR_interactivity-DVSiPm30.js";import{R as r,j as o,F as a}from"./declarationMapper-r-RREw_K.js";import{R as p}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class h extends i{constructor(e){super(e),this.config=e,this.object=this.registerDataInput("object",r),this.array=this.registerDataInput("array",r),this.index=this.registerDataOutput("index",o,new a(-1))}_updateOutputs(e){const s=this.object.getValue(e),t=this.array.getValue(e);t&&this.index.setValue(new a(t.indexOf(s)),e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphIndexOfBlock"}}p("FlowGraphIndexOfBlock",h);export{h as FlowGraphIndexOfBlock};
//# sourceMappingURL=flowGraphIndexOfBlock-6T3vKTlj.js.map
