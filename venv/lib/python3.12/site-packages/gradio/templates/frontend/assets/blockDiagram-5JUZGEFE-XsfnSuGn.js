import{_ as d,d as R,l as L,a1 as oe,F as rt,a2 as j,a3 as Wt,a4 as he,u as $,a5 as de,j as ge,t as ue,A as pe,a6 as fe,a7 as xt,e as xe,i as Tt}from"./mermaid.core-DGK6UhOk.js";import{c as ye}from"./clone-Bxj9bjXK.js";import{G as be}from"./graph-w0q23cSo.js";import{c as we}from"./channel-BVcFyT3i.js";import{s as O}from"./select-BigU4G0v.js";import{l as me,k as Le}from"./step-Ce-xBr2D.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./_baseUniq-CIsjNB2G.js";var yt=function(){var e=d(function(D,y,g,f){for(g=g||{},f=D.length;f--;g[D[f]]=y);return g},"o"),t=[1,7],s=[1,13],n=[1,14],i=[1,15],r=[1,19],a=[1,16],l=[1,17],c=[1,18],u=[8,30],h=[8,21,28,29,30,31,32,40,44,47],x=[1,23],w=[1,24],b=[8,15,16,21,28,29,30,31,32,40,44,47],S=[8,15,16,21,27,28,29,30,31,32,40,44,47],E=[1,49],k={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(y,g,f,m,v,o,W){var p=o.length-1;switch(v){case 4:m.getLogger().debug("Rule: separator (NL) ");break;case 5:m.getLogger().debug("Rule: separator (Space) ");break;case 6:m.getLogger().debug("Rule: separator (EOF) ");break;case 7:m.getLogger().debug("Rule: hierarchy: ",o[p-1]),m.setHierarchy(o[p-1]);break;case 8:m.getLogger().debug("Stop NL ");break;case 9:m.getLogger().debug("Stop EOF ");break;case 10:m.getLogger().debug("Stop NL2 ");break;case 11:m.getLogger().debug("Stop EOF2 ");break;case 12:m.getLogger().debug("Rule: statement: ",o[p]),typeof o[p].length=="number"?this.$=o[p]:this.$=[o[p]];break;case 13:m.getLogger().debug("Rule: statement #2: ",o[p-1]),this.$=[o[p-1]].concat(o[p]);break;case 14:m.getLogger().debug("Rule: link: ",o[p],y),this.$={edgeTypeStr:o[p],label:""};break;case 15:m.getLogger().debug("Rule: LABEL link: ",o[p-3],o[p-1],o[p]),this.$={edgeTypeStr:o[p],label:o[p-1]};break;case 18:const I=parseInt(o[p]),Z=m.generateId();this.$={id:Z,type:"space",label:"",width:I,children:[]};break;case 23:m.getLogger().debug("Rule: (nodeStatement link node) ",o[p-2],o[p-1],o[p]," typestr: ",o[p-1].edgeTypeStr);const V=m.edgeStrToEdgeData(o[p-1].edgeTypeStr);this.$=[{id:o[p-2].id,label:o[p-2].label,type:o[p-2].type,directions:o[p-2].directions},{id:o[p-2].id+"-"+o[p].id,start:o[p-2].id,end:o[p].id,label:o[p-1].label,type:"edge",directions:o[p].directions,arrowTypeEnd:V,arrowTypeStart:"arrow_open"},{id:o[p].id,label:o[p].label,type:m.typeStr2Type(o[p].typeStr),directions:o[p].directions}];break;case 24:m.getLogger().debug("Rule: nodeStatement (abc88 node size) ",o[p-1],o[p]),this.$={id:o[p-1].id,label:o[p-1].label,type:m.typeStr2Type(o[p-1].typeStr),directions:o[p-1].directions,widthInColumns:parseInt(o[p],10)};break;case 25:m.getLogger().debug("Rule: nodeStatement (node) ",o[p]),this.$={id:o[p].id,label:o[p].label,type:m.typeStr2Type(o[p].typeStr),directions:o[p].directions,widthInColumns:1};break;case 26:m.getLogger().debug("APA123",this?this:"na"),m.getLogger().debug("COLUMNS: ",o[p]),this.$={type:"column-setting",columns:o[p]==="auto"?-1:parseInt(o[p])};break;case 27:m.getLogger().debug("Rule: id-block statement : ",o[p-2],o[p-1]),m.generateId(),this.$={...o[p-2],type:"composite",children:o[p-1]};break;case 28:m.getLogger().debug("Rule: blockStatement : ",o[p-2],o[p-1],o[p]);const at=m.generateId();this.$={id:at,type:"composite",label:"",children:o[p-1]};break;case 29:m.getLogger().debug("Rule: node (NODE_ID separator): ",o[p]),this.$={id:o[p]};break;case 30:m.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",o[p-1],o[p]),this.$={id:o[p-1],label:o[p].label,typeStr:o[p].typeStr,directions:o[p].directions};break;case 31:m.getLogger().debug("Rule: dirList: ",o[p]),this.$=[o[p]];break;case 32:m.getLogger().debug("Rule: dirList: ",o[p-1],o[p]),this.$=[o[p-1]].concat(o[p]);break;case 33:m.getLogger().debug("Rule: nodeShapeNLabel: ",o[p-2],o[p-1],o[p]),this.$={typeStr:o[p-2]+o[p],label:o[p-1]};break;case 34:m.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",o[p-3],o[p-2]," #3:",o[p-1],o[p]),this.$={typeStr:o[p-3]+o[p],label:o[p-2],directions:o[p-1]};break;case 35:case 36:this.$={type:"classDef",id:o[p-1].trim(),css:o[p].trim()};break;case 37:this.$={type:"applyClass",id:o[p-1].trim(),styleClass:o[p].trim()};break;case 38:this.$={type:"applyStyles",id:o[p-1].trim(),stylesStr:o[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{8:[1,20]},e(u,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:s,29:n,31:i,32:r,40:a,44:l,47:c}),e(h,[2,16],{14:22,15:x,16:w}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(b,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,32:r},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(S,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(u,[2,13]),{26:35,32:r},{32:[2,14]},{17:[1,36]},e(b,[2,24]),{11:37,13:4,14:22,15:x,16:w,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(S,[2,30]),{18:[1,43]},{18:[1,44]},e(b,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{37:[1,47]},{34:48,35:E},{15:[1,50]},e(h,[2,27]),e(S,[2,33]),{39:[1,51]},{34:52,35:E,39:[2,31]},{32:[2,15]},e(S,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(y,g){if(g.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=g,f}},"parseError"),parse:d(function(y){var g=this,f=[0],m=[],v=[null],o=[],W=this.table,p="",I=0,Z=0,V=2,at=1,ne=o.slice.call(arguments,1),z=Object.create(this.lexer),q={yy:{}};for(var gt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,gt)&&(q.yy[gt]=this.yy[gt]);z.setInput(y,q.yy),q.yy.lexer=z,q.yy.parser=this,typeof z.yylloc>"u"&&(z.yylloc={});var ut=z.yylloc;o.push(ut);var le=z.options&&z.options.ranges;typeof q.yy.parseError=="function"?this.parseError=q.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ce(P){f.length=f.length-2*P,v.length=v.length-P,o.length=o.length-P}d(ce,"popStack");function Dt(){var P;return P=m.pop()||z.lex()||at,typeof P!="number"&&(P instanceof Array&&(m=P,P=m.pop()),P=g.symbols_[P]||P),P}d(Dt,"lex");for(var F,J,H,pt,Q={},st,G,Nt,it;;){if(J=f[f.length-1],this.defaultActions[J]?H=this.defaultActions[J]:((F===null||typeof F>"u")&&(F=Dt()),H=W[J]&&W[J][F]),typeof H>"u"||!H.length||!H[0]){var ft="";it=[];for(st in W[J])this.terminals_[st]&&st>V&&it.push("'"+this.terminals_[st]+"'");z.showPosition?ft="Parse error on line "+(I+1)+`:
`+z.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[F]||F)+"'":ft="Parse error on line "+(I+1)+": Unexpected "+(F==at?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(ft,{text:z.match,token:this.terminals_[F]||F,line:z.yylineno,loc:ut,expected:it})}if(H[0]instanceof Array&&H.length>1)throw new Error("Parse Error: multiple actions possible at state: "+J+", token: "+F);switch(H[0]){case 1:f.push(F),v.push(z.yytext),o.push(z.yylloc),f.push(H[1]),F=null,Z=z.yyleng,p=z.yytext,I=z.yylineno,ut=z.yylloc;break;case 2:if(G=this.productions_[H[1]][1],Q.$=v[v.length-G],Q._$={first_line:o[o.length-(G||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(G||1)].first_column,last_column:o[o.length-1].last_column},le&&(Q._$.range=[o[o.length-(G||1)].range[0],o[o.length-1].range[1]]),pt=this.performAction.apply(Q,[p,Z,I,q.yy,H[1],v,o].concat(ne)),typeof pt<"u")return pt;G&&(f=f.slice(0,-1*G*2),v=v.slice(0,-1*G),o=o.slice(0,-1*G)),f.push(this.productions_[H[1]][0]),v.push(Q.$),o.push(Q._$),Nt=W[f[f.length-2]][f[f.length-1]],f.push(Nt);break;case 3:return!0}}return!0},"parse")},B=function(){var D={EOF:1,parseError:d(function(g,f){if(this.yy.parser)this.yy.parser.parseError(g,f);else throw new Error(g)},"parseError"),setInput:d(function(y,g){return this.yy=g||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var g=y.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:d(function(y){var g=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var m=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var v=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===m.length?this.yylloc.first_column:0)+m[m.length-f.length].length-f[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[v[0],v[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(y){this.unput(this.match.slice(y))},"less"),pastInput:d(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var y=this.pastInput(),g=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(y,g){var f,m,v;if(this.options.backtrack_lexer&&(v={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(v.yylloc.range=this.yylloc.range.slice(0))),m=y[0].match(/(?:\r\n?|\n).*/g),m&&(this.yylineno+=m.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:m?m[m.length-1].length-m[m.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var o in v)this[o]=v[o];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,g,f,m;this._more||(this.yytext="",this.match="");for(var v=this._currentRules(),o=0;o<v.length;o++)if(f=this._input.match(this.rules[v[o]]),f&&(!g||f[0].length>g[0].length)){if(g=f,m=o,this.options.backtrack_lexer){if(y=this.test_match(f,v[o]),y!==!1)return y;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(y=this.test_match(g,v[m]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,f,m,v){switch(m){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;case 2:return g.getLogger().debug("Found nl-block"),31;case 3:return g.getLogger().debug("Found space-block"),29;case 4:g.getLogger().debug(".",f.yytext);break;case 5:g.getLogger().debug("_",f.yytext);break;case 6:return 5;case 7:return f.yytext=-1,28;case 8:return f.yytext=f.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",f.yytext),28;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",f.yytext),"STR";case 15:return f.yytext=f.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;case 16:return f.yytext="1",g.getLogger().debug("COLUMNS (LEX)",f.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 66:return this.pushState("NODE"),36;case 67:return this.pushState("NODE"),36;case 68:return this.pushState("NODE"),36;case 69:return this.pushState("NODE"),36;case 70:return this.pushState("NODE"),36;case 71:return this.pushState("NODE"),36;case 72:return this.pushState("NODE"),36;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;case 75:return g.getLogger().debug("Lex: NODE_ID",f.yytext),32;case 76:return g.getLogger().debug("Lex: EOF",f.yytext),8;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",f.yytext),"DIR";case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",f.yytext),"DIR";case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",f.yytext),"DIR";case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",f.yytext),"DIR";case 91:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",f.yytext),"DIR";case 92:return f.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 94:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 95:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 96:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 97:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 98:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 99:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 105:return g.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return D}();k.lexer=B;function _(){this.yy={}}return d(_,"Parser"),_.prototype=k,k.Parser=_,new _}();yt.parser=yt;var Se=yt,X=new Map,St=[],bt=new Map,Bt="color",Ct="fill",ke="bgFill",Pt=",",ve=R(),ct=new Map,Ee=d(e=>xe.sanitizeText(e,ve),"sanitizeText"),_e=d(function(e,t=""){let s=ct.get(e);s||(s={id:e,styles:[],textStyles:[]},ct.set(e,s)),t?.split(Pt).forEach(n=>{const i=n.replace(/([^;]*);/,"$1").trim();if(RegExp(Bt).exec(n)){const a=i.replace(Ct,ke).replace(Bt,Ct);s.textStyles.push(a)}s.styles.push(i)})},"addStyleClass"),De=d(function(e,t=""){const s=X.get(e);t!=null&&(s.styles=t.split(Pt))},"addStyle2Node"),Ne=d(function(e,t){e.split(",").forEach(function(s){let n=X.get(s);if(n===void 0){const i=s.trim();n={id:i,type:"na",children:[]},X.set(i,n)}n.classes||(n.classes=[]),n.classes.push(t)})},"setCssClass"),Yt=d((e,t)=>{const s=e.flat(),n=[];for(const i of s){if(i.label&&(i.label=Ee(i.label)),i.type==="classDef"){_e(i.id,i.css);continue}if(i.type==="applyClass"){Ne(i.id,i?.styleClass??"");continue}if(i.type==="applyStyles"){i?.stylesStr&&De(i.id,i?.stylesStr);continue}if(i.type==="column-setting")t.columns=i.columns??-1;else if(i.type==="edge"){const r=(bt.get(i.id)??0)+1;bt.set(i.id,r),i.id=r+"-"+i.id,St.push(i)}else{i.label||(i.type==="composite"?i.label="":i.label=i.id);const r=X.get(i.id);if(r===void 0?X.set(i.id,i):(i.type!=="na"&&(r.type=i.type),i.label!==i.id&&(r.label=i.label)),i.children&&Yt(i.children,i),i.type==="space"){const a=i.width??1;for(let l=0;l<a;l++){const c=ye(i);c.id=c.id+"-"+l,X.set(c.id,c),n.push(c)}}else r===void 0&&n.push(i)}}t.children=n},"populateBlockDatabase"),kt=[],et={id:"root",type:"composite",children:[],columns:-1},Te=d(()=>{L.debug("Clear called"),ue(),et={id:"root",type:"composite",children:[],columns:-1},X=new Map([["root",et]]),kt=[],ct=new Map,St=[],bt=new Map},"clear");function Ht(e){switch(L.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return L.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(Ht,"typeStr2Type");function Kt(e){switch(L.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(Kt,"edgeTypeStr2Type");function Xt(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}d(Xt,"edgeStrToEdgeData");var It=0,Be=d(()=>(It++,"id-"+Math.random().toString(36).substr(2,12)+"-"+It),"generateId"),Ce=d(e=>{et.children=e,Yt(e,et),kt=et.children},"setHierarchy"),Ie=d(e=>{const t=X.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Oe=d(()=>[...X.values()],"getBlocksFlat"),Re=d(()=>kt||[],"getBlocks"),ze=d(()=>St,"getEdges"),Ae=d(e=>X.get(e),"getBlock"),Me=d(e=>{X.set(e.id,e)},"setBlock"),Fe=d(()=>console,"getLogger"),We=d(function(){return ct},"getClasses"),Pe={getConfig:d(()=>rt().block,"getConfig"),typeStr2Type:Ht,edgeTypeStr2Type:Kt,edgeStrToEdgeData:Xt,getLogger:Fe,getBlocksFlat:Oe,getBlocks:Re,getEdges:ze,setHierarchy:Ce,getBlock:Ae,setBlock:Me,getColumns:Ie,getClasses:We,clear:Te,generateId:Be},Ye=Pe,nt=d((e,t)=>{const s=we,n=s(e,"r"),i=s(e,"g"),r=s(e,"b");return pe(n,i,r,t)},"fade"),He=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${nt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${nt(e.mainBkg,.5)};
    fill: ${nt(e.clusterBkg,.5)};
    stroke: ${nt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),Ke=He,Xe=d((e,t,s,n)=>{t.forEach(i=>{tr[i](e,s,n)})},"insertMarkers"),Ue=d((e,t,s)=>{L.trace("Making markers for ",s),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),je=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),Ve=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),Ge=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),Ze=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),qe=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),Je=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),Qe=d((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),$e=d((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),tr={extension:Ue,composition:je,aggregation:Ve,dependency:Ge,lollipop:Ze,point:qe,circle:Je,cross:Qe,barb:$e},er=Xe,C=R()?.block?.padding??8;function Ut(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const s=t%e,n=Math.floor(t/e);return{px:s,py:n}}d(Ut,"calculateBlockPosition");var rr=d(e=>{let t=0,s=0;for(const n of e.children){const{width:i,height:r,x:a,y:l}=n.size??{width:0,height:0,x:0,y:0};L.debug("getMaxChildSize abc95 child:",n.id,"width:",i,"height:",r,"x:",a,"y:",l,n.type),n.type!=="space"&&(i>t&&(t=i/(e.widthInColumns??1)),r>s&&(s=r))}return{width:t,height:s}},"getMaxChildSize");function ot(e,t,s=0,n=0){L.debug("setBlockSizes abc95 (start)",e.id,e?.size?.x,"block width =",e?.size,"sieblingWidth",s),e?.size?.width||(e.size={width:s,height:n,x:0,y:0});let i=0,r=0;if(e.children?.length>0){for(const b of e.children)ot(b,t);const a=rr(e);i=a.width,r=a.height,L.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",i,r);for(const b of e.children)b.size&&(L.debug(`abc95 Setting size of children of ${e.id} id=${b.id} ${i} ${r} ${JSON.stringify(b.size)}`),b.size.width=i*(b.widthInColumns??1)+C*((b.widthInColumns??1)-1),b.size.height=r,b.size.x=0,b.size.y=0,L.debug(`abc95 updating size of ${e.id} children child:${b.id} maxWidth:${i} maxHeight:${r}`));for(const b of e.children)ot(b,t,i,r);const l=e.columns??-1;let c=0;for(const b of e.children)c+=b.widthInColumns??1;let u=e.children.length;l>0&&l<c&&(u=l);const h=Math.ceil(c/u);let x=u*(i+C)+C,w=h*(r+C)+C;if(x<s){L.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${s} sieblingHeight ${n} width ${x}`),x=s,w=n;const b=(s-u*C-C)/u,S=(n-h*C-C)/h;L.debug("Size indata abc88",e.id,"childWidth",b,"maxWidth",i),L.debug("Size indata abc88",e.id,"childHeight",S,"maxHeight",r),L.debug("Size indata abc88 xSize",u,"padding",C);for(const E of e.children)E.size&&(E.size.width=b,E.size.height=S,E.size.x=0,E.size.y=0)}if(L.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${h} columns ${l}${e.children.length} width=${Math.max(x,e.size?.width||0)}`),x<(e?.size?.width||0)){x=e?.size?.width||0;const b=l>0?Math.min(e.children.length,l):e.children.length;if(b>0){const S=(x-b*C-C)/b;L.debug("abc95 (growing to fit) width",e.id,x,e.size?.width,S);for(const E of e.children)E.size&&(E.size.width=S)}}e.size={width:x,height:w,x:0,y:0}}L.debug("setBlockSizes abc94 (done)",e.id,e?.size?.x,e?.size?.width,e?.size?.y,e?.size?.height)}d(ot,"setBlockSizes");function vt(e,t){L.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`);const s=e.columns??-1;if(L.debug("layoutBlocks columns abc95",e.id,"=>",s,e),e.children&&e.children.length>0){const n=e?.children[0]?.size?.width??0,i=e.children.length*n+(e.children.length-1)*C;L.debug("widthOfChildren 88",i,"posX");let r=0;L.debug("abc91 block?.size?.x",e.id,e?.size?.x);let a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-C,l=0;for(const c of e.children){const u=e;if(!c.size)continue;const{width:h,height:x}=c.size,{px:w,py:b}=Ut(s,r);if(b!=l&&(l=b,a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-C,L.debug("New row in layout for block",e.id," and child ",c.id,l)),L.debug(`abc89 layout blocks (child) id: ${c.id} Pos: ${r} (px, py) ${w},${b} (${u?.size?.x},${u?.size?.y}) parent: ${u.id} width: ${h}${C}`),u.size){const S=h/2;c.size.x=a+C+S,L.debug(`abc91 layout blocks (calc) px, pyid:${c.id} startingPos=X${a} new startingPosX${c.size.x} ${S} padding=${C} width=${h} halfWidth=${S} => x:${c.size.x} y:${c.size.y} ${c.widthInColumns} (width * (child?.w || 1)) / 2 ${h*(c?.widthInColumns??1)/2}`),a=c.size.x+S,c.size.y=u.size.y-u.size.height/2+b*(x+C)+x/2+C,L.debug(`abc88 layout blocks (calc) px, pyid:${c.id}startingPosX${a}${C}${S}=>x:${c.size.x}y:${c.size.y}${c.widthInColumns}(width * (child?.w || 1)) / 2${h*(c?.widthInColumns??1)/2}`)}c.children&&vt(c),r+=c?.widthInColumns??1,L.debug("abc88 columnsPos",c,r)}}L.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`)}d(vt,"layoutBlocks");function Et(e,{minX:t,minY:s,maxX:n,maxY:i}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:r,y:a,width:l,height:c}=e.size;r-l/2<t&&(t=r-l/2),a-c/2<s&&(s=a-c/2),r+l/2>n&&(n=r+l/2),a+c/2>i&&(i=a+c/2)}if(e.children)for(const r of e.children)({minX:t,minY:s,maxX:n,maxY:i}=Et(r,{minX:t,minY:s,maxX:n,maxY:i}));return{minX:t,minY:s,maxX:n,maxY:i}}d(Et,"findBounds");function jt(e){const t=e.getBlock("root");if(!t)return;ot(t,e,0,0),vt(t),L.debug("getBlocks",JSON.stringify(t,null,2));const{minX:s,minY:n,maxX:i,maxY:r}=Et(t),a=r-n,l=i-s;return{x:s,y:n,width:l,height:a}}d(jt,"layout");function wt(e,t){t&&e.attr("style",t)}d(wt,"applyStyle");function Vt(e){const t=O(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),s=t.append("xhtml:div"),n=e.label,i=e.isNode?"nodeLabel":"edgeLabel",r=s.append("span");return r.html(n),wt(r,e.labelStyle),r.attr("class",i),wt(s,e.labelStyle),s.style("display","inline-block"),s.style("white-space","nowrap"),s.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(Vt,"addHtmlLabel");var ar=d((e,t,s,n)=>{let i=e||"";if(typeof i=="object"&&(i=i[0]),j(R().flowchart.htmlLabels)){i=i.replace(/\\n|\n/g,"<br />"),L.debug("vertexText"+i);const r={isNode:n,label:fe(xt(i)),labelStyle:t.replace("fill:","color:")};return Vt(r)}else{const r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("style",t.replace("color:","fill:"));let a=[];typeof i=="string"?a=i.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(i)?a=i:a=[];for(const l of a){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),s?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=l.trim(),r.appendChild(c)}return r}},"createLabel"),K=ar,sr=d((e,t,s,n,i)=>{t.arrowTypeStart&&Ot(e,"start",t.arrowTypeStart,s,n,i),t.arrowTypeEnd&&Ot(e,"end",t.arrowTypeEnd,s,n,i)},"addEdgeMarkers"),ir={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Ot=d((e,t,s,n,i,r)=>{const a=ir[s];if(!a){L.warn(`Unknown arrow type: ${s}`);return}const l=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${n}#${i}_${r}-${a}${l})`)},"addEdgeMarker"),mt={},M={},nr=d((e,t)=>{const s=R(),n=j(s.flowchart.htmlLabels),i=t.labelType==="markdown"?Wt(e,t.label,{style:t.labelStyle,useHtmlLabels:n,addSvgBackground:!0},s):K(t.label,t.labelStyle),r=e.insert("g").attr("class","edgeLabel"),a=r.insert("g").attr("class","label");a.node().appendChild(i);let l=i.getBBox();if(n){const u=i.children[0],h=O(i);l=u.getBoundingClientRect(),h.attr("width",l.width),h.attr("height",l.height)}a.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),mt[t.id]=r,t.width=l.width,t.height=l.height;let c;if(t.startLabelLeft){const u=K(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startLeft=h,tt(c,t.startLabelLeft)}if(t.startLabelRight){const u=K(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=h.node().appendChild(u),x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startRight=h,tt(c,t.startLabelRight)}if(t.endLabelLeft){const u=K(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endLeft=h,tt(c,t.endLabelLeft)}if(t.endLabelRight){const u=K(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endRight=h,tt(c,t.endLabelRight)}return i},"insertEdgeLabel");function tt(e,t){R().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(tt,"setTerminalWidth");var lr=d((e,t)=>{L.debug("Moving label abc88 ",e.id,e.label,mt[e.id],t);let s=t.updatedPath?t.updatedPath:t.originalPath;const n=R(),{subGraphTitleTotalMargin:i}=he(n);if(e.label){const r=mt[e.id];let a=e.x,l=e.y;if(s){const c=$.calcLabelPosition(s);L.debug("Moving label "+e.label+" from (",a,",",l,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(a=c.x,l=c.y)}r.attr("transform",`translate(${a}, ${l+i/2})`)}if(e.startLabelLeft){const r=M[e.id].startLeft;let a=e.x,l=e.y;if(s){const c=$.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.startLabelRight){const r=M[e.id].startRight;let a=e.x,l=e.y;if(s){const c=$.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelLeft){const r=M[e.id].endLeft;let a=e.x,l=e.y;if(s){const c=$.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelRight){const r=M[e.id].endRight;let a=e.x,l=e.y;if(s){const c=$.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",s);a=c.x,l=c.y}r.attr("transform",`translate(${a}, ${l})`)}},"positionEdgeLabel"),cr=d((e,t)=>{const s=e.x,n=e.y,i=Math.abs(t.x-s),r=Math.abs(t.y-n),a=e.width/2,l=e.height/2;return i>=a||r>=l},"outsideNode"),or=d((e,t,s)=>{L.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(s)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const n=e.x,i=e.y,r=Math.abs(n-s.x),a=e.width/2;let l=s.x<t.x?a-r:a+r;const c=e.height/2,u=Math.abs(t.y-s.y),h=Math.abs(t.x-s.x);if(Math.abs(i-t.y)*a>Math.abs(n-t.x)*c){let x=s.y<t.y?t.y-c-i:i-c-t.y;l=h*x/u;const w={x:s.x<t.x?s.x+l:s.x-h+l,y:s.y<t.y?s.y+u-x:s.y-u+x};return l===0&&(w.x=t.x,w.y=t.y),h===0&&(w.x=t.x),u===0&&(w.y=t.y),L.debug(`abc89 topp/bott calc, Q ${u}, q ${x}, R ${h}, r ${l}`,w),w}else{s.x<t.x?l=t.x-a-n:l=n-a-t.x;let x=u*l/h,w=s.x<t.x?s.x+h-l:s.x-h+l,b=s.y<t.y?s.y+x:s.y-x;return L.debug(`sides calc abc89, Q ${u}, q ${x}, R ${h}, r ${l}`,{_x:w,_y:b}),l===0&&(w=t.x,b=t.y),h===0&&(w=t.x),u===0&&(b=t.y),{x:w,y:b}}},"intersection"),Rt=d((e,t)=>{L.debug("abc88 cutPathAtIntersect",e,t);let s=[],n=e[0],i=!1;return e.forEach(r=>{if(!cr(t,r)&&!i){const a=or(t,n,r);let l=!1;s.forEach(c=>{l=l||c.x===a.x&&c.y===a.y}),s.some(c=>c.x===a.x&&c.y===a.y)||s.push(a),i=!0}else n=r,i||s.push(r)}),s},"cutPathAtIntersect"),hr=d(function(e,t,s,n,i,r,a){let l=s.points;L.debug("abc88 InsertEdge: edge=",s,"e=",t);let c=!1;const u=r.node(t.v);var h=r.node(t.w);h?.intersect&&u?.intersect&&(l=l.slice(1,s.points.length-1),l.unshift(u.intersect(l[0])),l.push(h.intersect(l[l.length-1]))),s.toCluster&&(L.debug("to cluster abc88",n[s.toCluster]),l=Rt(s.points,n[s.toCluster].node),c=!0),s.fromCluster&&(L.debug("from cluster abc88",n[s.fromCluster]),l=Rt(l.reverse(),n[s.fromCluster].node).reverse(),c=!0);const x=l.filter(y=>!Number.isNaN(y.y));let w=Le;s.curve&&(i==="graph"||i==="flowchart")&&(w=s.curve);const{x:b,y:S}=de(s),E=me().x(b).y(S).curve(w);let k;switch(s.thickness){case"normal":k="edge-thickness-normal";break;case"thick":k="edge-thickness-thick";break;case"invisible":k="edge-thickness-thick";break;default:k=""}switch(s.pattern){case"solid":k+=" edge-pattern-solid";break;case"dotted":k+=" edge-pattern-dotted";break;case"dashed":k+=" edge-pattern-dashed";break}const B=e.append("path").attr("d",E(x)).attr("id",s.id).attr("class"," "+k+(s.classes?" "+s.classes:"")).attr("style",s.style);let _="";(R().flowchart.arrowMarkerAbsolute||R().state.arrowMarkerAbsolute)&&(_=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,_=_.replace(/\(/g,"\\("),_=_.replace(/\)/g,"\\)")),sr(B,s,_,a,i);let D={};return c&&(D.updatedPath=l),D.originalPath=s.points,D},"insertEdge"),dr=d(e=>{const t=new Set;for(const s of e)switch(s){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(s);break}return t},"expandAndDeduplicateDirections"),gr=d((e,t,s)=>{const n=dr(e),i=2,r=t.height+2*s.padding,a=r/i,l=t.width+2*a+s.padding,c=s.padding/2;return n.has("right")&&n.has("left")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:a,y:0},{x:l/2,y:2*c},{x:l-a,y:0},{x:l,y:0},{x:l,y:-r/3},{x:l+2*c,y:-r/2},{x:l,y:-2*r/3},{x:l,y:-r},{x:l-a,y:-r},{x:l/2,y:-r-2*c},{x:a,y:-r},{x:0,y:-r},{x:0,y:-2*r/3},{x:-2*c,y:-r/2},{x:0,y:-r/3}]:n.has("right")&&n.has("left")&&n.has("up")?[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}]:n.has("right")&&n.has("left")&&n.has("down")?[{x:0,y:0},{x:a,y:-r},{x:l-a,y:-r},{x:l,y:0}]:n.has("right")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:l,y:-a},{x:l,y:-r+a},{x:0,y:-r}]:n.has("left")&&n.has("up")&&n.has("down")?[{x:l,y:0},{x:0,y:-a},{x:0,y:-r+a},{x:l,y:-r}]:n.has("right")&&n.has("left")?[{x:a,y:0},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")&&n.has("down")?[{x:l/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-r+c},{x:0,y:-r+c},{x:l/2,y:-r},{x:l,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c},{x:l,y:-c}]:n.has("right")&&n.has("up")?[{x:0,y:0},{x:l,y:-a},{x:0,y:-r}]:n.has("right")&&n.has("down")?[{x:0,y:0},{x:l,y:0},{x:0,y:-r}]:n.has("left")&&n.has("up")?[{x:l,y:0},{x:0,y:-a},{x:l,y:-r}]:n.has("left")&&n.has("down")?[{x:l,y:0},{x:0,y:0},{x:l,y:-r}]:n.has("right")?[{x:a,y:-c},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r+c}]:n.has("left")?[{x:a,y:0},{x:a,y:-c},{x:l-a,y:-c},{x:l-a,y:-r+c},{x:a,y:-r+c},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")?[{x:a,y:-c},{x:a,y:-r+c},{x:0,y:-r+c},{x:l/2,y:-r},{x:l,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c}]:n.has("down")?[{x:l/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-r+c},{x:l-a,y:-r+c},{x:l-a,y:-c},{x:l,y:-c}]:[{x:0,y:0}]},"getArrowPoints");function Gt(e,t){return e.intersect(t)}d(Gt,"intersectNode");var ur=Gt;function Zt(e,t,s,n){var i=e.x,r=e.y,a=i-n.x,l=r-n.y,c=Math.sqrt(t*t*l*l+s*s*a*a),u=Math.abs(t*s*a/c);n.x<i&&(u=-u);var h=Math.abs(t*s*l/c);return n.y<r&&(h=-h),{x:i+u,y:r+h}}d(Zt,"intersectEllipse");var qt=Zt;function Jt(e,t,s){return qt(e,t,t,s)}d(Jt,"intersectCircle");var pr=Jt;function Qt(e,t,s,n){var i,r,a,l,c,u,h,x,w,b,S,E,k,B,_;if(i=t.y-e.y,a=e.x-t.x,c=t.x*e.y-e.x*t.y,w=i*s.x+a*s.y+c,b=i*n.x+a*n.y+c,!(w!==0&&b!==0&&Lt(w,b))&&(r=n.y-s.y,l=s.x-n.x,u=n.x*s.y-s.x*n.y,h=r*e.x+l*e.y+u,x=r*t.x+l*t.y+u,!(h!==0&&x!==0&&Lt(h,x))&&(S=i*l-r*a,S!==0)))return E=Math.abs(S/2),k=a*u-l*c,B=k<0?(k-E)/S:(k+E)/S,k=r*c-i*u,_=k<0?(k-E)/S:(k+E)/S,{x:B,y:_}}d(Qt,"intersectLine");function Lt(e,t){return e*t>0}d(Lt,"sameSign");var fr=Qt,xr=$t;function $t(e,t,s){var n=e.x,i=e.y,r=[],a=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(S){a=Math.min(a,S.x),l=Math.min(l,S.y)}):(a=Math.min(a,t.x),l=Math.min(l,t.y));for(var c=n-e.width/2-a,u=i-e.height/2-l,h=0;h<t.length;h++){var x=t[h],w=t[h<t.length-1?h+1:0],b=fr(e,s,{x:c+x.x,y:u+x.y},{x:c+w.x,y:u+w.y});b&&r.push(b)}return r.length?(r.length>1&&r.sort(function(S,E){var k=S.x-s.x,B=S.y-s.y,_=Math.sqrt(k*k+B*B),D=E.x-s.x,y=E.y-s.y,g=Math.sqrt(D*D+y*y);return _<g?-1:_===g?0:1}),r[0]):e}d($t,"intersectPolygon");var yr=d((e,t)=>{var s=e.x,n=e.y,i=t.x-s,r=t.y-n,a=e.width/2,l=e.height/2,c,u;return Math.abs(r)*a>Math.abs(i)*l?(r<0&&(l=-l),c=r===0?0:l*i/r,u=l):(i<0&&(a=-a),c=a,u=i===0?0:a*r/i),{x:s+c,y:n+u}},"intersectRect"),br=yr,N={node:ur,circle:pr,ellipse:qt,polygon:xr,rect:br},A=d(async(e,t,s,n)=>{const i=R();let r;const a=t.useHtmlLabels||j(i.flowchart.htmlLabels);s?r=s:r="node default";const l=e.insert("g").attr("class",r).attr("id",t.domId||t.id),c=l.insert("g").attr("class","label").attr("style",t.labelStyle);let u;t.labelText===void 0?u="":u=typeof t.labelText=="string"?t.labelText:t.labelText[0];const h=c.node();let x;t.labelType==="markdown"?x=Wt(c,Tt(xt(u),i),{useHtmlLabels:a,width:t.width||i.flowchart.wrappingWidth,classes:"markdown-node-label"},i):x=h.appendChild(K(Tt(xt(u),i),t.labelStyle,!1,n));let w=x.getBBox();const b=t.padding/2;if(j(i.flowchart.htmlLabels)){const S=x.children[0],E=O(x),k=S.getElementsByTagName("img");if(k){const B=u.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...k].map(_=>new Promise(D=>{function y(){if(_.style.display="flex",_.style.flexDirection="column",B){const g=i.fontSize?i.fontSize:window.getComputedStyle(document.body).fontSize,m=parseInt(g,10)*5+"px";_.style.minWidth=m,_.style.maxWidth=m}else _.style.width="100%";D(_)}d(y,"setupImage"),setTimeout(()=>{_.complete&&y()}),_.addEventListener("error",y),_.addEventListener("load",y)})))}w=S.getBoundingClientRect(),E.attr("width",w.width),E.attr("height",w.height)}return a?c.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"):c.attr("transform","translate(0, "+-w.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:l,bbox:w,halfPadding:b,label:c}},"labelHelper"),T=d((e,t)=>{const s=t.node().getBBox();e.width=s.width,e.height=s.height},"updateNodeBounds");function U(e,t,s,n){return e.insert("polygon",":first-child").attr("points",n.map(function(i){return i.x+","+i.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+s/2+")")}d(U,"insertPolygonShape");var wr=d(async(e,t)=>{t.useHtmlLabels||R().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:n,bbox:i,halfPadding:r}=await A(e,t,"node "+t.classes,!0);L.info("Classes = ",t.classes);const a=n.insert("rect",":first-child");return a.attr("rx",t.rx).attr("ry",t.ry).attr("x",-i.width/2-r).attr("y",-i.height/2-r).attr("width",i.width+t.padding).attr("height",i.height+t.padding),T(t,a),t.intersect=function(l){return N.rect(t,l)},n},"note"),mr=wr,zt=d(e=>e?" "+e:"","formatClass"),Y=d((e,t)=>`${t||"node default"}${zt(e.classes)} ${zt(e.class)}`,"getClassesFromNode"),At=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=i+r,l=[{x:a/2,y:0},{x:a,y:-a/2},{x:a/2,y:-a},{x:0,y:-a/2}];L.info("Question main (Circle)");const c=U(s,a,a,l);return c.attr("style",t.style),T(t,c),t.intersect=function(u){return L.warn("Intersect called"),N.polygon(t,l,u)},s},"question"),Lr=d((e,t)=>{const s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=28,i=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}];return s.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(a){return N.circle(t,14,a)},s},"choice"),Sr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=4,r=n.height+t.padding,a=r/i,l=n.width+2*a+t.padding,c=[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}],u=U(s,l,r,c);return u.attr("style",t.style),T(t,u),t.intersect=function(h){return N.polygon(t,c,h)},s},"hexagon"),kr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,void 0,!0),i=2,r=n.height+2*t.padding,a=r/i,l=n.width+2*a+t.padding,c=gr(t.directions,n,t),u=U(s,l,r,c);return u.attr("style",t.style),T(t,u),t.intersect=function(h){return N.polygon(t,c,h)},s},"block_arrow"),vr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-r/2,y:0},{x:i,y:0},{x:i,y:-r},{x:-r/2,y:-r},{x:0,y:-r/2}];return U(s,i,r,a).attr("style",t.style),t.width=i+r,t.height=r,t.intersect=function(c){return N.polygon(t,a,c)},s},"rect_left_inv_arrow"),Er=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"lean_right"),_r=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:2*r/6,y:0},{x:i+r/6,y:0},{x:i-2*r/6,y:-r},{x:-r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"lean_left"),Dr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i+2*r/6,y:0},{x:i-r/6,y:-r},{x:r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"trapezoid"),Nr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:-2*r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"inv_trapezoid"),Tr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i+r/2,y:0},{x:i,y:-r/2},{x:i+r/2,y:-r},{x:0,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"rect_right_inv_arrow"),Br=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=i/2,a=r/(2.5+i/50),l=n.height+a+t.padding,c="M 0,"+a+" a "+r+","+a+" 0,0,0 "+i+" 0 a "+r+","+a+" 0,0,0 "+-i+" 0 l 0,"+l+" a "+r+","+a+" 0,0,0 "+i+" 0 l 0,"+-l,u=s.attr("label-offset-y",a).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-i/2+","+-(l/2+a)+")");return T(t,u),t.intersect=function(h){const x=N.rect(t,h),w=x.x-t.x;if(r!=0&&(Math.abs(w)<t.width/2||Math.abs(w)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-a)){let b=a*a*(1-w*w/(r*r));b!=0&&(b=Math.sqrt(b)),b=a-b,h.y-t.y>0&&(b=-b),x.y+=b}return x},s},"cylinder"),Cr=d(async(e,t)=>{const{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes+" "+t.class,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,c=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",a).attr("height",l),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ht(r,t.props.borders,a,l),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return T(t,r),t.intersect=function(h){return N.rect(t,h)},s},"rect"),Ir=d(async(e,t)=>{const{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,c=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",a).attr("height",l),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ht(r,t.props.borders,a,l),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return T(t,r),t.intersect=function(h){return N.rect(t,h)},s},"composite"),Or=d(async(e,t)=>{const{shapeSvg:s}=await A(e,t,"label",!0);L.trace("Classes = ",t.class);const n=s.insert("rect",":first-child"),i=0,r=0;if(n.attr("width",i).attr("height",r),s.attr("class","label edgeLabel"),t.props){const a=new Set(Object.keys(t.props));t.props.borders&&(ht(n,t.props.borders,i,r),a.delete("borders")),a.forEach(l=>{L.warn(`Unknown node property ${l}`)})}return T(t,n),t.intersect=function(a){return N.rect(t,a)},s},"labelRect");function ht(e,t,s,n){const i=[],r=d(l=>{i.push(l,0)},"addBorder"),a=d(l=>{i.push(0,l)},"skipBorder");t.includes("t")?(L.debug("add top border"),r(s)):a(s),t.includes("r")?(L.debug("add right border"),r(n)):a(n),t.includes("b")?(L.debug("add bottom border"),r(s)):a(s),t.includes("l")?(L.debug("add left border"),r(n)):a(n),e.attr("stroke-dasharray",i.join(" "))}d(ht,"applyNodePropertyBorders");var Rr=d((e,t)=>{let s;t.classes?s="node "+t.classes:s="node default";const n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),i=n.insert("rect",":first-child"),r=n.insert("line"),a=n.insert("g").attr("class","label"),l=t.labelText.flat?t.labelText.flat():t.labelText;let c="";typeof l=="object"?c=l[0]:c=l,L.info("Label text abc79",c,l,typeof l=="object");const u=a.node().appendChild(K(c,t.labelStyle,!0,!0));let h={width:0,height:0};if(j(R().flowchart.htmlLabels)){const E=u.children[0],k=O(u);h=E.getBoundingClientRect(),k.attr("width",h.width),k.attr("height",h.height)}L.info("Text 2",l);const x=l.slice(1,l.length);let w=u.getBBox();const b=a.node().appendChild(K(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(j(R().flowchart.htmlLabels)){const E=b.children[0],k=O(b);h=E.getBoundingClientRect(),k.attr("width",h.width),k.attr("height",h.height)}const S=t.padding/2;return O(b).attr("transform","translate( "+(h.width>w.width?0:(w.width-h.width)/2)+", "+(w.height+S+5)+")"),O(u).attr("transform","translate( "+(h.width<w.width?0:-(w.width-h.width)/2)+", 0)"),h=a.node().getBBox(),a.attr("transform","translate("+-h.width/2+", "+(-h.height/2-S+3)+")"),i.attr("class","outer title-state").attr("x",-h.width/2-S).attr("y",-h.height/2-S).attr("width",h.width+t.padding).attr("height",h.height+t.padding),r.attr("class","divider").attr("x1",-h.width/2-S).attr("x2",h.width/2+S).attr("y1",-h.height/2-S+w.height+S).attr("y2",-h.height/2-S+w.height+S),T(t,i),t.intersect=function(E){return N.rect(t,E)},n},"rectWithTitle"),zr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.height+t.padding,r=n.width+i/4+t.padding,a=s.insert("rect",":first-child").attr("style",t.style).attr("rx",i/2).attr("ry",i/2).attr("x",-r/2).attr("y",-i/2).attr("width",r).attr("height",i);return T(t,a),t.intersect=function(l){return N.rect(t,l)},s},"stadium"),Ar=d(async(e,t)=>{const{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,Y(t,void 0),!0),r=s.insert("circle",":first-child");return r.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),L.info("Circle main"),T(t,r),t.intersect=function(a){return L.info("Circle intersect",t,n.width/2+i,a),N.circle(t,n.width/2+i,a)},s},"circle"),Mr=d(async(e,t)=>{const{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,Y(t,void 0),!0),r=5,a=s.insert("g",":first-child"),l=a.insert("circle"),c=a.insert("circle");return a.attr("class",t.class),l.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i+r).attr("width",n.width+t.padding+r*2).attr("height",n.height+t.padding+r*2),c.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),L.info("DoubleCircle main"),T(t,l),t.intersect=function(u){return L.info("DoubleCircle intersect",t,n.width/2+i+r,u),N.circle(t,n.width/2+i+r,u)},s},"doublecircle"),Fr=d(async(e,t)=>{const{shapeSvg:s,bbox:n}=await A(e,t,Y(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i,y:0},{x:i,y:-r},{x:0,y:-r},{x:0,y:0},{x:-8,y:0},{x:i+8,y:0},{x:i+8,y:-r},{x:-8,y:-r},{x:-8,y:0}],l=U(s,i,r,a);return l.attr("style",t.style),T(t,l),t.intersect=function(c){return N.polygon(t,a,c)},s},"subroutine"),Wr=d((e,t)=>{const s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child");return n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),T(t,n),t.intersect=function(i){return N.circle(t,7,i)},s},"start"),Mt=d((e,t,s)=>{const n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let i=70,r=10;s==="LR"&&(i=10,r=70);const a=n.append("rect").attr("x",-1*i/2).attr("y",-1*r/2).attr("width",i).attr("height",r).attr("class","fork-join");return T(t,a),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(l){return N.rect(t,l)},n},"forkJoin"),Pr=d((e,t)=>{const s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child"),i=s.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),n.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),T(t,i),t.intersect=function(r){return N.circle(t,7,r)},s},"end"),Yr=d((e,t)=>{const s=t.padding/2,n=4,i=8;let r;t.classes?r="node "+t.classes:r="node default";const a=e.insert("g").attr("class",r).attr("id",t.domId||t.id),l=a.insert("rect",":first-child"),c=a.insert("line"),u=a.insert("line");let h=0,x=n;const w=a.insert("g").attr("class","label");let b=0;const S=t.classData.annotations?.[0],E=t.classData.annotations[0]?"«"+t.classData.annotations[0]+"»":"",k=w.node().appendChild(K(E,t.labelStyle,!0,!0));let B=k.getBBox();if(j(R().flowchart.htmlLabels)){const v=k.children[0],o=O(k);B=v.getBoundingClientRect(),o.attr("width",B.width),o.attr("height",B.height)}t.classData.annotations[0]&&(x+=B.height+n,h+=B.width);let _=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(R().flowchart.htmlLabels?_+="&lt;"+t.classData.type+"&gt;":_+="<"+t.classData.type+">");const D=w.node().appendChild(K(_,t.labelStyle,!0,!0));O(D).attr("class","classTitle");let y=D.getBBox();if(j(R().flowchart.htmlLabels)){const v=D.children[0],o=O(D);y=v.getBoundingClientRect(),o.attr("width",y.width),o.attr("height",y.height)}x+=y.height+n,y.width>h&&(h=y.width);const g=[];t.classData.members.forEach(v=>{const o=v.getDisplayDetails();let W=o.displayText;R().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const p=w.node().appendChild(K(W,o.cssStyle?o.cssStyle:t.labelStyle,!0,!0));let I=p.getBBox();if(j(R().flowchart.htmlLabels)){const Z=p.children[0],V=O(p);I=Z.getBoundingClientRect(),V.attr("width",I.width),V.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+n,g.push(p)}),x+=i;const f=[];if(t.classData.methods.forEach(v=>{const o=v.getDisplayDetails();let W=o.displayText;R().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const p=w.node().appendChild(K(W,o.cssStyle?o.cssStyle:t.labelStyle,!0,!0));let I=p.getBBox();if(j(R().flowchart.htmlLabels)){const Z=p.children[0],V=O(p);I=Z.getBoundingClientRect(),V.attr("width",I.width),V.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+n,f.push(p)}),x+=i,S){let v=(h-B.width)/2;O(k).attr("transform","translate( "+(-1*h/2+v)+", "+-1*x/2+")"),b=B.height+n}let m=(h-y.width)/2;return O(D).attr("transform","translate( "+(-1*h/2+m)+", "+(-1*x/2+b)+")"),b+=y.height+n,c.attr("class","divider").attr("x1",-h/2-s).attr("x2",h/2+s).attr("y1",-x/2-s+i+b).attr("y2",-x/2-s+i+b),b+=i,g.forEach(v=>{O(v).attr("transform","translate( "+-h/2+", "+(-1*x/2+b+i/2)+")");const o=v?.getBBox();b+=(o?.height??0)+n}),b+=i,u.attr("class","divider").attr("x1",-h/2-s).attr("x2",h/2+s).attr("y1",-x/2-s+i+b).attr("y2",-x/2-s+i+b),b+=i,f.forEach(v=>{O(v).attr("transform","translate( "+-h/2+", "+(-1*x/2+b)+")");const o=v?.getBBox();b+=(o?.height??0)+n}),l.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-s).attr("y",-(x/2)-s).attr("width",h+t.padding).attr("height",x+t.padding),T(t,l),t.intersect=function(v){return N.rect(t,v)},a},"class_box"),Ft={rhombus:At,composite:Ir,question:At,rect:Cr,labelRect:Or,rectWithTitle:Rr,choice:Lr,circle:Ar,doublecircle:Mr,stadium:zr,hexagon:Sr,block_arrow:kr,rect_left_inv_arrow:vr,lean_right:Er,lean_left:_r,trapezoid:Dr,inv_trapezoid:Nr,rect_right_inv_arrow:Tr,cylinder:Br,start:Wr,end:Pr,note:mr,subroutine:Fr,fork:Mt,join:Mt,class_box:Yr},lt={},te=d(async(e,t,s)=>{let n,i;if(t.link){let r;R().securityLevel==="sandbox"?r="_top":t.linkTarget&&(r=t.linkTarget||"_blank"),n=e.insert("svg:a").attr("xlink:href",t.link).attr("target",r),i=await Ft[t.shape](n,t,s)}else i=await Ft[t.shape](e,t,s),n=i;return t.tooltip&&i.attr("title",t.tooltip),t.class&&i.attr("class","node default "+t.class),lt[t.id]=n,t.haveCallback&&lt[t.id].attr("class",lt[t.id].attr("class")+" clickable"),n},"insertNode"),Hr=d(e=>{const t=lt[e.id];L.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const s=8,n=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+n-e.width/2)+", "+(e.y-e.height/2-s)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),n},"positionNode");function _t(e,t,s=!1){const n=e;let i="default";(n?.classes?.length||0)>0&&(i=(n?.classes??[]).join(" ")),i=i+" flowchart-label";let r=0,a="",l;switch(n.type){case"round":r=5,a="rect";break;case"composite":r=0,a="composite",l=0;break;case"square":a="rect";break;case"diamond":a="question";break;case"hexagon":a="hexagon";break;case"block_arrow":a="block_arrow";break;case"odd":a="rect_left_inv_arrow";break;case"lean_right":a="lean_right";break;case"lean_left":a="lean_left";break;case"trapezoid":a="trapezoid";break;case"inv_trapezoid":a="inv_trapezoid";break;case"rect_left_inv_arrow":a="rect_left_inv_arrow";break;case"circle":a="circle";break;case"ellipse":a="ellipse";break;case"stadium":a="stadium";break;case"subroutine":a="subroutine";break;case"cylinder":a="cylinder";break;case"group":a="rect";break;case"doublecircle":a="doublecircle";break;default:a="rect"}const c=oe(n?.styles??[]),u=n.label,h=n.size??{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:a,labelText:u,rx:r,ry:r,class:i,style:c.style,id:n.id,directions:n.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:s,intersect:void 0,type:n.type,padding:l??rt()?.block?.padding??0}}d(_t,"getNodeFromBlock");async function ee(e,t,s){const n=_t(t,s,!1);if(n.type==="group")return;const i=rt(),r=await te(e,n,{config:i}),a=r.node().getBBox(),l=s.getBlock(n.id);l.size={width:a.width,height:a.height,x:0,y:0,node:r},s.setBlock(l),r.remove()}d(ee,"calculateBlockSize");async function re(e,t,s){const n=_t(t,s,!0);if(s.getBlock(n.id).type!=="space"){const r=rt();await te(e,n,{config:r}),t.intersect=n?.intersect,Hr(n)}}d(re,"insertBlockPositioned");async function dt(e,t,s,n){for(const i of t)await n(e,i,s),i.children&&await dt(e,i.children,s,n)}d(dt,"performOperations");async function ae(e,t,s){await dt(e,t,s,ee)}d(ae,"calculateBlockSizes");async function se(e,t,s){await dt(e,t,s,re)}d(se,"insertBlocks");async function ie(e,t,s,n,i){const r=new be({multigraph:!0,compound:!0});r.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const a of s)a.size&&r.setNode(a.id,{width:a.size.width,height:a.size.height,intersect:a.intersect});for(const a of t)if(a.start&&a.end){const l=n.getBlock(a.start),c=n.getBlock(a.end);if(l?.size&&c?.size){const u=l.size,h=c.size,x=[{x:u.x,y:u.y},{x:u.x+(h.x-u.x)/2,y:u.y+(h.y-u.y)/2},{x:h.x,y:h.y}];hr(e,{v:a.start,w:a.end,name:a.id},{...a,arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",r,i),a.label&&(await nr(e,{...a,label:a.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),lr({...a,x:x[1].x,y:x[1].y},{originalPath:x}))}}}d(ie,"insertEdges");var Kr=d(function(e,t){return t.db.getClasses()},"getClasses"),Xr=d(async function(e,t,s,n){const{securityLevel:i,block:r}=rt(),a=n.db;let l;i==="sandbox"&&(l=O("#i"+t));const c=i==="sandbox"?O(l.nodes()[0].contentDocument.body):O("body"),u=i==="sandbox"?c.select(`[id="${t}"]`):O(`[id="${t}"]`);er(u,["point","circle","cross"],n.type,t);const x=a.getBlocks(),w=a.getBlocksFlat(),b=a.getEdges(),S=u.insert("g").attr("class","block");await ae(S,x,a);const E=jt(a);if(await se(S,x,a),await ie(S,b,w,a,t),E){const k=E,B=Math.max(1,Math.round(.125*(k.width/k.height))),_=k.height+B+10,D=k.width+10,{useMaxWidth:y}=r;ge(u,_,D,!!y),L.debug("Here Bounds",E,k),u.attr("viewBox",`${k.x-5} ${k.y-5} ${k.width+10} ${k.height+10}`)}},"draw"),Ur={draw:Xr,getClasses:Kr},ra={parser:Se,db:Ye,renderer:Ur,styles:Ke};export{ra as diagram};
//# sourceMappingURL=blockDiagram-5JUZGEFE-XsfnSuGn.js.map
