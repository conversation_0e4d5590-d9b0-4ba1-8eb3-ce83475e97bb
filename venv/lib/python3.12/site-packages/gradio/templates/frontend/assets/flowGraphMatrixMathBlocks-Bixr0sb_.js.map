{"version": 3, "file": "flowGraphMatrixMathBlocks-Bixr0sb_.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMatrixMathBlocks.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { getRichTypeByFlowGraphType, RichTypeBoolean, RichTypeMatrix, RichTypeNumber, RichTypeQuaternion, RichTypeVector3, } from \"../../../flowGraphRichTypes.js\";\nimport { Matrix, Quaternion, Vector3 } from \"../../../../Maths/math.vector.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphUnaryOperationBlock } from \"../flowGraphUnaryOperationBlock.js\";\nimport { FlowGraphBinaryOperationBlock } from \"../flowGraphBinaryOperationBlock.js\";\n/**\n * Transposes a matrix.\n */\nexport class FlowGraphTransposeBlock extends FlowGraphUnaryOperationBlock {\n    /**\n     * Creates a new instance of the block.\n     * @param config the configuration of the block\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), (a) => (a.transpose ? a.transpose() : Matrix.Transpose(a)), \"FlowGraphTransposeBlock\" /* FlowGraphBlockNames.Transpose */, config);\n    }\n}\nRegisterClass(\"FlowGraphTransposeBlock\" /* FlowGraphBlockNames.Transpose */, FlowGraphTransposeBlock);\n/**\n * Gets the determinant of a matrix.\n */\nexport class FlowGraphDeterminantBlock extends FlowGraphUnaryOperationBlock {\n    /**\n     * Creates a new instance of the block.\n     * @param config the configuration of the block\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), RichTypeNumber, (a) => a.determinant(), \"FlowGraphDeterminantBlock\" /* FlowGraphBlockNames.Determinant */, config);\n    }\n}\nRegisterClass(\"FlowGraphDeterminantBlock\" /* FlowGraphBlockNames.Determinant */, FlowGraphDeterminantBlock);\n/**\n * Inverts a matrix.\n */\nexport class FlowGraphInvertMatrixBlock extends FlowGraphUnaryOperationBlock {\n    /**\n     * Creates a new instance of the inverse block.\n     * @param config the configuration of the block\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), (a) => (a.inverse ? a.inverse() : Matrix.Invert(a)), \"FlowGraphInvertMatrixBlock\" /* FlowGraphBlockNames.InvertMatrix */, config);\n    }\n}\nRegisterClass(\"FlowGraphInvertMatrixBlock\" /* FlowGraphBlockNames.InvertMatrix */, FlowGraphInvertMatrixBlock);\n/**\n * Multiplies two matrices.\n */\nexport class FlowGraphMatrixMultiplicationBlock extends FlowGraphBinaryOperationBlock {\n    /**\n     * Creates a new instance of the multiplication block.\n     * Note - this is similar to the math multiplication if not using matrix per-component multiplication.\n     * @param config the configuration of the block\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), getRichTypeByFlowGraphType(config?.matrixType || \"Matrix\" /* FlowGraphTypes.Matrix */), (a, b) => b.multiply(a), \"FlowGraphMatrixMultiplicationBlock\" /* FlowGraphBlockNames.MatrixMultiplication */, config);\n    }\n}\nRegisterClass(\"FlowGraphMatrixMultiplicationBlock\" /* FlowGraphBlockNames.MatrixMultiplication */, FlowGraphMatrixMultiplicationBlock);\n/**\n * Matrix decompose block\n */\nexport class FlowGraphMatrixDecomposeBlock extends FlowGraphBlock {\n    constructor(config) {\n        super(config);\n        this.input = this.registerDataInput(\"input\", RichTypeMatrix);\n        this.position = this.registerDataOutput(\"position\", RichTypeVector3);\n        this.rotationQuaternion = this.registerDataOutput(\"rotationQuaternion\", RichTypeQuaternion);\n        this.scaling = this.registerDataOutput(\"scaling\", RichTypeVector3);\n        this.isValid = this.registerDataOutput(\"isValid\", RichTypeBoolean, false);\n    }\n    _updateOutputs(context) {\n        const cachedExecutionId = context._getExecutionVariable(this, \"executionId\", -1);\n        const cachedPosition = context._getExecutionVariable(this, \"cachedPosition\", null);\n        const cachedRotation = context._getExecutionVariable(this, \"cachedRotation\", null);\n        const cachedScaling = context._getExecutionVariable(this, \"cachedScaling\", null);\n        if (cachedExecutionId === context.executionId && cachedPosition && cachedRotation && cachedScaling) {\n            this.position.setValue(cachedPosition, context);\n            this.rotationQuaternion.setValue(cachedRotation, context);\n            this.scaling.setValue(cachedScaling, context);\n        }\n        else {\n            const matrix = this.input.getValue(context);\n            const position = cachedPosition || new Vector3();\n            const rotation = cachedRotation || new Quaternion();\n            const scaling = cachedScaling || new Vector3();\n            // check matrix last column components should be 0,0,0,1\n            // round them to 4 decimal places\n            const m3 = Math.round(matrix.m[3] * 10000) / 10000;\n            const m7 = Math.round(matrix.m[7] * 10000) / 10000;\n            const m11 = Math.round(matrix.m[11] * 10000) / 10000;\n            const m15 = Math.round(matrix.m[15] * 10000) / 10000;\n            if (m3 !== 0 || m7 !== 0 || m11 !== 0 || m15 !== 1) {\n                this.isValid.setValue(false, context);\n                this.position.setValue(Vector3.Zero(), context);\n                this.rotationQuaternion.setValue(Quaternion.Identity(), context);\n                this.scaling.setValue(Vector3.One(), context);\n                return;\n            }\n            // make the checks for validity\n            const valid = matrix.decompose(scaling, rotation, position);\n            this.isValid.setValue(valid, context);\n            this.position.setValue(position, context);\n            this.rotationQuaternion.setValue(rotation, context);\n            this.scaling.setValue(scaling, context);\n            context._setExecutionVariable(this, \"cachedPosition\", position);\n            context._setExecutionVariable(this, \"cachedRotation\", rotation);\n            context._setExecutionVariable(this, \"cachedScaling\", scaling);\n            context._setExecutionVariable(this, \"executionId\", context.executionId);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphMatrixDecompose\" /* FlowGraphBlockNames.MatrixDecompose */;\n    }\n}\nRegisterClass(\"FlowGraphMatrixDecompose\" /* FlowGraphBlockNames.MatrixDecompose */, FlowGraphMatrixDecomposeBlock);\n/**\n * Matrix compose block\n */\nexport class FlowGraphMatrixComposeBlock extends FlowGraphBlock {\n    constructor(config) {\n        super(config);\n        this.position = this.registerDataInput(\"position\", RichTypeVector3);\n        this.rotationQuaternion = this.registerDataInput(\"rotationQuaternion\", RichTypeQuaternion);\n        this.scaling = this.registerDataInput(\"scaling\", RichTypeVector3);\n        this.value = this.registerDataOutput(\"value\", RichTypeMatrix);\n    }\n    _updateOutputs(context) {\n        const cachedExecutionId = context._getExecutionVariable(this, \"executionId\", -1);\n        const cachedMatrix = context._getExecutionVariable(this, \"cachedMatrix\", null);\n        if (cachedExecutionId === context.executionId && cachedMatrix) {\n            this.value.setValue(cachedMatrix, context);\n        }\n        else {\n            const matrix = Matrix.Compose(this.scaling.getValue(context), this.rotationQuaternion.getValue(context), this.position.getValue(context));\n            this.value.setValue(matrix, context);\n            context._setExecutionVariable(this, \"cachedMatrix\", matrix);\n            context._setExecutionVariable(this, \"executionId\", context.executionId);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphMatrixCompose\" /* FlowGraphBlockNames.MatrixCompose */;\n    }\n}\nRegisterClass(\"FlowGraphMatrixCompose\" /* FlowGraphBlockNames.MatrixCompose */, FlowGraphMatrixComposeBlock);\n//# sourceMappingURL=flowGraphMatrixMathBlocks.js.map"], "names": ["FlowGraphTransposeBlock", "FlowGraphUnaryOperationBlock", "config", "getRichTypeByFlowGraphType", "a", "Matrix", "RegisterClass", "FlowGraphDeterminantBlock", "RichTypeNumber", "FlowGraphInvertMatrixBlock", "FlowGraphMatrixMultiplicationBlock", "FlowGraphBinaryOperationBlock", "b", "FlowGraphMatrixDecomposeBlock", "FlowGraphBlock", "RichTypeMatrix", "RichTypeVector3", "RichTypeQuaternion", "RichTypeBoolean", "context", "cachedExecutionId", "cachedPosition", "cachedRotation", "cachedScaling", "matrix", "position", "Vector3", "rotation", "Quaternion", "scaling", "m3", "m7", "m11", "m15", "valid", "FlowGraphMatrixComposeBlock", "cachedMatrix"], "mappings": "2dASO,MAAMA,UAAgCC,CAA6B,CAKtE,YAAYC,EAAQ,CAChB,MAAMC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+BC,EAA2BD,GAAQ,YAAc,QAAQ,EAAgCE,GAAOA,EAAE,UAAYA,EAAE,UAAS,EAAKC,EAAO,UAAUD,CAAC,EAAI,0BAA+DF,CAAM,CAC1T,CACL,CACAI,EAAc,0BAA+DN,CAAuB,EAI7F,MAAMO,UAAkCN,CAA6B,CAKxE,YAAYC,EAAQ,CAChB,MAAMC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+BM,EAAiBJ,GAAMA,EAAE,YAAa,EAAE,4BAAmEF,CAAM,CAClN,CACL,CACAI,EAAc,4BAAmEC,CAAyB,EAInG,MAAME,UAAmCR,CAA6B,CAKzE,YAAYC,EAAQ,CAChB,MAAMC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+BC,EAA2BD,GAAQ,YAAc,QAAQ,EAAgCE,GAAOA,EAAE,QAAUA,EAAE,QAAO,EAAKC,EAAO,OAAOD,CAAC,EAAI,6BAAqEF,CAAM,CACzT,CACL,CACAI,EAAc,6BAAqEG,CAA0B,EAItG,MAAMC,UAA2CC,CAA8B,CAMlF,YAAYT,EAAQ,CAChB,MAAMC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+BC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+BC,EAA2BD,GAAQ,YAAc,QAAQ,EAA+B,CAACE,EAAGQ,IAAMA,EAAE,SAASR,CAAC,EAAG,qCAAqFF,CAAM,CACrY,CACL,CACAI,EAAc,qCAAqFI,CAAkC,EAI9H,MAAMG,UAAsCC,CAAe,CAC9D,YAAYZ,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,MAAQ,KAAK,kBAAkB,QAASa,CAAc,EAC3D,KAAK,SAAW,KAAK,mBAAmB,WAAYC,CAAe,EACnE,KAAK,mBAAqB,KAAK,mBAAmB,qBAAsBC,CAAkB,EAC1F,KAAK,QAAU,KAAK,mBAAmB,UAAWD,CAAe,EACjE,KAAK,QAAU,KAAK,mBAAmB,UAAWE,EAAiB,EAAK,CAC3E,CACD,eAAeC,EAAS,CACpB,MAAMC,EAAoBD,EAAQ,sBAAsB,KAAM,cAAe,EAAE,EACzEE,EAAiBF,EAAQ,sBAAsB,KAAM,iBAAkB,IAAI,EAC3EG,EAAiBH,EAAQ,sBAAsB,KAAM,iBAAkB,IAAI,EAC3EI,EAAgBJ,EAAQ,sBAAsB,KAAM,gBAAiB,IAAI,EAC/E,GAAIC,IAAsBD,EAAQ,aAAeE,GAAkBC,GAAkBC,EACjF,KAAK,SAAS,SAASF,EAAgBF,CAAO,EAC9C,KAAK,mBAAmB,SAASG,EAAgBH,CAAO,EACxD,KAAK,QAAQ,SAASI,EAAeJ,CAAO,MAE3C,CACD,MAAMK,EAAS,KAAK,MAAM,SAASL,CAAO,EACpCM,EAAWJ,GAAkB,IAAIK,EACjCC,EAAWL,GAAkB,IAAIM,EACjCC,EAAUN,GAAiB,IAAIG,EAG/BI,EAAK,KAAK,MAAMN,EAAO,EAAE,CAAC,EAAI,GAAK,EAAI,IACvCO,EAAK,KAAK,MAAMP,EAAO,EAAE,CAAC,EAAI,GAAK,EAAI,IACvCQ,EAAM,KAAK,MAAMR,EAAO,EAAE,EAAE,EAAI,GAAK,EAAI,IACzCS,EAAM,KAAK,MAAMT,EAAO,EAAE,EAAE,EAAI,GAAK,EAAI,IAC/C,GAAIM,IAAO,GAAKC,IAAO,GAAKC,IAAQ,GAAKC,IAAQ,EAAG,CAChD,KAAK,QAAQ,SAAS,GAAOd,CAAO,EACpC,KAAK,SAAS,SAASO,EAAQ,KAAI,EAAIP,CAAO,EAC9C,KAAK,mBAAmB,SAASS,EAAW,SAAQ,EAAIT,CAAO,EAC/D,KAAK,QAAQ,SAASO,EAAQ,IAAG,EAAIP,CAAO,EAC5C,MACH,CAED,MAAMe,EAAQV,EAAO,UAAUK,EAASF,EAAUF,CAAQ,EAC1D,KAAK,QAAQ,SAASS,EAAOf,CAAO,EACpC,KAAK,SAAS,SAASM,EAAUN,CAAO,EACxC,KAAK,mBAAmB,SAASQ,EAAUR,CAAO,EAClD,KAAK,QAAQ,SAASU,EAASV,CAAO,EACtCA,EAAQ,sBAAsB,KAAM,iBAAkBM,CAAQ,EAC9DN,EAAQ,sBAAsB,KAAM,iBAAkBQ,CAAQ,EAC9DR,EAAQ,sBAAsB,KAAM,gBAAiBU,CAAO,EAC5DV,EAAQ,sBAAsB,KAAM,cAAeA,EAAQ,WAAW,CACzE,CACJ,CACD,cAAe,CACX,MAAO,0BACV,CACL,CACAb,EAAc,2BAAsEO,CAA6B,EAI1G,MAAMsB,UAAoCrB,CAAe,CAC5D,YAAYZ,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,SAAW,KAAK,kBAAkB,WAAYc,CAAe,EAClE,KAAK,mBAAqB,KAAK,kBAAkB,qBAAsBC,CAAkB,EACzF,KAAK,QAAU,KAAK,kBAAkB,UAAWD,CAAe,EAChE,KAAK,MAAQ,KAAK,mBAAmB,QAASD,CAAc,CAC/D,CACD,eAAeI,EAAS,CACpB,MAAMC,EAAoBD,EAAQ,sBAAsB,KAAM,cAAe,EAAE,EACzEiB,EAAejB,EAAQ,sBAAsB,KAAM,eAAgB,IAAI,EAC7E,GAAIC,IAAsBD,EAAQ,aAAeiB,EAC7C,KAAK,MAAM,SAASA,EAAcjB,CAAO,MAExC,CACD,MAAMK,EAASnB,EAAO,QAAQ,KAAK,QAAQ,SAASc,CAAO,EAAG,KAAK,mBAAmB,SAASA,CAAO,EAAG,KAAK,SAAS,SAASA,CAAO,CAAC,EACxI,KAAK,MAAM,SAASK,EAAQL,CAAO,EACnCA,EAAQ,sBAAsB,KAAM,eAAgBK,CAAM,EAC1DL,EAAQ,sBAAsB,KAAM,cAAeA,EAAQ,WAAW,CACzE,CACJ,CACD,cAAe,CACX,MAAO,wBACV,CACL,CACAb,EAAc,yBAAkE6B,CAA2B", "x_google_ignoreList": [0]}