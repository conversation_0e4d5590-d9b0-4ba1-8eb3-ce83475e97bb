{"version": 3, "file": "flowGraphInterpolationBlock-BqJW2qFy.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphInterpolationBlock.js"], "sourcesContent": ["\nimport { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { getRichTypeByAnimationType, getRichTypeByFlowGraphType, RichTypeAny, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { Animation } from \"../../../../Animations/animation.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * This block is responsible for interpolating between two values.\n * The babylon concept used is Animation, and it is the output of this block.\n *\n * Note that values will be parsed when the in connection is triggered. until then changing the value will not trigger a new interpolation.\n *\n * Internally this block uses the Animation class.\n *\n * Note that if the interpolation is already running a signal will be sent to stop the animation group running it.\n */\nexport class FlowGraphInterpolationBlock extends FlowGraphBlock {\n    constructor(config = {}) {\n        super(config);\n        /**\n         * The keyframes to interpolate between.\n         * Each keyframe has a duration input and a value input.\n         */\n        this.keyFrames = [];\n        const type = typeof config?.animationType === \"string\"\n            ? getRichTypeByFlowGraphType(config.animationType)\n            : getRichTypeByAnimationType(config?.animationType ?? 0);\n        const numberOfKeyFrames = config?.keyFramesCount ?? 1;\n        const duration = this.registerDataInput(`duration_0`, RichTypeNumber, 0);\n        const value = this.registerDataInput(`value_0`, type);\n        this.keyFrames.push({ duration, value });\n        for (let i = 1; i < numberOfKeyFrames + 1; i++) {\n            const duration = this.registerDataInput(`duration_${i}`, RichTypeNumber, i === numberOfKeyFrames ? config.duration : undefined);\n            const value = this.registerDataInput(`value_${i}`, type);\n            this.keyFrames.push({ duration, value });\n        }\n        this.initialValue = this.keyFrames[0].value;\n        this.endValue = this.keyFrames[numberOfKeyFrames].value;\n        this.easingFunction = this.registerDataInput(\"easingFunction\", RichTypeAny);\n        this.animation = this.registerDataOutput(\"animation\", RichTypeAny);\n        this.propertyName = this.registerDataInput(\"propertyName\", RichTypeAny, config?.propertyName);\n        this.customBuildAnimation = this.registerDataInput(\"customBuildAnimation\", RichTypeAny);\n    }\n    _updateOutputs(context) {\n        const interpolationAnimations = context._getGlobalContextVariable(\"interpolationAnimations\", []);\n        const propertyName = this.propertyName.getValue(context);\n        const easingFunction = this.easingFunction.getValue(context);\n        const animation = this._createAnimation(context, propertyName, easingFunction);\n        // If an old animation exists, it will be ignored here.\n        // This is because if the animation is running and they both have the same target, the old will be stopped.\n        // This doesn't happen here, it happens in the play animation block.\n        this.animation.setValue(animation, context);\n        // to make sure no 2 interpolations are running on the same target, we will mark the animation in the context\n        if (Array.isArray(animation)) {\n            for (const anim of animation) {\n                interpolationAnimations.push(anim.uniqueId);\n            }\n        }\n        else {\n            interpolationAnimations.push(animation.uniqueId);\n        }\n        context._setGlobalContextVariable(\"interpolationAnimations\", interpolationAnimations);\n    }\n    _createAnimation(context, propertyName, easingFunction) {\n        const type = this.initialValue.richType;\n        const keys = [];\n        // add initial value\n        const currentValue = this.initialValue.getValue(context) || type.defaultValue;\n        keys.push({ frame: 0, value: currentValue });\n        const numberOfKeyFrames = this.config?.numberOfKeyFrames ?? 1;\n        for (let i = 1; i < numberOfKeyFrames + 1; i++) {\n            const duration = this.keyFrames[i].duration?.getValue(context);\n            let value = this.keyFrames[i].value?.getValue(context);\n            if (i === numberOfKeyFrames - 1) {\n                value = value || type.defaultValue;\n            }\n            if (duration !== undefined && value) {\n                // convert duration to frames, based on 60 fps\n                keys.push({ frame: duration * 60, value });\n            }\n        }\n        const customBuildAnimation = this.customBuildAnimation.getValue(context);\n        if (customBuildAnimation) {\n            return customBuildAnimation()(keys, 60, type.animationType, easingFunction);\n        }\n        if (typeof propertyName === \"string\") {\n            const animation = Animation.CreateAnimation(propertyName, type.animationType, 60, easingFunction);\n            animation.setKeys(keys);\n            return [animation];\n        }\n        else {\n            const animations = propertyName.map((name) => {\n                const animation = Animation.CreateAnimation(name, type.animationType, 60, easingFunction);\n                animation.setKeys(keys);\n                return animation;\n            });\n            return animations;\n        }\n    }\n    getClassName() {\n        return \"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */;\n    }\n}\nRegisterClass(\"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */, FlowGraphInterpolationBlock);\n// #L54P2C\n//# sourceMappingURL=flowGraphInterpolationBlock.js.map"], "names": ["FlowGraphInterpolationBlock", "FlowGraphBlock", "config", "type", "getRichTypeByFlowGraphType", "getRichTypeByAnimationType", "numberOfKeyFrames", "duration", "RichTypeNumber", "value", "i", "RichTypeAny", "context", "interpolationAnimations", "propertyName", "easingFunction", "animation", "anim", "keys", "currentValue", "customBuildAnimation", "Animation", "name", "RegisterClass"], "mappings": "6QAeO,MAAMA,UAAoCC,CAAe,CAC5D,YAAYC,EAAS,GAAI,CACrB,MAAMA,CAAM,EAKZ,KAAK,UAAY,GACjB,MAAMC,EAAO,OAAOD,GAAQ,eAAkB,SACxCE,EAA2BF,EAAO,aAAa,EAC/CG,EAA2BH,GAAQ,eAAiB,CAAC,EACrDI,EAAoBJ,GAAQ,gBAAkB,EAC9CK,EAAW,KAAK,kBAAkB,aAAcC,EAAgB,CAAC,EACjEC,EAAQ,KAAK,kBAAkB,UAAWN,CAAI,EACpD,KAAK,UAAU,KAAK,CAAE,SAAAI,EAAU,MAAAE,CAAO,CAAA,EACvC,QAASC,EAAI,EAAGA,EAAIJ,EAAoB,EAAGI,IAAK,CAC5C,MAAMH,EAAW,KAAK,kBAAkB,YAAYG,CAAC,GAAIF,EAAgBE,IAAMJ,EAAoBJ,EAAO,SAAW,MAAS,EACxHO,EAAQ,KAAK,kBAAkB,SAASC,CAAC,GAAIP,CAAI,EACvD,KAAK,UAAU,KAAK,CAAE,SAAAI,EAAU,MAAAE,CAAO,CAAA,CAC1C,CACD,KAAK,aAAe,KAAK,UAAU,CAAC,EAAE,MACtC,KAAK,SAAW,KAAK,UAAUH,CAAiB,EAAE,MAClD,KAAK,eAAiB,KAAK,kBAAkB,iBAAkBK,CAAW,EAC1E,KAAK,UAAY,KAAK,mBAAmB,YAAaA,CAAW,EACjE,KAAK,aAAe,KAAK,kBAAkB,eAAgBA,EAAaT,GAAQ,YAAY,EAC5F,KAAK,qBAAuB,KAAK,kBAAkB,uBAAwBS,CAAW,CACzF,CACD,eAAeC,EAAS,CACpB,MAAMC,EAA0BD,EAAQ,0BAA0B,0BAA2B,CAAE,CAAA,EACzFE,EAAe,KAAK,aAAa,SAASF,CAAO,EACjDG,EAAiB,KAAK,eAAe,SAASH,CAAO,EACrDI,EAAY,KAAK,iBAAiBJ,EAASE,EAAcC,CAAc,EAM7E,GAFA,KAAK,UAAU,SAASC,EAAWJ,CAAO,EAEtC,MAAM,QAAQI,CAAS,EACvB,UAAWC,KAAQD,EACfH,EAAwB,KAAKI,EAAK,QAAQ,OAI9CJ,EAAwB,KAAKG,EAAU,QAAQ,EAEnDJ,EAAQ,0BAA0B,0BAA2BC,CAAuB,CACvF,CACD,iBAAiBD,EAASE,EAAcC,EAAgB,CACpD,MAAMZ,EAAO,KAAK,aAAa,SACzBe,EAAO,CAAA,EAEPC,EAAe,KAAK,aAAa,SAASP,CAAO,GAAKT,EAAK,aACjEe,EAAK,KAAK,CAAE,MAAO,EAAG,MAAOC,CAAY,CAAE,EAC3C,MAAMb,EAAoB,KAAK,QAAQ,mBAAqB,EAC5D,QAASI,EAAI,EAAGA,EAAIJ,EAAoB,EAAGI,IAAK,CAC5C,MAAMH,EAAW,KAAK,UAAUG,CAAC,EAAE,UAAU,SAASE,CAAO,EAC7D,IAAIH,EAAQ,KAAK,UAAUC,CAAC,EAAE,OAAO,SAASE,CAAO,EACjDF,IAAMJ,EAAoB,IAC1BG,EAAQA,GAASN,EAAK,cAEtBI,IAAa,QAAaE,GAE1BS,EAAK,KAAK,CAAE,MAAOX,EAAW,GAAI,MAAAE,CAAK,CAAE,CAEhD,CACD,MAAMW,EAAuB,KAAK,qBAAqB,SAASR,CAAO,EACvE,GAAIQ,EACA,OAAOA,EAAsB,EAACF,EAAM,GAAIf,EAAK,cAAeY,CAAc,EAE9E,GAAI,OAAOD,GAAiB,SAAU,CAClC,MAAME,EAAYK,EAAU,gBAAgBP,EAAcX,EAAK,cAAe,GAAIY,CAAc,EAChG,OAAAC,EAAU,QAAQE,CAAI,EACf,CAACF,CAAS,CACpB,KAOG,QALmBF,EAAa,IAAKQ,GAAS,CAC1C,MAAMN,EAAYK,EAAU,gBAAgBC,EAAMnB,EAAK,cAAe,GAAIY,CAAc,EACxF,OAAAC,EAAU,QAAQE,CAAI,EACfF,CACvB,CAAa,CAGR,CACD,cAAe,CACX,MAAO,6BACV,CACL,CACAO,EAAc,8BAA4EvB,CAA2B", "x_google_ignoreList": [0]}