import{D as qe,_ as nt,an as Je,ao as Qe,n as Ke,o as je,s as _e,g as tr,c as er,b as rr,d as fe,l as be,t as ir,I as ar,T as nr,a3 as Ne,ap as Ee}from"./mermaid.core-DGK6UhOk.js";import{p as or}from"./chunk-IUKPXING-Kf55odCW.js";import{I as sr}from"./chunk-66XRIAFR-Cs7RKspm.js";import{p as lr}from"./mermaid-parser.core-4AQHUFmR.js";import{c as Pe}from"./cytoscape.esm-BPoQaFli.js";import{k as Le,f as hr}from"./index-Ccc2t4AG.js";import{s as fr}from"./select-BigU4G0v.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";import"./_baseUniq-CIsjNB2G.js";import"./_basePickBy-CxKuy9eP.js";import"./clone-Bxj9bjXK.js";import"./svelte/svelte.js";var Ge={exports:{}},pe={exports:{}},ye={exports:{}},xe;function cr(){return xe||(xe=1,function(A,Y){(function(P,L){A.exports=L()})(Le,function(){return function(M){var P={};function L(u){if(P[u])return P[u].exports;var h=P[u]={i:u,l:!1,exports:{}};return M[u].call(h.exports,h,h.exports,L),h.l=!0,h.exports}return L.m=M,L.c=P,L.i=function(u){return u},L.d=function(u,h,a){L.o(u,h)||Object.defineProperty(u,h,{configurable:!1,enumerable:!0,get:a})},L.n=function(u){var h=u&&u.__esModule?function(){return u.default}:function(){return u};return L.d(h,"a",h),h},L.o=function(u,h){return Object.prototype.hasOwnProperty.call(u,h)},L.p="",L(L.s=28)}([function(M,P,L){function u(){}u.QUALITY=1,u.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,u.DEFAULT_INCREMENTAL=!1,u.DEFAULT_ANIMATION_ON_LAYOUT=!0,u.DEFAULT_ANIMATION_DURING_LAYOUT=!1,u.DEFAULT_ANIMATION_PERIOD=50,u.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,u.DEFAULT_GRAPH_MARGIN=15,u.NODE_DIMENSIONS_INCLUDE_LABELS=!1,u.SIMPLE_NODE_SIZE=40,u.SIMPLE_NODE_HALF_SIZE=u.SIMPLE_NODE_SIZE/2,u.EMPTY_COMPOUND_NODE_SIZE=40,u.MIN_EDGE_LENGTH=1,u.WORLD_BOUNDARY=1e6,u.INITIAL_WORLD_BOUNDARY=u.WORLD_BOUNDARY/1e3,u.WORLD_CENTER_X=1200,u.WORLD_CENTER_Y=900,M.exports=u},function(M,P,L){var u=L(2),h=L(8),a=L(9);function r(f,i,v){u.call(this,v),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=v,this.bendpoints=[],this.source=f,this.target=i}r.prototype=Object.create(u.prototype);for(var e in u)r[e]=u[e];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(f,i){for(var v=this.getOtherEnd(f),t=i.getGraphManager().getRoot();;){if(v.getOwner()==i)return v;if(v.getOwner()==t)break;v=v.getOwner().getParent()}return null},r.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=h.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},M.exports=r},function(M,P,L){function u(h){this.vGraphObject=h}M.exports=u},function(M,P,L){var u=L(2),h=L(10),a=L(13),r=L(0),e=L(16),f=L(5);function i(t,o,s,c){s==null&&c==null&&(c=o),u.call(this,c),t.graphManager!=null&&(t=t.graphManager),this.estimatedSize=h.MIN_VALUE,this.inclusionTreeDepth=h.MAX_VALUE,this.vGraphObject=c,this.edges=[],this.graphManager=t,s!=null&&o!=null?this.rect=new a(o.x,o.y,s.width,s.height):this.rect=new a}i.prototype=Object.create(u.prototype);for(var v in u)i[v]=u[v];i.prototype.getEdges=function(){return this.edges},i.prototype.getChild=function(){return this.child},i.prototype.getOwner=function(){return this.owner},i.prototype.getWidth=function(){return this.rect.width},i.prototype.setWidth=function(t){this.rect.width=t},i.prototype.getHeight=function(){return this.rect.height},i.prototype.setHeight=function(t){this.rect.height=t},i.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},i.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},i.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},i.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},i.prototype.getRect=function(){return this.rect},i.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},i.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},i.prototype.setRect=function(t,o){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=o.width,this.rect.height=o.height},i.prototype.setCenter=function(t,o){this.rect.x=t-this.rect.width/2,this.rect.y=o-this.rect.height/2},i.prototype.setLocation=function(t,o){this.rect.x=t,this.rect.y=o},i.prototype.moveBy=function(t,o){this.rect.x+=t,this.rect.y+=o},i.prototype.getEdgeListToNode=function(t){var o=[],s=this;return s.edges.forEach(function(c){if(c.target==t){if(c.source!=s)throw"Incorrect edge source!";o.push(c)}}),o},i.prototype.getEdgesBetween=function(t){var o=[],s=this;return s.edges.forEach(function(c){if(!(c.source==s||c.target==s))throw"Incorrect edge source and/or target";(c.target==t||c.source==t)&&o.push(c)}),o},i.prototype.getNeighborsList=function(){var t=new Set,o=this;return o.edges.forEach(function(s){if(s.source==o)t.add(s.target);else{if(s.target!=o)throw"Incorrect incidency!";t.add(s.source)}}),t},i.prototype.withChildren=function(){var t=new Set,o,s;if(t.add(this),this.child!=null)for(var c=this.child.getNodes(),l=0;l<c.length;l++)o=c[l],s=o.withChildren(),s.forEach(function(T){t.add(T)});return t},i.prototype.getNoOfChildren=function(){var t=0,o;if(this.child==null)t=1;else for(var s=this.child.getNodes(),c=0;c<s.length;c++)o=s[c],t+=o.getNoOfChildren();return t==0&&(t=1),t},i.prototype.getEstimatedSize=function(){if(this.estimatedSize==h.MIN_VALUE)throw"assert failed";return this.estimatedSize},i.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},i.prototype.scatter=function(){var t,o,s=-r.INITIAL_WORLD_BOUNDARY,c=r.INITIAL_WORLD_BOUNDARY;t=r.WORLD_CENTER_X+e.nextDouble()*(c-s)+s;var l=-r.INITIAL_WORLD_BOUNDARY,T=r.INITIAL_WORLD_BOUNDARY;o=r.WORLD_CENTER_Y+e.nextDouble()*(T-l)+l,this.rect.x=t,this.rect.y=o},i.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var o=t.getRight()-t.getLeft(),s=t.getBottom()-t.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(o+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>o?(this.rect.x-=(this.labelWidth-o)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(o+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(s+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>s?(this.rect.y-=(this.labelHeight-s)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(s+this.labelHeight))}}},i.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==h.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},i.prototype.transform=function(t){var o=this.rect.x;o>r.WORLD_BOUNDARY?o=r.WORLD_BOUNDARY:o<-r.WORLD_BOUNDARY&&(o=-r.WORLD_BOUNDARY);var s=this.rect.y;s>r.WORLD_BOUNDARY?s=r.WORLD_BOUNDARY:s<-r.WORLD_BOUNDARY&&(s=-r.WORLD_BOUNDARY);var c=new f(o,s),l=t.inverseTransformPoint(c);this.setLocation(l.x,l.y)},i.prototype.getLeft=function(){return this.rect.x},i.prototype.getRight=function(){return this.rect.x+this.rect.width},i.prototype.getTop=function(){return this.rect.y},i.prototype.getBottom=function(){return this.rect.y+this.rect.height},i.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},M.exports=i},function(M,P,L){var u=L(0);function h(){}for(var a in u)h[a]=u[a];h.MAX_ITERATIONS=2500,h.DEFAULT_EDGE_LENGTH=50,h.DEFAULT_SPRING_STRENGTH=.45,h.DEFAULT_REPULSION_STRENGTH=4500,h.DEFAULT_GRAVITY_STRENGTH=.4,h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,h.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,h.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,h.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,h.COOLING_ADAPTATION_FACTOR=.33,h.ADAPTATION_LOWER_NODE_LIMIT=1e3,h.ADAPTATION_UPPER_NODE_LIMIT=5e3,h.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,h.MAX_NODE_DISPLACEMENT=h.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,h.MIN_REPULSION_DIST=h.DEFAULT_EDGE_LENGTH/10,h.CONVERGENCE_CHECK_PERIOD=100,h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,h.MIN_EDGE_LENGTH=1,h.GRID_CALCULATION_CHECK_PERIOD=10,M.exports=h},function(M,P,L){function u(h,a){h==null&&a==null?(this.x=0,this.y=0):(this.x=h,this.y=a)}u.prototype.getX=function(){return this.x},u.prototype.getY=function(){return this.y},u.prototype.setX=function(h){this.x=h},u.prototype.setY=function(h){this.y=h},u.prototype.getDifference=function(h){return new DimensionD(this.x-h.x,this.y-h.y)},u.prototype.getCopy=function(){return new u(this.x,this.y)},u.prototype.translate=function(h){return this.x+=h.width,this.y+=h.height,this},M.exports=u},function(M,P,L){var u=L(2),h=L(10),a=L(0),r=L(7),e=L(3),f=L(1),i=L(13),v=L(12),t=L(11);function o(c,l,T){u.call(this,T),this.estimatedSize=h.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=c,l!=null&&l instanceof r?this.graphManager=l:l!=null&&l instanceof Layout&&(this.graphManager=l.graphManager)}o.prototype=Object.create(u.prototype);for(var s in u)o[s]=u[s];o.prototype.getNodes=function(){return this.nodes},o.prototype.getEdges=function(){return this.edges},o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getParent=function(){return this.parent},o.prototype.getLeft=function(){return this.left},o.prototype.getRight=function(){return this.right},o.prototype.getTop=function(){return this.top},o.prototype.getBottom=function(){return this.bottom},o.prototype.isConnected=function(){return this.isConnected},o.prototype.add=function(c,l,T){if(l==null&&T==null){var g=c;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(g)>-1)throw"Node already in graph!";return g.owner=this,this.getNodes().push(g),g}else{var d=c;if(!(this.getNodes().indexOf(l)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(l.owner==T.owner&&l.owner==this))throw"Both owners must be this graph!";return l.owner!=T.owner?null:(d.source=l,d.target=T,d.isInterGraph=!1,this.getEdges().push(d),l.edges.push(d),T!=l&&T.edges.push(d),d)}},o.prototype.remove=function(c){var l=c;if(c instanceof e){if(l==null)throw"Node is null!";if(!(l.owner!=null&&l.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=l.edges.slice(),g,d=T.length,N=0;N<d;N++)g=T[N],g.isInterGraph?this.graphManager.remove(g):g.source.owner.remove(g);var F=this.nodes.indexOf(l);if(F==-1)throw"Node not in owner node list!";this.nodes.splice(F,1)}else if(c instanceof f){var g=c;if(g==null)throw"Edge is null!";if(!(g.source!=null&&g.target!=null))throw"Source and/or target is null!";if(!(g.source.owner!=null&&g.target.owner!=null&&g.source.owner==this&&g.target.owner==this))throw"Source and/or target owner is invalid!";var C=g.source.edges.indexOf(g),G=g.target.edges.indexOf(g);if(!(C>-1&&G>-1))throw"Source and/or target doesn't know this edge!";g.source.edges.splice(C,1),g.target!=g.source&&g.target.edges.splice(G,1);var F=g.source.owner.getEdges().indexOf(g);if(F==-1)throw"Not in owner's edge list!";g.source.owner.getEdges().splice(F,1)}},o.prototype.updateLeftTop=function(){for(var c=h.MAX_VALUE,l=h.MAX_VALUE,T,g,d,N=this.getNodes(),F=N.length,C=0;C<F;C++){var G=N[C];T=G.getTop(),g=G.getLeft(),c>T&&(c=T),l>g&&(l=g)}return c==h.MAX_VALUE?null:(N[0].getParent().paddingLeft!=null?d=N[0].getParent().paddingLeft:d=this.margin,this.left=l-d,this.top=c-d,new v(this.left,this.top))},o.prototype.updateBounds=function(c){for(var l=h.MAX_VALUE,T=-h.MAX_VALUE,g=h.MAX_VALUE,d=-h.MAX_VALUE,N,F,C,G,B,U=this.nodes,K=U.length,D=0;D<K;D++){var at=U[D];c&&at.child!=null&&at.updateBounds(),N=at.getLeft(),F=at.getRight(),C=at.getTop(),G=at.getBottom(),l>N&&(l=N),T<F&&(T=F),g>C&&(g=C),d<G&&(d=G)}var n=new i(l,g,T-l,d-g);l==h.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),U[0].getParent().paddingLeft!=null?B=U[0].getParent().paddingLeft:B=this.margin,this.left=n.x-B,this.right=n.x+n.width+B,this.top=n.y-B,this.bottom=n.y+n.height+B},o.calculateBounds=function(c){for(var l=h.MAX_VALUE,T=-h.MAX_VALUE,g=h.MAX_VALUE,d=-h.MAX_VALUE,N,F,C,G,B=c.length,U=0;U<B;U++){var K=c[U];N=K.getLeft(),F=K.getRight(),C=K.getTop(),G=K.getBottom(),l>N&&(l=N),T<F&&(T=F),g>C&&(g=C),d<G&&(d=G)}var D=new i(l,g,T-l,d-g);return D},o.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},o.prototype.getEstimatedSize=function(){if(this.estimatedSize==h.MIN_VALUE)throw"assert failed";return this.estimatedSize},o.prototype.calcEstimatedSize=function(){for(var c=0,l=this.nodes,T=l.length,g=0;g<T;g++){var d=l[g];c+=d.calcEstimatedSize()}return c==0?this.estimatedSize=a.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=c/Math.sqrt(this.nodes.length),this.estimatedSize},o.prototype.updateConnected=function(){var c=this;if(this.nodes.length==0){this.isConnected=!0;return}var l=new t,T=new Set,g=this.nodes[0],d,N,F=g.withChildren();for(F.forEach(function(D){l.push(D),T.add(D)});l.length!==0;){g=l.shift(),d=g.getEdges();for(var C=d.length,G=0;G<C;G++){var B=d[G];if(N=B.getOtherEndInGraph(g,this),N!=null&&!T.has(N)){var U=N.withChildren();U.forEach(function(D){l.push(D),T.add(D)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var K=0;T.forEach(function(D){D.owner==c&&K++}),K==this.nodes.length&&(this.isConnected=!0)}},M.exports=o},function(M,P,L){var u,h=L(1);function a(r){u=L(6),this.layout=r,this.graphs=[],this.edges=[]}a.prototype.addRoot=function(){var r=this.layout.newGraph(),e=this.layout.newNode(null),f=this.add(r,e);return this.setRootGraph(f),this.rootGraph},a.prototype.add=function(r,e,f,i,v){if(f==null&&i==null&&v==null){if(r==null)throw"Graph is null!";if(e==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(e.child!=null)throw"Already has a child!";return r.parent=e,e.child=r,r}else{v=f,i=e,f=r;var t=i.getOwner(),o=v.getOwner();if(!(t!=null&&t.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(o!=null&&o.getGraphManager()==this))throw"Target not in this graph mgr!";if(t==o)return f.isInterGraph=!1,t.add(f,i,v);if(f.isInterGraph=!0,f.source=i,f.target=v,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},a.prototype.remove=function(r){if(r instanceof u){var e=r;if(e.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(e==this.rootGraph||e.parent!=null&&e.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(e.getEdges());for(var i,v=f.length,t=0;t<v;t++)i=f[t],e.remove(i);var o=[];o=o.concat(e.getNodes());var s;v=o.length;for(var t=0;t<v;t++)s=o[t],e.remove(s);e==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(e);this.graphs.splice(c,1),e.parent=null}else if(r instanceof h){if(i=r,i==null)throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(!(i.source!=null&&i.target!=null))throw"Source and/or target is null!";if(!(i.source.edges.indexOf(i)!=-1&&i.target.edges.indexOf(i)!=-1))throw"Source and/or target doesn't know this edge!";var c=i.source.edges.indexOf(i);if(i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),!(i.source.owner!=null&&i.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(i.source.owner.getGraphManager().edges.indexOf(i)==-1)throw"Not in owner graph manager's edge list!";var c=i.source.owner.getGraphManager().edges.indexOf(i);i.source.owner.getGraphManager().edges.splice(c,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],e=this.getGraphs(),f=e.length,i=0;i<f;i++)r=r.concat(e[i].getNodes());this.allNodes=r}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(this.allEdges==null){var r=[],e=this.getGraphs();e.length;for(var f=0;f<e.length;f++)r=r.concat(e[f].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(r,e){if(!(r!=null&&e!=null))throw"assert failed";if(r==e)return!0;var f=r.getOwner(),i;do{if(i=f.getParent(),i==null)break;if(i==e)return!0;if(f=i.getOwner(),f==null)break}while(!0);f=e.getOwner();do{if(i=f.getParent(),i==null)break;if(i==r)return!0;if(f=i.getOwner(),f==null)break}while(!0);return!1},a.prototype.calcLowestCommonAncestors=function(){for(var r,e,f,i,v,t=this.getAllEdges(),o=t.length,s=0;s<o;s++){if(r=t[s],e=r.source,f=r.target,r.lca=null,r.sourceInLca=e,r.targetInLca=f,e==f){r.lca=e.getOwner();continue}for(i=e.getOwner();r.lca==null;){for(r.targetInLca=f,v=f.getOwner();r.lca==null;){if(v==i){r.lca=v;break}if(v==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=v.getParent(),v=r.targetInLca.getOwner()}if(i==this.rootGraph)break;r.lca==null&&(r.sourceInLca=i.getParent(),i=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},a.prototype.calcLowestCommonAncestor=function(r,e){if(r==e)return r.getOwner();var f=r.getOwner();do{if(f==null)break;var i=e.getOwner();do{if(i==null)break;if(i==f)return i;i=i.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},a.prototype.calcInclusionTreeDepths=function(r,e){r==null&&e==null&&(r=this.rootGraph,e=1);for(var f,i=r.getNodes(),v=i.length,t=0;t<v;t++)f=i[t],f.inclusionTreeDepth=e,f.child!=null&&this.calcInclusionTreeDepths(f.child,e+1)},a.prototype.includesInvalidEdge=function(){for(var r,e=[],f=this.edges.length,i=0;i<f;i++)r=this.edges[i],this.isOneAncestorOfOther(r.source,r.target)&&e.push(r);for(var i=0;i<e.length;i++)this.remove(e[i]);return!1},M.exports=a},function(M,P,L){var u=L(12);function h(){}h.calcSeparationAmount=function(a,r,e,f){if(!a.intersects(r))throw"assert failed";var i=new Array(2);this.decideDirectionsForOverlappingNodes(a,r,i),e[0]=Math.min(a.getRight(),r.getRight())-Math.max(a.x,r.x),e[1]=Math.min(a.getBottom(),r.getBottom())-Math.max(a.y,r.y),a.getX()<=r.getX()&&a.getRight()>=r.getRight()?e[0]+=Math.min(r.getX()-a.getX(),a.getRight()-r.getRight()):r.getX()<=a.getX()&&r.getRight()>=a.getRight()&&(e[0]+=Math.min(a.getX()-r.getX(),r.getRight()-a.getRight())),a.getY()<=r.getY()&&a.getBottom()>=r.getBottom()?e[1]+=Math.min(r.getY()-a.getY(),a.getBottom()-r.getBottom()):r.getY()<=a.getY()&&r.getBottom()>=a.getBottom()&&(e[1]+=Math.min(a.getY()-r.getY(),r.getBottom()-a.getBottom()));var v=Math.abs((r.getCenterY()-a.getCenterY())/(r.getCenterX()-a.getCenterX()));r.getCenterY()===a.getCenterY()&&r.getCenterX()===a.getCenterX()&&(v=1);var t=v*e[0],o=e[1]/v;e[0]<o?o=e[0]:t=e[1],e[0]=-1*i[0]*(o/2+f),e[1]=-1*i[1]*(t/2+f)},h.decideDirectionsForOverlappingNodes=function(a,r,e){a.getCenterX()<r.getCenterX()?e[0]=-1:e[0]=1,a.getCenterY()<r.getCenterY()?e[1]=-1:e[1]=1},h.getIntersection2=function(a,r,e){var f=a.getCenterX(),i=a.getCenterY(),v=r.getCenterX(),t=r.getCenterY();if(a.intersects(r))return e[0]=f,e[1]=i,e[2]=v,e[3]=t,!0;var o=a.getX(),s=a.getY(),c=a.getRight(),l=a.getX(),T=a.getBottom(),g=a.getRight(),d=a.getWidthHalf(),N=a.getHeightHalf(),F=r.getX(),C=r.getY(),G=r.getRight(),B=r.getX(),U=r.getBottom(),K=r.getRight(),D=r.getWidthHalf(),at=r.getHeightHalf(),n=!1,m=!1;if(f===v){if(i>t)return e[0]=f,e[1]=s,e[2]=v,e[3]=U,!1;if(i<t)return e[0]=f,e[1]=T,e[2]=v,e[3]=C,!1}else if(i===t){if(f>v)return e[0]=o,e[1]=i,e[2]=G,e[3]=t,!1;if(f<v)return e[0]=c,e[1]=i,e[2]=F,e[3]=t,!1}else{var p=a.height/a.width,E=r.height/r.width,y=(t-i)/(v-f),I=void 0,w=void 0,R=void 0,W=void 0,x=void 0,q=void 0;if(-p===y?f>v?(e[0]=l,e[1]=T,n=!0):(e[0]=c,e[1]=s,n=!0):p===y&&(f>v?(e[0]=o,e[1]=s,n=!0):(e[0]=g,e[1]=T,n=!0)),-E===y?v>f?(e[2]=B,e[3]=U,m=!0):(e[2]=G,e[3]=C,m=!0):E===y&&(v>f?(e[2]=F,e[3]=C,m=!0):(e[2]=K,e[3]=U,m=!0)),n&&m)return!1;if(f>v?i>t?(I=this.getCardinalDirection(p,y,4),w=this.getCardinalDirection(E,y,2)):(I=this.getCardinalDirection(-p,y,3),w=this.getCardinalDirection(-E,y,1)):i>t?(I=this.getCardinalDirection(-p,y,1),w=this.getCardinalDirection(-E,y,3)):(I=this.getCardinalDirection(p,y,2),w=this.getCardinalDirection(E,y,4)),!n)switch(I){case 1:W=s,R=f+-N/y,e[0]=R,e[1]=W;break;case 2:R=g,W=i+d*y,e[0]=R,e[1]=W;break;case 3:W=T,R=f+N/y,e[0]=R,e[1]=W;break;case 4:R=l,W=i+-d*y,e[0]=R,e[1]=W;break}if(!m)switch(w){case 1:q=C,x=v+-at/y,e[2]=x,e[3]=q;break;case 2:x=K,q=t+D*y,e[2]=x,e[3]=q;break;case 3:q=U,x=v+at/y,e[2]=x,e[3]=q;break;case 4:x=B,q=t+-D*y,e[2]=x,e[3]=q;break}}return!1},h.getCardinalDirection=function(a,r,e){return a>r?e:1+e%4},h.getIntersection=function(a,r,e,f){if(f==null)return this.getIntersection2(a,r,e);var i=a.x,v=a.y,t=r.x,o=r.y,s=e.x,c=e.y,l=f.x,T=f.y,g=void 0,d=void 0,N=void 0,F=void 0,C=void 0,G=void 0,B=void 0,U=void 0,K=void 0;return N=o-v,C=i-t,B=t*v-i*o,F=T-c,G=s-l,U=l*c-s*T,K=N*G-F*C,K===0?null:(g=(C*U-G*B)/K,d=(F*B-N*U)/K,new u(g,d))},h.angleOfVector=function(a,r,e,f){var i=void 0;return a!==e?(i=Math.atan((f-r)/(e-a)),e<a?i+=Math.PI:f<r&&(i+=this.TWO_PI)):f<r?i=this.ONE_AND_HALF_PI:i=this.HALF_PI,i},h.doIntersect=function(a,r,e,f){var i=a.x,v=a.y,t=r.x,o=r.y,s=e.x,c=e.y,l=f.x,T=f.y,g=(t-i)*(T-c)-(l-s)*(o-v);if(g===0)return!1;var d=((T-c)*(l-i)+(s-l)*(T-v))/g,N=((v-o)*(l-i)+(t-i)*(T-v))/g;return 0<d&&d<1&&0<N&&N<1},h.findCircleLineIntersections=function(a,r,e,f,i,v,t){var o=(e-a)*(e-a)+(f-r)*(f-r),s=2*((a-i)*(e-a)+(r-v)*(f-r)),c=(a-i)*(a-i)+(r-v)*(r-v)-t*t,l=s*s-4*o*c;if(l>=0){var T=(-s+Math.sqrt(s*s-4*o*c))/(2*o),g=(-s-Math.sqrt(s*s-4*o*c))/(2*o),d=null;return T>=0&&T<=1?[T]:g>=0&&g<=1?[g]:d}else return null},h.HALF_PI=.5*Math.PI,h.ONE_AND_HALF_PI=1.5*Math.PI,h.TWO_PI=2*Math.PI,h.THREE_PI=3*Math.PI,M.exports=h},function(M,P,L){function u(){}u.sign=function(h){return h>0?1:h<0?-1:0},u.floor=function(h){return h<0?Math.ceil(h):Math.floor(h)},u.ceil=function(h){return h<0?Math.floor(h):Math.ceil(h)},M.exports=u},function(M,P,L){function u(){}u.MAX_VALUE=2147483647,u.MIN_VALUE=-2147483648,M.exports=u},function(M,P,L){var u=function(){function i(v,t){for(var o=0;o<t.length;o++){var s=t[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(v,s.key,s)}}return function(v,t,o){return t&&i(v.prototype,t),o&&i(v,o),v}}();function h(i,v){if(!(i instanceof v))throw new TypeError("Cannot call a class as a function")}var a=function(v){return{value:v,next:null,prev:null}},r=function(v,t,o,s){return v!==null?v.next=t:s.head=t,o!==null?o.prev=t:s.tail=t,t.prev=v,t.next=o,s.length++,t},e=function(v,t){var o=v.prev,s=v.next;return o!==null?o.next=s:t.head=s,s!==null?s.prev=o:t.tail=o,v.prev=v.next=null,t.length--,v},f=function(){function i(v){var t=this;h(this,i),this.length=0,this.head=null,this.tail=null,v?.forEach(function(o){return t.push(o)})}return u(i,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,o){return r(o.prev,a(t),o,this)}},{key:"insertAfter",value:function(t,o){return r(o,a(t),o.next,this)}},{key:"insertNodeBefore",value:function(t,o){return r(o.prev,t,o,this)}},{key:"insertNodeAfter",value:function(t,o){return r(o,t,o.next,this)}},{key:"push",value:function(t){return r(this.tail,a(t),null,this)}},{key:"unshift",value:function(t){return r(null,a(t),this.head,this)}},{key:"remove",value:function(t){return e(t,this)}},{key:"pop",value:function(){return e(this.tail,this).value}},{key:"popNode",value:function(){return e(this.tail,this)}},{key:"shift",value:function(){return e(this.head,this).value}},{key:"shiftNode",value:function(){return e(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var o=1,s=this.head;o<t;)s=s.next,o++;return s.value}}},{key:"set_object_at",value:function(t,o){if(t<=this.length()){for(var s=1,c=this.head;s<t;)c=c.next,s++;c.value=o}}}]),i}();M.exports=f},function(M,P,L){function u(h,a,r){this.x=null,this.y=null,h==null&&a==null&&r==null?(this.x=0,this.y=0):typeof h=="number"&&typeof a=="number"&&r==null?(this.x=h,this.y=a):h.constructor.name=="Point"&&a==null&&r==null&&(r=h,this.x=r.x,this.y=r.y)}u.prototype.getX=function(){return this.x},u.prototype.getY=function(){return this.y},u.prototype.getLocation=function(){return new u(this.x,this.y)},u.prototype.setLocation=function(h,a,r){h.constructor.name=="Point"&&a==null&&r==null?(r=h,this.setLocation(r.x,r.y)):typeof h=="number"&&typeof a=="number"&&r==null&&(parseInt(h)==h&&parseInt(a)==a?this.move(h,a):(this.x=Math.floor(h+.5),this.y=Math.floor(a+.5)))},u.prototype.move=function(h,a){this.x=h,this.y=a},u.prototype.translate=function(h,a){this.x+=h,this.y+=a},u.prototype.equals=function(h){if(h.constructor.name=="Point"){var a=h;return this.x==a.x&&this.y==a.y}return this==h},u.prototype.toString=function(){return new u().constructor.name+"[x="+this.x+",y="+this.y+"]"},M.exports=u},function(M,P,L){function u(h,a,r,e){this.x=0,this.y=0,this.width=0,this.height=0,h!=null&&a!=null&&r!=null&&e!=null&&(this.x=h,this.y=a,this.width=r,this.height=e)}u.prototype.getX=function(){return this.x},u.prototype.setX=function(h){this.x=h},u.prototype.getY=function(){return this.y},u.prototype.setY=function(h){this.y=h},u.prototype.getWidth=function(){return this.width},u.prototype.setWidth=function(h){this.width=h},u.prototype.getHeight=function(){return this.height},u.prototype.setHeight=function(h){this.height=h},u.prototype.getRight=function(){return this.x+this.width},u.prototype.getBottom=function(){return this.y+this.height},u.prototype.intersects=function(h){return!(this.getRight()<h.x||this.getBottom()<h.y||h.getRight()<this.x||h.getBottom()<this.y)},u.prototype.getCenterX=function(){return this.x+this.width/2},u.prototype.getMinX=function(){return this.getX()},u.prototype.getMaxX=function(){return this.getX()+this.width},u.prototype.getCenterY=function(){return this.y+this.height/2},u.prototype.getMinY=function(){return this.getY()},u.prototype.getMaxY=function(){return this.getY()+this.height},u.prototype.getWidthHalf=function(){return this.width/2},u.prototype.getHeightHalf=function(){return this.height/2},M.exports=u},function(M,P,L){var u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};function h(){}h.lastID=0,h.createID=function(a){return h.isPrimitive(a)?a:(a.uniqueID!=null||(a.uniqueID=h.getString(),h.lastID++),a.uniqueID)},h.getString=function(a){return a==null&&(a=h.lastID),"Object#"+a},h.isPrimitive=function(a){var r=typeof a>"u"?"undefined":u(a);return a==null||r!="object"&&r!="function"},M.exports=h},function(M,P,L){function u(s){if(Array.isArray(s)){for(var c=0,l=Array(s.length);c<s.length;c++)l[c]=s[c];return l}else return Array.from(s)}var h=L(0),a=L(7),r=L(3),e=L(1),f=L(6),i=L(5),v=L(17),t=L(29);function o(s){t.call(this),this.layoutQuality=h.QUALITY,this.createBendsAsNeeded=h.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=h.DEFAULT_INCREMENTAL,this.animationOnLayout=h.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=h.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=h.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=h.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,s!=null&&(this.isRemoteUse=s)}o.RANDOM_SEED=1,o.prototype=Object.create(t.prototype),o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},o.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},o.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},o.prototype.newGraphManager=function(){var s=new a(this);return this.graphManager=s,s},o.prototype.newGraph=function(s){return new f(null,this.graphManager,s)},o.prototype.newNode=function(s){return new r(this.graphManager,s)},o.prototype.newEdge=function(s){return new e(null,null,s)},o.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},o.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var s;return this.checkLayoutSuccess()?s=!1:s=this.layout(),h.ANIMATE==="during"?!1:(s&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,s)},o.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},o.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var s=this.graphManager.getAllEdges(),c=0;c<s.length;c++)s[c];for(var l=this.graphManager.getRoot().getNodes(),c=0;c<l.length;c++)l[c];this.update(this.graphManager.getRoot())}},o.prototype.update=function(s){if(s==null)this.update2();else if(s instanceof r){var c=s;if(c.getChild()!=null)for(var l=c.getChild().getNodes(),T=0;T<l.length;T++)update(l[T]);if(c.vGraphObject!=null){var g=c.vGraphObject;g.update(c)}}else if(s instanceof e){var d=s;if(d.vGraphObject!=null){var N=d.vGraphObject;N.update(d)}}else if(s instanceof f){var F=s;if(F.vGraphObject!=null){var C=F.vGraphObject;C.update(F)}}},o.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=h.QUALITY,this.animationDuringLayout=h.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=h.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=h.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=h.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=h.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=h.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},o.prototype.transform=function(s){if(s==null)this.transform(new i(0,0));else{var c=new v,l=this.graphManager.getRoot().updateLeftTop();if(l!=null){c.setWorldOrgX(s.x),c.setWorldOrgY(s.y),c.setDeviceOrgX(l.x),c.setDeviceOrgY(l.y);for(var T=this.getAllNodes(),g,d=0;d<T.length;d++)g=T[d],g.transform(c)}}},o.prototype.positionNodesRandomly=function(s){if(s==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var c,l,T=s.getNodes(),g=0;g<T.length;g++)c=T[g],l=c.getChild(),l==null||l.getNodes().length==0?c.scatter():(this.positionNodesRandomly(l),c.updateBounds())},o.prototype.getFlatForest=function(){for(var s=[],c=!0,l=this.graphManager.getRoot().getNodes(),T=!0,g=0;g<l.length;g++)l[g].getChild()!=null&&(T=!1);if(!T)return s;var d=new Set,N=[],F=new Map,C=[];for(C=C.concat(l);C.length>0&&c;){for(N.push(C[0]);N.length>0&&c;){var G=N[0];N.splice(0,1),d.add(G);for(var B=G.getEdges(),g=0;g<B.length;g++){var U=B[g].getOtherEnd(G);if(F.get(G)!=U)if(!d.has(U))N.push(U),F.set(U,G);else{c=!1;break}}}if(!c)s=[];else{var K=[].concat(u(d));s.push(K);for(var g=0;g<K.length;g++){var D=K[g],at=C.indexOf(D);at>-1&&C.splice(at,1)}d=new Set,F=new Map}}return s},o.prototype.createDummyNodesForBendpoints=function(s){for(var c=[],l=s.source,T=this.graphManager.calcLowestCommonAncestor(s.source,s.target),g=0;g<s.bendpoints.length;g++){var d=this.newNode(null);d.setRect(new Point(0,0),new Dimension(1,1)),T.add(d);var N=this.newEdge(null);this.graphManager.add(N,l,d),c.add(d),l=d}var N=this.newEdge(null);return this.graphManager.add(N,l,s.target),this.edgeToDummyNodes.set(s,c),s.isInterGraph()?this.graphManager.remove(s):T.remove(s),c},o.prototype.createBendpointsFromDummyNodes=function(){var s=[];s=s.concat(this.graphManager.getAllEdges()),s=[].concat(u(this.edgeToDummyNodes.keys())).concat(s);for(var c=0;c<s.length;c++){var l=s[c];if(l.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(l),g=0;g<T.length;g++){var d=T[g],N=new i(d.getCenterX(),d.getCenterY()),F=l.bendpoints.get(g);F.x=N.x,F.y=N.y,d.getOwner().remove(d)}this.graphManager.add(l,l.source,l.target)}}},o.transform=function(s,c,l,T){if(l!=null&&T!=null){var g=c;if(s<=50){var d=c/l;g-=(c-d)/50*(50-s)}else{var N=c*T;g+=(N-c)/50*(s-50)}return g}else{var F,C;return s<=50?(F=9*c/500,C=c/10):(F=9*c/50,C=-8*c),F*s+C}},o.findCenterOfTree=function(s){var c=[];c=c.concat(s);var l=[],T=new Map,g=!1,d=null;(c.length==1||c.length==2)&&(g=!0,d=c[0]);for(var N=0;N<c.length;N++){var F=c[N],C=F.getNeighborsList().size;T.set(F,F.getNeighborsList().size),C==1&&l.push(F)}var G=[];for(G=G.concat(l);!g;){var B=[];B=B.concat(G),G=[];for(var N=0;N<c.length;N++){var F=c[N],U=c.indexOf(F);U>=0&&c.splice(U,1);var K=F.getNeighborsList();K.forEach(function(n){if(l.indexOf(n)<0){var m=T.get(n),p=m-1;p==1&&G.push(n),T.set(n,p)}})}l=l.concat(G),(c.length==1||c.length==2)&&(g=!0,d=c[0])}return d},o.prototype.setGraphManager=function(s){this.graphManager=s},M.exports=o},function(M,P,L){function u(){}u.seed=1,u.x=0,u.nextDouble=function(){return u.x=Math.sin(u.seed++)*1e4,u.x-Math.floor(u.x)},M.exports=u},function(M,P,L){var u=L(5);function h(a,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}h.prototype.getWorldOrgX=function(){return this.lworldOrgX},h.prototype.setWorldOrgX=function(a){this.lworldOrgX=a},h.prototype.getWorldOrgY=function(){return this.lworldOrgY},h.prototype.setWorldOrgY=function(a){this.lworldOrgY=a},h.prototype.getWorldExtX=function(){return this.lworldExtX},h.prototype.setWorldExtX=function(a){this.lworldExtX=a},h.prototype.getWorldExtY=function(){return this.lworldExtY},h.prototype.setWorldExtY=function(a){this.lworldExtY=a},h.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},h.prototype.setDeviceOrgX=function(a){this.ldeviceOrgX=a},h.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},h.prototype.setDeviceOrgY=function(a){this.ldeviceOrgY=a},h.prototype.getDeviceExtX=function(){return this.ldeviceExtX},h.prototype.setDeviceExtX=function(a){this.ldeviceExtX=a},h.prototype.getDeviceExtY=function(){return this.ldeviceExtY},h.prototype.setDeviceExtY=function(a){this.ldeviceExtY=a},h.prototype.transformX=function(a){var r=0,e=this.lworldExtX;return e!=0&&(r=this.ldeviceOrgX+(a-this.lworldOrgX)*this.ldeviceExtX/e),r},h.prototype.transformY=function(a){var r=0,e=this.lworldExtY;return e!=0&&(r=this.ldeviceOrgY+(a-this.lworldOrgY)*this.ldeviceExtY/e),r},h.prototype.inverseTransformX=function(a){var r=0,e=this.ldeviceExtX;return e!=0&&(r=this.lworldOrgX+(a-this.ldeviceOrgX)*this.lworldExtX/e),r},h.prototype.inverseTransformY=function(a){var r=0,e=this.ldeviceExtY;return e!=0&&(r=this.lworldOrgY+(a-this.ldeviceOrgY)*this.lworldExtY/e),r},h.prototype.inverseTransformPoint=function(a){var r=new u(this.inverseTransformX(a.x),this.inverseTransformY(a.y));return r},M.exports=h},function(M,P,L){function u(t){if(Array.isArray(t)){for(var o=0,s=Array(t.length);o<t.length;o++)s[o]=t[o];return s}else return Array.from(t)}var h=L(15),a=L(4),r=L(0),e=L(8),f=L(9);function i(){h.call(this),this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=a.MAX_ITERATIONS}i.prototype=Object.create(h.prototype);for(var v in h)i[v]=h[v];i.prototype.initParameters=function(){h.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=a.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},i.prototype.calcIdealEdgeLengths=function(){for(var t,o,s,c,l,T,g,d=this.getGraphManager().getAllEdges(),N=0;N<d.length;N++)t=d[N],o=t.idealLength,t.isInterGraph&&(c=t.getSource(),l=t.getTarget(),T=t.getSourceInLca().getEstimatedSize(),g=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=T+g-2*r.SIMPLE_NODE_SIZE),s=t.getLca().getInclusionTreeDepth(),t.idealLength+=o*a.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+l.getInclusionTreeDepth()-2*s))},i.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>a.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*a.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-a.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>a.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(a.COOLING_ADAPTATION_FACTOR,1-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*(1-a.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},i.prototype.calcSpringForces=function(){for(var t=this.getAllEdges(),o,s=0;s<t.length;s++)o=t[s],this.calcSpringForce(o,o.idealLength)},i.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s,c,l,T,g=this.getAllNodes(),d;if(this.useFRGridVariant)for(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&t&&this.updateGrid(),d=new Set,s=0;s<g.length;s++)l=g[s],this.calculateRepulsionForceOfANode(l,d,t,o),d.add(l);else for(s=0;s<g.length;s++)for(l=g[s],c=s+1;c<g.length;c++)T=g[c],l.getOwner()==T.getOwner()&&this.calcRepulsionForce(l,T)},i.prototype.calcGravitationalForces=function(){for(var t,o=this.getAllNodesToApplyGravitation(),s=0;s<o.length;s++)t=o[s],this.calcGravitationalForce(t)},i.prototype.moveNodes=function(){for(var t=this.getAllNodes(),o,s=0;s<t.length;s++)o=t[s],o.move()},i.prototype.calcSpringForce=function(t,o){var s=t.getSource(),c=t.getTarget(),l,T,g,d;if(this.uniformLeafNodeSizes&&s.getChild()==null&&c.getChild()==null)t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;l=t.getLength(),l!=0&&(T=t.edgeElasticity*(l-o),g=T*(t.lengthX/l),d=T*(t.lengthY/l),s.springForceX+=g,s.springForceY+=d,c.springForceX-=g,c.springForceY-=d)},i.prototype.calcRepulsionForce=function(t,o){var s=t.getRect(),c=o.getRect(),l=new Array(2),T=new Array(4),g,d,N,F,C,G,B;if(s.intersects(c)){e.calcSeparationAmount(s,c,l,a.DEFAULT_EDGE_LENGTH/2),G=2*l[0],B=2*l[1];var U=t.noOfChildren*o.noOfChildren/(t.noOfChildren+o.noOfChildren);t.repulsionForceX-=U*G,t.repulsionForceY-=U*B,o.repulsionForceX+=U*G,o.repulsionForceY+=U*B}else this.uniformLeafNodeSizes&&t.getChild()==null&&o.getChild()==null?(g=c.getCenterX()-s.getCenterX(),d=c.getCenterY()-s.getCenterY()):(e.getIntersection(s,c,T),g=T[2]-T[0],d=T[3]-T[1]),Math.abs(g)<a.MIN_REPULSION_DIST&&(g=f.sign(g)*a.MIN_REPULSION_DIST),Math.abs(d)<a.MIN_REPULSION_DIST&&(d=f.sign(d)*a.MIN_REPULSION_DIST),N=g*g+d*d,F=Math.sqrt(N),C=(t.nodeRepulsion/2+o.nodeRepulsion/2)*t.noOfChildren*o.noOfChildren/N,G=C*g/F,B=C*d/F,t.repulsionForceX-=G,t.repulsionForceY-=B,o.repulsionForceX+=G,o.repulsionForceY+=B},i.prototype.calcGravitationalForce=function(t){var o,s,c,l,T,g,d,N;o=t.getOwner(),s=(o.getRight()+o.getLeft())/2,c=(o.getTop()+o.getBottom())/2,l=t.getCenterX()-s,T=t.getCenterY()-c,g=Math.abs(l)+t.getWidth()/2,d=Math.abs(T)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(N=o.getEstimatedSize()*this.gravityRangeFactor,(g>N||d>N)&&(t.gravitationForceX=-this.gravityConstant*l,t.gravitationForceY=-this.gravityConstant*T)):(N=o.getEstimatedSize()*this.compoundGravityRangeFactor,(g>N||d>N)&&(t.gravitationForceX=-this.gravityConstant*l*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},i.prototype.isConverged=function(){var t,o=!1;return this.totalIterations>this.maxIterations/3&&(o=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||o},i.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},i.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,o=this.graphManager.getAllNodes(),s=0;s<o.length;s++)t=o[s],t.noOfChildren=t.getNoOfChildren()},i.prototype.calcGrid=function(t){var o=0,s=0;o=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),s=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var c=new Array(o),l=0;l<o;l++)c[l]=new Array(s);for(var l=0;l<o;l++)for(var T=0;T<s;T++)c[l][T]=new Array;return c},i.prototype.addNodeToGrid=function(t,o,s){var c=0,l=0,T=0,g=0;c=parseInt(Math.floor((t.getRect().x-o)/this.repulsionRange)),l=parseInt(Math.floor((t.getRect().width+t.getRect().x-o)/this.repulsionRange)),T=parseInt(Math.floor((t.getRect().y-s)/this.repulsionRange)),g=parseInt(Math.floor((t.getRect().height+t.getRect().y-s)/this.repulsionRange));for(var d=c;d<=l;d++)for(var N=T;N<=g;N++)this.grid[d][N].push(t),t.setGridCoordinates(c,l,T,g)},i.prototype.updateGrid=function(){var t,o,s=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<s.length;t++)o=s[t],this.addNodeToGrid(o,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},i.prototype.calculateRepulsionForceOfANode=function(t,o,s,c){if(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&s||c){var l=new Set;t.surrounding=new Array;for(var T,g=this.grid,d=t.startX-1;d<t.finishX+2;d++)for(var N=t.startY-1;N<t.finishY+2;N++)if(!(d<0||N<0||d>=g.length||N>=g[0].length)){for(var F=0;F<g[d][N].length;F++)if(T=g[d][N][F],!(t.getOwner()!=T.getOwner()||t==T)&&!o.has(T)&&!l.has(T)){var C=Math.abs(t.getCenterX()-T.getCenterX())-(t.getWidth()/2+T.getWidth()/2),G=Math.abs(t.getCenterY()-T.getCenterY())-(t.getHeight()/2+T.getHeight()/2);C<=this.repulsionRange&&G<=this.repulsionRange&&l.add(T)}}t.surrounding=[].concat(u(l))}for(d=0;d<t.surrounding.length;d++)this.calcRepulsionForce(t,t.surrounding[d])},i.prototype.calcRepulsionRange=function(){return 0},M.exports=i},function(M,P,L){var u=L(1),h=L(4);function a(e,f,i){u.call(this,e,f,i),this.idealLength=h.DEFAULT_EDGE_LENGTH,this.edgeElasticity=h.DEFAULT_SPRING_STRENGTH}a.prototype=Object.create(u.prototype);for(var r in u)a[r]=u[r];M.exports=a},function(M,P,L){var u=L(3),h=L(4);function a(e,f,i,v){u.call(this,e,f,i,v),this.nodeRepulsion=h.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}a.prototype=Object.create(u.prototype);for(var r in u)a[r]=u[r];a.prototype.setGridCoordinates=function(e,f,i,v){this.startX=e,this.finishX=f,this.startY=i,this.finishY=v},M.exports=a},function(M,P,L){function u(h,a){this.width=0,this.height=0,h!==null&&a!==null&&(this.height=a,this.width=h)}u.prototype.getWidth=function(){return this.width},u.prototype.setWidth=function(h){this.width=h},u.prototype.getHeight=function(){return this.height},u.prototype.setHeight=function(h){this.height=h},M.exports=u},function(M,P,L){var u=L(14);function h(){this.map={},this.keys=[]}h.prototype.put=function(a,r){var e=u.createID(a);this.contains(e)||(this.map[e]=r,this.keys.push(a))},h.prototype.contains=function(a){return u.createID(a),this.map[a]!=null},h.prototype.get=function(a){var r=u.createID(a);return this.map[r]},h.prototype.keySet=function(){return this.keys},M.exports=h},function(M,P,L){var u=L(14);function h(){this.set={}}h.prototype.add=function(a){var r=u.createID(a);this.contains(r)||(this.set[r]=a)},h.prototype.remove=function(a){delete this.set[u.createID(a)]},h.prototype.clear=function(){this.set={}},h.prototype.contains=function(a){return this.set[u.createID(a)]==a},h.prototype.isEmpty=function(){return this.size()===0},h.prototype.size=function(){return Object.keys(this.set).length},h.prototype.addAllTo=function(a){for(var r=Object.keys(this.set),e=r.length,f=0;f<e;f++)a.push(this.set[r[f]])},h.prototype.size=function(){return Object.keys(this.set).length},h.prototype.addAll=function(a){for(var r=a.length,e=0;e<r;e++){var f=a[e];this.add(f)}},M.exports=h},function(M,P,L){function u(){}u.multMat=function(h,a){for(var r=[],e=0;e<h.length;e++){r[e]=[];for(var f=0;f<a[0].length;f++){r[e][f]=0;for(var i=0;i<h[0].length;i++)r[e][f]+=h[e][i]*a[i][f]}}return r},u.transpose=function(h){for(var a=[],r=0;r<h[0].length;r++){a[r]=[];for(var e=0;e<h.length;e++)a[r][e]=h[e][r]}return a},u.multCons=function(h,a){for(var r=[],e=0;e<h.length;e++)r[e]=h[e]*a;return r},u.minusOp=function(h,a){for(var r=[],e=0;e<h.length;e++)r[e]=h[e]-a[e];return r},u.dotProduct=function(h,a){for(var r=0,e=0;e<h.length;e++)r+=h[e]*a[e];return r},u.mag=function(h){return Math.sqrt(this.dotProduct(h,h))},u.normalize=function(h){for(var a=[],r=this.mag(h),e=0;e<h.length;e++)a[e]=h[e]/r;return a},u.multGamma=function(h){for(var a=[],r=0,e=0;e<h.length;e++)r+=h[e];r*=-1/h.length;for(var f=0;f<h.length;f++)a[f]=r+h[f];return a},u.multL=function(h,a,r){for(var e=[],f=[],i=[],v=0;v<a[0].length;v++){for(var t=0,o=0;o<a.length;o++)t+=-.5*a[o][v]*h[o];f[v]=t}for(var s=0;s<r.length;s++){for(var c=0,l=0;l<r.length;l++)c+=r[s][l]*f[l];i[s]=c}for(var T=0;T<a.length;T++){for(var g=0,d=0;d<a[0].length;d++)g+=a[T][d]*i[d];e[T]=g}return e},M.exports=u},function(M,P,L){var u=function(){function e(f,i){for(var v=0;v<i.length;v++){var t=i[v];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(f,t.key,t)}}return function(f,i,v){return i&&e(f.prototype,i),v&&e(f,v),f}}();function h(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function")}var a=L(11),r=function(){function e(f,i){h(this,e),(i!==null||i!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var v=void 0;f instanceof a?v=f.size():v=f.length,this._quicksort(f,0,v-1)}return u(e,[{key:"_quicksort",value:function(i,v,t){if(v<t){var o=this._partition(i,v,t);this._quicksort(i,v,o),this._quicksort(i,o+1,t)}}},{key:"_partition",value:function(i,v,t){for(var o=this._get(i,v),s=v,c=t;;){for(;this.compareFunction(o,this._get(i,c));)c--;for(;this.compareFunction(this._get(i,s),o);)s++;if(s<c)this._swap(i,s,c),s++,c--;else return c}}},{key:"_get",value:function(i,v){return i instanceof a?i.get_object_at(v):i[v]}},{key:"_set",value:function(i,v,t){i instanceof a?i.set_object_at(v,t):i[v]=t}},{key:"_swap",value:function(i,v,t){var o=this._get(i,v);this._set(i,v,this._get(i,t)),this._set(i,t,o)}},{key:"_defaultCompareFunction",value:function(i,v){return v>i}}]),e}();M.exports=r},function(M,P,L){function u(){}u.svd=function(h){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=h.length,this.n=h[0].length;var a=Math.min(this.m,this.n);this.s=function(Nt){for(var Ct=[];Nt-- >0;)Ct.push(0);return Ct}(Math.min(this.m+1,this.n)),this.U=function(Nt){var Ct=function kt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(kt(Gt.slice(1)));return $t};return Ct(Nt)}([this.m,a]),this.V=function(Nt){var Ct=function kt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(kt(Gt.slice(1)));return $t};return Ct(Nt)}([this.n,this.n]);for(var r=function(Nt){for(var Ct=[];Nt-- >0;)Ct.push(0);return Ct}(this.n),e=function(Nt){for(var Ct=[];Nt-- >0;)Ct.push(0);return Ct}(this.m),f=!0,i=Math.min(this.m-1,this.n),v=Math.max(0,Math.min(this.n-2,this.m)),t=0;t<Math.max(i,v);t++){if(t<i){this.s[t]=0;for(var o=t;o<this.m;o++)this.s[t]=u.hypot(this.s[t],h[o][t]);if(this.s[t]!==0){h[t][t]<0&&(this.s[t]=-this.s[t]);for(var s=t;s<this.m;s++)h[s][t]/=this.s[t];h[t][t]+=1}this.s[t]=-this.s[t]}for(var c=t+1;c<this.n;c++){if(function(Nt,Ct){return Nt&&Ct}(t<i,this.s[t]!==0)){for(var l=0,T=t;T<this.m;T++)l+=h[T][t]*h[T][c];l=-l/h[t][t];for(var g=t;g<this.m;g++)h[g][c]+=l*h[g][t]}r[c]=h[t][c]}if(function(Nt,Ct){return Ct}(f,t<i))for(var d=t;d<this.m;d++)this.U[d][t]=h[d][t];if(t<v){r[t]=0;for(var N=t+1;N<this.n;N++)r[t]=u.hypot(r[t],r[N]);if(r[t]!==0){r[t+1]<0&&(r[t]=-r[t]);for(var F=t+1;F<this.n;F++)r[F]/=r[t];r[t+1]+=1}if(r[t]=-r[t],function(Nt,Ct){return Nt&&Ct}(t+1<this.m,r[t]!==0)){for(var C=t+1;C<this.m;C++)e[C]=0;for(var G=t+1;G<this.n;G++)for(var B=t+1;B<this.m;B++)e[B]+=r[G]*h[B][G];for(var U=t+1;U<this.n;U++)for(var K=-r[U]/r[t+1],D=t+1;D<this.m;D++)h[D][U]+=K*e[D]}for(var at=t+1;at<this.n;at++)this.V[at][t]=r[at]}}var n=Math.min(this.n,this.m+1);i<this.n&&(this.s[i]=h[i][i]),this.m<n&&(this.s[n-1]=0),v+1<n&&(r[v]=h[v][n-1]),r[n-1]=0;{for(var m=i;m<a;m++){for(var p=0;p<this.m;p++)this.U[p][m]=0;this.U[m][m]=1}for(var E=i-1;E>=0;E--)if(this.s[E]!==0){for(var y=E+1;y<a;y++){for(var I=0,w=E;w<this.m;w++)I+=this.U[w][E]*this.U[w][y];I=-I/this.U[E][E];for(var R=E;R<this.m;R++)this.U[R][y]+=I*this.U[R][E]}for(var W=E;W<this.m;W++)this.U[W][E]=-this.U[W][E];this.U[E][E]=1+this.U[E][E];for(var x=0;x<E-1;x++)this.U[x][E]=0}else{for(var q=0;q<this.m;q++)this.U[q][E]=0;this.U[E][E]=1}}for(var V=this.n-1;V>=0;V--){if(function(Nt,Ct){return Nt&&Ct}(V<v,r[V]!==0))for(var X=V+1;X<a;X++){for(var et=0,z=V+1;z<this.n;z++)et+=this.V[z][V]*this.V[z][X];et=-et/this.V[V+1][V];for(var O=V+1;O<this.n;O++)this.V[O][X]+=et*this.V[O][V]}for(var H=0;H<this.n;H++)this.V[H][V]=0;this.V[V][V]=1}for(var $=n-1,_=Math.pow(2,-52),ht=Math.pow(2,-966);n>0;){var J=void 0,Rt=void 0;for(J=n-2;J>=-1&&J!==-1;J--)if(Math.abs(r[J])<=ht+_*(Math.abs(this.s[J])+Math.abs(this.s[J+1]))){r[J]=0;break}if(J===n-2)Rt=4;else{var Lt=void 0;for(Lt=n-1;Lt>=J&&Lt!==J;Lt--){var vt=(Lt!==n?Math.abs(r[Lt]):0)+(Lt!==J+1?Math.abs(r[Lt-1]):0);if(Math.abs(this.s[Lt])<=ht+_*vt){this.s[Lt]=0;break}}Lt===J?Rt=3:Lt===n-1?Rt=1:(Rt=2,J=Lt)}switch(J++,Rt){case 1:{var rt=r[n-2];r[n-2]=0;for(var gt=n-2;gt>=J;gt--){var Tt=u.hypot(this.s[gt],rt),Mt=this.s[gt]/Tt,Dt=rt/Tt;this.s[gt]=Tt,gt!==J&&(rt=-Dt*r[gt-1],r[gt-1]=Mt*r[gt-1]);for(var mt=0;mt<this.n;mt++)Tt=Mt*this.V[mt][gt]+Dt*this.V[mt][n-1],this.V[mt][n-1]=-Dt*this.V[mt][gt]+Mt*this.V[mt][n-1],this.V[mt][gt]=Tt}}break;case 2:{var xt=r[J-1];r[J-1]=0;for(var St=J;St<n;St++){var Vt=u.hypot(this.s[St],xt),Xt=this.s[St]/Vt,Ut=xt/Vt;this.s[St]=Vt,xt=-Ut*r[St],r[St]=Xt*r[St];for(var bt=0;bt<this.m;bt++)Vt=Xt*this.U[bt][St]+Ut*this.U[bt][J-1],this.U[bt][J-1]=-Ut*this.U[bt][St]+Xt*this.U[bt][J-1],this.U[bt][St]=Vt}}break;case 3:{var Ht=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[n-1]),Math.abs(this.s[n-2])),Math.abs(r[n-2])),Math.abs(this.s[J])),Math.abs(r[J])),Bt=this.s[n-1]/Ht,S=this.s[n-2]/Ht,b=r[n-2]/Ht,k=this.s[J]/Ht,Q=r[J]/Ht,Z=((S+Bt)*(S-Bt)+b*b)/2,it=Bt*b*(Bt*b),ut=0;(function(Nt,Ct){return Nt||Ct})(Z!==0,it!==0)&&(ut=Math.sqrt(Z*Z+it),Z<0&&(ut=-ut),ut=it/(Z+ut));for(var ot=(k+Bt)*(k-Bt)+ut,tt=k*Q,j=J;j<n-1;j++){var dt=u.hypot(ot,tt),wt=ot/dt,yt=tt/dt;j!==J&&(r[j-1]=dt),ot=wt*this.s[j]+yt*r[j],r[j]=wt*r[j]-yt*this.s[j],tt=yt*this.s[j+1],this.s[j+1]=wt*this.s[j+1];for(var It=0;It<this.n;It++)dt=wt*this.V[It][j]+yt*this.V[It][j+1],this.V[It][j+1]=-yt*this.V[It][j]+wt*this.V[It][j+1],this.V[It][j]=dt;if(dt=u.hypot(ot,tt),wt=ot/dt,yt=tt/dt,this.s[j]=dt,ot=wt*r[j]+yt*this.s[j+1],this.s[j+1]=-yt*r[j]+wt*this.s[j+1],tt=yt*r[j+1],r[j+1]=wt*r[j+1],j<this.m-1)for(var ft=0;ft<this.m;ft++)dt=wt*this.U[ft][j]+yt*this.U[ft][j+1],this.U[ft][j+1]=-yt*this.U[ft][j]+wt*this.U[ft][j+1],this.U[ft][j]=dt}r[n-2]=ot}break;case 4:{if(this.s[J]<=0){this.s[J]=this.s[J]<0?-this.s[J]:0;for(var st=0;st<=$;st++)this.V[st][J]=-this.V[st][J]}for(;J<$&&!(this.s[J]>=this.s[J+1]);){var At=this.s[J];if(this.s[J]=this.s[J+1],this.s[J+1]=At,J<this.n-1)for(var ct=0;ct<this.n;ct++)At=this.V[ct][J+1],this.V[ct][J+1]=this.V[ct][J],this.V[ct][J]=At;if(J<this.m-1)for(var lt=0;lt<this.m;lt++)At=this.U[lt][J+1],this.U[lt][J+1]=this.U[lt][J],this.U[lt][J]=At;J++}n--}break}}var Wt={U:this.U,V:this.V,S:this.s};return Wt},u.hypot=function(h,a){var r=void 0;return Math.abs(h)>Math.abs(a)?(r=a/h,r=Math.abs(h)*Math.sqrt(1+r*r)):a!=0?(r=h/a,r=Math.abs(a)*Math.sqrt(1+r*r)):r=0,r},M.exports=u},function(M,P,L){var u=function(){function r(e,f){for(var i=0;i<f.length;i++){var v=f[i];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(e,v.key,v)}}return function(e,f,i){return f&&r(e.prototype,f),i&&r(e,i),e}}();function h(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}var a=function(){function r(e,f){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;h(this,r),this.sequence1=e,this.sequence2=f,this.match_score=i,this.mismatch_penalty=v,this.gap_penalty=t,this.iMax=e.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var o=0;o<this.iMax;o++){this.grid[o]=new Array(this.jMax);for(var s=0;s<this.jMax;s++)this.grid[o][s]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[c][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return u(r,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var i=1;i<this.iMax;i++)this.grid[i][0]=this.grid[i-1][0]+this.gap_penalty,this.tracebackGrid[i][0]=[!1,!0,!1];for(var v=1;v<this.iMax;v++)for(var t=1;t<this.jMax;t++){var o=void 0;this.sequence1[v-1]===this.sequence2[t-1]?o=this.grid[v-1][t-1]+this.match_score:o=this.grid[v-1][t-1]+this.mismatch_penalty;var s=this.grid[v-1][t]+this.gap_penalty,c=this.grid[v][t-1]+this.gap_penalty,l=[o,s,c],T=this.arrayAllMaxIndexes(l);this.grid[v][t]=l[T[0]],this.tracebackGrid[v][t]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var i=f[0],v=this.tracebackGrid[i.pos[0]][i.pos[1]];v[0]&&f.push({pos:[i.pos[0]-1,i.pos[1]-1],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),v[1]&&f.push({pos:[i.pos[0]-1,i.pos[1]],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:"-"+i.seq2}),v[2]&&f.push({pos:[i.pos[0],i.pos[1]-1],seq1:"-"+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),i.pos[0]===0&&i.pos[1]===0&&this.alignments.push({sequence1:i.seq1,sequence2:i.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,i){for(var v=[],t=-1;(t=f.indexOf(i,t+1))!==-1;)v.push(t);return v}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),r}();M.exports=a},function(M,P,L){var u=function(){};u.FDLayout=L(18),u.FDLayoutConstants=L(4),u.FDLayoutEdge=L(19),u.FDLayoutNode=L(20),u.DimensionD=L(21),u.HashMap=L(22),u.HashSet=L(23),u.IGeometry=L(8),u.IMath=L(9),u.Integer=L(10),u.Point=L(12),u.PointD=L(5),u.RandomSeed=L(16),u.RectangleD=L(13),u.Transform=L(17),u.UniqueIDGeneretor=L(14),u.Quicksort=L(25),u.LinkedList=L(11),u.LGraphObject=L(2),u.LGraph=L(6),u.LEdge=L(1),u.LGraphManager=L(7),u.LNode=L(3),u.Layout=L(15),u.LayoutConstants=L(0),u.NeedlemanWunsch=L(27),u.Matrix=L(24),u.SVD=L(26),M.exports=u},function(M,P,L){function u(){this.listeners=[]}var h=u.prototype;h.addListener=function(a,r){this.listeners.push({event:a,callback:r})},h.removeListener=function(a,r){for(var e=this.listeners.length;e>=0;e--){var f=this.listeners[e];f.event===a&&f.callback===r&&this.listeners.splice(e,1)}},h.emit=function(a,r){for(var e=0;e<this.listeners.length;e++){var f=this.listeners[e];a===f.event&&f.callback(r)}},M.exports=u}])})}(ye)),ye.exports}var Ie;function ur(){return Ie||(Ie=1,function(A,Y){(function(P,L){A.exports=L(cr())})(Le,function(M){return(()=>{var P={45:(a,r,e)=>{var f={};f.layoutBase=e(551),f.CoSEConstants=e(806),f.CoSEEdge=e(767),f.CoSEGraph=e(880),f.CoSEGraphManager=e(578),f.CoSELayout=e(765),f.CoSENode=e(991),f.ConstraintHandler=e(902),a.exports=f},806:(a,r,e)=>{var f=e(551).FDLayoutConstants;function i(){}for(var v in f)i[v]=f[v];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=f.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,i.ENFORCE_CONSTRAINTS=!0,i.APPLY_LAYOUT=!0,i.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,i.TREE_REDUCTION_ON_INCREMENTAL=!0,i.PURE_INCREMENTAL=i.DEFAULT_INCREMENTAL,a.exports=i},767:(a,r,e)=>{var f=e(551).FDLayoutEdge;function i(t,o,s){f.call(this,t,o,s)}i.prototype=Object.create(f.prototype);for(var v in f)i[v]=f[v];a.exports=i},880:(a,r,e)=>{var f=e(551).LGraph;function i(t,o,s){f.call(this,t,o,s)}i.prototype=Object.create(f.prototype);for(var v in f)i[v]=f[v];a.exports=i},578:(a,r,e)=>{var f=e(551).LGraphManager;function i(t){f.call(this,t)}i.prototype=Object.create(f.prototype);for(var v in f)i[v]=f[v];a.exports=i},765:(a,r,e)=>{var f=e(551).FDLayout,i=e(578),v=e(880),t=e(991),o=e(767),s=e(806),c=e(902),l=e(551).FDLayoutConstants,T=e(551).LayoutConstants,g=e(551).Point,d=e(551).PointD,N=e(551).DimensionD,F=e(551).Layout,C=e(551).Integer,G=e(551).IGeometry,B=e(551).LGraph,U=e(551).Transform,K=e(551).LinkedList;function D(){f.call(this),this.toBeTiled={},this.constraints={}}D.prototype=Object.create(f.prototype);for(var at in f)D[at]=f[at];D.prototype.newGraphManager=function(){var n=new i(this);return this.graphManager=n,n},D.prototype.newGraph=function(n){return new v(null,this.graphManager,n)},D.prototype.newNode=function(n){return new t(this.graphManager,n)},D.prototype.newEdge=function(n){return new o(null,null,n)},D.prototype.initParameters=function(){f.prototype.initParameters.call(this,arguments),this.isSubLayout||(s.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=s.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=s.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=l.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=l.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},D.prototype.initSpringEmbedder=function(){f.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/l.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},D.prototype.layout=function(){var n=T.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},D.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(s.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(I){return m.has(I)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(E){return m.has(E)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),s.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},D.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%l.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),m=this.nodesWithGravity.filter(function(y){return n.has(y)});this.graphManager.setAllNodesToApplyGravitation(m),this.graphManager.updateBounds(),this.updateGrid(),s.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),s.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,E=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,E),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},D.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),m={},p=0;p<n.length;p++){var E=n[p].rect,y=n[p].id;m[y]={id:y,x:E.getCenterX(),y:E.getCenterY(),w:E.width,h:E.height}}return m},D.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(l.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},D.prototype.moveNodes=function(){for(var n=this.getAllNodes(),m,p=0;p<n.length;p++)m=n[p],m.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var p=0;p<n.length;p++)m=n[p],m.move()},D.prototype.initConstraintVariables=function(){var n=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var m=this.graphManager.getAllNodes(),p=0;p<m.length;p++){var E=m[p];this.idToNodeMap.set(E.id,E)}var y=function O(H){for(var $=H.getChild().getNodes(),_,ht=0,J=0;J<$.length;J++)_=$[J],_.getChild()==null?n.fixedNodeSet.has(_.id)&&(ht+=100):ht+=O(_);return ht};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function($){n.fixedNodeSet.add($.nodeId)});for(var m=this.graphManager.getAllNodes(),E,p=0;p<m.length;p++)if(E=m[p],E.getChild()!=null){var I=y(E);I>0&&(E.fixedNodeWeight=I)}}if(this.constraints.relativePlacementConstraint){var w=new Map,R=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(O){n.fixedNodesOnHorizontal.add(O),n.fixedNodesOnVertical.add(O)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var W=this.constraints.alignmentConstraint.vertical,p=0;p<W.length;p++)this.dummyToNodeForVerticalAlignment.set("dummy"+p,[]),W[p].forEach(function(H){w.set(H,"dummy"+p),n.dummyToNodeForVerticalAlignment.get("dummy"+p).push(H),n.fixedNodeSet.has(H)&&n.fixedNodesOnHorizontal.add("dummy"+p)});if(this.constraints.alignmentConstraint.horizontal)for(var x=this.constraints.alignmentConstraint.horizontal,p=0;p<x.length;p++)this.dummyToNodeForHorizontalAlignment.set("dummy"+p,[]),x[p].forEach(function(H){R.set(H,"dummy"+p),n.dummyToNodeForHorizontalAlignment.get("dummy"+p).push(H),n.fixedNodeSet.has(H)&&n.fixedNodesOnVertical.add("dummy"+p)})}if(s.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(O){var H,$,_;for(_=O.length-1;_>=2*O.length/3;_--)H=Math.floor(Math.random()*(_+1)),$=O[_],O[_]=O[H],O[H]=$;return O},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var H=w.has(O.left)?w.get(O.left):O.left,$=w.has(O.right)?w.get(O.right):O.right;n.nodesInRelativeHorizontal.includes(H)||(n.nodesInRelativeHorizontal.push(H),n.nodeToRelativeConstraintMapHorizontal.set(H,[]),n.dummyToNodeForVerticalAlignment.has(H)?n.nodeToTempPositionMapHorizontal.set(H,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(H)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(H,n.idToNodeMap.get(H).getCenterX())),n.nodesInRelativeHorizontal.includes($)||(n.nodesInRelativeHorizontal.push($),n.nodeToRelativeConstraintMapHorizontal.set($,[]),n.dummyToNodeForVerticalAlignment.has($)?n.nodeToTempPositionMapHorizontal.set($,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get($)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set($,n.idToNodeMap.get($).getCenterX())),n.nodeToRelativeConstraintMapHorizontal.get(H).push({right:$,gap:O.gap}),n.nodeToRelativeConstraintMapHorizontal.get($).push({left:H,gap:O.gap})}else{var _=R.has(O.top)?R.get(O.top):O.top,ht=R.has(O.bottom)?R.get(O.bottom):O.bottom;n.nodesInRelativeVertical.includes(_)||(n.nodesInRelativeVertical.push(_),n.nodeToRelativeConstraintMapVertical.set(_,[]),n.dummyToNodeForHorizontalAlignment.has(_)?n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(_)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(_).getCenterY())),n.nodesInRelativeVertical.includes(ht)||(n.nodesInRelativeVertical.push(ht),n.nodeToRelativeConstraintMapVertical.set(ht,[]),n.dummyToNodeForHorizontalAlignment.has(ht)?n.nodeToTempPositionMapVertical.set(ht,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(ht)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(ht,n.idToNodeMap.get(ht).getCenterY())),n.nodeToRelativeConstraintMapVertical.get(_).push({bottom:ht,gap:O.gap}),n.nodeToRelativeConstraintMapVertical.get(ht).push({top:_,gap:O.gap})}});else{var q=new Map,V=new Map;this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var H=w.has(O.left)?w.get(O.left):O.left,$=w.has(O.right)?w.get(O.right):O.right;q.has(H)?q.get(H).push($):q.set(H,[$]),q.has($)?q.get($).push(H):q.set($,[H])}else{var _=R.has(O.top)?R.get(O.top):O.top,ht=R.has(O.bottom)?R.get(O.bottom):O.bottom;V.has(_)?V.get(_).push(ht):V.set(_,[ht]),V.has(ht)?V.get(ht).push(_):V.set(ht,[_])}});var X=function(H,$){var _=[],ht=[],J=new K,Rt=new Set,Lt=0;return H.forEach(function(vt,rt){if(!Rt.has(rt)){_[Lt]=[],ht[Lt]=!1;var gt=rt;for(J.push(gt),Rt.add(gt),_[Lt].push(gt);J.length!=0;){gt=J.shift(),$.has(gt)&&(ht[Lt]=!0);var Tt=H.get(gt);Tt.forEach(function(Mt){Rt.has(Mt)||(J.push(Mt),Rt.add(Mt),_[Lt].push(Mt))})}Lt++}}),{components:_,isFixed:ht}},et=X(q,n.fixedNodesOnHorizontal);this.componentsOnHorizontal=et.components,this.fixedComponentsOnHorizontal=et.isFixed;var z=X(V,n.fixedNodesOnVertical);this.componentsOnVertical=z.components,this.fixedComponentsOnVertical=z.isFixed}}},D.prototype.updateDisplacements=function(){var n=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(z){var O=n.idToNodeMap.get(z.nodeId);O.displacementX=0,O.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var m=this.constraints.alignmentConstraint.vertical,p=0;p<m.length;p++){for(var E=0,y=0;y<m[p].length;y++){if(this.fixedNodeSet.has(m[p][y])){E=0;break}E+=this.idToNodeMap.get(m[p][y]).displacementX}for(var I=E/m[p].length,y=0;y<m[p].length;y++)this.idToNodeMap.get(m[p][y]).displacementX=I}if(this.constraints.alignmentConstraint.horizontal)for(var w=this.constraints.alignmentConstraint.horizontal,p=0;p<w.length;p++){for(var R=0,y=0;y<w[p].length;y++){if(this.fixedNodeSet.has(w[p][y])){R=0;break}R+=this.idToNodeMap.get(w[p][y]).displacementY}for(var W=R/w[p].length,y=0;y<w[p].length;y++)this.idToNodeMap.get(w[p][y]).displacementY=W}}if(this.constraints.relativePlacementConstraint)if(s.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForVerticalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(z)[0]).displacementX:O=n.idToNodeMap.get(z).displacementX,n.nodeToRelativeConstraintMapHorizontal.get(z).forEach(function(H){if(H.right){var $=n.nodeToTempPositionMapHorizontal.get(H.right)-n.nodeToTempPositionMapHorizontal.get(z)-O;$<H.gap&&(O-=H.gap-$)}else{var $=n.nodeToTempPositionMapHorizontal.get(z)-n.nodeToTempPositionMapHorizontal.get(H.left)+O;$<H.gap&&(O+=H.gap-$)}}),n.nodeToTempPositionMapHorizontal.set(z,n.nodeToTempPositionMapHorizontal.get(z)+O),n.dummyToNodeForVerticalAlignment.has(z)?n.dummyToNodeForVerticalAlignment.get(z).forEach(function(H){n.idToNodeMap.get(H).displacementX=O}):n.idToNodeMap.get(z).displacementX=O}}),this.nodesInRelativeVertical.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForHorizontalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(z)[0]).displacementY:O=n.idToNodeMap.get(z).displacementY,n.nodeToRelativeConstraintMapVertical.get(z).forEach(function(H){if(H.bottom){var $=n.nodeToTempPositionMapVertical.get(H.bottom)-n.nodeToTempPositionMapVertical.get(z)-O;$<H.gap&&(O-=H.gap-$)}else{var $=n.nodeToTempPositionMapVertical.get(z)-n.nodeToTempPositionMapVertical.get(H.top)+O;$<H.gap&&(O+=H.gap-$)}}),n.nodeToTempPositionMapVertical.set(z,n.nodeToTempPositionMapVertical.get(z)+O),n.dummyToNodeForHorizontalAlignment.has(z)?n.dummyToNodeForHorizontalAlignment.get(z).forEach(function(H){n.idToNodeMap.get(H).displacementY=O}):n.idToNodeMap.get(z).displacementY=O}});else{for(var p=0;p<this.componentsOnHorizontal.length;p++){var x=this.componentsOnHorizontal[p];if(this.fixedComponentsOnHorizontal[p])for(var y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(H){n.idToNodeMap.get(H).displacementX=0}):this.idToNodeMap.get(x[y]).displacementX=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForVerticalAlignment.has(x[y])){var X=this.dummyToNodeForVerticalAlignment.get(x[y]);q+=X.length*this.idToNodeMap.get(X[0]).displacementX,V+=X.length}else q+=this.idToNodeMap.get(x[y]).displacementX,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(H){n.idToNodeMap.get(H).displacementX=et}):this.idToNodeMap.get(x[y]).displacementX=et}}for(var p=0;p<this.componentsOnVertical.length;p++){var x=this.componentsOnVertical[p];if(this.fixedComponentsOnVertical[p])for(var y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function($){n.idToNodeMap.get($).displacementY=0}):this.idToNodeMap.get(x[y]).displacementY=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForHorizontalAlignment.has(x[y])){var X=this.dummyToNodeForHorizontalAlignment.get(x[y]);q+=X.length*this.idToNodeMap.get(X[0]).displacementY,V+=X.length}else q+=this.idToNodeMap.get(x[y]).displacementY,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function(J){n.idToNodeMap.get(J).displacementY=et}):this.idToNodeMap.get(x[y]).displacementY=et}}}},D.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],m,p=this.graphManager.getGraphs(),E=p.length,y;for(y=0;y<E;y++)m=p[y],m.updateConnected(),m.isConnected||(n=n.concat(m.getNodes()));return n},D.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var m=new Set,p;for(p=0;p<n.length;p++){var E=n[p];if(!m.has(E)){var y=E.getSource(),I=E.getTarget();if(y==I)E.getBendpoints().push(new d),E.getBendpoints().push(new d),this.createDummyNodesForBendpoints(E),m.add(E);else{var w=[];if(w=w.concat(y.getEdgeListToNode(I)),w=w.concat(I.getEdgeListToNode(y)),!m.has(w[0])){if(w.length>1){var R;for(R=0;R<w.length;R++){var W=w[R];W.getBendpoints().push(new d),this.createDummyNodesForBendpoints(W)}}w.forEach(function(x){m.add(x)})}}}if(m.size==n.length)break}},D.prototype.positionNodesRadially=function(n){for(var m=new g(0,0),p=Math.ceil(Math.sqrt(n.length)),E=0,y=0,I=0,w=new d(0,0),R=0;R<n.length;R++){R%p==0&&(I=0,y=E,R!=0&&(y+=s.DEFAULT_COMPONENT_SEPERATION),E=0);var W=n[R],x=F.findCenterOfTree(W);m.x=I,m.y=y,w=D.radialLayout(W,x,m),w.y>E&&(E=Math.floor(w.y)),I=Math.floor(w.x+s.DEFAULT_COMPONENT_SEPERATION)}this.transform(new d(T.WORLD_CENTER_X-w.x/2,T.WORLD_CENTER_Y-w.y/2))},D.radialLayout=function(n,m,p){var E=Math.max(this.maxDiagonalInTree(n),s.DEFAULT_RADIAL_SEPARATION);D.branchRadialLayout(m,null,0,359,0,E);var y=B.calculateBounds(n),I=new U;I.setDeviceOrgX(y.getMinX()),I.setDeviceOrgY(y.getMinY()),I.setWorldOrgX(p.x),I.setWorldOrgY(p.y);for(var w=0;w<n.length;w++){var R=n[w];R.transform(I)}var W=new d(y.getMaxX(),y.getMaxY());return I.inverseTransformPoint(W)},D.branchRadialLayout=function(n,m,p,E,y,I){var w=(E-p+1)/2;w<0&&(w+=180);var R=(w+p)%360,W=R*G.TWO_PI/360,x=y*Math.cos(W),q=y*Math.sin(W);n.setCenter(x,q);var V=[];V=V.concat(n.getEdges());var X=V.length;m!=null&&X--;for(var et=0,z=V.length,O,H=n.getEdgesBetween(m);H.length>1;){var $=H[0];H.splice(0,1);var _=V.indexOf($);_>=0&&V.splice(_,1),z--,X--}m!=null?O=(V.indexOf(H[0])+1)%z:O=0;for(var ht=Math.abs(E-p)/X,J=O;et!=X;J=++J%z){var Rt=V[J].getOtherEnd(n);if(Rt!=m){var Lt=(p+et*ht)%360,vt=(Lt+ht)%360;D.branchRadialLayout(Rt,n,Lt,vt,y+I,I),et++}}},D.maxDiagonalInTree=function(n){for(var m=C.MIN_VALUE,p=0;p<n.length;p++){var E=n[p],y=E.getDiagonal();y>m&&(m=y)}return m},D.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},D.prototype.groupZeroDegreeMembers=function(){var n=this,m={};this.memberGroups={},this.idToDummyNode={};for(var p=[],E=this.graphManager.getAllNodes(),y=0;y<E.length;y++){var I=E[y],w=I.getParent();this.getNodeDegreeWithChildren(I)===0&&(w.id==null||!this.getToBeTiled(w))&&p.push(I)}for(var y=0;y<p.length;y++){var I=p[y],R=I.getParent().id;typeof m[R]>"u"&&(m[R]=[]),m[R]=m[R].concat(I)}Object.keys(m).forEach(function(W){if(m[W].length>1){var x="DummyCompound_"+W;n.memberGroups[x]=m[W];var q=m[W][0].getParent(),V=new t(n.graphManager);V.id=x,V.paddingLeft=q.paddingLeft||0,V.paddingRight=q.paddingRight||0,V.paddingBottom=q.paddingBottom||0,V.paddingTop=q.paddingTop||0,n.idToDummyNode[x]=V;var X=n.getGraphManager().add(n.newGraph(),V),et=q.getChild();et.add(V);for(var z=0;z<m[W].length;z++){var O=m[W][z];et.remove(O),X.add(O)}}})},D.prototype.clearCompounds=function(){var n={},m={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)m[this.compoundOrder[p].id]=this.compoundOrder[p],n[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,m)},D.prototype.clearZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var E=n.idToDummyNode[p];if(m[p]=n.tileNodes(n.memberGroups[p],E.paddingLeft+E.paddingRight),E.rect.width=m[p].width,E.rect.height=m[p].height,E.setCenter(m[p].centerX,m[p].centerY),E.labelMarginLeft=0,E.labelMarginTop=0,s.NODE_DIMENSIONS_INCLUDE_LABELS){var y=E.rect.width,I=E.rect.height;E.labelWidth&&(E.labelPosHorizontal=="left"?(E.rect.x-=E.labelWidth,E.setWidth(y+E.labelWidth),E.labelMarginLeft=E.labelWidth):E.labelPosHorizontal=="center"&&E.labelWidth>y?(E.rect.x-=(E.labelWidth-y)/2,E.setWidth(E.labelWidth),E.labelMarginLeft=(E.labelWidth-y)/2):E.labelPosHorizontal=="right"&&E.setWidth(y+E.labelWidth)),E.labelHeight&&(E.labelPosVertical=="top"?(E.rect.y-=E.labelHeight,E.setHeight(I+E.labelHeight),E.labelMarginTop=E.labelHeight):E.labelPosVertical=="center"&&E.labelHeight>I?(E.rect.y-=(E.labelHeight-I)/2,E.setHeight(E.labelHeight),E.labelMarginTop=(E.labelHeight-I)/2):E.labelPosVertical=="bottom"&&E.setHeight(I+E.labelHeight))}})},D.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var m=this.compoundOrder[n],p=m.id,E=m.paddingLeft,y=m.paddingTop,I=m.labelMarginLeft,w=m.labelMarginTop;this.adjustLocations(this.tiledMemberPack[p],m.rect.x,m.rect.y,E,y,I,w)}},D.prototype.repopulateZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack;Object.keys(m).forEach(function(p){var E=n.idToDummyNode[p],y=E.paddingLeft,I=E.paddingTop,w=E.labelMarginLeft,R=E.labelMarginTop;n.adjustLocations(m[p],E.rect.x,E.rect.y,y,I,w,R)})},D.prototype.getToBeTiled=function(n){var m=n.id;if(this.toBeTiled[m]!=null)return this.toBeTiled[m];var p=n.getChild();if(p==null)return this.toBeTiled[m]=!1,!1;for(var E=p.getNodes(),y=0;y<E.length;y++){var I=E[y];if(this.getNodeDegree(I)>0)return this.toBeTiled[m]=!1,!1;if(I.getChild()==null){this.toBeTiled[I.id]=!1;continue}if(!this.getToBeTiled(I))return this.toBeTiled[m]=!1,!1}return this.toBeTiled[m]=!0,!0},D.prototype.getNodeDegree=function(n){n.id;for(var m=n.getEdges(),p=0,E=0;E<m.length;E++){var y=m[E];y.getSource().id!==y.getTarget().id&&(p=p+1)}return p},D.prototype.getNodeDegreeWithChildren=function(n){var m=this.getNodeDegree(n);if(n.getChild()==null)return m;for(var p=n.getChild().getNodes(),E=0;E<p.length;E++){var y=p[E];m+=this.getNodeDegreeWithChildren(y)}return m},D.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},D.prototype.fillCompexOrderByDFS=function(n){for(var m=0;m<n.length;m++){var p=n[m];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},D.prototype.adjustLocations=function(n,m,p,E,y,I,w){m+=E+I,p+=y+w;for(var R=m,W=0;W<n.rows.length;W++){var x=n.rows[W];m=R;for(var q=0,V=0;V<x.length;V++){var X=x[V];X.rect.x=m,X.rect.y=p,m+=X.rect.width+n.horizontalPadding,X.rect.height>q&&(q=X.rect.height)}p+=q+n.verticalPadding}},D.prototype.tileCompoundMembers=function(n,m){var p=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(E){var y=m[E];if(p.tiledMemberPack[E]=p.tileNodes(n[E],y.paddingLeft+y.paddingRight),y.rect.width=p.tiledMemberPack[E].width,y.rect.height=p.tiledMemberPack[E].height,y.setCenter(p.tiledMemberPack[E].centerX,p.tiledMemberPack[E].centerY),y.labelMarginLeft=0,y.labelMarginTop=0,s.NODE_DIMENSIONS_INCLUDE_LABELS){var I=y.rect.width,w=y.rect.height;y.labelWidth&&(y.labelPosHorizontal=="left"?(y.rect.x-=y.labelWidth,y.setWidth(I+y.labelWidth),y.labelMarginLeft=y.labelWidth):y.labelPosHorizontal=="center"&&y.labelWidth>I?(y.rect.x-=(y.labelWidth-I)/2,y.setWidth(y.labelWidth),y.labelMarginLeft=(y.labelWidth-I)/2):y.labelPosHorizontal=="right"&&y.setWidth(I+y.labelWidth)),y.labelHeight&&(y.labelPosVertical=="top"?(y.rect.y-=y.labelHeight,y.setHeight(w+y.labelHeight),y.labelMarginTop=y.labelHeight):y.labelPosVertical=="center"&&y.labelHeight>w?(y.rect.y-=(y.labelHeight-w)/2,y.setHeight(y.labelHeight),y.labelMarginTop=(y.labelHeight-w)/2):y.labelPosVertical=="bottom"&&y.setHeight(w+y.labelHeight))}})},D.prototype.tileNodes=function(n,m){var p=this.tileNodesByFavoringDim(n,m,!0),E=this.tileNodesByFavoringDim(n,m,!1),y=this.getOrgRatio(p),I=this.getOrgRatio(E),w;return I<y?w=E:w=p,w},D.prototype.getOrgRatio=function(n){var m=n.width,p=n.height,E=m/p;return E<1&&(E=1/E),E},D.prototype.calcIdealRowWidth=function(n,m){var p=s.TILING_PADDING_VERTICAL,E=s.TILING_PADDING_HORIZONTAL,y=n.length,I=0,w=0,R=0;n.forEach(function(z){I+=z.getWidth(),w+=z.getHeight(),z.getWidth()>R&&(R=z.getWidth())});var W=I/y,x=w/y,q=Math.pow(p-E,2)+4*(W+E)*(x+p)*y,V=(E-p+Math.sqrt(q))/(2*(W+E)),X;m?(X=Math.ceil(V),X==V&&X++):X=Math.floor(V);var et=X*(W+E)-E;return R>et&&(et=R),et+=E*2,et},D.prototype.tileNodesByFavoringDim=function(n,m,p){var E=s.TILING_PADDING_VERTICAL,y=s.TILING_PADDING_HORIZONTAL,I=s.TILING_COMPARE_BY,w={rows:[],rowWidth:[],rowHeight:[],width:0,height:m,verticalPadding:E,horizontalPadding:y,centerX:0,centerY:0};I&&(w.idealRowWidth=this.calcIdealRowWidth(n,p));var R=function(O){return O.rect.width*O.rect.height},W=function(O,H){return R(H)-R(O)};n.sort(function(z,O){var H=W;return w.idealRowWidth?(H=I,H(z.id,O.id)):H(z,O)});for(var x=0,q=0,V=0;V<n.length;V++){var X=n[V];x+=X.getCenterX(),q+=X.getCenterY()}w.centerX=x/n.length,w.centerY=q/n.length;for(var V=0;V<n.length;V++){var X=n[V];if(w.rows.length==0)this.insertNodeToRow(w,X,0,m);else if(this.canAddHorizontal(w,X.rect.width,X.rect.height)){var et=w.rows.length-1;w.idealRowWidth||(et=this.getShortestRowIndex(w)),this.insertNodeToRow(w,X,et,m)}else this.insertNodeToRow(w,X,w.rows.length,m);this.shiftToLastRow(w)}return w},D.prototype.insertNodeToRow=function(n,m,p,E){var y=E;if(p==n.rows.length){var I=[];n.rows.push(I),n.rowWidth.push(y),n.rowHeight.push(0)}var w=n.rowWidth[p]+m.rect.width;n.rows[p].length>0&&(w+=n.horizontalPadding),n.rowWidth[p]=w,n.width<w&&(n.width=w);var R=m.rect.height;p>0&&(R+=n.verticalPadding);var W=0;R>n.rowHeight[p]&&(W=n.rowHeight[p],n.rowHeight[p]=R,W=n.rowHeight[p]-W),n.height+=W,n.rows[p].push(m)},D.prototype.getShortestRowIndex=function(n){for(var m=-1,p=Number.MAX_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]<p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.getLongestRowIndex=function(n){for(var m=-1,p=Number.MIN_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]>p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.canAddHorizontal=function(n,m,p){if(n.idealRowWidth){var E=n.rows.length-1,y=n.rowWidth[E];return y+m+n.horizontalPadding<=n.idealRowWidth}var I=this.getShortestRowIndex(n);if(I<0)return!0;var w=n.rowWidth[I];if(w+n.horizontalPadding+m<=n.width)return!0;var R=0;n.rowHeight[I]<p&&I>0&&(R=p+n.verticalPadding-n.rowHeight[I]);var W;n.width-w>=m+n.horizontalPadding?W=(n.height+R)/(w+m+n.horizontalPadding):W=(n.height+R)/n.width,R=p+n.verticalPadding;var x;return n.width<m?x=(n.height+R)/m:x=(n.height+R)/n.width,x<1&&(x=1/x),W<1&&(W=1/W),W<x},D.prototype.shiftToLastRow=function(n){var m=this.getLongestRowIndex(n),p=n.rowWidth.length-1,E=n.rows[m],y=E[E.length-1],I=y.width+n.horizontalPadding;if(n.width-n.rowWidth[p]>I&&m!=p){E.splice(-1,1),n.rows[p].push(y),n.rowWidth[m]=n.rowWidth[m]-I,n.rowWidth[p]=n.rowWidth[p]+I,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var w=Number.MIN_VALUE,R=0;R<E.length;R++)E[R].height>w&&(w=E[R].height);m>0&&(w+=n.verticalPadding);var W=n.rowHeight[m]+n.rowHeight[p];n.rowHeight[m]=w,n.rowHeight[p]<y.height+n.verticalPadding&&(n.rowHeight[p]=y.height+n.verticalPadding);var x=n.rowHeight[m]+n.rowHeight[p];n.height+=x-W,this.shiftToLastRow(n)}},D.prototype.tilingPreLayout=function(){s.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},D.prototype.tilingPostLayout=function(){s.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},D.prototype.reduceTrees=function(){for(var n=[],m=!0,p;m;){var E=this.graphManager.getAllNodes(),y=[];m=!1;for(var I=0;I<E.length;I++)if(p=E[I],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null){if(s.PURE_INCREMENTAL){var w=p.getEdges()[0].getOtherEnd(p),R=new N(p.getCenterX()-w.getCenterX(),p.getCenterY()-w.getCenterY());y.push([p,p.getEdges()[0],p.getOwner(),R])}else y.push([p,p.getEdges()[0],p.getOwner()]);m=!0}if(m==!0){for(var W=[],x=0;x<y.length;x++)y[x][0].getEdges().length==1&&(W.push(y[x]),y[x][0].getOwner().remove(y[x][0]));n.push(W),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},D.prototype.growTree=function(n){for(var m=n.length,p=n[m-1],E,y=0;y<p.length;y++)E=p[y],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},D.prototype.findPlaceforPrunedNode=function(n){var m,p,E=n[0];if(E==n[1].source?p=n[1].target:p=n[1].source,s.PURE_INCREMENTAL)E.setCenter(p.getCenterX()+n[3].getWidth(),p.getCenterY()+n[3].getHeight());else{var y=p.startX,I=p.finishX,w=p.startY,R=p.finishY,W=0,x=0,q=0,V=0,X=[W,q,x,V];if(w>0)for(var et=y;et<=I;et++)X[0]+=this.grid[et][w-1].length+this.grid[et][w].length-1;if(I<this.grid.length-1)for(var et=w;et<=R;et++)X[1]+=this.grid[I+1][et].length+this.grid[I][et].length-1;if(R<this.grid[0].length-1)for(var et=y;et<=I;et++)X[2]+=this.grid[et][R+1].length+this.grid[et][R].length-1;if(y>0)for(var et=w;et<=R;et++)X[3]+=this.grid[y-1][et].length+this.grid[y][et].length-1;for(var z=C.MAX_VALUE,O,H,$=0;$<X.length;$++)X[$]<z?(z=X[$],O=1,H=$):X[$]==z&&O++;if(O==3&&z==0)X[0]==0&&X[1]==0&&X[2]==0?m=1:X[0]==0&&X[1]==0&&X[3]==0?m=0:X[0]==0&&X[2]==0&&X[3]==0?m=3:X[1]==0&&X[2]==0&&X[3]==0&&(m=2);else if(O==2&&z==0){var _=Math.floor(Math.random()*2);X[0]==0&&X[1]==0?_==0?m=0:m=1:X[0]==0&&X[2]==0?_==0?m=0:m=2:X[0]==0&&X[3]==0?_==0?m=0:m=3:X[1]==0&&X[2]==0?_==0?m=1:m=2:X[1]==0&&X[3]==0?_==0?m=1:m=3:_==0?m=2:m=3}else if(O==4&&z==0){var _=Math.floor(Math.random()*4);m=_}else m=H;m==0?E.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-l.DEFAULT_EDGE_LENGTH-E.getHeight()/2):m==1?E.setCenter(p.getCenterX()+p.getWidth()/2+l.DEFAULT_EDGE_LENGTH+E.getWidth()/2,p.getCenterY()):m==2?E.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+l.DEFAULT_EDGE_LENGTH+E.getHeight()/2):E.setCenter(p.getCenterX()-p.getWidth()/2-l.DEFAULT_EDGE_LENGTH-E.getWidth()/2,p.getCenterY())}},a.exports=D},991:(a,r,e)=>{var f=e(551).FDLayoutNode,i=e(551).IMath;function v(o,s,c,l){f.call(this,o,s,c,l)}v.prototype=Object.create(f.prototype);for(var t in f)v[t]=f[t];v.prototype.calculateDisplacement=function(){var o=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=o.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=o.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=o.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=o.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementX=o.coolingFactor*o.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementY=o.coolingFactor*o.maxNodeDisplacement*i.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},v.prototype.propogateDisplacementToChildren=function(o,s){for(var c=this.getChild().getNodes(),l,T=0;T<c.length;T++)l=c[T],l.getChild()==null?(l.displacementX+=o,l.displacementY+=s):l.propogateDisplacementToChildren(o,s)},v.prototype.move=function(){var o=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),o.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},v.prototype.setPred1=function(o){this.pred1=o},v.prototype.getPred1=function(){return pred1},v.prototype.getPred2=function(){return pred2},v.prototype.setNext=function(o){this.next=o},v.prototype.getNext=function(){return next},v.prototype.setProcessed=function(o){this.processed=o},v.prototype.isProcessed=function(){return processed},a.exports=v},902:(a,r,e)=>{function f(c){if(Array.isArray(c)){for(var l=0,T=Array(c.length);l<c.length;l++)T[l]=c[l];return T}else return Array.from(c)}var i=e(806),v=e(551).LinkedList,t=e(551).Matrix,o=e(551).SVD;function s(){}s.handleConstraints=function(c){var l={};l.fixedNodeConstraint=c.constraints.fixedNodeConstraint,l.alignmentConstraint=c.constraints.alignmentConstraint,l.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var T=new Map,g=new Map,d=[],N=[],F=c.getAllNodes(),C=0,G=0;G<F.length;G++){var B=F[G];B.getChild()==null&&(g.set(B.id,C++),d.push(B.getCenterX()),N.push(B.getCenterY()),T.set(B.id,B))}l.relativePlacementConstraint&&l.relativePlacementConstraint.forEach(function(S){!S.gap&&S.gap!=0&&(S.left?S.gap=i.DEFAULT_EDGE_LENGTH+T.get(S.left).getWidth()/2+T.get(S.right).getWidth()/2:S.gap=i.DEFAULT_EDGE_LENGTH+T.get(S.top).getHeight()/2+T.get(S.bottom).getHeight()/2)});var U=function(b,k){return{x:b.x-k.x,y:b.y-k.y}},K=function(b){var k=0,Q=0;return b.forEach(function(Z){k+=d[g.get(Z)],Q+=N[g.get(Z)]}),{x:k/b.size,y:Q/b.size}},D=function(b,k,Q,Z,it){function ut(ft,st){var At=new Set(ft),ct=!0,lt=!1,Wt=void 0;try{for(var Nt=st[Symbol.iterator](),Ct;!(ct=(Ct=Nt.next()).done);ct=!0){var kt=Ct.value;At.add(kt)}}catch(Gt){lt=!0,Wt=Gt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(lt)throw Wt}}return At}var ot=new Map;b.forEach(function(ft,st){ot.set(st,0)}),b.forEach(function(ft,st){ft.forEach(function(At){ot.set(At.id,ot.get(At.id)+1)})});var tt=new Map,j=new Map,dt=new v;ot.forEach(function(ft,st){ft==0?(dt.push(st),Q||(k=="horizontal"?tt.set(st,g.has(st)?d[g.get(st)]:Z.get(st)):tt.set(st,g.has(st)?N[g.get(st)]:Z.get(st)))):tt.set(st,Number.NEGATIVE_INFINITY),Q&&j.set(st,new Set([st]))}),Q&&it.forEach(function(ft){var st=[];if(ft.forEach(function(lt){Q.has(lt)&&st.push(lt)}),st.length>0){var At=0;st.forEach(function(lt){k=="horizontal"?(tt.set(lt,g.has(lt)?d[g.get(lt)]:Z.get(lt)),At+=tt.get(lt)):(tt.set(lt,g.has(lt)?N[g.get(lt)]:Z.get(lt)),At+=tt.get(lt))}),At=At/st.length,ft.forEach(function(lt){Q.has(lt)||tt.set(lt,At)})}else{var ct=0;ft.forEach(function(lt){k=="horizontal"?ct+=g.has(lt)?d[g.get(lt)]:Z.get(lt):ct+=g.has(lt)?N[g.get(lt)]:Z.get(lt)}),ct=ct/ft.length,ft.forEach(function(lt){tt.set(lt,ct)})}});for(var wt=function(){var st=dt.shift(),At=b.get(st);At.forEach(function(ct){if(tt.get(ct.id)<tt.get(st)+ct.gap)if(Q&&Q.has(ct.id)){var lt=void 0;if(k=="horizontal"?lt=g.has(ct.id)?d[g.get(ct.id)]:Z.get(ct.id):lt=g.has(ct.id)?N[g.get(ct.id)]:Z.get(ct.id),tt.set(ct.id,lt),lt<tt.get(st)+ct.gap){var Wt=tt.get(st)+ct.gap-lt;j.get(st).forEach(function(Nt){tt.set(Nt,tt.get(Nt)-Wt)})}}else tt.set(ct.id,tt.get(st)+ct.gap);ot.set(ct.id,ot.get(ct.id)-1),ot.get(ct.id)==0&&dt.push(ct.id),Q&&j.set(ct.id,ut(j.get(st),j.get(ct.id)))})};dt.length!=0;)wt();if(Q){var yt=new Set;b.forEach(function(ft,st){ft.length==0&&yt.add(st)});var It=[];j.forEach(function(ft,st){if(yt.has(st)){var At=!1,ct=!0,lt=!1,Wt=void 0;try{for(var Nt=ft[Symbol.iterator](),Ct;!(ct=(Ct=Nt.next()).done);ct=!0){var kt=Ct.value;Q.has(kt)&&(At=!0)}}catch(Ft){lt=!0,Wt=Ft}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(lt)throw Wt}}if(!At){var Gt=!1,$t=void 0;It.forEach(function(Ft,qt){Ft.has([].concat(f(ft))[0])&&(Gt=!0,$t=qt)}),Gt?ft.forEach(function(Ft){It[$t].add(Ft)}):It.push(new Set(ft))}}}),It.forEach(function(ft,st){var At=Number.POSITIVE_INFINITY,ct=Number.POSITIVE_INFINITY,lt=Number.NEGATIVE_INFINITY,Wt=Number.NEGATIVE_INFINITY,Nt=!0,Ct=!1,kt=void 0;try{for(var Gt=ft[Symbol.iterator](),$t;!(Nt=($t=Gt.next()).done);Nt=!0){var Ft=$t.value,qt=void 0;k=="horizontal"?qt=g.has(Ft)?d[g.get(Ft)]:Z.get(Ft):qt=g.has(Ft)?N[g.get(Ft)]:Z.get(Ft);var _t=tt.get(Ft);qt<At&&(At=qt),qt>lt&&(lt=qt),_t<ct&&(ct=_t),_t>Wt&&(Wt=_t)}}catch(ie){Ct=!0,kt=ie}finally{try{!Nt&&Gt.return&&Gt.return()}finally{if(Ct)throw kt}}var ce=(At+lt)/2-(ct+Wt)/2,Kt=!0,te=!1,ee=void 0;try{for(var jt=ft[Symbol.iterator](),se;!(Kt=(se=jt.next()).done);Kt=!0){var re=se.value;tt.set(re,tt.get(re)+ce)}}catch(ie){te=!0,ee=ie}finally{try{!Kt&&jt.return&&jt.return()}finally{if(te)throw ee}}})}return tt},at=function(b){var k=0,Q=0,Z=0,it=0;if(b.forEach(function(j){j.left?d[g.get(j.left)]-d[g.get(j.right)]>=0?k++:Q++:N[g.get(j.top)]-N[g.get(j.bottom)]>=0?Z++:it++}),k>Q&&Z>it)for(var ut=0;ut<g.size;ut++)d[ut]=-1*d[ut],N[ut]=-1*N[ut];else if(k>Q)for(var ot=0;ot<g.size;ot++)d[ot]=-1*d[ot];else if(Z>it)for(var tt=0;tt<g.size;tt++)N[tt]=-1*N[tt]},n=function(b){var k=[],Q=new v,Z=new Set,it=0;return b.forEach(function(ut,ot){if(!Z.has(ot)){k[it]=[];var tt=ot;for(Q.push(tt),Z.add(tt),k[it].push(tt);Q.length!=0;){tt=Q.shift();var j=b.get(tt);j.forEach(function(dt){Z.has(dt.id)||(Q.push(dt.id),Z.add(dt.id),k[it].push(dt.id))})}it++}}),k},m=function(b){var k=new Map;return b.forEach(function(Q,Z){k.set(Z,[])}),b.forEach(function(Q,Z){Q.forEach(function(it){k.get(Z).push(it),k.get(it.id).push({id:Z,gap:it.gap,direction:it.direction})})}),k},p=function(b){var k=new Map;return b.forEach(function(Q,Z){k.set(Z,[])}),b.forEach(function(Q,Z){Q.forEach(function(it){k.get(it.id).push({id:Z,gap:it.gap,direction:it.direction})})}),k},E=[],y=[],I=!1,w=!1,R=new Set,W=new Map,x=new Map,q=[];if(l.fixedNodeConstraint&&l.fixedNodeConstraint.forEach(function(S){R.add(S.nodeId)}),l.relativePlacementConstraint&&(l.relativePlacementConstraint.forEach(function(S){S.left?(W.has(S.left)?W.get(S.left).push({id:S.right,gap:S.gap,direction:"horizontal"}):W.set(S.left,[{id:S.right,gap:S.gap,direction:"horizontal"}]),W.has(S.right)||W.set(S.right,[])):(W.has(S.top)?W.get(S.top).push({id:S.bottom,gap:S.gap,direction:"vertical"}):W.set(S.top,[{id:S.bottom,gap:S.gap,direction:"vertical"}]),W.has(S.bottom)||W.set(S.bottom,[]))}),x=m(W),q=n(x)),i.TRANSFORM_ON_CONSTRAINT_HANDLING){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>1)l.fixedNodeConstraint.forEach(function(S,b){E[b]=[S.position.x,S.position.y],y[b]=[d[g.get(S.nodeId)],N[g.get(S.nodeId)]]}),I=!0;else if(l.alignmentConstraint)(function(){var S=0;if(l.alignmentConstraint.vertical){for(var b=l.alignmentConstraint.vertical,k=function(tt){var j=new Set;b[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return R.has(yt)})),wt=void 0;dt.size>0?wt=d[g.get(dt.values().next().value)]:wt=K(j).x,b[tt].forEach(function(yt){E[S]=[wt,N[g.get(yt)]],y[S]=[d[g.get(yt)],N[g.get(yt)]],S++})},Q=0;Q<b.length;Q++)k(Q);I=!0}if(l.alignmentConstraint.horizontal){for(var Z=l.alignmentConstraint.horizontal,it=function(tt){var j=new Set;Z[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return R.has(yt)})),wt=void 0;dt.size>0?wt=d[g.get(dt.values().next().value)]:wt=K(j).y,Z[tt].forEach(function(yt){E[S]=[d[g.get(yt)],wt],y[S]=[d[g.get(yt)],N[g.get(yt)]],S++})},ut=0;ut<Z.length;ut++)it(ut);I=!0}l.relativePlacementConstraint&&(w=!0)})();else if(l.relativePlacementConstraint){for(var V=0,X=0,et=0;et<q.length;et++)q[et].length>V&&(V=q[et].length,X=et);if(V<x.size/2)at(l.relativePlacementConstraint),I=!1,w=!1;else{var z=new Map,O=new Map,H=[];q[X].forEach(function(S){W.get(S).forEach(function(b){b.direction=="horizontal"?(z.has(S)?z.get(S).push(b):z.set(S,[b]),z.has(b.id)||z.set(b.id,[]),H.push({left:S,right:b.id})):(O.has(S)?O.get(S).push(b):O.set(S,[b]),O.has(b.id)||O.set(b.id,[]),H.push({top:S,bottom:b.id}))})}),at(H),w=!1;var $=D(z,"horizontal"),_=D(O,"vertical");q[X].forEach(function(S,b){y[b]=[d[g.get(S)],N[g.get(S)]],E[b]=[],$.has(S)?E[b][0]=$.get(S):E[b][0]=d[g.get(S)],_.has(S)?E[b][1]=_.get(S):E[b][1]=N[g.get(S)]}),I=!0}}if(I){for(var ht=void 0,J=t.transpose(E),Rt=t.transpose(y),Lt=0;Lt<J.length;Lt++)J[Lt]=t.multGamma(J[Lt]),Rt[Lt]=t.multGamma(Rt[Lt]);var vt=t.multMat(J,t.transpose(Rt)),rt=o.svd(vt);ht=t.multMat(rt.V,t.transpose(rt.U));for(var gt=0;gt<g.size;gt++){var Tt=[d[gt],N[gt]],Mt=[ht[0][0],ht[1][0]],Dt=[ht[0][1],ht[1][1]];d[gt]=t.dotProduct(Tt,Mt),N[gt]=t.dotProduct(Tt,Dt)}w&&at(l.relativePlacementConstraint)}}if(i.ENFORCE_CONSTRAINTS){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>0){var mt={x:0,y:0};l.fixedNodeConstraint.forEach(function(S,b){var k={x:d[g.get(S.nodeId)],y:N[g.get(S.nodeId)]},Q=S.position,Z=U(Q,k);mt.x+=Z.x,mt.y+=Z.y}),mt.x/=l.fixedNodeConstraint.length,mt.y/=l.fixedNodeConstraint.length,d.forEach(function(S,b){d[b]+=mt.x}),N.forEach(function(S,b){N[b]+=mt.y}),l.fixedNodeConstraint.forEach(function(S){d[g.get(S.nodeId)]=S.position.x,N[g.get(S.nodeId)]=S.position.y})}if(l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var xt=l.alignmentConstraint.vertical,St=function(b){var k=new Set;xt[b].forEach(function(it){k.add(it)});var Q=new Set([].concat(f(k)).filter(function(it){return R.has(it)})),Z=void 0;Q.size>0?Z=d[g.get(Q.values().next().value)]:Z=K(k).x,k.forEach(function(it){R.has(it)||(d[g.get(it)]=Z)})},Vt=0;Vt<xt.length;Vt++)St(Vt);if(l.alignmentConstraint.horizontal)for(var Xt=l.alignmentConstraint.horizontal,Ut=function(b){var k=new Set;Xt[b].forEach(function(it){k.add(it)});var Q=new Set([].concat(f(k)).filter(function(it){return R.has(it)})),Z=void 0;Q.size>0?Z=N[g.get(Q.values().next().value)]:Z=K(k).y,k.forEach(function(it){R.has(it)||(N[g.get(it)]=Z)})},bt=0;bt<Xt.length;bt++)Ut(bt)}l.relativePlacementConstraint&&function(){var S=new Map,b=new Map,k=new Map,Q=new Map,Z=new Map,it=new Map,ut=new Set,ot=new Set;if(R.forEach(function(Yt){ut.add(Yt),ot.add(Yt)}),l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var tt=l.alignmentConstraint.vertical,j=function(Et){k.set("dummy"+Et,[]),tt[Et].forEach(function(Ot){S.set(Ot,"dummy"+Et),k.get("dummy"+Et).push(Ot),R.has(Ot)&&ut.add("dummy"+Et)}),Z.set("dummy"+Et,d[g.get(tt[Et][0])])},dt=0;dt<tt.length;dt++)j(dt);if(l.alignmentConstraint.horizontal)for(var wt=l.alignmentConstraint.horizontal,yt=function(Et){Q.set("dummy"+Et,[]),wt[Et].forEach(function(Ot){b.set(Ot,"dummy"+Et),Q.get("dummy"+Et).push(Ot),R.has(Ot)&&ot.add("dummy"+Et)}),it.set("dummy"+Et,N[g.get(wt[Et][0])])},It=0;It<wt.length;It++)yt(It)}var ft=new Map,st=new Map,At=function(Et){W.get(Et).forEach(function(Ot){var Jt=void 0,Zt=void 0;Ot.direction=="horizontal"?(Jt=S.get(Et)?S.get(Et):Et,S.get(Ot.id)?Zt={id:S.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:Zt=Ot,ft.has(Jt)?ft.get(Jt).push(Zt):ft.set(Jt,[Zt]),ft.has(Zt.id)||ft.set(Zt.id,[])):(Jt=b.get(Et)?b.get(Et):Et,b.get(Ot.id)?Zt={id:b.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:Zt=Ot,st.has(Jt)?st.get(Jt).push(Zt):st.set(Jt,[Zt]),st.has(Zt.id)||st.set(Zt.id,[]))})},ct=!0,lt=!1,Wt=void 0;try{for(var Nt=W.keys()[Symbol.iterator](),Ct;!(ct=(Ct=Nt.next()).done);ct=!0){var kt=Ct.value;At(kt)}}catch(Yt){lt=!0,Wt=Yt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(lt)throw Wt}}var Gt=m(ft),$t=m(st),Ft=n(Gt),qt=n($t),_t=p(ft),ce=p(st),Kt=[],te=[];Ft.forEach(function(Yt,Et){Kt[Et]=[],Yt.forEach(function(Ot){_t.get(Ot).length==0&&Kt[Et].push(Ot)})}),qt.forEach(function(Yt,Et){te[Et]=[],Yt.forEach(function(Ot){ce.get(Ot).length==0&&te[Et].push(Ot)})});var ee=D(ft,"horizontal",ut,Z,Kt),jt=D(st,"vertical",ot,it,te),se=function(Et){k.get(Et)?k.get(Et).forEach(function(Ot){d[g.get(Ot)]=ee.get(Et)}):d[g.get(Et)]=ee.get(Et)},re=!0,ie=!1,Ce=void 0;try{for(var ue=ee.keys()[Symbol.iterator](),Me;!(re=(Me=ue.next()).done);re=!0){var ge=Me.value;se(ge)}}catch(Yt){ie=!0,Ce=Yt}finally{try{!re&&ue.return&&ue.return()}finally{if(ie)throw Ce}}var Ze=function(Et){Q.get(Et)?Q.get(Et).forEach(function(Ot){N[g.get(Ot)]=jt.get(Et)}):N[g.get(Et)]=jt.get(Et)},de=!0,we=!1,Oe=void 0;try{for(var ve=jt.keys()[Symbol.iterator](),De;!(de=(De=ve.next()).done);de=!0){var ge=De.value;Ze(ge)}}catch(Yt){we=!0,Oe=Yt}finally{try{!de&&ve.return&&ve.return()}finally{if(we)throw Oe}}}()}for(var Ht=0;Ht<F.length;Ht++){var Bt=F[Ht];Bt.getChild()==null&&Bt.setCenter(d[g.get(Bt.id)],N[g.get(Bt.id)])}},a.exports=s},551:a=>{a.exports=M}},L={};function u(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return P[a](e,e.exports,u),e.exports}var h=u(45);return h})()})}(pe)),pe.exports}(function(A,Y){(function(P,L){A.exports=L(ur())})(Le,function(M){return(()=>{var P={658:a=>{a.exports=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments.length,f=Array(e>1?e-1:0),i=1;i<e;i++)f[i-1]=arguments[i];return f.forEach(function(v){Object.keys(v).forEach(function(t){return r[t]=v[t]})}),r}},548:(a,r,e)=>{var f=function(){function t(o,s){var c=[],l=!0,T=!1,g=void 0;try{for(var d=o[Symbol.iterator](),N;!(l=(N=d.next()).done)&&(c.push(N.value),!(s&&c.length===s));l=!0);}catch(F){T=!0,g=F}finally{try{!l&&d.return&&d.return()}finally{if(T)throw g}}return c}return function(o,s){if(Array.isArray(o))return o;if(Symbol.iterator in Object(o))return t(o,s);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),i=e(140).layoutBase.LinkedList,v={};v.getTopMostNodes=function(t){for(var o={},s=0;s<t.length;s++)o[t[s].id()]=!0;var c=t.filter(function(l,T){typeof l=="number"&&(l=T);for(var g=l.parent()[0];g!=null;){if(o[g.id()])return!1;g=g.parent()[0]}return!0});return c},v.connectComponents=function(t,o,s,c){var l=new i,T=new Set,g=[],d=void 0,N=void 0,F=void 0,C=!1,G=1,B=[],U=[],K=function(){var at=t.collection();U.push(at);var n=s[0],m=t.collection();m.merge(n).merge(n.descendants().intersection(o)),g.push(n),m.forEach(function(y){l.push(y),T.add(y),at.merge(y)});for(var p=function(){n=l.shift();var I=t.collection();n.neighborhood().nodes().forEach(function(x){o.intersection(n.edgesWith(x)).length>0&&I.merge(x)});for(var w=0;w<I.length;w++){var R=I[w];if(d=s.intersection(R.union(R.ancestors())),d!=null&&!T.has(d[0])){var W=d.union(d.descendants());W.forEach(function(x){l.push(x),T.add(x),at.merge(x),s.has(x)&&g.push(x)})}}};l.length!=0;)p();if(at.forEach(function(y){o.intersection(y.connectedEdges()).forEach(function(I){at.has(I.source())&&at.has(I.target())&&at.merge(I)})}),g.length==s.length&&(C=!0),!C||C&&G>1){N=g[0],F=N.connectedEdges().length,g.forEach(function(y){y.connectedEdges().length<F&&(F=y.connectedEdges().length,N=y)}),B.push(N.id());var E=t.collection();E.merge(g[0]),g.forEach(function(y){E.merge(y)}),g=[],s=s.difference(E),G++}};do K();while(!C);return c&&B.length>0&&c.set("dummy"+(c.size+1),B),U},v.relocateComponent=function(t,o,s){if(!s.fixedNodeConstraint){var c=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,T=Number.POSITIVE_INFINITY,g=Number.NEGATIVE_INFINITY;if(s.quality=="draft"){var d=!0,N=!1,F=void 0;try{for(var C=o.nodeIndexes[Symbol.iterator](),G;!(d=(G=C.next()).done);d=!0){var B=G.value,U=f(B,2),K=U[0],D=U[1],at=s.cy.getElementById(K);if(at){var n=at.boundingBox(),m=o.xCoords[D]-n.w/2,p=o.xCoords[D]+n.w/2,E=o.yCoords[D]-n.h/2,y=o.yCoords[D]+n.h/2;m<c&&(c=m),p>l&&(l=p),E<T&&(T=E),y>g&&(g=y)}}}catch(x){N=!0,F=x}finally{try{!d&&C.return&&C.return()}finally{if(N)throw F}}var I=t.x-(l+c)/2,w=t.y-(g+T)/2;o.xCoords=o.xCoords.map(function(x){return x+I}),o.yCoords=o.yCoords.map(function(x){return x+w})}else{Object.keys(o).forEach(function(x){var q=o[x],V=q.getRect().x,X=q.getRect().x+q.getRect().width,et=q.getRect().y,z=q.getRect().y+q.getRect().height;V<c&&(c=V),X>l&&(l=X),et<T&&(T=et),z>g&&(g=z)});var R=t.x-(l+c)/2,W=t.y-(g+T)/2;Object.keys(o).forEach(function(x){var q=o[x];q.setCenter(q.getCenterX()+R,q.getCenterY()+W)})}}},v.calcBoundingBox=function(t,o,s,c){for(var l=Number.MAX_SAFE_INTEGER,T=Number.MIN_SAFE_INTEGER,g=Number.MAX_SAFE_INTEGER,d=Number.MIN_SAFE_INTEGER,N=void 0,F=void 0,C=void 0,G=void 0,B=t.descendants().not(":parent"),U=B.length,K=0;K<U;K++){var D=B[K];N=o[c.get(D.id())]-D.width()/2,F=o[c.get(D.id())]+D.width()/2,C=s[c.get(D.id())]-D.height()/2,G=s[c.get(D.id())]+D.height()/2,l>N&&(l=N),T<F&&(T=F),g>C&&(g=C),d<G&&(d=G)}var at={};return at.topLeftX=l,at.topLeftY=g,at.width=T-l,at.height=d-g,at},v.calcParentsWithoutChildren=function(t,o){var s=t.collection();return o.nodes(":parent").forEach(function(c){var l=!1;c.children().forEach(function(T){T.css("display")!="none"&&(l=!0)}),l||s.merge(c)}),s},a.exports=v},816:(a,r,e)=>{var f=e(548),i=e(140).CoSELayout,v=e(140).CoSENode,t=e(140).layoutBase.PointD,o=e(140).layoutBase.DimensionD,s=e(140).layoutBase.LayoutConstants,c=e(140).layoutBase.FDLayoutConstants,l=e(140).CoSEConstants,T=function(d,N){var F=d.cy,C=d.eles,G=C.nodes(),B=C.edges(),U=void 0,K=void 0,D=void 0,at={};d.randomize&&(U=N.nodeIndexes,K=N.xCoords,D=N.yCoords);var n=function(x){return typeof x=="function"},m=function(x,q){return n(x)?x(q):x},p=f.calcParentsWithoutChildren(F,C),E=function W(x,q,V,X){for(var et=q.length,z=0;z<et;z++){var O=q[z],H=null;O.intersection(p).length==0&&(H=O.children());var $=void 0,_=O.layoutDimensions({nodeDimensionsIncludeLabels:X.nodeDimensionsIncludeLabels});if(O.outerWidth()!=null&&O.outerHeight()!=null)if(X.randomize)if(!O.isParent())$=x.add(new v(V.graphManager,new t(K[U.get(O.id())]-_.w/2,D[U.get(O.id())]-_.h/2),new o(parseFloat(_.w),parseFloat(_.h))));else{var ht=f.calcBoundingBox(O,K,D,U);O.intersection(p).length==0?$=x.add(new v(V.graphManager,new t(ht.topLeftX,ht.topLeftY),new o(ht.width,ht.height))):$=x.add(new v(V.graphManager,new t(ht.topLeftX,ht.topLeftY),new o(parseFloat(_.w),parseFloat(_.h))))}else $=x.add(new v(V.graphManager,new t(O.position("x")-_.w/2,O.position("y")-_.h/2),new o(parseFloat(_.w),parseFloat(_.h))));else $=x.add(new v(this.graphManager));if($.id=O.data("id"),$.nodeRepulsion=m(X.nodeRepulsion,O),$.paddingLeft=parseInt(O.css("padding")),$.paddingTop=parseInt(O.css("padding")),$.paddingRight=parseInt(O.css("padding")),$.paddingBottom=parseInt(O.css("padding")),X.nodeDimensionsIncludeLabels&&($.labelWidth=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,$.labelHeight=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,$.labelPosVertical=O.css("text-valign"),$.labelPosHorizontal=O.css("text-halign")),at[O.data("id")]=$,isNaN($.rect.x)&&($.rect.x=0),isNaN($.rect.y)&&($.rect.y=0),H!=null&&H.length>0){var J=void 0;J=V.getGraphManager().add(V.newGraph(),$),W(J,H,V,X)}}},y=function(x,q,V){for(var X=0,et=0,z=0;z<V.length;z++){var O=V[z],H=at[O.data("source")],$=at[O.data("target")];if(H&&$&&H!==$&&H.getEdgesBetween($).length==0){var _=q.add(x.newEdge(),H,$);_.id=O.id(),_.idealLength=m(d.idealEdgeLength,O),_.edgeElasticity=m(d.edgeElasticity,O),X+=_.idealLength,et++}}d.idealEdgeLength!=null&&(et>0?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=X/et:n(d.idealEdgeLength)?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=50:l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=d.idealEdgeLength,l.MIN_REPULSION_DIST=c.MIN_REPULSION_DIST=c.DEFAULT_EDGE_LENGTH/10,l.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH)},I=function(x,q){q.fixedNodeConstraint&&(x.constraints.fixedNodeConstraint=q.fixedNodeConstraint),q.alignmentConstraint&&(x.constraints.alignmentConstraint=q.alignmentConstraint),q.relativePlacementConstraint&&(x.constraints.relativePlacementConstraint=q.relativePlacementConstraint)};d.nestingFactor!=null&&(l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=d.nestingFactor),d.gravity!=null&&(l.DEFAULT_GRAVITY_STRENGTH=c.DEFAULT_GRAVITY_STRENGTH=d.gravity),d.numIter!=null&&(l.MAX_ITERATIONS=c.MAX_ITERATIONS=d.numIter),d.gravityRange!=null&&(l.DEFAULT_GRAVITY_RANGE_FACTOR=c.DEFAULT_GRAVITY_RANGE_FACTOR=d.gravityRange),d.gravityCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=d.gravityCompound),d.gravityRangeCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=d.gravityRangeCompound),d.initialEnergyOnIncremental!=null&&(l.DEFAULT_COOLING_FACTOR_INCREMENTAL=c.DEFAULT_COOLING_FACTOR_INCREMENTAL=d.initialEnergyOnIncremental),d.tilingCompareBy!=null&&(l.TILING_COMPARE_BY=d.tilingCompareBy),d.quality=="proof"?s.QUALITY=2:s.QUALITY=0,l.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=s.NODE_DIMENSIONS_INCLUDE_LABELS=d.nodeDimensionsIncludeLabels,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=s.DEFAULT_INCREMENTAL=!d.randomize,l.ANIMATE=c.ANIMATE=s.ANIMATE=d.animate,l.TILE=d.tile,l.TILING_PADDING_VERTICAL=typeof d.tilingPaddingVertical=="function"?d.tilingPaddingVertical.call():d.tilingPaddingVertical,l.TILING_PADDING_HORIZONTAL=typeof d.tilingPaddingHorizontal=="function"?d.tilingPaddingHorizontal.call():d.tilingPaddingHorizontal,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=s.DEFAULT_INCREMENTAL=!0,l.PURE_INCREMENTAL=!d.randomize,s.DEFAULT_UNIFORM_LEAF_NODE_SIZES=d.uniformNodeDimensions,d.step=="transformed"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!1),d.step=="enforced"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!1),d.step=="cose"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!0),d.step=="all"&&(d.randomize?l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!0),d.fixedNodeConstraint||d.alignmentConstraint||d.relativePlacementConstraint?l.TREE_REDUCTION_ON_INCREMENTAL=!1:l.TREE_REDUCTION_ON_INCREMENTAL=!0;var w=new i,R=w.newGraphManager();return E(R.addRoot(),f.getTopMostNodes(G),w,d),y(w,R,B),I(w,d),w.runLayout(),at};a.exports={coseLayout:T}},212:(a,r,e)=>{var f=function(){function d(N,F){for(var C=0;C<F.length;C++){var G=F[C];G.enumerable=G.enumerable||!1,G.configurable=!0,"value"in G&&(G.writable=!0),Object.defineProperty(N,G.key,G)}}return function(N,F,C){return F&&d(N.prototype,F),C&&d(N,C),N}}();function i(d,N){if(!(d instanceof N))throw new TypeError("Cannot call a class as a function")}var v=e(658),t=e(548),o=e(657),s=o.spectralLayout,c=e(816),l=c.coseLayout,T=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(N){return 4500},idealEdgeLength:function(N){return 50},edgeElasticity:function(N){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),g=function(){function d(N){i(this,d),this.options=v({},T,N)}return f(d,[{key:"run",value:function(){var F=this,C=this.options,G=C.cy,B=C.eles,U=[],K=[],D=void 0,at=[];C.fixedNodeConstraint&&(!Array.isArray(C.fixedNodeConstraint)||C.fixedNodeConstraint.length==0)&&(C.fixedNodeConstraint=void 0),C.alignmentConstraint&&(C.alignmentConstraint.vertical&&(!Array.isArray(C.alignmentConstraint.vertical)||C.alignmentConstraint.vertical.length==0)&&(C.alignmentConstraint.vertical=void 0),C.alignmentConstraint.horizontal&&(!Array.isArray(C.alignmentConstraint.horizontal)||C.alignmentConstraint.horizontal.length==0)&&(C.alignmentConstraint.horizontal=void 0)),C.relativePlacementConstraint&&(!Array.isArray(C.relativePlacementConstraint)||C.relativePlacementConstraint.length==0)&&(C.relativePlacementConstraint=void 0);var n=C.fixedNodeConstraint||C.alignmentConstraint||C.relativePlacementConstraint;n&&(C.tile=!1,C.packComponents=!1);var m=void 0,p=!1;if(G.layoutUtilities&&C.packComponents&&(m=G.layoutUtilities("get"),m||(m=G.layoutUtilities()),p=!0),B.nodes().length>0)if(p){var I=t.getTopMostNodes(C.eles.nodes());if(D=t.connectComponents(G,C.eles,I),D.forEach(function(vt){var rt=vt.boundingBox();at.push({x:rt.x1+rt.w/2,y:rt.y1+rt.h/2})}),C.randomize&&D.forEach(function(vt){C.eles=vt,U.push(s(C))}),C.quality=="default"||C.quality=="proof"){var w=G.collection();if(C.tile){var R=new Map,W=[],x=[],q=0,V={nodeIndexes:R,xCoords:W,yCoords:x},X=[];if(D.forEach(function(vt,rt){vt.edges().length==0&&(vt.nodes().forEach(function(gt,Tt){w.merge(vt.nodes()[Tt]),gt.isParent()||(V.nodeIndexes.set(vt.nodes()[Tt].id(),q++),V.xCoords.push(vt.nodes()[0].position().x),V.yCoords.push(vt.nodes()[0].position().y))}),X.push(rt))}),w.length>1){var et=w.boundingBox();at.push({x:et.x1+et.w/2,y:et.y1+et.h/2}),D.push(w),U.push(V);for(var z=X.length-1;z>=0;z--)D.splice(X[z],1),U.splice(X[z],1),at.splice(X[z],1)}}D.forEach(function(vt,rt){C.eles=vt,K.push(l(C,U[rt])),t.relocateComponent(at[rt],K[rt],C)})}else D.forEach(function(vt,rt){t.relocateComponent(at[rt],U[rt],C)});var O=new Set;if(D.length>1){var H=[],$=B.filter(function(vt){return vt.css("display")=="none"});D.forEach(function(vt,rt){var gt=void 0;if(C.quality=="draft"&&(gt=U[rt].nodeIndexes),vt.nodes().not($).length>0){var Tt={};Tt.edges=[],Tt.nodes=[];var Mt=void 0;vt.nodes().not($).forEach(function(Dt){if(C.quality=="draft")if(!Dt.isParent())Mt=gt.get(Dt.id()),Tt.nodes.push({x:U[rt].xCoords[Mt]-Dt.boundingbox().w/2,y:U[rt].yCoords[Mt]-Dt.boundingbox().h/2,width:Dt.boundingbox().w,height:Dt.boundingbox().h});else{var mt=t.calcBoundingBox(Dt,U[rt].xCoords,U[rt].yCoords,gt);Tt.nodes.push({x:mt.topLeftX,y:mt.topLeftY,width:mt.width,height:mt.height})}else K[rt][Dt.id()]&&Tt.nodes.push({x:K[rt][Dt.id()].getLeft(),y:K[rt][Dt.id()].getTop(),width:K[rt][Dt.id()].getWidth(),height:K[rt][Dt.id()].getHeight()})}),vt.edges().forEach(function(Dt){var mt=Dt.source(),xt=Dt.target();if(mt.css("display")!="none"&&xt.css("display")!="none")if(C.quality=="draft"){var St=gt.get(mt.id()),Vt=gt.get(xt.id()),Xt=[],Ut=[];if(mt.isParent()){var bt=t.calcBoundingBox(mt,U[rt].xCoords,U[rt].yCoords,gt);Xt.push(bt.topLeftX+bt.width/2),Xt.push(bt.topLeftY+bt.height/2)}else Xt.push(U[rt].xCoords[St]),Xt.push(U[rt].yCoords[St]);if(xt.isParent()){var Ht=t.calcBoundingBox(xt,U[rt].xCoords,U[rt].yCoords,gt);Ut.push(Ht.topLeftX+Ht.width/2),Ut.push(Ht.topLeftY+Ht.height/2)}else Ut.push(U[rt].xCoords[Vt]),Ut.push(U[rt].yCoords[Vt]);Tt.edges.push({startX:Xt[0],startY:Xt[1],endX:Ut[0],endY:Ut[1]})}else K[rt][mt.id()]&&K[rt][xt.id()]&&Tt.edges.push({startX:K[rt][mt.id()].getCenterX(),startY:K[rt][mt.id()].getCenterY(),endX:K[rt][xt.id()].getCenterX(),endY:K[rt][xt.id()].getCenterY()})}),Tt.nodes.length>0&&(H.push(Tt),O.add(rt))}});var _=m.packComponents(H,C.randomize).shifts;if(C.quality=="draft")U.forEach(function(vt,rt){var gt=vt.xCoords.map(function(Mt){return Mt+_[rt].dx}),Tt=vt.yCoords.map(function(Mt){return Mt+_[rt].dy});vt.xCoords=gt,vt.yCoords=Tt});else{var ht=0;O.forEach(function(vt){Object.keys(K[vt]).forEach(function(rt){var gt=K[vt][rt];gt.setCenter(gt.getCenterX()+_[ht].dx,gt.getCenterY()+_[ht].dy)}),ht++})}}}else{var E=C.eles.boundingBox();if(at.push({x:E.x1+E.w/2,y:E.y1+E.h/2}),C.randomize){var y=s(C);U.push(y)}C.quality=="default"||C.quality=="proof"?(K.push(l(C,U[0])),t.relocateComponent(at[0],K[0],C)):t.relocateComponent(at[0],U[0],C)}var J=function(rt,gt){if(C.quality=="default"||C.quality=="proof"){typeof rt=="number"&&(rt=gt);var Tt=void 0,Mt=void 0,Dt=rt.data("id");return K.forEach(function(xt){Dt in xt&&(Tt={x:xt[Dt].getRect().getCenterX(),y:xt[Dt].getRect().getCenterY()},Mt=xt[Dt])}),C.nodeDimensionsIncludeLabels&&(Mt.labelWidth&&(Mt.labelPosHorizontal=="left"?Tt.x+=Mt.labelWidth/2:Mt.labelPosHorizontal=="right"&&(Tt.x-=Mt.labelWidth/2)),Mt.labelHeight&&(Mt.labelPosVertical=="top"?Tt.y+=Mt.labelHeight/2:Mt.labelPosVertical=="bottom"&&(Tt.y-=Mt.labelHeight/2))),Tt==null&&(Tt={x:rt.position("x"),y:rt.position("y")}),{x:Tt.x,y:Tt.y}}else{var mt=void 0;return U.forEach(function(xt){var St=xt.nodeIndexes.get(rt.id());St!=null&&(mt={x:xt.xCoords[St],y:xt.yCoords[St]})}),mt==null&&(mt={x:rt.position("x"),y:rt.position("y")}),{x:mt.x,y:mt.y}}};if(C.quality=="default"||C.quality=="proof"||C.randomize){var Rt=t.calcParentsWithoutChildren(G,B),Lt=B.filter(function(vt){return vt.css("display")=="none"});C.eles=B.not(Lt),B.nodes().not(":parent").not(Lt).layoutPositions(F,C,J),Rt.length>0&&Rt.forEach(function(vt){vt.position(J(vt))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),d}();a.exports=g},657:(a,r,e)=>{var f=e(548),i=e(140).layoutBase.Matrix,v=e(140).layoutBase.SVD,t=function(s){var c=s.cy,l=s.eles,T=l.nodes(),g=l.nodes(":parent"),d=new Map,N=new Map,F=new Map,C=[],G=[],B=[],U=[],K=[],D=[],at=[],n=[],m=void 0,p=1e8,E=1e-9,y=s.piTol,I=s.samplingType,w=s.nodeSeparation,R=void 0,W=function(){for(var b=0,k=0,Q=!1;k<R;){b=Math.floor(Math.random()*m),Q=!1;for(var Z=0;Z<k;Z++)if(U[Z]==b){Q=!0;break}if(!Q)U[k]=b,k++;else continue}},x=function(b,k,Q){for(var Z=[],it=0,ut=0,ot=0,tt=void 0,j=[],dt=0,wt=1,yt=0;yt<m;yt++)j[yt]=p;for(Z[ut]=b,j[b]=0;ut>=it;){ot=Z[it++];for(var It=C[ot],ft=0;ft<It.length;ft++)tt=N.get(It[ft]),j[tt]==p&&(j[tt]=j[ot]+1,Z[++ut]=tt);D[ot][k]=j[ot]*w}if(Q){for(var st=0;st<m;st++)D[st][k]<K[st]&&(K[st]=D[st][k]);for(var At=0;At<m;At++)K[At]>dt&&(dt=K[At],wt=At)}return wt},q=function(b){var k=void 0;if(b){k=Math.floor(Math.random()*m);for(var Z=0;Z<m;Z++)K[Z]=p;for(var it=0;it<R;it++)U[it]=k,k=x(k,it,b)}else{W();for(var Q=0;Q<R;Q++)x(U[Q],Q,b)}for(var ut=0;ut<m;ut++)for(var ot=0;ot<R;ot++)D[ut][ot]*=D[ut][ot];for(var tt=0;tt<R;tt++)at[tt]=[];for(var j=0;j<R;j++)for(var dt=0;dt<R;dt++)at[j][dt]=D[U[dt]][j]},V=function(){for(var b=v.svd(at),k=b.S,Q=b.U,Z=b.V,it=k[0]*k[0]*k[0],ut=[],ot=0;ot<R;ot++){ut[ot]=[];for(var tt=0;tt<R;tt++)ut[ot][tt]=0,ot==tt&&(ut[ot][tt]=k[ot]/(k[ot]*k[ot]+it/(k[ot]*k[ot])))}n=i.multMat(i.multMat(Z,ut),i.transpose(Q))},X=function(){for(var b=void 0,k=void 0,Q=[],Z=[],it=[],ut=[],ot=0;ot<m;ot++)Q[ot]=Math.random(),Z[ot]=Math.random();Q=i.normalize(Q),Z=i.normalize(Z);for(var tt=E,j=E,dt=void 0;;){for(var wt=0;wt<m;wt++)it[wt]=Q[wt];if(Q=i.multGamma(i.multL(i.multGamma(it),D,n)),b=i.dotProduct(it,Q),Q=i.normalize(Q),tt=i.dotProduct(it,Q),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var yt=0;yt<m;yt++)it[yt]=Q[yt];for(j=E;;){for(var It=0;It<m;It++)ut[It]=Z[It];if(ut=i.minusOp(ut,i.multCons(it,i.dotProduct(it,ut))),Z=i.multGamma(i.multL(i.multGamma(ut),D,n)),k=i.dotProduct(ut,Z),Z=i.normalize(Z),tt=i.dotProduct(ut,Z),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var ft=0;ft<m;ft++)ut[ft]=Z[ft];G=i.multCons(it,Math.sqrt(Math.abs(b))),B=i.multCons(ut,Math.sqrt(Math.abs(k)))};f.connectComponents(c,l,f.getTopMostNodes(T),d),g.forEach(function(S){f.connectComponents(c,l,f.getTopMostNodes(S.descendants().intersection(l)),d)});for(var et=0,z=0;z<T.length;z++)T[z].isParent()||N.set(T[z].id(),et++);var O=!0,H=!1,$=void 0;try{for(var _=d.keys()[Symbol.iterator](),ht;!(O=(ht=_.next()).done);O=!0){var J=ht.value;N.set(J,et++)}}catch(S){H=!0,$=S}finally{try{!O&&_.return&&_.return()}finally{if(H)throw $}}for(var Rt=0;Rt<N.size;Rt++)C[Rt]=[];g.forEach(function(S){for(var b=S.children().intersection(l);b.nodes(":childless").length==0;)b=b.nodes()[0].children().intersection(l);var k=0,Q=b.nodes(":childless")[0].connectedEdges().length;b.nodes(":childless").forEach(function(Z,it){Z.connectedEdges().length<Q&&(Q=Z.connectedEdges().length,k=it)}),F.set(S.id(),b.nodes(":childless")[k].id())}),T.forEach(function(S){var b=void 0;S.isParent()?b=N.get(F.get(S.id())):b=N.get(S.id()),S.neighborhood().nodes().forEach(function(k){l.intersection(S.edgesWith(k)).length>0&&(k.isParent()?C[b].push(F.get(k.id())):C[b].push(k.id()))})});var Lt=function(b){var k=N.get(b),Q=void 0;d.get(b).forEach(function(Z){c.getElementById(Z).isParent()?Q=F.get(Z):Q=Z,C[k].push(Q),C[N.get(Q)].push(b)})},vt=!0,rt=!1,gt=void 0;try{for(var Tt=d.keys()[Symbol.iterator](),Mt;!(vt=(Mt=Tt.next()).done);vt=!0){var Dt=Mt.value;Lt(Dt)}}catch(S){rt=!0,gt=S}finally{try{!vt&&Tt.return&&Tt.return()}finally{if(rt)throw gt}}m=N.size;var mt=void 0;if(m>2){R=m<s.sampleSize?m:s.sampleSize;for(var xt=0;xt<m;xt++)D[xt]=[];for(var St=0;St<R;St++)n[St]=[];return s.quality=="draft"||s.step=="all"?(q(I),V(),X(),mt={nodeIndexes:N,xCoords:G,yCoords:B}):(N.forEach(function(S,b){G.push(c.getElementById(b).position("x")),B.push(c.getElementById(b).position("y"))}),mt={nodeIndexes:N,xCoords:G,yCoords:B}),mt}else{var Vt=N.keys(),Xt=c.getElementById(Vt.next().value),Ut=Xt.position(),bt=Xt.outerWidth();if(G.push(Ut.x),B.push(Ut.y),m==2){var Ht=c.getElementById(Vt.next().value),Bt=Ht.outerWidth();G.push(Ut.x+bt/2+Bt/2+s.idealEdgeLength),B.push(Ut.y)}return mt={nodeIndexes:N,xCoords:G,yCoords:B},mt}};a.exports={spectralLayout:t}},579:(a,r,e)=>{var f=e(212),i=function(t){t&&t("layout","fcose",f)};typeof cytoscape<"u"&&i(cytoscape),a.exports=i},140:a=>{a.exports=M}},L={};function u(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return P[a](e,e.exports,u),e.exports}var h=u(579);return h})()})})(Ge);var gr=Ge.exports;const dr=hr(gr);var Re={L:"left",R:"right",T:"top",B:"bottom"},Se={L:nt(A=>`${A},${A/2} 0,${A} 0,0`,"L"),R:nt(A=>`0,${A/2} ${A},0 ${A},${A}`,"R"),T:nt(A=>`0,0 ${A},0 ${A/2},${A}`,"T"),B:nt(A=>`${A/2},0 ${A},${A} 0,${A}`,"B")},le={L:nt((A,Y)=>A-Y+2,"L"),R:nt((A,Y)=>A-2,"R"),T:nt((A,Y)=>A-Y+2,"T"),B:nt((A,Y)=>A-2,"B")},vr=nt(function(A){return zt(A)?A==="L"?"R":"L":A==="T"?"B":"T"},"getOppositeArchitectureDirection"),Fe=nt(function(A){const Y=A;return Y==="L"||Y==="R"||Y==="T"||Y==="B"},"isArchitectureDirection"),zt=nt(function(A){const Y=A;return Y==="L"||Y==="R"},"isArchitectureDirectionX"),Qt=nt(function(A){const Y=A;return Y==="T"||Y==="B"},"isArchitectureDirectionY"),Ae=nt(function(A,Y){const M=zt(A)&&Qt(Y),P=Qt(A)&&zt(Y);return M||P},"isArchitectureDirectionXY"),pr=nt(function(A){const Y=A[0],M=A[1],P=zt(Y)&&Qt(M),L=Qt(Y)&&zt(M);return P||L},"isArchitecturePairXY"),yr=nt(function(A){return A!=="LL"&&A!=="RR"&&A!=="TT"&&A!=="BB"},"isValidArchitectureDirectionPair"),me=nt(function(A,Y){const M=`${A}${Y}`;return yr(M)?M:void 0},"getArchitectureDirectionPair"),Er=nt(function([A,Y],M){const P=M[0],L=M[1];return zt(P)?Qt(L)?[A+(P==="L"?-1:1),Y+(L==="T"?1:-1)]:[A+(P==="L"?-1:1),Y]:zt(L)?[A+(L==="L"?1:-1),Y+(P==="T"?1:-1)]:[A,Y+(P==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),mr=nt(function(A){return A==="LT"||A==="TL"?[1,1]:A==="BL"||A==="LB"?[1,-1]:A==="BR"||A==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),Tr=nt(function(A,Y){return Ae(A,Y)?"bend":zt(A)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),Nr=nt(function(A){return A.type==="service"},"isArchitectureService"),Lr=nt(function(A){return A.type==="junction"},"isArchitectureJunction"),Ue=nt(A=>A.data(),"edgeData"),ne=nt(A=>A.data(),"nodeData"),Ye=qe.architecture,pt=new sr(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:Ye,dataStructures:void 0,elements:{}})),Ar=nt(()=>{pt.reset(),ir()},"clear"),Cr=nt(function({id:A,icon:Y,in:M,title:P,iconText:L}){if(pt.records.registeredIds[A]!==void 0)throw new Error(`The service id [${A}] is already in use by another ${pt.records.registeredIds[A]}`);if(M!==void 0){if(A===M)throw new Error(`The service [${A}] cannot be placed within itself`);if(pt.records.registeredIds[M]===void 0)throw new Error(`The service [${A}]'s parent does not exist. Please make sure the parent is created before this service`);if(pt.records.registeredIds[M]==="node")throw new Error(`The service [${A}]'s parent is not a group`)}pt.records.registeredIds[A]="node",pt.records.nodes[A]={id:A,type:"service",icon:Y,iconText:L,title:P,edges:[],in:M}},"addService"),Mr=nt(()=>Object.values(pt.records.nodes).filter(Nr),"getServices"),wr=nt(function({id:A,in:Y}){pt.records.registeredIds[A]="node",pt.records.nodes[A]={id:A,type:"junction",edges:[],in:Y}},"addJunction"),Or=nt(()=>Object.values(pt.records.nodes).filter(Lr),"getJunctions"),Dr=nt(()=>Object.values(pt.records.nodes),"getNodes"),Te=nt(A=>pt.records.nodes[A],"getNode"),xr=nt(function({id:A,icon:Y,in:M,title:P}){if(pt.records.registeredIds[A]!==void 0)throw new Error(`The group id [${A}] is already in use by another ${pt.records.registeredIds[A]}`);if(M!==void 0){if(A===M)throw new Error(`The group [${A}] cannot be placed within itself`);if(pt.records.registeredIds[M]===void 0)throw new Error(`The group [${A}]'s parent does not exist. Please make sure the parent is created before this group`);if(pt.records.registeredIds[M]==="node")throw new Error(`The group [${A}]'s parent is not a group`)}pt.records.registeredIds[A]="group",pt.records.groups[A]={id:A,icon:Y,title:P,in:M}},"addGroup"),Ir=nt(()=>Object.values(pt.records.groups),"getGroups"),Rr=nt(function({lhsId:A,rhsId:Y,lhsDir:M,rhsDir:P,lhsInto:L,rhsInto:u,lhsGroup:h,rhsGroup:a,title:r}){if(!Fe(M))throw new Error(`Invalid direction given for left hand side of edge ${A}--${Y}. Expected (L,R,T,B) got ${M}`);if(!Fe(P))throw new Error(`Invalid direction given for right hand side of edge ${A}--${Y}. Expected (L,R,T,B) got ${P}`);if(pt.records.nodes[A]===void 0&&pt.records.groups[A]===void 0)throw new Error(`The left-hand id [${A}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(pt.records.nodes[Y]===void 0&&pt.records.groups[A]===void 0)throw new Error(`The right-hand id [${Y}] does not yet exist. Please create the service/group before declaring an edge to it.`);const e=pt.records.nodes[A].in,f=pt.records.nodes[Y].in;if(h&&e&&f&&e==f)throw new Error(`The left-hand id [${A}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(a&&e&&f&&e==f)throw new Error(`The right-hand id [${Y}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const i={lhsId:A,lhsDir:M,lhsInto:L,lhsGroup:h,rhsId:Y,rhsDir:P,rhsInto:u,rhsGroup:a,title:r};pt.records.edges.push(i),pt.records.nodes[A]&&pt.records.nodes[Y]&&(pt.records.nodes[A].edges.push(pt.records.edges[pt.records.edges.length-1]),pt.records.nodes[Y].edges.push(pt.records.edges[pt.records.edges.length-1]))},"addEdge"),Sr=nt(()=>pt.records.edges,"getEdges"),Fr=nt(()=>{if(pt.records.dataStructures===void 0){const A={},Y=Object.entries(pt.records.nodes).reduce((a,[r,e])=>(a[r]=e.edges.reduce((f,i)=>{const v=Te(i.lhsId)?.in,t=Te(i.rhsId)?.in;if(v&&t&&v!==t){const o=Tr(i.lhsDir,i.rhsDir);o!=="bend"&&(A[v]??={},A[v][t]=o,A[t]??={},A[t][v]=o)}if(i.lhsId===r){const o=me(i.lhsDir,i.rhsDir);o&&(f[o]=i.rhsId)}else{const o=me(i.rhsDir,i.lhsDir);o&&(f[o]=i.lhsId)}return f},{}),a),{}),M=Object.keys(Y)[0],P={[M]:1},L=Object.keys(Y).reduce((a,r)=>r===M?a:{...a,[r]:1},{}),u=nt(a=>{const r={[a]:[0,0]},e=[a];for(;e.length>0;){const f=e.shift();if(f){P[f]=1,delete L[f];const i=Y[f],[v,t]=r[f];Object.entries(i).forEach(([o,s])=>{P[s]||(r[s]=Er([v,t],o),e.push(s))})}}return r},"BFS"),h=[u(M)];for(;Object.keys(L).length>0;)h.push(u(Object.keys(L)[0]));pt.records.dataStructures={adjList:Y,spatialMaps:h,groupAlignments:A}}return pt.records.dataStructures},"getDataStructures"),br=nt((A,Y)=>{pt.records.elements[A]=Y},"setElementForId"),Pr=nt(A=>pt.records.elements[A],"getElementById"),he={clear:Ar,setDiagramTitle:Ke,getDiagramTitle:je,setAccTitle:_e,getAccTitle:tr,setAccDescription:er,getAccDescription:rr,addService:Cr,getServices:Mr,addJunction:wr,getJunctions:Or,getNodes:Dr,getNode:Te,addGroup:xr,getGroups:Ir,addEdge:Rr,getEdges:Sr,setElementForId:br,getElementById:Pr,getDataStructures:Fr};function Pt(A){const Y=fe().architecture;return Y?.[A]?Y[A]:Ye[A]}nt(Pt,"getConfigField");var Gr=nt((A,Y)=>{or(A,Y),A.groups.map(Y.addGroup),A.services.map(M=>Y.addService({...M,type:"service"})),A.junctions.map(M=>Y.addJunction({...M,type:"junction"})),A.edges.map(Y.addEdge)},"populateDb"),Ur={parse:nt(async A=>{const Y=await lr("architecture",A);be.debug(Y),Gr(Y,he)},"parse")},Yr=nt(A=>`
  .edge {
    stroke-width: ${A.archEdgeWidth};
    stroke: ${A.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${A.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${A.archGroupBorderColor};
    stroke-width: ${A.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Xr=Yr,ae=nt(A=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${A}</g>`,"wrapIcon"),oe={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:ae('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:ae('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:ae('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:ae('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:ae('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:Je,blank:{body:ae("")}}},Hr=nt(async function(A,Y){const M=Pt("padding"),P=Pt("iconSize"),L=P/2,u=P/6,h=u/2;await Promise.all(Y.edges().map(async a=>{const{source:r,sourceDir:e,sourceArrow:f,sourceGroup:i,target:v,targetDir:t,targetArrow:o,targetGroup:s,label:c}=Ue(a);let{x:l,y:T}=a[0].sourceEndpoint();const{x:g,y:d}=a[0].midpoint();let{x:N,y:F}=a[0].targetEndpoint();const C=M+4;if(i&&(zt(e)?l+=e==="L"?-C:C:T+=e==="T"?-C:C+18),s&&(zt(t)?N+=t==="L"?-C:C:F+=t==="T"?-C:C+18),!i&&he.getNode(r)?.type==="junction"&&(zt(e)?l+=e==="L"?L:-L:T+=e==="T"?L:-L),!s&&he.getNode(v)?.type==="junction"&&(zt(t)?N+=t==="L"?L:-L:F+=t==="T"?L:-L),a[0]._private.rscratch){const G=A.insert("g");if(G.insert("path").attr("d",`M ${l},${T} L ${g},${d} L${N},${F} `).attr("class","edge"),f){const B=zt(e)?le[e](l,u):l-h,U=Qt(e)?le[e](T,u):T-h;G.insert("polygon").attr("points",Se[e](u)).attr("transform",`translate(${B},${U})`).attr("class","arrow")}if(o){const B=zt(t)?le[t](N,u):N-h,U=Qt(t)?le[t](F,u):F-h;G.insert("polygon").attr("points",Se[t](u)).attr("transform",`translate(${B},${U})`).attr("class","arrow")}if(c){const B=Ae(e,t)?"XY":zt(e)?"X":"Y";let U=0;B==="X"?U=Math.abs(l-N):B==="Y"?U=Math.abs(T-F)/1.5:U=Math.abs(l-N)/2;const K=G.append("g");if(await Ne(K,c,{useHtmlLabels:!1,width:U,classes:"architecture-service-label"},fe()),K.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),B==="X")K.attr("transform","translate("+g+", "+d+")");else if(B==="Y")K.attr("transform","translate("+g+", "+d+") rotate(-90)");else if(B==="XY"){const D=me(e,t);if(D&&pr(D)){const at=K.node().getBoundingClientRect(),[n,m]=mr(D);K.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*n*m*45})`);const p=K.node().getBoundingClientRect();K.attr("transform",`
                translate(${g}, ${d-at.height/2})
                translate(${n*p.width/2}, ${m*p.height/2})
                rotate(${-1*n*m*45}, 0, ${at.height/2})
              `)}}}}}))},"drawEdges"),Wr=nt(async function(A,Y){const P=Pt("padding")*.75,L=Pt("fontSize"),h=Pt("iconSize")/2;await Promise.all(Y.nodes().map(async a=>{const r=ne(a);if(r.type==="group"){const{h:e,w:f,x1:i,y1:v}=a.boundingBox();A.append("rect").attr("x",i+h).attr("y",v+h).attr("width",f).attr("height",e).attr("class","node-bkg");const t=A.append("g");let o=i,s=v;if(r.icon){const c=t.append("g");c.html(`<g>${await Ee(r.icon,{height:P,width:P,fallbackPrefix:oe.prefix})}</g>`),c.attr("transform","translate("+(o+h+1)+", "+(s+h+1)+")"),o+=P,s+=L/2-1-2}if(r.label){const c=t.append("g");await Ne(c,r.label,{useHtmlLabels:!1,width:f,classes:"architecture-service-label"},fe()),c.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),c.attr("transform","translate("+(o+h+4)+", "+(s+h+2)+")")}}}))},"drawGroups"),Vr=nt(async function(A,Y,M){for(const P of M){const L=Y.append("g"),u=Pt("iconSize");if(P.title){const e=L.append("g");await Ne(e,P.title,{useHtmlLabels:!1,width:u*1.5,classes:"architecture-service-label"},fe()),e.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),e.attr("transform","translate("+u/2+", "+u+")")}const h=L.append("g");if(P.icon)h.html(`<g>${await Ee(P.icon,{height:u,width:u,fallbackPrefix:oe.prefix})}</g>`);else if(P.iconText){h.html(`<g>${await Ee("blank",{height:u,width:u,fallbackPrefix:oe.prefix})}</g>`);const i=h.append("g").append("foreignObject").attr("width",u).attr("height",u).append("div").attr("class","node-icon-text").attr("style",`height: ${u}px;`).append("div").html(P.iconText),v=parseInt(window.getComputedStyle(i.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;i.attr("style",`-webkit-line-clamp: ${Math.floor((u-2)/v)};`)}else h.append("path").attr("class","node-bkg").attr("id","node-"+P.id).attr("d",`M0 ${u} v${-u} q0,-5 5,-5 h${u} q5,0 5,5 v${u} H0 Z`);L.attr("class","architecture-service");const{width:a,height:r}=L._groups[0][0].getBBox();P.width=a,P.height=r,A.setElementForId(P.id,L)}return 0},"drawServices"),zr=nt(function(A,Y,M){M.forEach(P=>{const L=Y.append("g"),u=Pt("iconSize");L.append("g").append("rect").attr("id","node-"+P.id).attr("fill-opacity","0").attr("width",u).attr("height",u),L.attr("class","architecture-junction");const{width:a,height:r}=L._groups[0][0].getBBox();L.width=a,L.height=r,A.setElementForId(P.id,L)})},"drawJunctions");Qe([{name:oe.prefix,icons:oe}]);Pe.use(dr);function Xe(A,Y){A.forEach(M=>{Y.add({group:"nodes",data:{type:"service",id:M.id,icon:M.icon,label:M.title,parent:M.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-service"})})}nt(Xe,"addServices");function He(A,Y){A.forEach(M=>{Y.add({group:"nodes",data:{type:"junction",id:M.id,parent:M.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-junction"})})}nt(He,"addJunctions");function We(A,Y){Y.nodes().map(M=>{const P=ne(M);if(P.type==="group")return;P.x=M.position().x,P.y=M.position().y,A.getElementById(P.id).attr("transform","translate("+(P.x||0)+","+(P.y||0)+")")})}nt(We,"positionNodes");function Ve(A,Y){A.forEach(M=>{Y.add({group:"nodes",data:{type:"group",id:M.id,icon:M.icon,label:M.title,parent:M.in},classes:"node-group"})})}nt(Ve,"addGroups");function ze(A,Y){A.forEach(M=>{const{lhsId:P,rhsId:L,lhsInto:u,lhsGroup:h,rhsInto:a,lhsDir:r,rhsDir:e,rhsGroup:f,title:i}=M,v=Ae(M.lhsDir,M.rhsDir)?"segments":"straight",t={id:`${P}-${L}`,label:i,source:P,sourceDir:r,sourceArrow:u,sourceGroup:h,sourceEndpoint:r==="L"?"0 50%":r==="R"?"100% 50%":r==="T"?"50% 0":"50% 100%",target:L,targetDir:e,targetArrow:a,targetGroup:f,targetEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%"};Y.add({group:"edges",data:t,classes:v})})}nt(ze,"addEdges");function Be(A,Y,M){const P=nt((a,r)=>Object.entries(a).reduce((e,[f,i])=>{let v=0;const t=Object.entries(i);if(t.length===1)return e[f]=t[0][1],e;for(let o=0;o<t.length-1;o++)for(let s=o+1;s<t.length;s++){const[c,l]=t[o],[T,g]=t[s];if(M[c]?.[T]===r)e[f]??=[],e[f]=[...e[f],...l,...g];else if(c==="default"||T==="default")e[f]??=[],e[f]=[...e[f],...l,...g];else{const N=`${f}-${v++}`;e[N]=l;const F=`${f}-${v++}`;e[F]=g}}return e},{}),"flattenAlignments"),L=Y.map(a=>{const r={},e={};return Object.entries(a).forEach(([f,[i,v]])=>{const t=A.getNode(f)?.in??"default";r[v]??={},r[v][t]??=[],r[v][t].push(f),e[i]??={},e[i][t]??=[],e[i][t].push(f)}),{horiz:Object.values(P(r,"horizontal")).filter(f=>f.length>1),vert:Object.values(P(e,"vertical")).filter(f=>f.length>1)}}),[u,h]=L.reduce(([a,r],{horiz:e,vert:f})=>[[...a,...e],[...r,...f]],[[],[]]);return{horizontal:u,vertical:h}}nt(Be,"getAlignments");function $e(A){const Y=[],M=nt(L=>`${L[0]},${L[1]}`,"posToStr"),P=nt(L=>L.split(",").map(u=>parseInt(u)),"strToPos");return A.forEach(L=>{const u=Object.fromEntries(Object.entries(L).map(([e,f])=>[M(f),e])),h=[M([0,0])],a={},r={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;h.length>0;){const e=h.shift();if(e){a[e]=1;const f=u[e];if(f){const i=P(e);Object.entries(r).forEach(([v,t])=>{const o=M([i[0]+t[0],i[1]+t[1]]),s=u[o];s&&!a[o]&&(h.push(o),Y.push({[Re[v]]:s,[Re[vr(v)]]:f,gap:1.5*Pt("iconSize")}))})}}}}),Y}nt($e,"getRelativeConstraints");function ke(A,Y,M,P,L,{spatialMaps:u,groupAlignments:h}){return new Promise(a=>{const r=fr("body").append("div").attr("id","cy").attr("style","display:none"),e=Pe({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${Pt("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${Pt("padding")}px`}}]});r.remove(),Ve(M,e),Xe(A,e),He(Y,e),ze(P,e);const f=Be(L,u,h),i=$e(u),v=e.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){const[o,s]=t.connectedNodes(),{parent:c}=ne(o),{parent:l}=ne(s);return c===l?1.5*Pt("iconSize"):.5*Pt("iconSize")},edgeElasticity(t){const[o,s]=t.connectedNodes(),{parent:c}=ne(o),{parent:l}=ne(s);return c===l?.45:.001},alignmentConstraint:f,relativePlacementConstraint:i});v.one("layoutstop",()=>{function t(o,s,c,l){let T,g;const{x:d,y:N}=o,{x:F,y:C}=s;g=(l-N+(d-c)*(N-C)/(d-F))/Math.sqrt(1+Math.pow((N-C)/(d-F),2)),T=Math.sqrt(Math.pow(l-N,2)+Math.pow(c-d,2)-Math.pow(g,2));const G=Math.sqrt(Math.pow(F-d,2)+Math.pow(C-N,2));T=T/G;let B=(F-d)*(l-N)-(C-N)*(c-d);switch(!0){case B>=0:B=1;break;case B<0:B=-1;break}let U=(F-d)*(c-d)+(C-N)*(l-N);switch(!0){case U>=0:U=1;break;case U<0:U=-1;break}return g=Math.abs(g)*B,T=T*U,{distances:g,weights:T}}nt(t,"getSegmentWeights"),e.startBatch();for(const o of Object.values(e.edges()))if(o.data?.()){const{x:s,y:c}=o.source().position(),{x:l,y:T}=o.target().position();if(s!==l&&c!==T){const g=o.sourceEndpoint(),d=o.targetEndpoint(),{sourceDir:N}=Ue(o),[F,C]=Qt(N)?[g.x,d.y]:[d.x,g.y],{weights:G,distances:B}=t(g,d,F,C);o.style("segment-distances",B),o.style("segment-weights",G)}}e.endBatch(),v.run()}),v.run(),e.ready(t=>{be.info("Ready",t),a(e)})})}nt(ke,"layoutArchitecture");var Br=nt(async(A,Y,M,P)=>{const L=P.db,u=L.getServices(),h=L.getJunctions(),a=L.getGroups(),r=L.getEdges(),e=L.getDataStructures(),f=ar(Y),i=f.append("g");i.attr("class","architecture-edges");const v=f.append("g");v.attr("class","architecture-services");const t=f.append("g");t.attr("class","architecture-groups"),await Vr(L,v,u),zr(L,v,h);const o=await ke(u,h,a,r,L,e);await Hr(i,o),await Wr(t,o),We(L,o),nr(void 0,f,Pt("padding"),Pt("useMaxWidth"))},"draw"),$r={draw:Br},ni={parser:Ur,db:he,renderer:$r,styles:Xr};export{ni as diagram};
//# sourceMappingURL=architectureDiagram-PQUH6ZAG-BOwpk7xQ.js.map
