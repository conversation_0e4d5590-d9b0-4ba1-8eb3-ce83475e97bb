{"version": 3, "file": "iblScaledLuminance.fragment-BEWM-ZCL.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/iblScaledLuminance.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"iblScaledLuminancePixelShader\";\nconst shader = `precision highp sampler2D;precision highp samplerCube;\n#include<helperFunctions>\nvarying vec2 vUV;\n#ifdef IBL_USE_CUBE_MAP\nuniform samplerCube iblSource;\n#else\nuniform sampler2D iblSource;\n#endif\nuniform int iblWidth;uniform int iblHeight;float fetchLuminance(vec2 coords) {\n#ifdef IBL_USE_CUBE_MAP\nvec3 direction=equirectangularToCubemapDirection(coords);vec3 color=textureCubeLodEXT(iblSource,direction,0.0).rgb;\n#else\nvec3 color=textureLod(iblSource,coords,0.0).rgb;\n#endif\nreturn dot(color,LuminanceEncodeApprox);}\nvoid main(void) {float deform=sin(vUV.y*PI);float luminance=fetchLuminance(vUV);gl_FragColor=vec4(vec3(deform*luminance),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const iblScaledLuminancePixelShader = { name, shader };\n//# sourceMappingURL=iblScaledLuminance.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblScaledLuminancePixelShader"], "mappings": "qIAGA,MAAMA,EAAO,gCACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iIAiBVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAgC,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}