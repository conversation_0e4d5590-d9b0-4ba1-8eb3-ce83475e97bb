import{R as t}from"./index-Cb4A4-Xi.js";import{b as r}from"./KHR_interactivity-DVSiPm30.js";import{b as l}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class s extends r{constructor(e){super(e),this.delayIndex=this.registerDataInput("delayIndex",l)}_execute(e,n){const i=this.delayIndex.getValue(e);if(i<=0||isNaN(i)||!isFinite(i))return this._reportError(e,"Invalid delay index");const a=e._getExecutionVariable(this,"pendingDelays",[])[i];a&&a.dispose(),this.out._activateSignal(e)}getClassName(){return"FlowGraphCancelDelayBlock"}}t("FlowGraphCancelDelayBlock",s);export{s as FlowGraphCancelDelayBlock};
//# sourceMappingURL=flowGraphCancelDelayBlock-DR3yfbrj.js.map
