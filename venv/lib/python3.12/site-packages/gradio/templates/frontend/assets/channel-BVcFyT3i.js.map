{"version": 3, "file": "channel-BVcFyT3i.js", "sources": ["../../../../node_modules/.pnpm/khroma@2.1.0/node_modules/khroma/dist/methods/channel.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst channel = (color, channel) => {\n    return _.lang.round(Color.parse(color)[channel]);\n};\n/* EXPORT */\nexport default channel;\n"], "names": ["channel", "color", "_", "Color"], "mappings": "sDAIK,MAACA,EAAU,CAACC,EAAOD,IACbE,EAAE,KAAK,MAAMC,EAAM,MAAMF,CAAK,EAAED,CAAO,CAAC", "x_google_ignoreList": [0]}