import{f as a,i as o,g as u}from"./KHR_interactivity-DVSiPm30.js";import{R as r}from"./declarationMapper-r-RREw_K.js";import{R as c}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class l extends a{constructor(t){super(t),this.config=t,this.default=this._registerSignalOutput("default"),this._caseToOutputFlow=new Map,this.case=this.registerDataInput("case",r),(this.config.cases||[]).forEach(s=>{this._caseToOutputFlow.set(s,this._registerSignalOutput(`out_${s}`))})}_execute(t,s){const e=this.case.getValue(t);let i;o(e)?i=this._getOutputFlowForCase(u(e)):i=this._getOutputFlowForCase(e),i?i._activateSignal(t):this.default._activateSignal(t)}addCase(t){this.config.cases.includes(t)||(this.config.cases.push(t),this._caseToOutputFlow.set(t,this._registerSignalOutput(`out_${t}`)))}removeCase(t){if(!this.config.cases.includes(t))return;const s=this.config.cases.indexOf(t);this.config.cases.splice(s,1),this._caseToOutputFlow.delete(t)}_getOutputFlowForCase(t){return this._caseToOutputFlow.get(t)}getClassName(){return"FlowGraphSwitchBlock"}serialize(t){super.serialize(t),t.cases=this.config.cases}}c("FlowGraphSwitchBlock",l);export{l as FlowGraphSwitchBlock};
//# sourceMappingURL=flowGraphSwitchBlock-C4FBt2Pq.js.map
