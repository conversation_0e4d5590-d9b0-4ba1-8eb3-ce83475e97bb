import{b as r}from"./declarationMapper-r-RREw_K.js";import{b as i}from"./KHR_interactivity-DVSiPm30.js";import{R as s}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class n extends i{constructor(t){super(t),this.count=this.registerDataInput("count",r),this.reset=this._registerSignalInput("reset"),this.currentCount=this.registerDataOutput("currentCount",r)}_execute(t,u){if(u===this.reset){t._setExecutionVariable(this,"debounceCount",0);return}const o=this.count.getValue(t),e=t._getExecutionVariable(this,"debounceCount",0)+1;this.currentCount.setValue(e,t),t._setExecutionVariable(this,"debounceCount",e),e>=o&&(this.out._activateSignal(t),t._setExecutionVariable(this,"debounceCount",0))}getClassName(){return"FlowGraphDebounceBlock"}}s("FlowGraphDebounceBlock",n);export{n as FlowGraphDebounceBlock};
//# sourceMappingURL=flowGraphDebounceBlock-DEdqoOLr.js.map
