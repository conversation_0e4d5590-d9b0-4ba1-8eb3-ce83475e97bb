import{R as l,a as c}from"./hdr-LsP_yQgA.js";import"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";class R{constructor(){this.supportCascades=!1}loadCubeData(){throw".hdr not supported in Cube."}loadData(s,e,p){const i=new Uint8Array(s.buffer,s.byteOffset,s.byteLength),t=l(i),r=c(i,t),n=t.width*t.height,o=new Float32Array(n*4);for(let a=0;a<n;a+=1)o[a*4]=r[a*3],o[a*4+1]=r[a*3+1],o[a*4+2]=r[a*3+2],o[a*4+3]=1;p(t.width,t.height,e.generateMipMaps,!1,()=>{const a=e.getEngine();e.type=1,e.format=5,e._gammaSpace=!1,a._uploadDataToTextureDirectly(e,o)})}}export{R as _HDRTextureLoader};
//# sourceMappingURL=hdrTextureLoader-B8B0Mm00.js.map
