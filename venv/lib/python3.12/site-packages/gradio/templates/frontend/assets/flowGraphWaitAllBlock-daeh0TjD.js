import{b as n}from"./KHR_interactivity-DVSiPm30.js";import{R as a}from"./index-Cb4A4-Xi.js";import{b as o}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class r extends n{constructor(i){super(i),this.config=i,this.inFlows=[],this._cachedActivationState=[],this.reset=this._registerSignalInput("reset"),this.completed=this._registerSignalOutput("completed"),this.remainingInputs=this.registerDataOutput("remainingInputs",o,this.config.inputSignalCount||0);for(let e=0;e<this.config.inputSignalCount;e++)this.inFlows.push(this._registerSignalInput(`in_${e}`));this._unregisterSignalInput("in")}_getCurrentActivationState(i){const e=this._cachedActivationState;if(e.length=0,i._hasExecutionVariable(this,"activationState")){const s=i._getExecutionVariable(this,"activationState",[]);for(let t=0;t<s.length;t++)e.push(s[t])}else for(let s=0;s<this.config.inputSignalCount;s++)e.push(!1);return e}_execute(i,e){const s=this._getCurrentActivationState(i);if(e===this.reset)for(let t=0;t<this.config.inputSignalCount;t++)s[t]=!1;else{const t=this.inFlows.indexOf(e);t>=0&&(s[t]=!0)}if(this.remainingInputs.setValue(s.filter(t=>!t).length,i),i._setExecutionVariable(this,"activationState",s.slice()),s.includes(!1))e!==this.reset&&this.out._activateSignal(i);else{this.completed._activateSignal(i);for(let t=0;t<this.config.inputSignalCount;t++)s[t]=!1}}getClassName(){return"FlowGraphWaitAllBlock"}serialize(i){super.serialize(i),i.config.inputFlows=this.config.inputSignalCount}}a("FlowGraphWaitAllBlock",r);export{r as FlowGraphWaitAllBlock};
//# sourceMappingURL=flowGraphWaitAllBlock-daeh0TjD.js.map
