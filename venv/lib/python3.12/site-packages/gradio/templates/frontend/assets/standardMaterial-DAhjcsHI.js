const __vite__fileDeps=["./default.vertex-BzAfhmJ2.js","./index-Cb4A4-Xi.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./defaultUboDeclaration-B56YV8As.js","./meshUboDeclaration-DByRfuEp.js","./vertexColorMixing-WfbnM9z7.js","./helperFunctions-DzxrWFCN.js","./bakedVertexAnimation-w9ZZqSSS.js","./mainUVVaryingDeclaration-Co7HufPC.js","./logDepthVertex-pIt4nTxl.js","./logDepthDeclaration-BrICrv6l.js","./default.fragment-B_5_dYyZ.js","./oitFragment-DW_ve-To.js","./fresnelFunction-COC4uPN7.js","./fogFragment-DoL1EhNA.js","./decalFragment-D9EEhdKW.js","./default.vertex-C7rJ-W2P.js","./vertexColorMixing-D9v_X80C.js","./defaultUboDeclaration-D3paVB8d.js","./logDepthDeclaration-CoULWXNE.js","./helperFunctions-DvZkArRr.js","./mainUVVaryingDeclaration-BIyW7Mxl.js","./logDepthVertex-B1pXR1ao.js","./default.fragment-4__l7eZ9.js","./oitFragment-BeGwGnSY.js","./fogFragment-DCsJ5lp9.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as b}from"./index-Ccc2t4AG.js";import{_ as s,l as _,m as n,n as v,s as f,o as S,R as Y,p as X,P as z,C as A,q as W,D as Q,r as k,t as N,u as K,v as J,w as Z,x as j,y as M,z as R,T as c,E as $,F as q,H as ee,J as te,K as ie,U as se,W as re,X as ae,Y as D,Z as oe,$ as ne,a0 as le,a1 as P,a2 as ue,a3 as fe,a4 as he,a5 as Ee,a6 as Te,S as V,a7 as l,a8 as pe,a9 as ce,aa as O}from"./index-Cb4A4-Xi.js";const y={effect:null,subMesh:null};class _e extends pe{constructor(t){super(t),this.MAINUV1=!1,this.MAINUV2=!1,this.MAINUV3=!1,this.MAINUV4=!1,this.MAINUV5=!1,this.MAINUV6=!1,this.DIFFUSE=!1,this.DIFFUSEDIRECTUV=0,this.BAKED_VERTEX_ANIMATION_TEXTURE=!1,this.AMBIENT=!1,this.AMBIENTDIRECTUV=0,this.OPACITY=!1,this.OPACITYDIRECTUV=0,this.OPACITYRGB=!1,this.REFLECTION=!1,this.EMISSIVE=!1,this.EMISSIVEDIRECTUV=0,this.SPECULAR=!1,this.SPECULARDIRECTUV=0,this.BUMP=!1,this.BUMPDIRECTUV=0,this.PARALLAX=!1,this.PARALLAX_RHS=!1,this.PARALLAXOCCLUSION=!1,this.SPECULAROVERALPHA=!1,this.CLIPPLANE=!1,this.CLIPPLANE2=!1,this.CLIPPLANE3=!1,this.CLIPPLANE4=!1,this.CLIPPLANE5=!1,this.CLIPPLANE6=!1,this.ALPHATEST=!1,this.DEPTHPREPASS=!1,this.ALPHAFROMDIFFUSE=!1,this.POINTSIZE=!1,this.FOG=!1,this.SPECULARTERM=!1,this.DIFFUSEFRESNEL=!1,this.OPACITYFRESNEL=!1,this.REFLECTIONFRESNEL=!1,this.REFRACTIONFRESNEL=!1,this.EMISSIVEFRESNEL=!1,this.FRESNEL=!1,this.NORMAL=!1,this.TANGENT=!1,this.UV1=!1,this.UV2=!1,this.UV3=!1,this.UV4=!1,this.UV5=!1,this.UV6=!1,this.VERTEXCOLOR=!1,this.VERTEXALPHA=!1,this.NUM_BONE_INFLUENCERS=0,this.BonesPerMesh=0,this.BONETEXTURE=!1,this.BONES_VELOCITY_ENABLED=!1,this.INSTANCES=!1,this.THIN_INSTANCES=!1,this.INSTANCESCOLOR=!1,this.GLOSSINESS=!1,this.ROUGHNESS=!1,this.EMISSIVEASILLUMINATION=!1,this.LINKEMISSIVEWITHDIFFUSE=!1,this.REFLECTIONFRESNELFROMSPECULAR=!1,this.LIGHTMAP=!1,this.LIGHTMAPDIRECTUV=0,this.OBJECTSPACE_NORMALMAP=!1,this.USELIGHTMAPASSHADOWMAP=!1,this.REFLECTIONMAP_3D=!1,this.REFLECTIONMAP_SPHERICAL=!1,this.REFLECTIONMAP_PLANAR=!1,this.REFLECTIONMAP_CUBIC=!1,this.USE_LOCAL_REFLECTIONMAP_CUBIC=!1,this.USE_LOCAL_REFRACTIONMAP_CUBIC=!1,this.REFLECTIONMAP_PROJECTION=!1,this.REFLECTIONMAP_SKYBOX=!1,this.REFLECTIONMAP_EXPLICIT=!1,this.REFLECTIONMAP_EQUIRECTANGULAR=!1,this.REFLECTIONMAP_EQUIRECTANGULAR_FIXED=!1,this.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED=!1,this.REFLECTIONMAP_OPPOSITEZ=!1,this.INVERTCUBICMAP=!1,this.LOGARITHMICDEPTH=!1,this.REFRACTION=!1,this.REFRACTIONMAP_3D=!1,this.REFLECTIONOVERALPHA=!1,this.TWOSIDEDLIGHTING=!1,this.SHADOWFLOAT=!1,this.MORPHTARGETS=!1,this.MORPHTARGETS_POSITION=!1,this.MORPHTARGETS_NORMAL=!1,this.MORPHTARGETS_TANGENT=!1,this.MORPHTARGETS_UV=!1,this.MORPHTARGETS_UV2=!1,this.MORPHTARGETS_COLOR=!1,this.MORPHTARGETTEXTURE_HASPOSITIONS=!1,this.MORPHTARGETTEXTURE_HASNORMALS=!1,this.MORPHTARGETTEXTURE_HASTANGENTS=!1,this.MORPHTARGETTEXTURE_HASUVS=!1,this.MORPHTARGETTEXTURE_HASUV2S=!1,this.MORPHTARGETTEXTURE_HASCOLORS=!1,this.NUM_MORPH_INFLUENCERS=0,this.MORPHTARGETS_TEXTURE=!1,this.NONUNIFORMSCALING=!1,this.PREMULTIPLYALPHA=!1,this.ALPHATEST_AFTERALLALPHACOMPUTATIONS=!1,this.ALPHABLEND=!0,this.PREPASS=!1,this.PREPASS_COLOR=!1,this.PREPASS_COLOR_INDEX=-1,this.PREPASS_IRRADIANCE=!1,this.PREPASS_IRRADIANCE_INDEX=-1,this.PREPASS_ALBEDO=!1,this.PREPASS_ALBEDO_INDEX=-1,this.PREPASS_ALBEDO_SQRT=!1,this.PREPASS_ALBEDO_SQRT_INDEX=-1,this.PREPASS_DEPTH=!1,this.PREPASS_DEPTH_INDEX=-1,this.PREPASS_SCREENSPACE_DEPTH=!1,this.PREPASS_SCREENSPACE_DEPTH_INDEX=-1,this.PREPASS_NORMAL=!1,this.PREPASS_NORMAL_INDEX=-1,this.PREPASS_NORMAL_WORLDSPACE=!1,this.PREPASS_WORLD_NORMAL=!1,this.PREPASS_WORLD_NORMAL_INDEX=-1,this.PREPASS_POSITION=!1,this.PREPASS_POSITION_INDEX=-1,this.PREPASS_LOCAL_POSITION=!1,this.PREPASS_LOCAL_POSITION_INDEX=-1,this.PREPASS_VELOCITY=!1,this.PREPASS_VELOCITY_INDEX=-1,this.PREPASS_VELOCITY_LINEAR=!1,this.PREPASS_VELOCITY_LINEAR_INDEX=-1,this.PREPASS_REFLECTIVITY=!1,this.PREPASS_REFLECTIVITY_INDEX=-1,this.SCENE_MRT_COUNT=0,this.RGBDLIGHTMAP=!1,this.RGBDREFLECTION=!1,this.RGBDREFRACTION=!1,this.IMAGEPROCESSING=!1,this.VIGNETTE=!1,this.VIGNETTEBLENDMODEMULTIPLY=!1,this.VIGNETTEBLENDMODEOPAQUE=!1,this.TONEMAPPING=0,this.CONTRAST=!1,this.COLORCURVES=!1,this.COLORGRADING=!1,this.COLORGRADING3D=!1,this.SAMPLER3DGREENDEPTH=!1,this.SAMPLER3DBGRMAP=!1,this.DITHER=!1,this.IMAGEPROCESSINGPOSTPROCESS=!1,this.SKIPFINALCOLORCLAMP=!1,this.MULTIVIEW=!1,this.ORDER_INDEPENDENT_TRANSPARENCY=!1,this.ORDER_INDEPENDENT_TRANSPARENCY_16BITS=!1,this.CAMERA_ORTHOGRAPHIC=!1,this.CAMERA_PERSPECTIVE=!1,this.AREALIGHTSUPPORTED=!0,this.IS_REFLECTION_LINEAR=!1,this.IS_REFRACTION_LINEAR=!1,this.EXPOSURE=!1,this.DECAL_AFTER_DETAIL=!1,this.rebuild()}setReflectionMode(t){const a=["REFLECTIONMAP_CUBIC","REFLECTIONMAP_EXPLICIT","REFLECTIONMAP_PLANAR","REFLECTIONMAP_PROJECTION","REFLECTIONMAP_PROJECTION","REFLECTIONMAP_SKYBOX","REFLECTIONMAP_SPHERICAL","REFLECTIONMAP_EQUIRECTANGULAR","REFLECTIONMAP_EQUIRECTANGULAR_FIXED","REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED"];for(const h of a)this[h]=h===t}}class i extends z{get imageProcessingConfiguration(){return this._imageProcessingConfiguration}set imageProcessingConfiguration(t){this._attachImageProcessingConfiguration(t),this._markAllSubMeshesAsTexturesDirty()}_attachImageProcessingConfiguration(t){t!==this._imageProcessingConfiguration&&(this._imageProcessingConfiguration&&this._imageProcessingObserver&&this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver),t?this._imageProcessingConfiguration=t:this._imageProcessingConfiguration=this.getScene().imageProcessingConfiguration,this._imageProcessingConfiguration&&(this._imageProcessingObserver=this._imageProcessingConfiguration.onUpdateParameters.add(()=>{this._markAllSubMeshesAsImageProcessingDirty()})))}get isPrePassCapable(){return!this.disableDepthWrite}get cameraColorCurvesEnabled(){return this.imageProcessingConfiguration.colorCurvesEnabled}set cameraColorCurvesEnabled(t){this.imageProcessingConfiguration.colorCurvesEnabled=t}get cameraColorGradingEnabled(){return this.imageProcessingConfiguration.colorGradingEnabled}set cameraColorGradingEnabled(t){this.imageProcessingConfiguration.colorGradingEnabled=t}get cameraToneMappingEnabled(){return this._imageProcessingConfiguration.toneMappingEnabled}set cameraToneMappingEnabled(t){this._imageProcessingConfiguration.toneMappingEnabled=t}get cameraExposure(){return this._imageProcessingConfiguration.exposure}set cameraExposure(t){this._imageProcessingConfiguration.exposure=t}get cameraContrast(){return this._imageProcessingConfiguration.contrast}set cameraContrast(t){this._imageProcessingConfiguration.contrast=t}get cameraColorGradingTexture(){return this._imageProcessingConfiguration.colorGradingTexture}set cameraColorGradingTexture(t){this._imageProcessingConfiguration.colorGradingTexture=t}get cameraColorCurves(){return this._imageProcessingConfiguration.colorCurves}set cameraColorCurves(t){this._imageProcessingConfiguration.colorCurves=t}get canRenderToMRT(){return!0}constructor(t,a,h=!1){super(t,a,void 0,h||i.ForceGLSL),this._diffuseTexture=null,this._ambientTexture=null,this._opacityTexture=null,this._reflectionTexture=null,this._emissiveTexture=null,this._specularTexture=null,this._bumpTexture=null,this._lightmapTexture=null,this._refractionTexture=null,this.ambientColor=new A(0,0,0),this.diffuseColor=new A(1,1,1),this.specularColor=new A(1,1,1),this.emissiveColor=new A(0,0,0),this.specularPower=64,this._useAlphaFromDiffuseTexture=!1,this._useEmissiveAsIllumination=!1,this._linkEmissiveWithDiffuse=!1,this._useSpecularOverAlpha=!1,this._useReflectionOverAlpha=!1,this._disableLighting=!1,this._useObjectSpaceNormalMap=!1,this._useParallax=!1,this._useParallaxOcclusion=!1,this.parallaxScaleBias=.05,this._roughness=0,this.indexOfRefraction=.98,this.invertRefractionY=!0,this.alphaCutOff=.4,this._useLightmapAsShadowmap=!1,this._useReflectionFresnelFromSpecular=!1,this._useGlossinessFromSpecularMapAlpha=!1,this._maxSimultaneousLights=4,this._invertNormalMapX=!1,this._invertNormalMapY=!1,this._twoSidedLighting=!1,this._applyDecalMapAfterDetailMap=!1,this._shadersLoaded=!1,this._renderTargets=new W(16),this._globalAmbientColor=new A(0,0,0),this._cacheHasRenderTargetTextures=!1,this.detailMap=new Q(this),this._attachImageProcessingConfiguration(null),this.prePassConfiguration=new k,this.getRenderTargetTextures=()=>(this._renderTargets.reset(),i.ReflectionTextureEnabled&&this._reflectionTexture&&this._reflectionTexture.isRenderTarget&&this._renderTargets.push(this._reflectionTexture),i.RefractionTextureEnabled&&this._refractionTexture&&this._refractionTexture.isRenderTarget&&this._renderTargets.push(this._refractionTexture),this._eventInfo.renderTargets=this._renderTargets,this._callbackPluginEventFillRenderTargetTextures(this._eventInfo),this._renderTargets)}get hasRenderTargetTextures(){return i.ReflectionTextureEnabled&&this._reflectionTexture&&this._reflectionTexture.isRenderTarget||i.RefractionTextureEnabled&&this._refractionTexture&&this._refractionTexture.isRenderTarget?!0:this._cacheHasRenderTargetTextures}getClassName(){return"StandardMaterial"}needAlphaBlending(){return this._hasTransparencyMode?this._transparencyModeIsBlend:this._disableAlphaBlending?!1:this.alpha<1||this._opacityTexture!=null||this._shouldUseAlphaFromDiffuseTexture()||this._opacityFresnelParameters&&this._opacityFresnelParameters.isEnabled}needAlphaTesting(){return this._hasTransparencyMode?this._transparencyModeIsTest:this._hasAlphaChannel()&&(this._transparencyMode==null||this._transparencyMode===N.MATERIAL_ALPHATEST)}_shouldUseAlphaFromDiffuseTexture(){return this._diffuseTexture!=null&&this._diffuseTexture.hasAlpha&&this._useAlphaFromDiffuseTexture&&this._transparencyMode!==N.MATERIAL_OPAQUE}_hasAlphaChannel(){return this._diffuseTexture!=null&&this._diffuseTexture.hasAlpha||this._opacityTexture!=null}getAlphaTestTexture(){return this._diffuseTexture}isReadyForSubMesh(t,a,h=!1){this._uniformBufferLayoutBuilt||this.buildUniformLayout();const o=a._drawWrapper;if(o.effect&&this.isFrozen&&o._wasPreviouslyReady&&o._wasPreviouslyUsingInstances===h)return!0;a.materialDefines||(this._callbackPluginEventGeneric(4,this._eventInfo),a.materialDefines=new _e(this._eventInfo.defineNames));const u=this.getScene(),e=a.materialDefines;if(this._isReadyForSubMesh(a))return!0;const x=u.getEngine();e._needNormals=K(u,t,e,!0,this._maxSimultaneousLights,this._disableLighting),J(u,e);const r=this.needAlphaBlendingForMesh(t)&&this.getScene().useOrderIndependentTransparency;if(Z(u,e,this.canRenderToMRT&&!r),j(u,e,r),M.PrepareDefines(x.currentRenderPassId,t,e),e._areTexturesDirty){this._eventInfo.hasRenderTargetTextures=!1,this._callbackPluginEventHasRenderTargetTextures(this._eventInfo),this._cacheHasRenderTargetTextures=this._eventInfo.hasRenderTargetTextures,e._needUVs=!1;for(let T=1;T<=6;++T)e["MAINUV"+T]=!1;if(u.texturesEnabled){if(e.DIFFUSEDIRECTUV=0,e.BUMPDIRECTUV=0,e.AMBIENTDIRECTUV=0,e.OPACITYDIRECTUV=0,e.EMISSIVEDIRECTUV=0,e.SPECULARDIRECTUV=0,e.LIGHTMAPDIRECTUV=0,this._diffuseTexture&&i.DiffuseTextureEnabled)if(this._diffuseTexture.isReadyOrNotBlocking())R(this._diffuseTexture,e,"DIFFUSE");else return!1;else e.DIFFUSE=!1;if(this._ambientTexture&&i.AmbientTextureEnabled)if(this._ambientTexture.isReadyOrNotBlocking())R(this._ambientTexture,e,"AMBIENT");else return!1;else e.AMBIENT=!1;if(this._opacityTexture&&i.OpacityTextureEnabled)if(this._opacityTexture.isReadyOrNotBlocking())R(this._opacityTexture,e,"OPACITY"),e.OPACITYRGB=this._opacityTexture.getAlphaFromRGB;else return!1;else e.OPACITY=!1;if(this._reflectionTexture&&i.ReflectionTextureEnabled)if(this._reflectionTexture.isReadyOrNotBlocking()){switch(e._needNormals=!0,e.REFLECTION=!0,e.ROUGHNESS=this._roughness>0,e.REFLECTIONOVERALPHA=this._useReflectionOverAlpha,e.INVERTCUBICMAP=this._reflectionTexture.coordinatesMode===c.INVCUBIC_MODE,e.REFLECTIONMAP_3D=this._reflectionTexture.isCube,e.REFLECTIONMAP_OPPOSITEZ=e.REFLECTIONMAP_3D&&this.getScene().useRightHandedSystem?!this._reflectionTexture.invertZ:this._reflectionTexture.invertZ,e.RGBDREFLECTION=this._reflectionTexture.isRGBD,this._reflectionTexture.coordinatesMode){case c.EXPLICIT_MODE:e.setReflectionMode("REFLECTIONMAP_EXPLICIT");break;case c.PLANAR_MODE:e.setReflectionMode("REFLECTIONMAP_PLANAR");break;case c.PROJECTION_MODE:e.setReflectionMode("REFLECTIONMAP_PROJECTION");break;case c.SKYBOX_MODE:e.setReflectionMode("REFLECTIONMAP_SKYBOX");break;case c.SPHERICAL_MODE:e.setReflectionMode("REFLECTIONMAP_SPHERICAL");break;case c.EQUIRECTANGULAR_MODE:e.setReflectionMode("REFLECTIONMAP_EQUIRECTANGULAR");break;case c.FIXED_EQUIRECTANGULAR_MODE:e.setReflectionMode("REFLECTIONMAP_EQUIRECTANGULAR_FIXED");break;case c.FIXED_EQUIRECTANGULAR_MIRRORED_MODE:e.setReflectionMode("REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED");break;case c.CUBIC_MODE:case c.INVCUBIC_MODE:default:e.setReflectionMode("REFLECTIONMAP_CUBIC");break}e.USE_LOCAL_REFLECTIONMAP_CUBIC=!!this._reflectionTexture.boundingBoxSize}else return!1;else e.REFLECTION=!1,e.REFLECTIONMAP_OPPOSITEZ=!1;if(this._emissiveTexture&&i.EmissiveTextureEnabled)if(this._emissiveTexture.isReadyOrNotBlocking())R(this._emissiveTexture,e,"EMISSIVE");else return!1;else e.EMISSIVE=!1;if(this._lightmapTexture&&i.LightmapTextureEnabled)if(this._lightmapTexture.isReadyOrNotBlocking())R(this._lightmapTexture,e,"LIGHTMAP"),e.USELIGHTMAPASSHADOWMAP=this._useLightmapAsShadowmap,e.RGBDLIGHTMAP=this._lightmapTexture.isRGBD;else return!1;else e.LIGHTMAP=!1;if(this._specularTexture&&i.SpecularTextureEnabled)if(this._specularTexture.isReadyOrNotBlocking())R(this._specularTexture,e,"SPECULAR"),e.GLOSSINESS=this._useGlossinessFromSpecularMapAlpha;else return!1;else e.SPECULAR=!1;if(u.getEngine().getCaps().standardDerivatives&&this._bumpTexture&&i.BumpTextureEnabled){if(this._bumpTexture.isReady())R(this._bumpTexture,e,"BUMP"),e.PARALLAX=this._useParallax,e.PARALLAX_RHS=u.useRightHandedSystem,e.PARALLAXOCCLUSION=this._useParallaxOcclusion;else return!1;e.OBJECTSPACE_NORMALMAP=this._useObjectSpaceNormalMap}else e.BUMP=!1,e.PARALLAX=!1,e.PARALLAX_RHS=!1,e.PARALLAXOCCLUSION=!1;if(this._refractionTexture&&i.RefractionTextureEnabled)if(this._refractionTexture.isReadyOrNotBlocking())e._needUVs=!0,e.REFRACTION=!0,e.REFRACTIONMAP_3D=this._refractionTexture.isCube,e.RGBDREFRACTION=this._refractionTexture.isRGBD,e.USE_LOCAL_REFRACTIONMAP_CUBIC=!!this._refractionTexture.boundingBoxSize;else return!1;else e.REFRACTION=!1;e.TWOSIDEDLIGHTING=!this._backFaceCulling&&this._twoSidedLighting}else e.DIFFUSE=!1,e.AMBIENT=!1,e.OPACITY=!1,e.REFLECTION=!1,e.EMISSIVE=!1,e.LIGHTMAP=!1,e.BUMP=!1,e.REFRACTION=!1;e.ALPHAFROMDIFFUSE=this._shouldUseAlphaFromDiffuseTexture(),e.EMISSIVEASILLUMINATION=this._useEmissiveAsIllumination,e.LINKEMISSIVEWITHDIFFUSE=this._linkEmissiveWithDiffuse,e.SPECULAROVERALPHA=this._useSpecularOverAlpha,e.PREMULTIPLYALPHA=this.alphaMode===7||this.alphaMode===8,e.ALPHATEST_AFTERALLALPHACOMPUTATIONS=this.transparencyMode!==null,e.ALPHABLEND=this.transparencyMode===null||this.needAlphaBlendingForMesh(t)}if(this._eventInfo.isReadyForSubMesh=!0,this._eventInfo.defines=e,this._eventInfo.subMesh=a,this._callbackPluginEventIsReadyForSubMesh(this._eventInfo),!this._eventInfo.isReadyForSubMesh)return!1;if(e._areImageProcessingDirty&&this._imageProcessingConfiguration){if(!this._imageProcessingConfiguration.isReady())return!1;this._imageProcessingConfiguration.prepareDefines(e),e.IS_REFLECTION_LINEAR=this.reflectionTexture!=null&&!this.reflectionTexture.gammaSpace,e.IS_REFRACTION_LINEAR=this.refractionTexture!=null&&!this.refractionTexture.gammaSpace}if(e._areFresnelDirty&&(i.FresnelEnabled?(this._diffuseFresnelParameters||this._opacityFresnelParameters||this._emissiveFresnelParameters||this._refractionFresnelParameters||this._reflectionFresnelParameters)&&(e.DIFFUSEFRESNEL=this._diffuseFresnelParameters&&this._diffuseFresnelParameters.isEnabled,e.OPACITYFRESNEL=this._opacityFresnelParameters&&this._opacityFresnelParameters.isEnabled,e.REFLECTIONFRESNEL=this._reflectionFresnelParameters&&this._reflectionFresnelParameters.isEnabled,e.REFLECTIONFRESNELFROMSPECULAR=this._useReflectionFresnelFromSpecular,e.REFRACTIONFRESNEL=this._refractionFresnelParameters&&this._refractionFresnelParameters.isEnabled,e.EMISSIVEFRESNEL=this._emissiveFresnelParameters&&this._emissiveFresnelParameters.isEnabled,e._needNormals=!0,e.FRESNEL=!0):e.FRESNEL=!1),e.AREALIGHTUSED){for(let T=0;T<t.lightSources.length;T++)if(!t.lightSources[T]._isReady())return!1}$(t,u,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this.needAlphaTestingForMesh(t),e,this._applyDecalMapAfterDetailMap),q(u,x,this,e,h,null,a.getRenderingMesh().hasThinInstances),this._eventInfo.defines=e,this._eventInfo.mesh=t,this._callbackPluginEventPrepareDefinesBeforeAttributes(this._eventInfo),ee(t,e,!0,!0,!0),this._callbackPluginEventPrepareDefines(this._eventInfo);let d=!1;if(e.isDirty){const T=e._areLightsDisposed;e.markAsProcessed();const E=new ce;e.REFLECTION&&E.addFallback(0,"REFLECTION"),e.SPECULAR&&E.addFallback(0,"SPECULAR"),e.BUMP&&E.addFallback(0,"BUMP"),e.PARALLAX&&E.addFallback(1,"PARALLAX"),e.PARALLAX_RHS&&E.addFallback(1,"PARALLAX_RHS"),e.PARALLAXOCCLUSION&&E.addFallback(0,"PARALLAXOCCLUSION"),e.SPECULAROVERALPHA&&E.addFallback(0,"SPECULAROVERALPHA"),e.FOG&&E.addFallback(1,"FOG"),e.POINTSIZE&&E.addFallback(0,"POINTSIZE"),e.LOGARITHMICDEPTH&&E.addFallback(0,"LOGARITHMICDEPTH"),te(e,E,this._maxSimultaneousLights),e.SPECULARTERM&&E.addFallback(0,"SPECULARTERM"),e.DIFFUSEFRESNEL&&E.addFallback(1,"DIFFUSEFRESNEL"),e.OPACITYFRESNEL&&E.addFallback(2,"OPACITYFRESNEL"),e.REFLECTIONFRESNEL&&E.addFallback(3,"REFLECTIONFRESNEL"),e.EMISSIVEFRESNEL&&E.addFallback(4,"EMISSIVEFRESNEL"),e.FRESNEL&&E.addFallback(4,"FRESNEL"),e.MULTIVIEW&&E.addFallback(0,"MULTIVIEW");const p=[O.PositionKind];e.NORMAL&&p.push(O.NormalKind),e.TANGENT&&p.push(O.TangentKind);for(let g=1;g<=6;++g)e["UV"+g]&&p.push(`uv${g===1?"":g}`);e.VERTEXCOLOR&&p.push(O.ColorKind),ie(p,t,e,E),se(p,e),re(p,t,e),ae(p,t,e);let F="default";const m=["world","view","viewProjection","vEyePosition","vLightsType","vAmbientColor","vDiffuseColor","vSpecularColor","vEmissiveColor","visibility","vFogInfos","vFogColor","pointSize","vDiffuseInfos","vAmbientInfos","vOpacityInfos","vReflectionInfos","vEmissiveInfos","vSpecularInfos","vBumpInfos","vLightmapInfos","vRefractionInfos","mBones","diffuseMatrix","ambientMatrix","opacityMatrix","reflectionMatrix","emissiveMatrix","specularMatrix","bumpMatrix","normalMatrix","lightmapMatrix","refractionMatrix","diffuseLeftColor","diffuseRightColor","opacityParts","reflectionLeftColor","reflectionRightColor","emissiveLeftColor","emissiveRightColor","refractionLeftColor","refractionRightColor","vReflectionPosition","vReflectionSize","vRefractionPosition","vRefractionSize","logarithmicDepthConstant","vTangentSpaceParams","alphaCutOff","boneTextureWidth","morphTargetTextureInfo","morphTargetTextureIndices"],I=["diffuseSampler","ambientSampler","opacitySampler","reflectionCubeSampler","reflection2DSampler","emissiveSampler","specularSampler","bumpSampler","lightmapSampler","refractionCubeSampler","refraction2DSampler","boneSampler","morphTargets","oitDepthSampler","oitFrontColorSampler","areaLightsLTC1Sampler","areaLightsLTC2Sampler"],L=["Material","Scene","Mesh"],B={maxSimultaneousLights:this._maxSimultaneousLights,maxSimultaneousMorphTargets:e.NUM_MORPH_INFLUENCERS};this._eventInfo.fallbacks=E,this._eventInfo.fallbackRank=0,this._eventInfo.defines=e,this._eventInfo.uniforms=m,this._eventInfo.attributes=p,this._eventInfo.samplers=I,this._eventInfo.uniformBuffersNames=L,this._eventInfo.customCode=void 0,this._eventInfo.mesh=t,this._eventInfo.indexParameters=B,this._callbackPluginEventGeneric(128,this._eventInfo),M.AddUniformsAndSamplers(m,I),k.AddUniforms(m),D&&(D.PrepareUniforms(m,e),D.PrepareSamplers(I,e)),oe({uniformsNames:m,uniformBuffersNames:L,samplers:I,defines:e,maxSimultaneousLights:this._maxSimultaneousLights}),ne(m);const G={};this.customShaderNameResolve&&(F=this.customShaderNameResolve(F,m,L,I,e,p,G));const w=e.toString(),H=a.effect;let C=u.getEngine().createEffect(F,{attributes:p,uniformsNames:m,uniformBuffersNames:L,samplers:I,defines:w,fallbacks:E,onCompiled:this.onCompiled,onError:this.onError,indexParameters:B,processFinalCode:G.processFinalCode,processCodeAfterIncludes:this._eventInfo.customCode,multiTarget:e.PREPASS,shaderLanguage:this._shaderLanguage,extraInitializationsAsync:this._shadersLoaded?void 0:async()=>{this._shaderLanguage===1?await Promise.all([b(()=>import("./default.vertex-BzAfhmJ2.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]),import.meta.url),b(()=>import("./default.fragment-B_5_dYyZ.js"),__vite__mapDeps([12,1,2,3,4,5,13,9,7,14,15,11,16]),import.meta.url)]):await Promise.all([b(()=>import("./default.vertex-C7rJ-W2P.js"),__vite__mapDeps([17,1,2,3,18,19,20,21,22,23]),import.meta.url),b(()=>import("./default.fragment-4__l7eZ9.js"),__vite__mapDeps([24,1,2,3,25,19,20,22,21,26]),import.meta.url)]),this._shadersLoaded=!0}},x);if(this._eventInfo.customCode=void 0,C)if(this._onEffectCreatedObservable&&(y.effect=C,y.subMesh=a,this._onEffectCreatedObservable.notifyObservers(y)),this.allowShaderHotSwapping&&H&&!C.isReady()){if(C=H,e.markAsUnprocessed(),d=this.isFrozen,T)return e._areLightsDisposed=!0,!1}else u.resetCachedMaterial(),a.setEffect(C,e,this._materialContext)}return!a.effect||!a.effect.isReady()?!1:(e._renderId=u.getRenderId(),o._wasPreviouslyReady=!d,o._wasPreviouslyUsingInstances=h,this._checkScenePerformancePriority(),!0)}buildUniformLayout(){const t=this._uniformBuffer;t.addUniform("diffuseLeftColor",4),t.addUniform("diffuseRightColor",4),t.addUniform("opacityParts",4),t.addUniform("reflectionLeftColor",4),t.addUniform("reflectionRightColor",4),t.addUniform("refractionLeftColor",4),t.addUniform("refractionRightColor",4),t.addUniform("emissiveLeftColor",4),t.addUniform("emissiveRightColor",4),t.addUniform("vDiffuseInfos",2),t.addUniform("vAmbientInfos",2),t.addUniform("vOpacityInfos",2),t.addUniform("vReflectionInfos",2),t.addUniform("vReflectionPosition",3),t.addUniform("vReflectionSize",3),t.addUniform("vEmissiveInfos",2),t.addUniform("vLightmapInfos",2),t.addUniform("vSpecularInfos",2),t.addUniform("vBumpInfos",3),t.addUniform("diffuseMatrix",16),t.addUniform("ambientMatrix",16),t.addUniform("opacityMatrix",16),t.addUniform("reflectionMatrix",16),t.addUniform("emissiveMatrix",16),t.addUniform("lightmapMatrix",16),t.addUniform("specularMatrix",16),t.addUniform("bumpMatrix",16),t.addUniform("vTangentSpaceParams",2),t.addUniform("pointSize",1),t.addUniform("alphaCutOff",1),t.addUniform("refractionMatrix",16),t.addUniform("vRefractionInfos",4),t.addUniform("vRefractionPosition",3),t.addUniform("vRefractionSize",3),t.addUniform("vSpecularColor",4),t.addUniform("vEmissiveColor",3),t.addUniform("vDiffuseColor",4),t.addUniform("vAmbientColor",3),super.buildUniformLayout()}bindForSubMesh(t,a,h){const o=this.getScene(),u=h.materialDefines;if(!u)return;const e=h.effect;if(!e)return;this._activeEffect=e,a.getMeshUniformBuffer().bindToEffect(e,"Mesh"),a.transferToEffect(t),this._uniformBuffer.bindToEffect(e,"Material"),this.prePassConfiguration.bindForSubMesh(this._activeEffect,o,a,t,this.isFrozen),M.Bind(o.getEngine().currentRenderPassId,this._activeEffect,a,t,this),this._eventInfo.subMesh=h,this._callbackPluginEventHardBindForSubMesh(this._eventInfo),u.OBJECTSPACE_NORMALMAP&&(t.toNormalMatrix(this._normalMatrix),this.bindOnlyNormalMatrix(this._normalMatrix));const x=this._mustRebind(o,e,h,a.visibility);le(a,e);const r=this._uniformBuffer;if(x){if(this.bindViewProjection(e),!r.useUbo||!this.isFrozen||!r.isSync||h._drawWrapper._forceRebindOnNextCall){if(i.FresnelEnabled&&u.FRESNEL&&(this.diffuseFresnelParameters&&this.diffuseFresnelParameters.isEnabled&&(r.updateColor4("diffuseLeftColor",this.diffuseFresnelParameters.leftColor,this.diffuseFresnelParameters.power),r.updateColor4("diffuseRightColor",this.diffuseFresnelParameters.rightColor,this.diffuseFresnelParameters.bias)),this.opacityFresnelParameters&&this.opacityFresnelParameters.isEnabled&&r.updateColor4("opacityParts",new A(this.opacityFresnelParameters.leftColor.toLuminance(),this.opacityFresnelParameters.rightColor.toLuminance(),this.opacityFresnelParameters.bias),this.opacityFresnelParameters.power),this.reflectionFresnelParameters&&this.reflectionFresnelParameters.isEnabled&&(r.updateColor4("reflectionLeftColor",this.reflectionFresnelParameters.leftColor,this.reflectionFresnelParameters.power),r.updateColor4("reflectionRightColor",this.reflectionFresnelParameters.rightColor,this.reflectionFresnelParameters.bias)),this.refractionFresnelParameters&&this.refractionFresnelParameters.isEnabled&&(r.updateColor4("refractionLeftColor",this.refractionFresnelParameters.leftColor,this.refractionFresnelParameters.power),r.updateColor4("refractionRightColor",this.refractionFresnelParameters.rightColor,this.refractionFresnelParameters.bias)),this.emissiveFresnelParameters&&this.emissiveFresnelParameters.isEnabled&&(r.updateColor4("emissiveLeftColor",this.emissiveFresnelParameters.leftColor,this.emissiveFresnelParameters.power),r.updateColor4("emissiveRightColor",this.emissiveFresnelParameters.rightColor,this.emissiveFresnelParameters.bias))),o.texturesEnabled){if(this._diffuseTexture&&i.DiffuseTextureEnabled&&(r.updateFloat2("vDiffuseInfos",this._diffuseTexture.coordinatesIndex,this._diffuseTexture.level),P(this._diffuseTexture,r,"diffuse")),this._ambientTexture&&i.AmbientTextureEnabled&&(r.updateFloat2("vAmbientInfos",this._ambientTexture.coordinatesIndex,this._ambientTexture.level),P(this._ambientTexture,r,"ambient")),this._opacityTexture&&i.OpacityTextureEnabled&&(r.updateFloat2("vOpacityInfos",this._opacityTexture.coordinatesIndex,this._opacityTexture.level),P(this._opacityTexture,r,"opacity")),this._hasAlphaChannel()&&r.updateFloat("alphaCutOff",this.alphaCutOff),this._reflectionTexture&&i.ReflectionTextureEnabled){if(r.updateFloat2("vReflectionInfos",this._reflectionTexture.level,this.roughness),r.updateMatrix("reflectionMatrix",this._reflectionTexture.getReflectionTextureMatrix()),this._reflectionTexture.boundingBoxSize){const d=this._reflectionTexture;r.updateVector3("vReflectionPosition",d.boundingBoxPosition),r.updateVector3("vReflectionSize",d.boundingBoxSize)}}else r.updateFloat2("vReflectionInfos",0,this.roughness);if(this._emissiveTexture&&i.EmissiveTextureEnabled&&(r.updateFloat2("vEmissiveInfos",this._emissiveTexture.coordinatesIndex,this._emissiveTexture.level),P(this._emissiveTexture,r,"emissive")),this._lightmapTexture&&i.LightmapTextureEnabled&&(r.updateFloat2("vLightmapInfos",this._lightmapTexture.coordinatesIndex,this._lightmapTexture.level),P(this._lightmapTexture,r,"lightmap")),this._specularTexture&&i.SpecularTextureEnabled&&(r.updateFloat2("vSpecularInfos",this._specularTexture.coordinatesIndex,this._specularTexture.level),P(this._specularTexture,r,"specular")),this._bumpTexture&&o.getEngine().getCaps().standardDerivatives&&i.BumpTextureEnabled&&(r.updateFloat3("vBumpInfos",this._bumpTexture.coordinatesIndex,1/this._bumpTexture.level,this.parallaxScaleBias),P(this._bumpTexture,r,"bump"),o._mirroredCameraPosition?r.updateFloat2("vTangentSpaceParams",this._invertNormalMapX?1:-1,this._invertNormalMapY?1:-1):r.updateFloat2("vTangentSpaceParams",this._invertNormalMapX?-1:1,this._invertNormalMapY?-1:1)),this._refractionTexture&&i.RefractionTextureEnabled){let d=1;if(this._refractionTexture.isCube||(r.updateMatrix("refractionMatrix",this._refractionTexture.getReflectionTextureMatrix()),this._refractionTexture.depth&&(d=this._refractionTexture.depth)),r.updateFloat4("vRefractionInfos",this._refractionTexture.level,this.indexOfRefraction,d,this.invertRefractionY?-1:1),this._refractionTexture.boundingBoxSize){const T=this._refractionTexture;r.updateVector3("vRefractionPosition",T.boundingBoxPosition),r.updateVector3("vRefractionSize",T.boundingBoxSize)}}}this.pointsCloud&&r.updateFloat("pointSize",this.pointSize),r.updateColor4("vSpecularColor",this.specularColor,this.specularPower),r.updateColor3("vEmissiveColor",i.EmissiveTextureEnabled?this.emissiveColor:A.BlackReadOnly),r.updateColor4("vDiffuseColor",this.diffuseColor,this.alpha),o.ambientColor.multiplyToRef(this.ambientColor,this._globalAmbientColor),r.updateColor3("vAmbientColor",this._globalAmbientColor)}o.texturesEnabled&&(this._diffuseTexture&&i.DiffuseTextureEnabled&&e.setTexture("diffuseSampler",this._diffuseTexture),this._ambientTexture&&i.AmbientTextureEnabled&&e.setTexture("ambientSampler",this._ambientTexture),this._opacityTexture&&i.OpacityTextureEnabled&&e.setTexture("opacitySampler",this._opacityTexture),this._reflectionTexture&&i.ReflectionTextureEnabled&&(this._reflectionTexture.isCube?e.setTexture("reflectionCubeSampler",this._reflectionTexture):e.setTexture("reflection2DSampler",this._reflectionTexture)),this._emissiveTexture&&i.EmissiveTextureEnabled&&e.setTexture("emissiveSampler",this._emissiveTexture),this._lightmapTexture&&i.LightmapTextureEnabled&&e.setTexture("lightmapSampler",this._lightmapTexture),this._specularTexture&&i.SpecularTextureEnabled&&e.setTexture("specularSampler",this._specularTexture),this._bumpTexture&&o.getEngine().getCaps().standardDerivatives&&i.BumpTextureEnabled&&e.setTexture("bumpSampler",this._bumpTexture),this._refractionTexture&&i.RefractionTextureEnabled&&(this._refractionTexture.isCube?e.setTexture("refractionCubeSampler",this._refractionTexture):e.setTexture("refraction2DSampler",this._refractionTexture))),this.getScene().useOrderIndependentTransparency&&this.needAlphaBlendingForMesh(a)&&this.getScene().depthPeelingRenderer.bind(e),this._eventInfo.subMesh=h,this._callbackPluginEventBindForSubMesh(this._eventInfo),ue(e,this,o),this.bindEyePosition(e)}else o.getEngine()._features.needToAlwaysBindUniformBuffers&&(this._needToBindSceneUbo=!0);(x||!this.isFrozen)&&(o.lightsEnabled&&!this._disableLighting&&fe(o,a,e,u,this._maxSimultaneousLights),(o.fogEnabled&&a.applyFog&&o.fogMode!==X.FOGMODE_NONE||this._reflectionTexture||this._refractionTexture||a.receiveShadows||u.PREPASS)&&this.bindView(e),he(o,a,e),u.NUM_MORPH_INFLUENCERS&&Ee(a,e),u.BAKED_VERTEX_ANIMATION_TEXTURE&&a.bakedVertexAnimationManager?.bind(e,u.INSTANCES),this.useLogarithmicDepth&&Te(u,e,o),this._imageProcessingConfiguration&&!this._imageProcessingConfiguration.applyByPostProcess&&this._imageProcessingConfiguration.bind(this._activeEffect)),this._afterBind(a,this._activeEffect,h),r.update()}getAnimatables(){const t=super.getAnimatables();return this._diffuseTexture&&this._diffuseTexture.animations&&this._diffuseTexture.animations.length>0&&t.push(this._diffuseTexture),this._ambientTexture&&this._ambientTexture.animations&&this._ambientTexture.animations.length>0&&t.push(this._ambientTexture),this._opacityTexture&&this._opacityTexture.animations&&this._opacityTexture.animations.length>0&&t.push(this._opacityTexture),this._reflectionTexture&&this._reflectionTexture.animations&&this._reflectionTexture.animations.length>0&&t.push(this._reflectionTexture),this._emissiveTexture&&this._emissiveTexture.animations&&this._emissiveTexture.animations.length>0&&t.push(this._emissiveTexture),this._specularTexture&&this._specularTexture.animations&&this._specularTexture.animations.length>0&&t.push(this._specularTexture),this._bumpTexture&&this._bumpTexture.animations&&this._bumpTexture.animations.length>0&&t.push(this._bumpTexture),this._lightmapTexture&&this._lightmapTexture.animations&&this._lightmapTexture.animations.length>0&&t.push(this._lightmapTexture),this._refractionTexture&&this._refractionTexture.animations&&this._refractionTexture.animations.length>0&&t.push(this._refractionTexture),t}getActiveTextures(){const t=super.getActiveTextures();return this._diffuseTexture&&t.push(this._diffuseTexture),this._ambientTexture&&t.push(this._ambientTexture),this._opacityTexture&&t.push(this._opacityTexture),this._reflectionTexture&&t.push(this._reflectionTexture),this._emissiveTexture&&t.push(this._emissiveTexture),this._specularTexture&&t.push(this._specularTexture),this._bumpTexture&&t.push(this._bumpTexture),this._lightmapTexture&&t.push(this._lightmapTexture),this._refractionTexture&&t.push(this._refractionTexture),t}hasTexture(t){return!!(super.hasTexture(t)||this._diffuseTexture===t||this._ambientTexture===t||this._opacityTexture===t||this._reflectionTexture===t||this._emissiveTexture===t||this._specularTexture===t||this._bumpTexture===t||this._lightmapTexture===t||this._refractionTexture===t)}dispose(t,a){a&&(this._diffuseTexture?.dispose(),this._ambientTexture?.dispose(),this._opacityTexture?.dispose(),this._reflectionTexture?.dispose(),this._emissiveTexture?.dispose(),this._specularTexture?.dispose(),this._bumpTexture?.dispose(),this._lightmapTexture?.dispose(),this._refractionTexture?.dispose()),this._imageProcessingConfiguration&&this._imageProcessingObserver&&this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver),super.dispose(t,a)}clone(t,a=!0,h=""){const o=V.Clone(()=>new i(t,this.getScene()),this,{cloneTexturesOnlyOnce:a});return o.name=t,o.id=t,this.stencil.copyTo(o.stencil),this._clonePlugins(o,h),o}static Parse(t,a,h){const o=V.Parse(()=>new i(t.name,a),t,a,h);return t.stencil&&o.stencil.parse(t.stencil,a,h),N._ParsePlugins(t,o,a,h),o}static get DiffuseTextureEnabled(){return l.DiffuseTextureEnabled}static set DiffuseTextureEnabled(t){l.DiffuseTextureEnabled=t}static get DetailTextureEnabled(){return l.DetailTextureEnabled}static set DetailTextureEnabled(t){l.DetailTextureEnabled=t}static get AmbientTextureEnabled(){return l.AmbientTextureEnabled}static set AmbientTextureEnabled(t){l.AmbientTextureEnabled=t}static get OpacityTextureEnabled(){return l.OpacityTextureEnabled}static set OpacityTextureEnabled(t){l.OpacityTextureEnabled=t}static get ReflectionTextureEnabled(){return l.ReflectionTextureEnabled}static set ReflectionTextureEnabled(t){l.ReflectionTextureEnabled=t}static get EmissiveTextureEnabled(){return l.EmissiveTextureEnabled}static set EmissiveTextureEnabled(t){l.EmissiveTextureEnabled=t}static get SpecularTextureEnabled(){return l.SpecularTextureEnabled}static set SpecularTextureEnabled(t){l.SpecularTextureEnabled=t}static get BumpTextureEnabled(){return l.BumpTextureEnabled}static set BumpTextureEnabled(t){l.BumpTextureEnabled=t}static get LightmapTextureEnabled(){return l.LightmapTextureEnabled}static set LightmapTextureEnabled(t){l.LightmapTextureEnabled=t}static get RefractionTextureEnabled(){return l.RefractionTextureEnabled}static set RefractionTextureEnabled(t){l.RefractionTextureEnabled=t}static get ColorGradingTextureEnabled(){return l.ColorGradingTextureEnabled}static set ColorGradingTextureEnabled(t){l.ColorGradingTextureEnabled=t}static get FresnelEnabled(){return l.FresnelEnabled}static set FresnelEnabled(t){l.FresnelEnabled=t}}i.ForceGLSL=!1;s([_("diffuseTexture")],i.prototype,"_diffuseTexture",void 0);s([n("_markAllSubMeshesAsTexturesAndMiscDirty")],i.prototype,"diffuseTexture",void 0);s([_("ambientTexture")],i.prototype,"_ambientTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"ambientTexture",void 0);s([_("opacityTexture")],i.prototype,"_opacityTexture",void 0);s([n("_markAllSubMeshesAsTexturesAndMiscDirty")],i.prototype,"opacityTexture",void 0);s([_("reflectionTexture")],i.prototype,"_reflectionTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"reflectionTexture",void 0);s([_("emissiveTexture")],i.prototype,"_emissiveTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"emissiveTexture",void 0);s([_("specularTexture")],i.prototype,"_specularTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"specularTexture",void 0);s([_("bumpTexture")],i.prototype,"_bumpTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"bumpTexture",void 0);s([_("lightmapTexture")],i.prototype,"_lightmapTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"lightmapTexture",void 0);s([_("refractionTexture")],i.prototype,"_refractionTexture",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"refractionTexture",void 0);s([v("ambient")],i.prototype,"ambientColor",void 0);s([v("diffuse")],i.prototype,"diffuseColor",void 0);s([v("specular")],i.prototype,"specularColor",void 0);s([v("emissive")],i.prototype,"emissiveColor",void 0);s([f()],i.prototype,"specularPower",void 0);s([f("useAlphaFromDiffuseTexture")],i.prototype,"_useAlphaFromDiffuseTexture",void 0);s([n("_markAllSubMeshesAsTexturesAndMiscDirty")],i.prototype,"useAlphaFromDiffuseTexture",void 0);s([f("useEmissiveAsIllumination")],i.prototype,"_useEmissiveAsIllumination",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useEmissiveAsIllumination",void 0);s([f("linkEmissiveWithDiffuse")],i.prototype,"_linkEmissiveWithDiffuse",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"linkEmissiveWithDiffuse",void 0);s([f("useSpecularOverAlpha")],i.prototype,"_useSpecularOverAlpha",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useSpecularOverAlpha",void 0);s([f("useReflectionOverAlpha")],i.prototype,"_useReflectionOverAlpha",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useReflectionOverAlpha",void 0);s([f("disableLighting")],i.prototype,"_disableLighting",void 0);s([n("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0);s([f("useObjectSpaceNormalMap")],i.prototype,"_useObjectSpaceNormalMap",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useObjectSpaceNormalMap",void 0);s([f("useParallax")],i.prototype,"_useParallax",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useParallax",void 0);s([f("useParallaxOcclusion")],i.prototype,"_useParallaxOcclusion",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useParallaxOcclusion",void 0);s([f()],i.prototype,"parallaxScaleBias",void 0);s([f("roughness")],i.prototype,"_roughness",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"roughness",void 0);s([f()],i.prototype,"indexOfRefraction",void 0);s([f()],i.prototype,"invertRefractionY",void 0);s([f()],i.prototype,"alphaCutOff",void 0);s([f("useLightmapAsShadowmap")],i.prototype,"_useLightmapAsShadowmap",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useLightmapAsShadowmap",void 0);s([S("diffuseFresnelParameters")],i.prototype,"_diffuseFresnelParameters",void 0);s([n("_markAllSubMeshesAsFresnelDirty")],i.prototype,"diffuseFresnelParameters",void 0);s([S("opacityFresnelParameters")],i.prototype,"_opacityFresnelParameters",void 0);s([n("_markAllSubMeshesAsFresnelAndMiscDirty")],i.prototype,"opacityFresnelParameters",void 0);s([S("reflectionFresnelParameters")],i.prototype,"_reflectionFresnelParameters",void 0);s([n("_markAllSubMeshesAsFresnelDirty")],i.prototype,"reflectionFresnelParameters",void 0);s([S("refractionFresnelParameters")],i.prototype,"_refractionFresnelParameters",void 0);s([n("_markAllSubMeshesAsFresnelDirty")],i.prototype,"refractionFresnelParameters",void 0);s([S("emissiveFresnelParameters")],i.prototype,"_emissiveFresnelParameters",void 0);s([n("_markAllSubMeshesAsFresnelDirty")],i.prototype,"emissiveFresnelParameters",void 0);s([f("useReflectionFresnelFromSpecular")],i.prototype,"_useReflectionFresnelFromSpecular",void 0);s([n("_markAllSubMeshesAsFresnelDirty")],i.prototype,"useReflectionFresnelFromSpecular",void 0);s([f("useGlossinessFromSpecularMapAlpha")],i.prototype,"_useGlossinessFromSpecularMapAlpha",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"useGlossinessFromSpecularMapAlpha",void 0);s([f("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0);s([n("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0);s([f("invertNormalMapX")],i.prototype,"_invertNormalMapX",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"invertNormalMapX",void 0);s([f("invertNormalMapY")],i.prototype,"_invertNormalMapY",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"invertNormalMapY",void 0);s([f("twoSidedLighting")],i.prototype,"_twoSidedLighting",void 0);s([n("_markAllSubMeshesAsTexturesDirty")],i.prototype,"twoSidedLighting",void 0);s([f("applyDecalMapAfterDetailMap")],i.prototype,"_applyDecalMapAfterDetailMap",void 0);s([n("_markAllSubMeshesAsMiscDirty")],i.prototype,"applyDecalMapAfterDetailMap",void 0);Y("BABYLON.StandardMaterial",i);X.DefaultMaterialFactory=U=>new i("default material",U);export{i as S};
//# sourceMappingURL=standardMaterial-DAhjcsHI.js.map
