import{c as i}from"./declarationMapper-r-RREw_K.js";import{f as o}from"./KHR_interactivity-DVSiPm30.js";import{R as r}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class e extends o{constructor(t){super(t),this.condition=this.registerDataInput("condition",i),this.onTrue=this._registerSignalOutput("onTrue"),this.onFalse=this._registerSignalOutput("onFalse")}_execute(t){this.condition.getValue(t)?this.onTrue._activateSignal(t):this.onFalse._activateSignal(t)}getClassName(){return"FlowGraphBranchBlock"}}r("FlowGraphBranchBlock",e);export{e as FlowGraphBranchBlock};
//# sourceMappingURL=flowGraphBranchBlock-Cmz3HUH_.js.map
