{"version": 3, "file": "flowGraphMathBlocks-BzIhuErF.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConstantOperationBlock.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathBlocks.js"], "sourcesContent": ["import { FlowGraphCachedOperationBlock } from \"./flowGraphCachedOperationBlock.js\";\n/**\n * Block that outputs a value of type ResultT, resulting of an operation with no inputs.\n * This block is being extended by some math operations and should not be used directly.\n * @internal\n */\nexport class FlowGraphConstantOperationBlock extends FlowGraphCachedOperationBlock {\n    constructor(richType, _operation, _className, config) {\n        super(richType, config);\n        this._operation = _operation;\n        this._className = _className;\n    }\n    /**\n     * the operation performed by this block\n     * @param context the graph context\n     * @returns the result of the operation\n     */\n    _doOperation(context) {\n        return this._operation(context);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return this._className;\n    }\n}\n//# sourceMappingURL=flowGraphConstantOperationBlock.js.map", "import { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { getRichTypeByFlowGraphType, RichTypeAny, RichTypeBoolean, RichTypeFlowGraphInteger, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphBinaryOperationBlock } from \"../flowGraphBinaryOperationBlock.js\";\nimport { FlowGraphConstantOperationBlock } from \"../flowGraphConstantOperationBlock.js\";\nimport { Quaternion, Matrix, Vector2, Vector3, Vector4 } from \"../../../../Maths/math.vector.js\";\nimport { FlowGraphUnaryOperationBlock } from \"../flowGraphUnaryOperationBlock.js\";\nimport { FlowGraphTernaryOperationBlock } from \"../flowGraphTernaryOperationBlock.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\nimport { FlowGraphMatrix2D, FlowGraphMatrix3D } from \"../../../CustomTypes/flowGraphMatrix.js\";\nimport { _areSameIntegerClass, _areSameMatrixClass, _areSameVectorClass, _getClassNameOf, getNumericValue, isNumeric } from \"../../../utils.js\";\n/**\n * Polymorphic add block.\n */\nexport class FlowGraphAddBlock extends FlowGraphBinaryOperationBlock {\n    /**\n     * Construct a new add block.\n     * @param config optional configuration\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), (a, b) => this._polymorphicAdd(a, b), \"FlowGraphAddBlock\" /* FlowGraphBlockNames.Add */, config);\n    }\n    _polymorphicAdd(a, b) {\n        const aClassName = _getClassNameOf(a);\n        const bClassName = _getClassNameOf(b);\n        if (_areSameVectorClass(aClassName, bClassName) || _areSameMatrixClass(aClassName, bClassName) || _areSameIntegerClass(aClassName, bClassName)) {\n            // cast to vector3, but any other cast will be fine\n            return a.add(b);\n        }\n        else if (aClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */ || bClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */) {\n            // this is a simple add, and should be also supported between Quat and Vector4. Therefore -\n            return a.add(b);\n        }\n        else {\n            return a + b;\n        }\n    }\n}\nRegisterClass(\"FlowGraphAddBlock\" /* FlowGraphBlockNames.Add */, FlowGraphAddBlock);\n/**\n * Polymorphic subtract block.\n */\nexport class FlowGraphSubtractBlock extends FlowGraphBinaryOperationBlock {\n    /**\n     * Construct a new subtract block.\n     * @param config optional configuration\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), (a, b) => this._polymorphicSubtract(a, b), \"FlowGraphSubtractBlock\" /* FlowGraphBlockNames.Subtract */, config);\n    }\n    _polymorphicSubtract(a, b) {\n        const aClassName = _getClassNameOf(a);\n        const bClassName = _getClassNameOf(b);\n        if (_areSameVectorClass(aClassName, bClassName) || _areSameIntegerClass(aClassName, bClassName) || _areSameMatrixClass(aClassName, bClassName)) {\n            return a.subtract(b);\n        }\n        else if (aClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */ || bClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */) {\n            // this is a simple subtract, and should be also supported between Quat and Vector4. Therefore -\n            return a.subtract(b);\n        }\n        else {\n            return a - b;\n        }\n    }\n}\nRegisterClass(\"FlowGraphSubtractBlock\" /* FlowGraphBlockNames.Subtract */, FlowGraphSubtractBlock);\n/**\n * Polymorphic multiply block.\n * In case of matrix, it is configurable whether the multiplication is done per component.\n */\nexport class FlowGraphMultiplyBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), (a, b) => this._polymorphicMultiply(a, b), \"FlowGraphMultiplyBlock\" /* FlowGraphBlockNames.Multiply */, config);\n    }\n    _polymorphicMultiply(a, b) {\n        const aClassName = _getClassNameOf(a);\n        const bClassName = _getClassNameOf(b);\n        if (_areSameVectorClass(aClassName, bClassName) || _areSameIntegerClass(aClassName, bClassName)) {\n            return a.multiply(b);\n        }\n        else if (aClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */ || bClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */) {\n            // this is a simple multiply (per component!), and should be also supported between Quat and Vector4. Therefore -\n            const aClone = a.clone();\n            aClone.x *= b.x;\n            aClone.y *= b.y;\n            aClone.z *= b.z;\n            aClone.w *= b.w;\n            return aClone;\n        }\n        else if (_areSameMatrixClass(aClassName, bClassName)) {\n            if (this.config?.useMatrixPerComponent) {\n                // this is the definition of multiplication of glTF interactivity\n                // get a's m as array, and multiply each component with b's m\n                const aM = a.m;\n                for (let i = 0; i < aM.length; i++) {\n                    aM[i] *= b.m[i];\n                }\n                if (aClassName === \"Matrix2D\" /* FlowGraphTypes.Matrix2D */) {\n                    return new FlowGraphMatrix2D(aM);\n                }\n                else if (aClassName === \"Matrix3D\" /* FlowGraphTypes.Matrix3D */) {\n                    return new FlowGraphMatrix3D(aM);\n                }\n                else {\n                    return Matrix.FromArray(aM);\n                }\n            }\n            else {\n                a = a;\n                b = b;\n                return b.multiply(a);\n            }\n        }\n        else {\n            return a * b;\n        }\n    }\n}\nRegisterClass(\"FlowGraphMultiplyBlock\" /* FlowGraphBlockNames.Multiply */, FlowGraphMultiplyBlock);\n/**\n * Polymorphic division block.\n */\nexport class FlowGraphDivideBlock extends FlowGraphBinaryOperationBlock {\n    /**\n     * Construct a new divide block.\n     * @param config - Optional configuration\n     */\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), getRichTypeByFlowGraphType(config?.type), (a, b) => this._polymorphicDivide(a, b), \"FlowGraphDivideBlock\" /* FlowGraphBlockNames.Divide */, config);\n    }\n    _polymorphicDivide(a, b) {\n        const aClassName = _getClassNameOf(a);\n        const bClassName = _getClassNameOf(b);\n        if (_areSameVectorClass(aClassName, bClassName) || _areSameIntegerClass(aClassName, bClassName)) {\n            // cast to vector3, but it can be casted to any vector type\n            return a.divide(b);\n        }\n        else if (aClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */ || bClassName === \"Quaternion\" /* FlowGraphTypes.Quaternion */) {\n            // this is a simple division (per component!), and should be also supported between Quat and Vector4. Therefore -\n            const aClone = a.clone();\n            aClone.x /= b.x;\n            aClone.y /= b.y;\n            aClone.z /= b.z;\n            aClone.w /= b.w;\n            return aClone;\n        }\n        else if (_areSameMatrixClass(aClassName, bClassName)) {\n            if (this.config?.useMatrixPerComponent) {\n                // get a's m as array, and divide each component with b's m\n                const aM = a.m;\n                for (let i = 0; i < aM.length; i++) {\n                    aM[i] /= b.m[i];\n                }\n                if (aClassName === \"Matrix2D\" /* FlowGraphTypes.Matrix2D */) {\n                    return new FlowGraphMatrix2D(aM);\n                }\n                else if (aClassName === \"Matrix3D\" /* FlowGraphTypes.Matrix3D */) {\n                    return new FlowGraphMatrix3D(aM);\n                }\n                else {\n                    return Matrix.FromArray(aM);\n                }\n            }\n            else {\n                a = a;\n                b = b;\n                return a.divide(b);\n            }\n        }\n        else {\n            return a / b;\n        }\n    }\n}\nRegisterClass(\"FlowGraphDivideBlock\" /* FlowGraphBlockNames.Divide */, FlowGraphDivideBlock);\n/**\n * Random number between min and max (defaults to 0 to 1)\n *\n * This node will cache the result for he same node reference. i.e., a Math.eq that references the SAME random node will always return true.\n */\nexport class FlowGraphRandomBlock extends FlowGraphConstantOperationBlock {\n    /**\n     * Construct a new random block.\n     * @param config optional configuration\n     */\n    constructor(config) {\n        super(RichTypeNumber, (context) => this._random(context), \"FlowGraphRandomBlock\" /* FlowGraphBlockNames.Random */, config);\n        this.min = this.registerDataInput(\"min\", RichTypeNumber, config?.min ?? 0);\n        this.max = this.registerDataInput(\"max\", RichTypeNumber, config?.max ?? 1);\n        if (config?.seed) {\n            this._seed = config.seed;\n        }\n    }\n    _isSeed(seed = this._seed) {\n        return seed !== undefined;\n    }\n    _getRandomValue() {\n        if (this._isSeed(this._seed)) {\n            // compute seed-based random number, deterministic randomness!\n            const x = Math.sin(this._seed++) * 10000;\n            return x - Math.floor(x);\n        }\n        return Math.random();\n    }\n    _random(context) {\n        const min = this.min.getValue(context);\n        const max = this.max.getValue(context);\n        return this._getRandomValue() * (max - min) + min;\n    }\n}\nRegisterClass(\"FlowGraphRandomBlock\" /* FlowGraphBlockNames.Random */, FlowGraphRandomBlock);\n/**\n * E constant.\n */\nexport class FlowGraphEBlock extends FlowGraphConstantOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, () => Math.E, \"FlowGraphEBlock\" /* FlowGraphBlockNames.E */, config);\n    }\n}\nRegisterClass(\"FlowGraphEBlock\" /* FlowGraphBlockNames.E */, FlowGraphEBlock);\n/**\n * Pi constant.\n */\nexport class FlowGraphPiBlock extends FlowGraphConstantOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, () => Math.PI, \"FlowGraphPIBlock\" /* FlowGraphBlockNames.PI */, config);\n    }\n}\nRegisterClass(\"FlowGraphPIBlock\" /* FlowGraphBlockNames.PI */, FlowGraphPiBlock);\n/**\n * Positive inf constant.\n */\nexport class FlowGraphInfBlock extends FlowGraphConstantOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, () => Number.POSITIVE_INFINITY, \"FlowGraphInfBlock\" /* FlowGraphBlockNames.Inf */, config);\n    }\n}\nRegisterClass(\"FlowGraphInfBlock\" /* FlowGraphBlockNames.Inf */, FlowGraphInfBlock);\n/**\n * NaN constant.\n */\nexport class FlowGraphNaNBlock extends FlowGraphConstantOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, () => Number.NaN, \"FlowGraphNaNBlock\" /* FlowGraphBlockNames.NaN */, config);\n    }\n}\nRegisterClass(\"FlowGraphNaNBlock\" /* FlowGraphBlockNames.NaN */, FlowGraphNaNBlock);\nfunction _componentWiseUnaryOperation(a, op) {\n    const aClassName = _getClassNameOf(a);\n    switch (aClassName) {\n        case \"FlowGraphInteger\":\n            a = a;\n            return new FlowGraphInteger(op(a.value));\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            a = a;\n            return new Vector2(op(a.x), op(a.y));\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            a = a;\n            return new Vector3(op(a.x), op(a.y), op(a.z));\n        case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            a = a;\n            return new Vector4(op(a.x), op(a.y), op(a.z), op(a.w));\n        case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n            a = a;\n            return new Quaternion(op(a.x), op(a.y), op(a.z), op(a.w));\n        case \"Matrix\" /* FlowGraphTypes.Matrix */:\n            a = a;\n            return Matrix.FromArray(a.m.map(op));\n        case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n            a = a;\n            // reason for not using .map is performance\n            return new FlowGraphMatrix2D(a.m.map(op));\n        case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n            a = a;\n            return new FlowGraphMatrix3D(a.m.map(op));\n        default:\n            a = a;\n            return op(a);\n    }\n}\n/**\n * Absolute value block.\n */\nexport class FlowGraphAbsBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicAbs(a), \"FlowGraphAbsBlock\" /* FlowGraphBlockNames.Abs */, config);\n    }\n    _polymorphicAbs(a) {\n        return _componentWiseUnaryOperation(a, Math.abs);\n    }\n}\nRegisterClass(\"FlowGraphAbsBlock\" /* FlowGraphBlockNames.Abs */, FlowGraphAbsBlock);\n/**\n * Sign block.\n */\nexport class FlowGraphSignBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicSign(a), \"FlowGraphSignBlock\" /* FlowGraphBlockNames.Sign */, config);\n    }\n    _polymorphicSign(a) {\n        return _componentWiseUnaryOperation(a, Math.sign);\n    }\n}\nRegisterClass(\"FlowGraphSignBlock\" /* FlowGraphBlockNames.Sign */, FlowGraphSignBlock);\n/**\n * Truncation block.\n */\nexport class FlowGraphTruncBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicTrunc(a), \"FlowGraphTruncBlock\" /* FlowGraphBlockNames.Trunc */, config);\n    }\n    _polymorphicTrunc(a) {\n        return _componentWiseUnaryOperation(a, Math.trunc);\n    }\n}\nRegisterClass(\"FlowGraphTruncBlock\" /* FlowGraphBlockNames.Trunc */, FlowGraphTruncBlock);\n/**\n * Floor block.\n */\nexport class FlowGraphFloorBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicFloor(a), \"FlowGraphFloorBlock\" /* FlowGraphBlockNames.Floor */, config);\n    }\n    _polymorphicFloor(a) {\n        return _componentWiseUnaryOperation(a, Math.floor);\n    }\n}\nRegisterClass(\"FlowGraphFloorBlock\" /* FlowGraphBlockNames.Floor */, FlowGraphFloorBlock);\n/**\n * Ceiling block.\n */\nexport class FlowGraphCeilBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicCeiling(a), \"FlowGraphCeilBlock\" /* FlowGraphBlockNames.Ceil */, config);\n    }\n    _polymorphicCeiling(a) {\n        return _componentWiseUnaryOperation(a, Math.ceil);\n    }\n}\nRegisterClass(\"FlowGraphCeilBlock\" /* FlowGraphBlockNames.Ceil */, FlowGraphCeilBlock);\n/**\n * Round block.\n */\nexport class FlowGraphRoundBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicRound(a), \"FlowGraphRoundBlock\" /* FlowGraphBlockNames.Round */, config);\n    }\n    _polymorphicRound(a) {\n        return _componentWiseUnaryOperation(a, (a) => (a < 0 && this.config?.roundHalfAwayFromZero ? -Math.round(-a) : Math.round(a)));\n    }\n}\nRegisterClass(\"FlowGraphRoundBlock\" /* FlowGraphBlockNames.Round */, FlowGraphRoundBlock);\n/**\n * A block that returns the fractional part of a number.\n */\nexport class FlowGraphFractionBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicFraction(a), \"FlowGraphFractBlock\" /* FlowGraphBlockNames.Fraction */, config);\n    }\n    _polymorphicFraction(a) {\n        return _componentWiseUnaryOperation(a, (a) => a - Math.floor(a));\n    }\n}\nRegisterClass(\"FlowGraphFractBlock\" /* FlowGraphBlockNames.Fraction */, FlowGraphFractionBlock);\n/**\n * Negation block.\n */\nexport class FlowGraphNegationBlock extends FlowGraphUnaryOperationBlock {\n    /**\n     * construct a new negation block.\n     * @param config optional configuration\n     */\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicNeg(a), \"FlowGraphNegationBlock\" /* FlowGraphBlockNames.Negation */, config);\n    }\n    _polymorphicNeg(a) {\n        return _componentWiseUnaryOperation(a, (a) => -a);\n    }\n}\nRegisterClass(\"FlowGraphNegationBlock\" /* FlowGraphBlockNames.Negation */, FlowGraphNegationBlock);\nfunction _componentWiseBinaryOperation(a, b, op) {\n    const aClassName = _getClassNameOf(a);\n    switch (aClassName) {\n        case \"FlowGraphInteger\":\n            a = a;\n            b = b;\n            return new FlowGraphInteger(op(a.value, b.value));\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            a = a;\n            b = b;\n            return new Vector2(op(a.x, b.x), op(a.y, b.y));\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            a = a;\n            b = b;\n            return new Vector3(op(a.x, b.x), op(a.y, b.y), op(a.z, b.z));\n        case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            a = a;\n            b = b;\n            return new Vector4(op(a.x, b.x), op(a.y, b.y), op(a.z, b.z), op(a.w, b.w));\n        case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n            a = a;\n            b = b;\n            return new Quaternion(op(a.x, b.x), op(a.y, b.y), op(a.z, b.z), op(a.w, b.w));\n        case \"Matrix\" /* FlowGraphTypes.Matrix */:\n            a = a;\n            return Matrix.FromArray(a.m.map((v, i) => op(v, b.m[i])));\n        case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n            a = a;\n            return new FlowGraphMatrix2D(a.m.map((v, i) => op(v, b.m[i])));\n        case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n            a = a;\n            return new FlowGraphMatrix3D(a.m.map((v, i) => op(v, b.m[i])));\n        default:\n            return op(a, b);\n    }\n}\n/**\n * Remainder block.\n */\nexport class FlowGraphModuloBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, (a, b) => this._polymorphicRemainder(a, b), \"FlowGraphModuloBlock\" /* FlowGraphBlockNames.Modulo */, config);\n    }\n    _polymorphicRemainder(a, b) {\n        return _componentWiseBinaryOperation(a, b, (a, b) => a % b);\n    }\n}\nRegisterClass(\"FlowGraphModuloBlock\" /* FlowGraphBlockNames.Modulo */, FlowGraphModuloBlock);\n/**\n * Min block.\n */\nexport class FlowGraphMinBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, (a, b) => this._polymorphicMin(a, b), \"FlowGraphMinBlock\" /* FlowGraphBlockNames.Min */, config);\n    }\n    _polymorphicMin(a, b) {\n        return _componentWiseBinaryOperation(a, b, Math.min);\n    }\n}\nRegisterClass(\"FlowGraphMinBlock\" /* FlowGraphBlockNames.Min */, FlowGraphMinBlock);\n/**\n * Max block\n */\nexport class FlowGraphMaxBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, (a, b) => this._polymorphicMax(a, b), \"FlowGraphMaxBlock\" /* FlowGraphBlockNames.Max */, config);\n    }\n    _polymorphicMax(a, b) {\n        return _componentWiseBinaryOperation(a, b, Math.max);\n    }\n}\nRegisterClass(\"FlowGraphMaxBlock\" /* FlowGraphBlockNames.Max */, FlowGraphMaxBlock);\nfunction _clamp(a, b, c) {\n    return Math.min(Math.max(a, Math.min(b, c)), Math.max(b, c));\n}\nfunction _componentWiseTernaryOperation(a, b, c, op) {\n    const aClassName = _getClassNameOf(a);\n    switch (aClassName) {\n        case \"FlowGraphInteger\":\n            a = a;\n            b = b;\n            c = c;\n            return new FlowGraphInteger(op(a.value, b.value, c.value));\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            a = a;\n            b = b;\n            c = c;\n            return new Vector2(op(a.x, b.x, c.x), op(a.y, b.y, c.y));\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            a = a;\n            b = b;\n            c = c;\n            return new Vector3(op(a.x, b.x, c.x), op(a.y, b.y, c.y), op(a.z, b.z, c.z));\n        case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            a = a;\n            b = b;\n            c = c;\n            return new Vector4(op(a.x, b.x, c.x), op(a.y, b.y, c.y), op(a.z, b.z, c.z), op(a.w, b.w, c.w));\n        case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n            a = a;\n            b = b;\n            c = c;\n            return new Quaternion(op(a.x, b.x, c.x), op(a.y, b.y, c.y), op(a.z, b.z, c.z), op(a.w, b.w, c.w));\n        case \"Matrix\" /* FlowGraphTypes.Matrix */:\n            return Matrix.FromArray(a.m.map((v, i) => op(v, b.m[i], c.m[i])));\n        case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n            return new FlowGraphMatrix2D(a.m.map((v, i) => op(v, b.m[i], c.m[i])));\n        case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n            return new FlowGraphMatrix3D(a.m.map((v, i) => op(v, b.m[i], c.m[i])));\n        default:\n            return op(a, b, c);\n    }\n}\n/**\n * Clamp block.\n */\nexport class FlowGraphClampBlock extends FlowGraphTernaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, RichTypeAny, (a, b, c) => this._polymorphicClamp(a, b, c), \"FlowGraphClampBlock\" /* FlowGraphBlockNames.Clamp */, config);\n    }\n    _polymorphicClamp(a, b, c) {\n        return _componentWiseTernaryOperation(a, b, c, _clamp);\n    }\n}\nRegisterClass(\"FlowGraphClampBlock\" /* FlowGraphBlockNames.Clamp */, FlowGraphClampBlock);\nfunction _saturate(a) {\n    return Math.min(Math.max(a, 0), 1);\n}\n/**\n * Saturate block.\n */\nexport class FlowGraphSaturateBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicSaturate(a), \"FlowGraphSaturateBlock\" /* FlowGraphBlockNames.Saturate */, config);\n    }\n    _polymorphicSaturate(a) {\n        return _componentWiseUnaryOperation(a, _saturate);\n    }\n}\nRegisterClass(\"FlowGraphSaturateBlock\" /* FlowGraphBlockNames.Saturate */, FlowGraphSaturateBlock);\nfunction _interpolate(a, b, c) {\n    return (1 - c) * a + c * b;\n}\n/**\n * Interpolate block.\n */\nexport class FlowGraphMathInterpolationBlock extends FlowGraphTernaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, RichTypeAny, (a, b, c) => this._polymorphicInterpolate(a, b, c), \"FlowGraphMathInterpolationBlock\" /* FlowGraphBlockNames.MathInterpolation */, config);\n    }\n    _polymorphicInterpolate(a, b, c) {\n        return _componentWiseTernaryOperation(a, b, c, _interpolate);\n    }\n}\nRegisterClass(\"FlowGraphMathInterpolationBlock\" /* FlowGraphBlockNames.MathInterpolation */, FlowGraphMathInterpolationBlock);\n/**\n * Equals block.\n */\nexport class FlowGraphEqualityBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeBoolean, (a, b) => this._polymorphicEq(a, b), \"FlowGraphEqualityBlock\" /* FlowGraphBlockNames.Equality */, config);\n    }\n    _polymorphicEq(a, b) {\n        const aClassName = _getClassNameOf(a);\n        const bClassName = _getClassNameOf(b);\n        if (_areSameVectorClass(aClassName, bClassName) || _areSameMatrixClass(aClassName, bClassName) || _areSameIntegerClass(aClassName, bClassName)) {\n            return a.equals(b);\n        }\n        else {\n            return a === b;\n        }\n    }\n}\nRegisterClass(\"FlowGraphEqualityBlock\" /* FlowGraphBlockNames.Equality */, FlowGraphEqualityBlock);\nfunction _comparisonOperators(a, b, op) {\n    if (isNumeric(a) && isNumeric(b)) {\n        return op(getNumericValue(a), getNumericValue(b));\n    }\n    else {\n        throw new Error(`Cannot compare ${a} and ${b}`);\n    }\n}\n/**\n * Less than block.\n */\nexport class FlowGraphLessThanBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeBoolean, (a, b) => this._polymorphicLessThan(a, b), \"FlowGraphLessThanBlock\" /* FlowGraphBlockNames.LessThan */, config);\n    }\n    _polymorphicLessThan(a, b) {\n        return _comparisonOperators(a, b, (a, b) => a < b);\n    }\n}\nRegisterClass(\"FlowGraphLessThanBlock\" /* FlowGraphBlockNames.LessThan */, FlowGraphLessThanBlock);\n/**\n * Less than or equal block.\n */\nexport class FlowGraphLessThanOrEqualBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeBoolean, (a, b) => this._polymorphicLessThanOrEqual(a, b), \"FlowGraphLessThanOrEqualBlock\" /* FlowGraphBlockNames.LessThanOrEqual */, config);\n    }\n    _polymorphicLessThanOrEqual(a, b) {\n        return _comparisonOperators(a, b, (a, b) => a <= b);\n    }\n}\nRegisterClass(\"FlowGraphLessThanOrEqualBlock\" /* FlowGraphBlockNames.LessThanOrEqual */, FlowGraphLessThanOrEqualBlock);\n/**\n * Greater than block.\n */\nexport class FlowGraphGreaterThanBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeBoolean, (a, b) => this._polymorphicGreaterThan(a, b), \"FlowGraphGreaterThanBlock\" /* FlowGraphBlockNames.GreaterThan */, config);\n    }\n    _polymorphicGreaterThan(a, b) {\n        return _comparisonOperators(a, b, (a, b) => a > b);\n    }\n}\nRegisterClass(\"FlowGraphGreaterThanBlock\" /* FlowGraphBlockNames.GreaterThan */, FlowGraphGreaterThanBlock);\n/**\n * Greater than or equal block.\n */\nexport class FlowGraphGreaterThanOrEqualBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeBoolean, (a, b) => this._polymorphicGreaterThanOrEqual(a, b), \"FlowGraphGreaterThanOrEqualBlock\" /* FlowGraphBlockNames.GreaterThanOrEqual */, config);\n    }\n    _polymorphicGreaterThanOrEqual(a, b) {\n        return _comparisonOperators(a, b, (a, b) => a >= b);\n    }\n}\nRegisterClass(\"FlowGraphGreaterThanOrEqualBlock\" /* FlowGraphBlockNames.GreaterThanOrEqual */, FlowGraphGreaterThanOrEqualBlock);\n/**\n * Is NaN block.\n */\nexport class FlowGraphIsNanBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeBoolean, (a) => this._polymorphicIsNan(a), \"FlowGraphIsNaNBlock\" /* FlowGraphBlockNames.IsNaN */, config);\n    }\n    _polymorphicIsNan(a) {\n        if (isNumeric(a)) {\n            return isNaN(getNumericValue(a));\n        }\n        else {\n            throw new Error(`Cannot get NaN of ${a}`);\n        }\n    }\n}\nRegisterClass(\"FlowGraphIsNaNBlock\" /* FlowGraphBlockNames.IsNaN */, FlowGraphIsNanBlock);\n/**\n * Is Inf block.\n */\nexport class FlowGraphIsInfinityBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeBoolean, (a) => this._polymorphicIsInf(a), \"FlowGraphIsInfBlock\" /* FlowGraphBlockNames.IsInfinity */, config);\n    }\n    _polymorphicIsInf(a) {\n        if (isNumeric(a)) {\n            return !isFinite(getNumericValue(a));\n        }\n        else {\n            throw new Error(`Cannot get isInf of ${a}`);\n        }\n    }\n}\nRegisterClass(\"FlowGraphIsInfBlock\" /* FlowGraphBlockNames.IsInfinity */, FlowGraphIsInfinityBlock);\n/**\n * Convert degrees to radians block.\n */\nexport class FlowGraphDegToRadBlock extends FlowGraphUnaryOperationBlock {\n    /**\n     * Constructs a new instance of the flow graph math block.\n     * @param config - Optional configuration for the flow graph block.\n     */\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicDegToRad(a), \"FlowGraphDegToRadBlock\" /* FlowGraphBlockNames.DegToRad */, config);\n    }\n    _degToRad(a) {\n        return (a * Math.PI) / 180;\n    }\n    _polymorphicDegToRad(a) {\n        return _componentWiseUnaryOperation(a, this._degToRad);\n    }\n}\nRegisterClass(\"FlowGraphDegToRadBlock\" /* FlowGraphBlockNames.DegToRad */, FlowGraphDegToRadBlock);\n/**\n * Convert radians to degrees block.\n */\nexport class FlowGraphRadToDegBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicRadToDeg(a), \"FlowGraphRadToDegBlock\" /* FlowGraphBlockNames.RadToDeg */, config);\n    }\n    _radToDeg(a) {\n        return (a * 180) / Math.PI;\n    }\n    _polymorphicRadToDeg(a) {\n        return _componentWiseUnaryOperation(a, this._radToDeg);\n    }\n}\nRegisterClass(\"FlowGraphRadToDegBlock\" /* FlowGraphBlockNames.RadToDeg */, FlowGraphRadToDegBlock);\n/**\n * Sin block.\n */\nexport class FlowGraphSinBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicSin(a), \"FlowGraphSinBlock\" /* FlowGraphBlockNames.Sin */, config);\n    }\n    _polymorphicSin(a) {\n        return _componentWiseUnaryOperation(a, Math.sin);\n    }\n}\n/**\n * Cos block.\n */\nexport class FlowGraphCosBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicCos(a), \"FlowGraphCosBlock\" /* FlowGraphBlockNames.Cos */, config);\n    }\n    _polymorphicCos(a) {\n        return _componentWiseUnaryOperation(a, Math.cos);\n    }\n}\n/**\n * Tan block.\n */\nexport class FlowGraphTanBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicTan(a), \"FlowGraphTanBlock\" /* FlowGraphBlockNames.Tan */, config);\n    }\n    _polymorphicTan(a) {\n        return _componentWiseUnaryOperation(a, Math.tan);\n    }\n}\n/**\n * Arcsin block.\n */\nexport class FlowGraphAsinBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicAsin(a), \"FlowGraphASinBlock\" /* FlowGraphBlockNames.Asin */, config);\n    }\n    _polymorphicAsin(a) {\n        return _componentWiseUnaryOperation(a, Math.asin);\n    }\n}\nRegisterClass(\"FlowGraphASinBlock\" /* FlowGraphBlockNames.Asin */, FlowGraphAsinBlock);\n/**\n * Arccos block.\n */\nexport class FlowGraphAcosBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicAcos(a), \"FlowGraphACosBlock\" /* FlowGraphBlockNames.Acos */, config);\n    }\n    _polymorphicAcos(a) {\n        return _componentWiseUnaryOperation(a, Math.acos);\n    }\n}\nRegisterClass(\"FlowGraphACosBlock\" /* FlowGraphBlockNames.Acos */, FlowGraphAcosBlock);\n/**\n * Arctan block.\n */\nexport class FlowGraphAtanBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeNumber, (a) => this._polymorphicAtan(a), \"FlowGraphATanBlock\" /* FlowGraphBlockNames.Atan */, config);\n    }\n    _polymorphicAtan(a) {\n        return _componentWiseUnaryOperation(a, Math.atan);\n    }\n}\nRegisterClass(\"FlowGraphATanBlock\" /* FlowGraphBlockNames.Atan */, FlowGraphAtanBlock);\n/**\n * Arctan2 block.\n */\nexport class FlowGraphAtan2Block extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeAny, (a, b) => this._polymorphicAtan2(a, b), \"FlowGraphATan2Block\" /* FlowGraphBlockNames.Atan2 */, config);\n    }\n    _polymorphicAtan2(a, b) {\n        return _componentWiseBinaryOperation(a, b, Math.atan2);\n    }\n}\nRegisterClass(\"FlowGraphATan2Block\" /* FlowGraphBlockNames.Atan2 */, FlowGraphAtan2Block);\n/**\n * Hyperbolic sin block.\n */\nexport class FlowGraphSinhBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicSinh(a), \"FlowGraphSinhBlock\" /* FlowGraphBlockNames.Sinh */, config);\n    }\n    _polymorphicSinh(a) {\n        return _componentWiseUnaryOperation(a, Math.sinh);\n    }\n}\nRegisterClass(\"FlowGraphSinhBlock\" /* FlowGraphBlockNames.Sinh */, FlowGraphSinhBlock);\n/**\n * Hyperbolic cos block.\n */\nexport class FlowGraphCoshBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicCosh(a), \"FlowGraphCoshBlock\" /* FlowGraphBlockNames.Cosh */, config);\n    }\n    _polymorphicCosh(a) {\n        return _componentWiseUnaryOperation(a, Math.cosh);\n    }\n}\nRegisterClass(\"FlowGraphCoshBlock\" /* FlowGraphBlockNames.Cosh */, FlowGraphCoshBlock);\n/**\n * Hyperbolic tan block.\n */\nexport class FlowGraphTanhBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicTanh(a), \"FlowGraphTanhBlock\" /* FlowGraphBlockNames.Tanh */, config);\n    }\n    _polymorphicTanh(a) {\n        return _componentWiseUnaryOperation(a, Math.tanh);\n    }\n}\nRegisterClass(\"FlowGraphTanhBlock\" /* FlowGraphBlockNames.Tanh */, FlowGraphTanhBlock);\n/**\n * Hyperbolic arcsin block.\n */\nexport class FlowGraphAsinhBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicAsinh(a), \"FlowGraphASinhBlock\" /* FlowGraphBlockNames.Asinh */, config);\n    }\n    _polymorphicAsinh(a) {\n        return _componentWiseUnaryOperation(a, Math.asinh);\n    }\n}\nRegisterClass(\"FlowGraphASinhBlock\" /* FlowGraphBlockNames.Asinh */, FlowGraphAsinhBlock);\n/**\n * Hyperbolic arccos block.\n */\nexport class FlowGraphAcoshBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicAcosh(a), \"FlowGraphACoshBlock\" /* FlowGraphBlockNames.Acosh */, config);\n    }\n    _polymorphicAcosh(a) {\n        return _componentWiseUnaryOperation(a, Math.acosh);\n    }\n}\nRegisterClass(\"FlowGraphACoshBlock\" /* FlowGraphBlockNames.Acosh */, FlowGraphAcoshBlock);\n/**\n * Hyperbolic arctan block.\n */\nexport class FlowGraphAtanhBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicAtanh(a), \"FlowGraphATanhBlock\" /* FlowGraphBlockNames.Atanh */, config);\n    }\n    _polymorphicAtanh(a) {\n        return _componentWiseUnaryOperation(a, Math.atanh);\n    }\n}\nRegisterClass(\"FlowGraphATanhBlock\" /* FlowGraphBlockNames.Atanh */, FlowGraphAtanhBlock);\n/**\n * Exponential block.\n */\nexport class FlowGraphExpBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicExp(a), \"FlowGraphExponentialBlock\" /* FlowGraphBlockNames.Exponential */, config);\n    }\n    _polymorphicExp(a) {\n        return _componentWiseUnaryOperation(a, Math.exp);\n    }\n}\nRegisterClass(\"FlowGraphExponentialBlock\" /* FlowGraphBlockNames.Exponential */, FlowGraphExpBlock);\n/**\n * Logarithm block.\n */\nexport class FlowGraphLogBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicLog(a), \"FlowGraphLogBlock\" /* FlowGraphBlockNames.Log */, config);\n    }\n    _polymorphicLog(a) {\n        return _componentWiseUnaryOperation(a, Math.log);\n    }\n}\nRegisterClass(\"FlowGraphLogBlock\" /* FlowGraphBlockNames.Log */, FlowGraphLogBlock);\n/**\n * Base 2 logarithm block.\n */\nexport class FlowGraphLog2Block extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicLog2(a), \"FlowGraphLog2Block\" /* FlowGraphBlockNames.Log2 */, config);\n    }\n    _polymorphicLog2(a) {\n        return _componentWiseUnaryOperation(a, Math.log2);\n    }\n}\nRegisterClass(\"FlowGraphLog2Block\" /* FlowGraphBlockNames.Log2 */, FlowGraphLog2Block);\n/**\n * Base 10 logarithm block.\n */\nexport class FlowGraphLog10Block extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicLog10(a), \"FlowGraphLog10Block\" /* FlowGraphBlockNames.Log10 */, config);\n    }\n    _polymorphicLog10(a) {\n        return _componentWiseUnaryOperation(a, Math.log10);\n    }\n}\nRegisterClass(\"FlowGraphLog10Block\" /* FlowGraphBlockNames.Log10 */, FlowGraphLog10Block);\n/**\n * Square root block.\n */\nexport class FlowGraphSquareRootBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicSqrt(a), \"FlowGraphSquareRootBlock\" /* FlowGraphBlockNames.SquareRoot */, config);\n    }\n    _polymorphicSqrt(a) {\n        return _componentWiseUnaryOperation(a, Math.sqrt);\n    }\n}\nRegisterClass(\"FlowGraphSquareRootBlock\" /* FlowGraphBlockNames.SquareRoot */, FlowGraphSquareRootBlock);\n/**\n * Cube root block.\n */\nexport class FlowGraphCubeRootBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, (a) => this._polymorphicCubeRoot(a), \"FlowGraphCubeRootBlock\" /* FlowGraphBlockNames.CubeRoot */, config);\n    }\n    _polymorphicCubeRoot(a) {\n        return _componentWiseUnaryOperation(a, Math.cbrt);\n    }\n}\nRegisterClass(\"FlowGraphCubeRootBlock\" /* FlowGraphBlockNames.CubeRoot */, FlowGraphCubeRootBlock);\n/**\n * Power block.\n */\nexport class FlowGraphPowerBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeNumber, RichTypeNumber, (a, b) => this._polymorphicPow(a, b), \"FlowGraphPowerBlock\" /* FlowGraphBlockNames.Power */, config);\n    }\n    _polymorphicPow(a, b) {\n        return _componentWiseBinaryOperation(a, b, Math.pow);\n    }\n}\nRegisterClass(\"FlowGraphPowerBlock\" /* FlowGraphBlockNames.Power */, FlowGraphPowerBlock);\n/**\n * Bitwise NOT operation\n */\nexport class FlowGraphBitwiseNotBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), (a) => {\n            if (typeof a === \"boolean\") {\n                return !a;\n            }\n            else if (typeof a === \"number\") {\n                return ~a;\n            }\n            return new FlowGraphInteger(~a.value);\n        }, \"FlowGraphBitwiseNotBlock\" /* FlowGraphBlockNames.BitwiseNot */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseNotBlock\" /* FlowGraphBlockNames.BitwiseNot */, FlowGraphBitwiseNotBlock);\n/**\n * Bitwise AND operation\n */\nexport class FlowGraphBitwiseAndBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), (a, b) => {\n            if (typeof a === \"boolean\" && typeof b === \"boolean\") {\n                return a && b;\n            }\n            else if (typeof a === \"number\" && typeof b === \"number\") {\n                return a & b;\n            }\n            else if (typeof a === \"object\" && typeof b === \"object\") {\n                return new FlowGraphInteger(a.value & b.value);\n            }\n            else {\n                throw new Error(`Cannot perform bitwise AND on ${a} and ${b}`);\n            }\n        }, \"FlowGraphBitwiseAndBlock\" /* FlowGraphBlockNames.BitwiseAnd */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseAndBlock\" /* FlowGraphBlockNames.BitwiseAnd */, FlowGraphBitwiseAndBlock);\n/**\n * Bitwise OR operation\n */\nexport class FlowGraphBitwiseOrBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), (a, b) => {\n            if (typeof a === \"boolean\" && typeof b === \"boolean\") {\n                return a || b;\n            }\n            else if (typeof a === \"number\" && typeof b === \"number\") {\n                return a | b;\n            }\n            else if (typeof a === \"object\" && typeof b === \"object\") {\n                return new FlowGraphInteger(a.value | b.value);\n            }\n            else {\n                throw new Error(`Cannot perform bitwise OR on ${a} and ${b}`);\n            }\n        }, \"FlowGraphBitwiseOrBlock\" /* FlowGraphBlockNames.BitwiseOr */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseOrBlock\" /* FlowGraphBlockNames.BitwiseOr */, FlowGraphBitwiseOrBlock);\n/**\n * Bitwise XOR operation\n */\nexport class FlowGraphBitwiseXorBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), getRichTypeByFlowGraphType(config?.valueType || \"FlowGraphInteger\" /* FlowGraphTypes.Integer */), (a, b) => {\n            if (typeof a === \"boolean\" && typeof b === \"boolean\") {\n                return a !== b;\n            }\n            else if (typeof a === \"number\" && typeof b === \"number\") {\n                return a ^ b;\n            }\n            else if (typeof a === \"object\" && typeof b === \"object\") {\n                return new FlowGraphInteger(a.value ^ b.value);\n            }\n            else {\n                throw new Error(`Cannot perform bitwise XOR on ${a} and ${b}`);\n            }\n        }, \"FlowGraphBitwiseXorBlock\" /* FlowGraphBlockNames.BitwiseXor */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseXorBlock\" /* FlowGraphBlockNames.BitwiseXor */, FlowGraphBitwiseXorBlock);\n/**\n * Bitwise left shift operation\n */\nexport class FlowGraphBitwiseLeftShiftBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, (a, b) => new FlowGraphInteger(a.value << b.value), \"FlowGraphBitwiseLeftShiftBlock\" /* FlowGraphBlockNames.BitwiseLeftShift */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseLeftShiftBlock\" /* FlowGraphBlockNames.BitwiseLeftShift */, FlowGraphBitwiseLeftShiftBlock);\n/**\n * Bitwise right shift operation\n */\nexport class FlowGraphBitwiseRightShiftBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, (a, b) => new FlowGraphInteger(a.value >> b.value), \"FlowGraphBitwiseRightShiftBlock\" /* FlowGraphBlockNames.BitwiseRightShift */, config);\n    }\n}\nRegisterClass(\"FlowGraphBitwiseRightShiftBlock\" /* FlowGraphBlockNames.BitwiseRightShift */, FlowGraphBitwiseRightShiftBlock);\n/**\n * Count leading zeros operation\n */\nexport class FlowGraphLeadingZerosBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, (a) => new FlowGraphInteger(Math.clz32(a.value)), \"FlowGraphLeadingZerosBlock\" /* FlowGraphBlockNames.LeadingZeros */, config);\n    }\n}\nRegisterClass(\"FlowGraphLeadingZerosBlock\" /* FlowGraphBlockNames.LeadingZeros */, FlowGraphLeadingZerosBlock);\n/**\n * Count trailing zeros operation\n */\nexport class FlowGraphTrailingZerosBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, (a) => new FlowGraphInteger(a.value ? 31 - Math.clz32(a.value & -a.value) : 32), \"FlowGraphTrailingZerosBlock\" /* FlowGraphBlockNames.TrailingZeros */, config);\n    }\n}\nRegisterClass(\"FlowGraphTrailingZerosBlock\" /* FlowGraphBlockNames.TrailingZeros */, FlowGraphTrailingZerosBlock);\n/**\n * Given a number (which is converted to a 32-bit integer), return the\n * number of bits set to one on that number.\n * @param n the number to run the op on\n * @returns the number of bits set to one on that number\n */\nfunction _countOnes(n) {\n    let result = 0;\n    while (n) {\n        // This zeroes out all bits except for the least significant one.\n        // So if the bit is set, it will be 1, otherwise it will be 0.\n        result += n & 1;\n        // This shifts n's bits to the right by one\n        n >>= 1;\n    }\n    return result;\n}\n/**\n * Count one bits operation\n */\nexport class FlowGraphOneBitsCounterBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeFlowGraphInteger, (a) => new FlowGraphInteger(_countOnes(a.value)), \"FlowGraphOneBitsCounterBlock\" /* FlowGraphBlockNames.OneBitsCounter */, config);\n    }\n}\nRegisterClass(\"FlowGraphOneBitsCounterBlock\" /* FlowGraphBlockNames.OneBitsCounter */, FlowGraphOneBitsCounterBlock);\n//# sourceMappingURL=flowGraphMathBlocks.js.map"], "names": ["FlowGraphConstantOperationBlock", "FlowGraphCachedOperationBlock", "richType", "_operation", "_className", "config", "context", "FlowGraphAddBlock", "FlowGraphBinaryOperationBlock", "getRichTypeByFlowGraphType", "a", "b", "aClassName", "_getClassNameOf", "bClassName", "_areSameVectorClass", "_areSameMatrixClass", "_areSameIntegerClass", "RegisterClass", "FlowGraphSubtractBlock", "FlowGraphMultiplyBlock", "aClone", "aM", "i", "FlowGraphMatrix2D", "FlowGraphMatrix3D", "Matrix", "FlowGraphDivideBlock", "FlowGraphRandomBlock", "RichTypeNumber", "seed", "x", "min", "max", "FlowGraphEBlock", "FlowGraphPiBlock", "FlowGraphInfBlock", "FlowGraphNaNBlock", "_componentWiseUnaryOperation", "op", "FlowGraphInteger", "Vector2", "Vector3", "Vector4", "Quaternion", "FlowGraphAbsBlock", "FlowGraphUnaryOperationBlock", "FlowGraphSignBlock", "FlowGraphTruncBlock", "FlowGraphFloorBlock", "FlowGraphCeilBlock", "RichTypeAny", "FlowGraphRoundBlock", "FlowGraphFractionBlock", "FlowGraphNegationBlock", "_componentWiseBinaryOperation", "v", "FlowGraphModuloBlock", "FlowGraphMinBlock", "FlowGraphMaxBlock", "_clamp", "c", "_componentWiseTernaryOperation", "FlowGraphClampBlock", "FlowGraphTernaryOperationBlock", "_saturate", "FlowGraphSaturateBlock", "_interpolate", "FlowGraphMathInterpolationBlock", "FlowGraphEqualityBlock", "RichTypeBoolean", "_comparisonOperators", "isNumeric", "getNumericValue", "FlowGraphLessThanBlock", "FlowGraphLessThanOrEqualBlock", "FlowGraphGreaterThanBlock", "FlowGraphGreaterThanOrEqualBlock", "FlowGraphIsNanBlock", "FlowGraphIsInfinityBlock", "FlowGraphDegToRadBlock", "FlowGraphRadToDegBlock", "FlowGraphSinBlock", "FlowGraphCosBlock", "FlowGraphTanBlock", "FlowGraphAsinBlock", "FlowGraphAcosBlock", "FlowGraphAtanBlock", "FlowGraphAtan2Block", "FlowGraphSinhBlock", "FlowGraphCoshBlock", "FlowGraphTanhBlock", "FlowGraphAsinhBlock", "FlowGraphAcoshBlock", "FlowGraphAtanhBlock", "FlowGraphExpBlock", "FlowGraphLogBlock", "FlowGraphLog2Block", "FlowGraphLog10Block", "FlowGraphSquareRootBlock", "FlowGraphCubeRootBlock", "FlowGraphPowerBlock", "FlowGraphBitwiseNotBlock", "FlowGraphBitwiseAndBlock", "FlowGraphBitwiseOrBlock", "FlowGraphBitwiseXorBlock", "FlowGraphBitwiseLeftShiftBlock", "RichTypeFlowGraphInteger", "FlowGraphBitwiseRightShiftBlock", "FlowGraphLeadingZerosBlock", "FlowGraphTrailingZerosBlock", "_countOnes", "n", "result", "FlowGraphOneBitsCounterBlock"], "mappings": "omBAMO,MAAMA,UAAwCC,CAA8B,CAC/E,YAAYC,EAAUC,EAAYC,EAAYC,EAAQ,CAClD,MAAMH,EAAUG,CAAM,EACtB,KAAK,WAAaF,EAClB,KAAK,WAAaC,CACrB,CAMD,aAAaE,EAAS,CAClB,OAAO,KAAK,WAAWA,CAAO,CACjC,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CACL,CCdO,MAAMC,UAA0BC,CAA8B,CAKjE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAG,CAACK,EAAGC,IAAM,KAAK,gBAAgBD,EAAGC,CAAC,EAAG,oBAAmDN,CAAM,CACtO,CACD,gBAAgBK,EAAGC,EAAG,CAClB,MAAMC,EAAaC,EAAgBH,CAAC,EAC9BI,EAAaD,EAAgBF,CAAC,EACpC,OAAII,EAAoBH,EAAYE,CAAU,GAAKE,EAAoBJ,EAAYE,CAAU,GAAKG,EAAqBL,EAAYE,CAAU,GAIpIF,IAAe,cAAgDE,IAAe,aAF5EJ,EAAE,IAAIC,CAAC,EAOPD,EAAIC,CAElB,CACL,CACAO,EAAc,oBAAmDX,CAAiB,EAI3E,MAAMY,UAA+BX,CAA8B,CAKtE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAG,CAACK,EAAGC,IAAM,KAAK,qBAAqBD,EAAGC,CAAC,EAAG,yBAA6DN,CAAM,CACrP,CACD,qBAAqBK,EAAGC,EAAG,CACvB,MAAMC,EAAaC,EAAgBH,CAAC,EAC9BI,EAAaD,EAAgBF,CAAC,EACpC,OAAII,EAAoBH,EAAYE,CAAU,GAAKG,EAAqBL,EAAYE,CAAU,GAAKE,EAAoBJ,EAAYE,CAAU,GAGpIF,IAAe,cAAgDE,IAAe,aAF5EJ,EAAE,SAASC,CAAC,EAOZD,EAAIC,CAElB,CACL,CACAO,EAAc,yBAA6DC,CAAsB,EAK1F,MAAMC,UAA+BZ,CAA8B,CACtE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAG,CAACK,EAAGC,IAAM,KAAK,qBAAqBD,EAAGC,CAAC,EAAG,yBAA6DN,CAAM,CACrP,CACD,qBAAqBK,EAAGC,EAAG,CACvB,MAAMC,EAAaC,EAAgBH,CAAC,EAC9BI,EAAaD,EAAgBF,CAAC,EACpC,GAAII,EAAoBH,EAAYE,CAAU,GAAKG,EAAqBL,EAAYE,CAAU,EAC1F,OAAOJ,EAAE,SAASC,CAAC,EAElB,GAAIC,IAAe,cAAgDE,IAAe,aAA8C,CAEjI,MAAMO,EAASX,EAAE,QACjB,OAAAW,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACPU,CACV,SACQL,EAAoBJ,EAAYE,CAAU,EAC/C,GAAI,KAAK,QAAQ,sBAAuB,CAGpC,MAAMQ,EAAKZ,EAAE,EACb,QAASa,EAAI,EAAGA,EAAID,EAAG,OAAQC,IAC3BD,EAAGC,CAAC,GAAKZ,EAAE,EAAEY,CAAC,EAElB,OAAIX,IAAe,WACR,IAAIY,EAAkBF,CAAE,EAE1BV,IAAe,WACb,IAAIa,EAAkBH,CAAE,EAGxBI,EAAO,UAAUJ,CAAE,CAEjC,KAEG,QAAAZ,EAAIA,EACJC,EAAIA,EACGA,EAAE,SAASD,CAAC,MAIvB,QAAOA,EAAIC,CAElB,CACL,CACAO,EAAc,yBAA6DE,CAAsB,EAI1F,MAAMO,UAA6BnB,CAA8B,CAKpE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAGI,EAA2BJ,GAAQ,IAAI,EAAG,CAACK,EAAGC,IAAM,KAAK,mBAAmBD,EAAGC,CAAC,EAAG,uBAAyDN,CAAM,CAC/O,CACD,mBAAmBK,EAAGC,EAAG,CACrB,MAAMC,EAAaC,EAAgBH,CAAC,EAC9BI,EAAaD,EAAgBF,CAAC,EACpC,GAAII,EAAoBH,EAAYE,CAAU,GAAKG,EAAqBL,EAAYE,CAAU,EAE1F,OAAOJ,EAAE,OAAOC,CAAC,EAEhB,GAAIC,IAAe,cAAgDE,IAAe,aAA8C,CAEjI,MAAMO,EAASX,EAAE,QACjB,OAAAW,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACdU,EAAO,GAAKV,EAAE,EACPU,CACV,SACQL,EAAoBJ,EAAYE,CAAU,EAC/C,GAAI,KAAK,QAAQ,sBAAuB,CAEpC,MAAMQ,EAAKZ,EAAE,EACb,QAASa,EAAI,EAAGA,EAAID,EAAG,OAAQC,IAC3BD,EAAGC,CAAC,GAAKZ,EAAE,EAAEY,CAAC,EAElB,OAAIX,IAAe,WACR,IAAIY,EAAkBF,CAAE,EAE1BV,IAAe,WACb,IAAIa,EAAkBH,CAAE,EAGxBI,EAAO,UAAUJ,CAAE,CAEjC,KAEG,QAAAZ,EAAIA,EACJC,EAAIA,EACGD,EAAE,OAAOC,CAAC,MAIrB,QAAOD,EAAIC,CAElB,CACL,CACAO,EAAc,uBAAyDS,CAAoB,EAMpF,MAAMC,UAA6B5B,CAAgC,CAKtE,YAAYK,EAAQ,CAChB,MAAMwB,EAAiBvB,GAAY,KAAK,QAAQA,CAAO,EAAG,uBAAyDD,CAAM,EACzH,KAAK,IAAM,KAAK,kBAAkB,MAAOwB,EAAgBxB,GAAQ,KAAO,CAAC,EACzE,KAAK,IAAM,KAAK,kBAAkB,MAAOwB,EAAgBxB,GAAQ,KAAO,CAAC,EACrEA,GAAQ,OACR,KAAK,MAAQA,EAAO,KAE3B,CACD,QAAQyB,EAAO,KAAK,MAAO,CACvB,OAAOA,IAAS,MACnB,CACD,iBAAkB,CACd,GAAI,KAAK,QAAQ,KAAK,KAAK,EAAG,CAE1B,MAAMC,EAAI,KAAK,IAAI,KAAK,OAAO,EAAI,IACnC,OAAOA,EAAI,KAAK,MAAMA,CAAC,CAC1B,CACD,OAAO,KAAK,QACf,CACD,QAAQzB,EAAS,CACb,MAAM0B,EAAM,KAAK,IAAI,SAAS1B,CAAO,EAC/B2B,EAAM,KAAK,IAAI,SAAS3B,CAAO,EACrC,OAAO,KAAK,gBAAiB,GAAI2B,EAAMD,GAAOA,CACjD,CACL,CACAd,EAAc,uBAAyDU,CAAoB,EAIpF,MAAMM,UAAwBlC,CAAgC,CACjE,YAAYK,EAAQ,CAChB,MAAMwB,EAAgB,IAAM,KAAK,EAAG,kBAA+CxB,CAAM,CAC5F,CACL,CACAa,EAAc,kBAA+CgB,CAAe,EAIrE,MAAMC,UAAyBnC,CAAgC,CAClE,YAAYK,EAAQ,CAChB,MAAMwB,EAAgB,IAAM,KAAK,GAAI,mBAAiDxB,CAAM,CAC/F,CACL,CACAa,EAAc,mBAAiDiB,CAAgB,EAIxE,MAAMC,UAA0BpC,CAAgC,CACnE,YAAYK,EAAQ,CAChB,MAAMwB,EAAgB,IAAM,OAAO,kBAAmB,oBAAmDxB,CAAM,CAClH,CACL,CACAa,EAAc,oBAAmDkB,CAAiB,EAI3E,MAAMC,UAA0BrC,CAAgC,CACnE,YAAYK,EAAQ,CAChB,MAAMwB,EAAgB,IAAM,OAAO,IAAK,oBAAmDxB,CAAM,CACpG,CACL,CACAa,EAAc,oBAAmDmB,CAAiB,EAClF,SAASC,EAA6B5B,EAAG6B,EAAI,CAEzC,OADmB1B,EAAgBH,CAAC,EAClB,CACd,IAAK,mBACD,OAAAA,EAAIA,EACG,IAAI8B,EAAiBD,EAAG7B,EAAE,KAAK,CAAC,EAC3C,IAAK,UACD,OAAAA,EAAIA,EACG,IAAI+B,EAAQF,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,CAAC,EACvC,IAAK,UACD,OAAAA,EAAIA,EACG,IAAIgC,EAAQH,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,CAAC,EAChD,IAAK,UACD,OAAAA,EAAIA,EACG,IAAIiC,EAAQJ,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,CAAC,EACzD,IAAK,aACD,OAAAA,EAAIA,EACG,IAAIkC,EAAWL,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,EAAG6B,EAAG7B,EAAE,CAAC,CAAC,EAC5D,IAAK,SACD,OAAAA,EAAIA,EACGgB,EAAO,UAAUhB,EAAE,EAAE,IAAI6B,CAAE,CAAC,EACvC,IAAK,WACD,OAAA7B,EAAIA,EAEG,IAAIc,EAAkBd,EAAE,EAAE,IAAI6B,CAAE,CAAC,EAC5C,IAAK,WACD,OAAA7B,EAAIA,EACG,IAAIe,EAAkBf,EAAE,EAAE,IAAI6B,CAAE,CAAC,EAC5C,QACI,OAAA7B,EAAIA,EACG6B,EAAG7B,CAAC,CAClB,CACL,CAIO,MAAMmC,UAA0BC,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,oBAAmDL,CAAM,CAClI,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CACAQ,EAAc,oBAAmD2B,CAAiB,EAI3E,MAAME,UAA2BD,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CACrI,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqD6B,CAAkB,EAI9E,MAAMC,UAA4BF,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACxI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuD8B,CAAmB,EAIjF,MAAMC,UAA4BH,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACxI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuD+B,CAAmB,EAIjF,MAAMC,UAA2BJ,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,oBAAoBA,CAAC,EAAG,qBAAqDL,CAAM,CAClI,CACD,oBAAoBK,EAAG,CACnB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDgC,CAAkB,EAI9E,MAAME,UAA4BN,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CAClI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAIA,GAAOA,EAAI,GAAK,KAAK,QAAQ,sBAAwB,CAAC,KAAK,MAAM,CAACA,CAAC,EAAI,KAAK,MAAMA,CAAC,CAAE,CAChI,CACL,CACAQ,EAAc,sBAAuDkC,CAAmB,EAIjF,MAAMC,UAA+BP,CAA6B,CACrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,qBAAqBA,CAAC,EAAG,sBAA0DL,CAAM,CACxI,CACD,qBAAqBK,EAAG,CACpB,OAAO4B,EAA6B5B,EAAIA,GAAMA,EAAI,KAAK,MAAMA,CAAC,CAAC,CAClE,CACL,CACAQ,EAAc,sBAA0DmC,CAAsB,EAIvF,MAAMC,UAA+BR,CAA6B,CAKrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,gBAAgBA,CAAC,EAAG,yBAA6DL,CAAM,CACtI,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAIA,GAAM,CAACA,CAAC,CACnD,CACL,CACAQ,EAAc,yBAA6DoC,CAAsB,EACjG,SAASC,EAA8B7C,EAAGC,EAAG4B,EAAI,CAE7C,OADmB1B,EAAgBH,CAAC,EAClB,CACd,IAAK,mBACD,OAAAA,EAAIA,EACJC,EAAIA,EACG,IAAI6B,EAAiBD,EAAG7B,EAAE,MAAOC,EAAE,KAAK,CAAC,EACpD,IAAK,UACD,OAAAD,EAAIA,EACJC,EAAIA,EACG,IAAI8B,EAAQF,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,CAAC,EACjD,IAAK,UACD,OAAAD,EAAIA,EACJC,EAAIA,EACG,IAAI+B,EAAQH,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,CAAC,EAC/D,IAAK,UACD,OAAAD,EAAIA,EACJC,EAAIA,EACG,IAAIgC,EAAQJ,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,CAAC,EAC7E,IAAK,aACD,OAAAD,EAAIA,EACJC,EAAIA,EACG,IAAIiC,EAAWL,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,EAAG4B,EAAG7B,EAAE,EAAGC,EAAE,CAAC,CAAC,EAChF,IAAK,SACD,OAAAD,EAAIA,EACGgB,EAAO,UAAUhB,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,CAAC,CAAC,CAAC,EAC5D,IAAK,WACD,OAAAb,EAAIA,EACG,IAAIc,EAAkBd,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,CAAC,CAAC,CAAC,EACjE,IAAK,WACD,OAAAb,EAAIA,EACG,IAAIe,EAAkBf,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,CAAC,CAAC,CAAC,EACjE,QACI,OAAOgB,EAAG7B,EAAGC,CAAC,CACrB,CACL,CAIO,MAAM8C,UAA6BjD,CAA8B,CACpE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,IAAM,KAAK,sBAAsBD,EAAGC,CAAC,EAAG,uBAAyDN,CAAM,CAC3J,CACD,sBAAsBK,EAAGC,EAAG,CACxB,OAAO4C,EAA8B7C,EAAGC,EAAG,CAACD,EAAGC,IAAMD,EAAIC,CAAC,CAC7D,CACL,CACAO,EAAc,uBAAyDuC,CAAoB,EAIpF,MAAMC,WAA0BlD,CAA8B,CACjE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,IAAM,KAAK,gBAAgBD,EAAGC,CAAC,EAAG,oBAAmDN,CAAM,CAC/I,CACD,gBAAgBK,EAAGC,EAAG,CAClB,OAAO4C,EAA8B7C,EAAGC,EAAG,KAAK,GAAG,CACtD,CACL,CACAO,EAAc,oBAAmDwC,EAAiB,EAI3E,MAAMC,WAA0BnD,CAA8B,CACjE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,IAAM,KAAK,gBAAgBD,EAAGC,CAAC,EAAG,oBAAmDN,CAAM,CAC/I,CACD,gBAAgBK,EAAGC,EAAG,CAClB,OAAO4C,EAA8B7C,EAAGC,EAAG,KAAK,GAAG,CACtD,CACL,CACAO,EAAc,oBAAmDyC,EAAiB,EAClF,SAASC,GAAOlD,EAAGC,EAAGkD,EAAG,CACrB,OAAO,KAAK,IAAI,KAAK,IAAInD,EAAG,KAAK,IAAIC,EAAGkD,CAAC,CAAC,EAAG,KAAK,IAAIlD,EAAGkD,CAAC,CAAC,CAC/D,CACA,SAASC,EAA+BpD,EAAGC,EAAGkD,EAAGtB,EAAI,CAEjD,OADmB1B,EAAgBH,CAAC,EAClB,CACd,IAAK,mBACD,OAAAA,EAAIA,EACJC,EAAIA,EACJkD,EAAIA,EACG,IAAIrB,EAAiBD,EAAG7B,EAAE,MAAOC,EAAE,MAAOkD,EAAE,KAAK,CAAC,EAC7D,IAAK,UACD,OAAAnD,EAAIA,EACJC,EAAIA,EACJkD,EAAIA,EACG,IAAIpB,EAAQF,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,CAAC,EAC3D,IAAK,UACD,OAAAnD,EAAIA,EACJC,EAAIA,EACJkD,EAAIA,EACG,IAAInB,EAAQH,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,CAAC,EAC9E,IAAK,UACD,OAAAnD,EAAIA,EACJC,EAAIA,EACJkD,EAAIA,EACG,IAAIlB,EAAQJ,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,CAAC,EACjG,IAAK,aACD,OAAAnD,EAAIA,EACJC,EAAIA,EACJkD,EAAIA,EACG,IAAIjB,EAAWL,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,EAAGtB,EAAG7B,EAAE,EAAGC,EAAE,EAAGkD,EAAE,CAAC,CAAC,EACpG,IAAK,SACD,OAAOnC,EAAO,UAAUhB,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,EAAGsC,EAAE,EAAEtC,CAAC,CAAC,CAAC,CAAC,EACpE,IAAK,WACD,OAAO,IAAIC,EAAkBd,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,EAAGsC,EAAE,EAAEtC,CAAC,CAAC,CAAC,CAAC,EACzE,IAAK,WACD,OAAO,IAAIE,EAAkBf,EAAE,EAAE,IAAI,CAAC8C,EAAGjC,IAAMgB,EAAGiB,EAAG7C,EAAE,EAAEY,CAAC,EAAGsC,EAAE,EAAEtC,CAAC,CAAC,CAAC,CAAC,EACzE,QACI,OAAOgB,EAAG7B,EAAGC,EAAGkD,CAAC,CACxB,CACL,CAIO,MAAME,WAA4BC,CAA+B,CACpE,YAAY3D,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,EAAGkD,IAAM,KAAK,kBAAkBnD,EAAGC,EAAGkD,CAAC,EAAG,sBAAuDxD,CAAM,CACxK,CACD,kBAAkBK,EAAGC,EAAGkD,EAAG,CACvB,OAAOC,EAA+BpD,EAAGC,EAAGkD,EAAGD,EAAM,CACxD,CACL,CACA1C,EAAc,sBAAuD6C,EAAmB,EACxF,SAASE,GAAUvD,EAAG,CAClB,OAAO,KAAK,IAAI,KAAK,IAAIA,EAAG,CAAC,EAAG,CAAC,CACrC,CAIO,MAAMwD,WAA+BpB,CAA6B,CACrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,qBAAqBA,CAAC,EAAG,yBAA6DL,CAAM,CAC3I,CACD,qBAAqBK,EAAG,CACpB,OAAO4B,EAA6B5B,EAAGuD,EAAS,CACnD,CACL,CACA/C,EAAc,yBAA6DgD,EAAsB,EACjG,SAASC,GAAazD,EAAGC,EAAGkD,EAAG,CAC3B,OAAQ,EAAIA,GAAKnD,EAAImD,EAAIlD,CAC7B,CAIO,MAAMyD,WAAwCJ,CAA+B,CAChF,YAAY3D,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,EAAGkD,IAAM,KAAK,wBAAwBnD,EAAGC,EAAGkD,CAAC,EAAG,kCAA+ExD,CAAM,CACtM,CACD,wBAAwBK,EAAGC,EAAGkD,EAAG,CAC7B,OAAOC,EAA+BpD,EAAGC,EAAGkD,EAAGM,EAAY,CAC9D,CACL,CACAjD,EAAc,kCAA+EkD,EAA+B,EAIrH,MAAMC,WAA+B7D,CAA8B,CACtE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAamB,EAAiB,CAAC5D,EAAGC,IAAM,KAAK,eAAeD,EAAGC,CAAC,EAAG,yBAA6DN,CAAM,CAC5J,CACD,eAAeK,EAAGC,EAAG,CACjB,MAAMC,EAAaC,EAAgBH,CAAC,EAC9BI,EAAaD,EAAgBF,CAAC,EACpC,OAAII,EAAoBH,EAAYE,CAAU,GAAKE,EAAoBJ,EAAYE,CAAU,GAAKG,EAAqBL,EAAYE,CAAU,EAClIJ,EAAE,OAAOC,CAAC,EAGVD,IAAMC,CAEpB,CACL,CACAO,EAAc,yBAA6DmD,EAAsB,EACjG,SAASE,EAAqB7D,EAAGC,EAAG4B,EAAI,CACpC,GAAIiC,EAAU9D,CAAC,GAAK8D,EAAU7D,CAAC,EAC3B,OAAO4B,EAAGkC,EAAgB/D,CAAC,EAAG+D,EAAgB9D,CAAC,CAAC,EAGhD,MAAM,IAAI,MAAM,kBAAkBD,CAAC,QAAQC,CAAC,EAAE,CAEtD,CAIO,MAAM+D,WAA+BlE,CAA8B,CACtE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAamB,EAAiB,CAAC5D,EAAGC,IAAM,KAAK,qBAAqBD,EAAGC,CAAC,EAAG,yBAA6DN,CAAM,CAClK,CACD,qBAAqBK,EAAGC,EAAG,CACvB,OAAO4D,EAAqB7D,EAAGC,EAAG,CAACD,EAAGC,IAAMD,EAAIC,CAAC,CACpD,CACL,CACAO,EAAc,yBAA6DwD,EAAsB,EAI1F,MAAMC,WAAsCnE,CAA8B,CAC7E,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAamB,EAAiB,CAAC5D,EAAGC,IAAM,KAAK,4BAA4BD,EAAGC,CAAC,EAAG,gCAA2EN,CAAM,CACvL,CACD,4BAA4BK,EAAGC,EAAG,CAC9B,OAAO4D,EAAqB7D,EAAGC,EAAG,CAACD,EAAGC,IAAMD,GAAKC,CAAC,CACrD,CACL,CACAO,EAAc,gCAA2EyD,EAA6B,EAI/G,MAAMC,WAAkCpE,CAA8B,CACzE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAamB,EAAiB,CAAC5D,EAAGC,IAAM,KAAK,wBAAwBD,EAAGC,CAAC,EAAG,4BAAmEN,CAAM,CAC3K,CACD,wBAAwBK,EAAGC,EAAG,CAC1B,OAAO4D,EAAqB7D,EAAGC,EAAG,CAACD,EAAGC,IAAMD,EAAIC,CAAC,CACpD,CACL,CACAO,EAAc,4BAAmE0D,EAAyB,EAInG,MAAMC,WAAyCrE,CAA8B,CAChF,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAamB,EAAiB,CAAC5D,EAAGC,IAAM,KAAK,+BAA+BD,EAAGC,CAAC,EAAG,mCAAiFN,CAAM,CAChM,CACD,+BAA+BK,EAAGC,EAAG,CACjC,OAAO4D,EAAqB7D,EAAGC,EAAG,CAACD,EAAGC,IAAMD,GAAKC,CAAC,CACrD,CACL,CACAO,EAAc,mCAAiF2D,EAAgC,EAIxH,MAAMC,WAA4BhC,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAamB,EAAkB5D,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACtI,CACD,kBAAkBK,EAAG,CACjB,GAAI8D,EAAU9D,CAAC,EACX,OAAO,MAAM+D,EAAgB/D,CAAC,CAAC,EAG/B,MAAM,IAAI,MAAM,qBAAqBA,CAAC,EAAE,CAE/C,CACL,CACAQ,EAAc,sBAAuD4D,EAAmB,EAIjF,MAAMC,WAAiCjC,CAA6B,CACvE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAamB,EAAkB5D,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAA4DL,CAAM,CAC3I,CACD,kBAAkBK,EAAG,CACjB,GAAI8D,EAAU9D,CAAC,EACX,MAAO,CAAC,SAAS+D,EAAgB/D,CAAC,CAAC,EAGnC,MAAM,IAAI,MAAM,uBAAuBA,CAAC,EAAE,CAEjD,CACL,CACAQ,EAAc,sBAA4D6D,EAAwB,EAI3F,MAAMC,WAA+BlC,CAA6B,CAKrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,qBAAqBA,CAAC,EAAG,yBAA6DL,CAAM,CAC3I,CACD,UAAUK,EAAG,CACT,OAAQA,EAAI,KAAK,GAAM,GAC1B,CACD,qBAAqBA,EAAG,CACpB,OAAO4B,EAA6B5B,EAAG,KAAK,SAAS,CACxD,CACL,CACAQ,EAAc,yBAA6D8D,EAAsB,EAI1F,MAAMC,WAA+BnC,CAA6B,CACrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,qBAAqBA,CAAC,EAAG,yBAA6DL,CAAM,CAC3I,CACD,UAAUK,EAAG,CACT,OAAQA,EAAI,IAAO,KAAK,EAC3B,CACD,qBAAqBA,EAAG,CACpB,OAAO4B,EAA6B5B,EAAG,KAAK,SAAS,CACxD,CACL,CACAQ,EAAc,yBAA6D+D,EAAsB,EAI1F,MAAMC,WAA0BpC,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,oBAAmDL,CAAM,CAClI,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CAIO,MAAMyE,WAA0BrC,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,oBAAmDL,CAAM,CAClI,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CAIO,MAAM0E,WAA0BtC,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,oBAAmDL,CAAM,CAClI,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CAIO,MAAM2E,WAA2BvC,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CACrI,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDmE,EAAkB,EAI9E,MAAMC,WAA2BxC,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CACrI,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDoE,EAAkB,EAI9E,MAAMC,WAA2BzC,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAMwB,EAAgBA,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CACrI,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDqE,EAAkB,EAI9E,MAAMC,WAA4BhF,CAA8B,CACnE,YAAYH,EAAQ,CAChB,MAAM8C,EAAaA,EAAaA,EAAa,CAACzC,EAAGC,IAAM,KAAK,kBAAkBD,EAAGC,CAAC,EAAG,sBAAuDN,CAAM,CACrJ,CACD,kBAAkBK,EAAGC,EAAG,CACpB,OAAO4C,EAA8B7C,EAAGC,EAAG,KAAK,KAAK,CACxD,CACL,CACAO,EAAc,sBAAuDsE,EAAmB,EAIjF,MAAMC,WAA2B3C,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CAC/H,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDuE,EAAkB,EAI9E,MAAMC,WAA2B5C,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CAC/H,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDwE,EAAkB,EAI9E,MAAMC,WAA2B7C,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAaA,EAAczC,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CAC/H,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqDyE,EAAkB,EAI9E,MAAMC,WAA4B9C,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACrI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuD0E,EAAmB,EAIjF,MAAMC,WAA4B/C,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACrI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuD2E,EAAmB,EAIjF,MAAMC,WAA4BhD,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACrI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuD4E,EAAmB,EAIjF,MAAMC,WAA0BjD,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,4BAAmEL,CAAM,CAC/I,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CACAQ,EAAc,4BAAmE6E,EAAiB,EAI3F,MAAMC,WAA0BlD,CAA6B,CAChE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,gBAAgBA,CAAC,EAAG,oBAAmDL,CAAM,CAC/H,CACD,gBAAgBK,EAAG,CACf,OAAO4B,EAA6B5B,EAAG,KAAK,GAAG,CAClD,CACL,CACAQ,EAAc,oBAAmD8E,EAAiB,EAI3E,MAAMC,WAA2BnD,CAA6B,CACjE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,qBAAqDL,CAAM,CAClI,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,qBAAqD+E,EAAkB,EAI9E,MAAMC,WAA4BpD,CAA6B,CAClE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,kBAAkBA,CAAC,EAAG,sBAAuDL,CAAM,CACrI,CACD,kBAAkBK,EAAG,CACjB,OAAO4B,EAA6B5B,EAAG,KAAK,KAAK,CACpD,CACL,CACAQ,EAAc,sBAAuDgF,EAAmB,EAIjF,MAAMC,WAAiCrD,CAA6B,CACvE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,iBAAiBA,CAAC,EAAG,2BAAiEL,CAAM,CAC9I,CACD,iBAAiBK,EAAG,CAChB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,2BAAiEiF,EAAwB,EAIhG,MAAMC,WAA+BtD,CAA6B,CACrE,YAAYzC,EAAQ,CAChB,MAAM8C,EAAatB,EAAiBnB,GAAM,KAAK,qBAAqBA,CAAC,EAAG,yBAA6DL,CAAM,CAC9I,CACD,qBAAqBK,EAAG,CACpB,OAAO4B,EAA6B5B,EAAG,KAAK,IAAI,CACnD,CACL,CACAQ,EAAc,yBAA6DkF,EAAsB,EAI1F,MAAMC,WAA4B7F,CAA8B,CACnE,YAAYH,EAAQ,CAChB,MAAM8C,EAAatB,EAAgBA,EAAgB,CAACnB,EAAGC,IAAM,KAAK,gBAAgBD,EAAGC,CAAC,EAAG,sBAAuDN,CAAM,CACzJ,CACD,gBAAgBK,EAAGC,EAAG,CAClB,OAAO4C,EAA8B7C,EAAGC,EAAG,KAAK,GAAG,CACtD,CACL,CACAO,EAAc,sBAAuDmF,EAAmB,EAIjF,MAAMC,WAAiCxD,CAA6B,CACvE,YAAYzC,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAGK,GACnM,OAAOA,GAAM,UACN,CAACA,EAEH,OAAOA,GAAM,SACX,CAACA,EAEL,IAAI8B,EAAiB,CAAC9B,EAAE,KAAK,EACrC,2BAAiEL,CAAM,CAC7E,CACL,CACAa,EAAc,2BAAiEoF,EAAwB,EAIhG,MAAMC,WAAiC/F,CAA8B,CACxE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,oBAAkD,CAACK,EAAGC,IAAM,CAClT,GAAI,OAAOD,GAAM,WAAa,OAAOC,GAAM,UACvC,OAAOD,GAAKC,EAEX,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAOD,EAAIC,EAEV,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAO,IAAI6B,EAAiB9B,EAAE,MAAQC,EAAE,KAAK,EAG7C,MAAM,IAAI,MAAM,iCAAiCD,CAAC,QAAQC,CAAC,EAAE,CAE7E,EAAW,2BAAiEN,CAAM,CAC7E,CACL,CACAa,EAAc,2BAAiEqF,EAAwB,EAIhG,MAAMC,WAAgChG,CAA8B,CACvE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,oBAAkD,CAACK,EAAGC,IAAM,CAClT,GAAI,OAAOD,GAAM,WAAa,OAAOC,GAAM,UACvC,OAAOD,GAAKC,EAEX,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAOD,EAAIC,EAEV,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAO,IAAI6B,EAAiB9B,EAAE,MAAQC,EAAE,KAAK,EAG7C,MAAM,IAAI,MAAM,gCAAgCD,CAAC,QAAQC,CAAC,EAAE,CAE5E,EAAW,0BAA+DN,CAAM,CAC3E,CACL,CACAa,EAAc,0BAA+DsF,EAAuB,EAI7F,MAAMC,WAAiCjG,CAA8B,CACxE,YAAYH,EAAQ,CAChB,MAAMI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,kBAAgD,EAAEI,EAA2BJ,GAAQ,WAAa,oBAAkD,CAACK,EAAGC,IAAM,CAClT,GAAI,OAAOD,GAAM,WAAa,OAAOC,GAAM,UACvC,OAAOD,IAAMC,EAEZ,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAOD,EAAIC,EAEV,GAAI,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAO,IAAI6B,EAAiB9B,EAAE,MAAQC,EAAE,KAAK,EAG7C,MAAM,IAAI,MAAM,iCAAiCD,CAAC,QAAQC,CAAC,EAAE,CAE7E,EAAW,2BAAiEN,CAAM,CAC7E,CACL,CACAa,EAAc,2BAAiEuF,EAAwB,EAIhG,MAAMC,WAAuClG,CAA8B,CAC9E,YAAYH,EAAQ,CAChB,MAAMsG,EAA0BA,EAA0BA,EAA0B,CAACjG,EAAGC,IAAM,IAAI6B,EAAiB9B,EAAE,OAASC,EAAE,KAAK,EAAG,iCAA6EN,CAAM,CAC9N,CACL,CACAa,EAAc,iCAA6EwF,EAA8B,EAIlH,MAAME,WAAwCpG,CAA8B,CAC/E,YAAYH,EAAQ,CAChB,MAAMsG,EAA0BA,EAA0BA,EAA0B,CAACjG,EAAGC,IAAM,IAAI6B,EAAiB9B,EAAE,OAASC,EAAE,KAAK,EAAG,kCAA+EN,CAAM,CAChO,CACL,CACAa,EAAc,kCAA+E0F,EAA+B,EAIrH,MAAMC,WAAmC/D,CAA6B,CACzE,YAAYzC,EAAQ,CAChB,MAAMsG,EAA0BA,EAA2BjG,GAAM,IAAI8B,EAAiB,KAAK,MAAM9B,EAAE,KAAK,CAAC,EAAG,6BAAqEL,CAAM,CAC1L,CACL,CACAa,EAAc,6BAAqE2F,EAA0B,EAItG,MAAMC,WAAoChE,CAA6B,CAC1E,YAAYzC,EAAQ,CAChB,MAAMsG,EAA0BA,EAA2BjG,GAAM,IAAI8B,EAAiB9B,EAAE,MAAQ,GAAK,KAAK,MAAMA,EAAE,MAAQ,CAACA,EAAE,KAAK,EAAI,EAAE,EAAG,8BAAuEL,CAAM,CAC3N,CACL,CACAa,EAAc,8BAAuE4F,EAA2B,EAOhH,SAASC,GAAWC,EAAG,CACnB,IAAIC,EAAS,EACb,KAAOD,GAGHC,GAAUD,EAAI,EAEdA,IAAM,EAEV,OAAOC,CACX,CAIO,MAAMC,WAAqCpE,CAA6B,CAC3E,YAAYzC,EAAQ,CAChB,MAAMsG,EAA0BA,EAA2BjG,GAAM,IAAI8B,EAAiBuE,GAAWrG,EAAE,KAAK,CAAC,EAAG,+BAAyEL,CAAM,CAC9L,CACL,CACAa,EAAc,+BAAyEgG,EAA4B", "x_google_ignoreList": [0, 1]}