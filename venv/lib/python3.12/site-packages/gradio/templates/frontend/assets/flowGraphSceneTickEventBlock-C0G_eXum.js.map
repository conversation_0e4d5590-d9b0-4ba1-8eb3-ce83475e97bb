{"version": 3, "file": "flowGraphSceneTickEventBlock-C0G_eXum.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneTickEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { RichTypeNumber } from \"../../flowGraphRichTypes.js\";\n/**\n * Block that triggers on scene tick (before each render).\n */\nexport class FlowGraphSceneTickEventBlock extends FlowGraphEventBlock {\n    constructor() {\n        super();\n        this.type = \"SceneBeforeRender\" /* FlowGraphEventType.SceneBeforeRender */;\n        this.timeSinceStart = this.registerDataOutput(\"timeSinceStart\", RichTypeNumber);\n        this.deltaTime = this.registerDataOutput(\"deltaTime\", RichTypeNumber);\n    }\n    /**\n     * @internal\n     */\n    _preparePendingTasks(_context) {\n        // no-op\n    }\n    /**\n     * @internal\n     */\n    _executeEvent(context, payload) {\n        this.timeSinceStart.setValue(payload.timeSinceStart, context);\n        this.deltaTime.setValue(payload.deltaTime, context);\n        this._execute(context);\n        return true;\n    }\n    /**\n     * @internal\n     */\n    _cancelPendingTasks(_context) {\n        // no-op\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphSceneTickEventBlock\" /* FlowGraphBlockNames.SceneTickEvent */;\n    }\n}\nRegisterClass(\"FlowGraphSceneTickEventBlock\" /* FlowGraphBlockNames.SceneTickEvent */, FlowGraphSceneTickEventBlock);\n//# sourceMappingURL=flowGraphSceneTickEventBlock.js.map"], "names": ["FlowGraphSceneTickEventBlock", "FlowGraphEventBlock", "RichTypeNumber", "_context", "context", "payload", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAAqCC,CAAoB,CAClE,aAAc,CACV,QACA,KAAK,KAAO,oBACZ,KAAK,eAAiB,KAAK,mBAAmB,iBAAkBC,CAAc,EAC9E,KAAK,UAAY,KAAK,mBAAmB,YAAaA,CAAc,CACvE,CAID,qBAAqBC,EAAU,CAE9B,CAID,cAAcC,EAASC,EAAS,CAC5B,YAAK,eAAe,SAASA,EAAQ,eAAgBD,CAAO,EAC5D,KAAK,UAAU,SAASC,EAAQ,UAAWD,CAAO,EAClD,KAAK,SAASA,CAAO,EACd,EACV,CAID,oBAAoBD,EAAU,CAE7B,CAID,cAAe,CACX,MAAO,8BACV,CACL,CACAG,EAAc,+BAAyEN,CAA4B", "x_google_ignoreList": [0]}