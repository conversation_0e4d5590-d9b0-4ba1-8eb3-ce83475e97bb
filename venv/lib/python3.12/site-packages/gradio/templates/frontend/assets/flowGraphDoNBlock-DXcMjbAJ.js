import{F as e,j as i}from"./declarationMapper-r-RREw_K.js";import{b as r}from"./KHR_interactivity-DVSiPm30.js";import{R as a}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class u extends r{constructor(t={}){super(t),this.config=t,this.config.startIndex=t.startIndex??new e(0),this.reset=this._registerSignalInput("reset"),this.maxExecutions=this.registerDataInput("maxExecutions",i),this.executionCount=this.registerDataOutput("executionCount",i,new e(0))}_execute(t,o){if(o===this.reset)this.executionCount.setValue(this.config.startIndex,t);else{const s=this.executionCount.getValue(t);s.value<this.maxExecutions.getValue(t).value&&(this.executionCount.setValue(new e(s.value+1),t),this.out._activateSignal(t))}}getClassName(){return"FlowGraphDoNBlock"}}a("FlowGraphDoNBlock",u);export{u as FlowGraphDoNBlock};
//# sourceMappingURL=flowGraphDoNBlock-DXcMjbAJ.js.map
