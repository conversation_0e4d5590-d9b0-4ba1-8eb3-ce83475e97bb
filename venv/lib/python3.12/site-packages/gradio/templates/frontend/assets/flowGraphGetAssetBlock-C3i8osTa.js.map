{"version": 3, "file": "flowGraphGetAssetBlock-C3i8osTa.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetAssetBlock.js"], "sourcesContent": ["import { GetFlowGraphAssetWithType } from \"../../flowGraphAssetsContext.js\";\nimport { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../CustomTypes/flowGraphInteger.js\";\nimport { getNumericValue } from \"../../utils.js\";\n/**\n * A block that will deliver an asset as an output, based on its type and place in the assets index.\n *\n * The assets are loaded from the assetsContext defined in the context running this block. The assetsContext is a class extending AbstractClass,\n * meaning it can be a Scene, an AssetsContainers, and any other class that extends AbstractClass.\n */\nexport class FlowGraphGetAssetBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this.type = this.registerDataInput(\"type\", RichTypeAny, config.type);\n        this.value = this.registerDataOutput(\"value\", RichTypeAny);\n        this.index = this.registerDataInput(\"index\", RichTypeAny, new FlowGraphInteger(getNumericValue(config.index ?? -1)));\n    }\n    _updateOutputs(context) {\n        const type = this.type.getValue(context);\n        const index = this.index.getValue(context);\n        // get the asset from the context\n        const asset = GetFlowGraphAssetWithType(context.assetsContext, type, getNumericValue(index), this.config.useIndexAsUniqueId);\n        this.value.setValue(asset, context);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphGetAssetBlock\" /* FlowGraphBlockNames.GetAsset */;\n    }\n}\nRegisterClass(\"FlowGraphGetAssetBlock\" /* FlowGraphBlockNames.GetAsset */, FlowGraphGetAssetBlock);\n//# sourceMappingURL=flowGraphGetAssetBlock.js.map"], "names": ["FlowGraphGetAssetBlock", "FlowGraphBlock", "config", "RichTypeAny", "FlowGraphInteger", "getNumericValue", "context", "type", "index", "asset", "GetFlowGraphAssetWithType", "RegisterClass"], "mappings": "qQAYO,MAAMA,UAA+BC,CAAe,CACvD,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAaD,EAAO,IAAI,EACnE,KAAK,MAAQ,KAAK,mBAAmB,QAASC,CAAW,EACzD,KAAK,MAAQ,KAAK,kBAAkB,QAASA,EAAa,IAAIC,EAAiBC,EAAgBH,EAAO,OAAS,EAAE,CAAC,CAAC,CACtH,CACD,eAAeI,EAAS,CACpB,MAAMC,EAAO,KAAK,KAAK,SAASD,CAAO,EACjCE,EAAQ,KAAK,MAAM,SAASF,CAAO,EAEnCG,EAAQC,EAA0BJ,EAAQ,cAAeC,EAAMF,EAAgBG,CAAK,EAAG,KAAK,OAAO,kBAAkB,EAC3H,KAAK,MAAM,SAASC,EAAOH,CAAO,CACrC,CAKD,cAAe,CACX,MAAO,wBACV,CACL,CACAK,EAAc,yBAA6DX,CAAsB", "x_google_ignoreList": [0]}