{"version": 3, "file": "mainUVVaryingDeclaration-Co7HufPC.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/mainUVVaryingDeclaration.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"mainUVVaryingDeclaration\";\nconst shader = `#ifdef MAINUV{X}\nvarying vMainUV{X}: vec2f;\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const mainUVVaryingDeclarationWGSL = { name, shader };\n//# sourceMappingURL=mainUVVaryingDeclaration.js.map"], "names": ["name", "shader", "ShaderStore"], "mappings": "wCAEA,MAAMA,EAAO,2BACPC,EAAS;AAAA;AAAA;AAAA,EAKVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC", "x_google_ignoreList": [0]}