{"version": 3, "file": "flowGraphSequenceBlock-Cr5WTdwC.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSequenceBlock.js"], "sourcesContent": ["import { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphExecutionBlock } from \"../../../flowGraphExecutionBlock.js\";\n/**\n * A block that executes its output flows in sequence.\n */\nexport class FlowGraphSequenceBlock extends FlowGraphExecutionBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * The output flows.\n         */\n        this.executionSignals = [];\n        this.setNumberOfOutputSignals(this.config.outputSignalCount);\n    }\n    _execute(context) {\n        for (let i = 0; i < this.executionSignals.length; i++) {\n            this.executionSignals[i]._activateSignal(context);\n        }\n    }\n    /**\n     * Sets the block's output flows. Would usually be passed from the constructor but can be changed afterwards.\n     * @param outputSignalCount the number of output flows\n     */\n    setNumberOfOutputSignals(outputSignalCount = 1) {\n        // check the size of the outFlow Array, see if it is not larger than needed\n        while (this.executionSignals.length > outputSignalCount) {\n            const flow = this.executionSignals.pop();\n            if (flow) {\n                flow.disconnectFromAll();\n                this._unregisterSignalOutput(flow.name);\n            }\n        }\n        while (this.executionSignals.length < outputSignalCount) {\n            this.executionSignals.push(this._registerSignalOutput(`out_${this.executionSignals.length}`));\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphSequenceBlock\" /* FlowGraphBlockNames.Sequence */;\n    }\n}\nRegisterClass(\"FlowGraphSequenceBlock\" /* FlowGraphBlockNames.Sequence */, FlowGraphSequenceBlock);\n//# sourceMappingURL=flowGraphSequenceBlock.js.map"], "names": ["FlowGraphSequenceBlock", "FlowGraphExecutionBlock", "config", "context", "i", "outputSignalCount", "flow", "RegisterClass"], "mappings": "oOAKO,MAAMA,UAA+BC,CAAwB,CAChE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAId,KAAK,iBAAmB,GACxB,KAAK,yBAAyB,KAAK,OAAO,iBAAiB,CAC9D,CACD,SAASC,EAAS,CACd,QAASC,EAAI,EAAGA,EAAI,KAAK,iBAAiB,OAAQA,IAC9C,KAAK,iBAAiBA,CAAC,EAAE,gBAAgBD,CAAO,CAEvD,CAKD,yBAAyBE,EAAoB,EAAG,CAE5C,KAAO,KAAK,iBAAiB,OAASA,GAAmB,CACrD,MAAMC,EAAO,KAAK,iBAAiB,IAAG,EAClCA,IACAA,EAAK,kBAAiB,EACtB,KAAK,wBAAwBA,EAAK,IAAI,EAE7C,CACD,KAAO,KAAK,iBAAiB,OAASD,GAClC,KAAK,iBAAiB,KAAK,KAAK,sBAAsB,OAAO,KAAK,iBAAiB,MAAM,EAAE,CAAC,CAEnG,CAID,cAAe,CACX,MAAO,wBACV,CACL,CACAE,EAAc,yBAA6DP,CAAsB", "x_google_ignoreList": [0]}