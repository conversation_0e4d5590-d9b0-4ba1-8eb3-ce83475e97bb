import{F as a,h as s}from"./KHR_interactivity-DVSiPm30.js";import{k as e}from"./declarationMapper-r-RREw_K.js";import{R as r}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class u extends a{constructor(t){super(t),this.config=t,this.output=this.registerDataOutput("output",e(t.value))}_updateOutputs(t){this.output.setValue(this.config.value,t)}getClassName(){return"FlowGraphConstantBlock"}serialize(t={},o=s){super.serialize(t),o("value",this.config.value,t.config)}}r("FlowGraphConstantBlock",u);export{u as FlowGraphConstantBlock};
//# sourceMappingURL=flowGraphConstantBlock-D1Zk6TpZ.js.map
