{"version": 3, "file": "hdrFiltering.vertex-d7uF6ZlR.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/hdrFiltering.vertex.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"hdrFilteringVertexShader\";\nconst shader = `attribute vec2 position;varying vec3 direction;uniform vec3 up;uniform vec3 right;uniform vec3 front;\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\nmat3 view=mat3(up,right,front);direction=view*vec3(position,1.0);gl_Position=vec4(position,0.0,1.0);\n#define CUSTOM_VERTEX_MAIN_END\n}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const hdrFilteringVertexShader = { name, shader };\n//# sourceMappingURL=hdrFiltering.vertex.js.map"], "names": ["name", "shader", "ShaderStore", "hdrFilteringVertexShader"], "mappings": "+FAEA,MAAMA,EAAO,2BACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAA2B,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}