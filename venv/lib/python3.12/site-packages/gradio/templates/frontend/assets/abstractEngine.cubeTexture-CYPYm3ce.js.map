{"version": 3, "file": "abstractEngine.cubeTexture-CYPYm3ce.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Engines/AbstractEngine/abstractEngine.cubeTexture.js"], "sourcesContent": ["import { InternalTexture } from \"../../Materials/Textures/internalTexture.js\";\nimport { Logger } from \"../../Misc/logger.js\";\nimport { LoadImage } from \"../../Misc/fileTools.js\";\nimport { RandomGUID } from \"../../Misc/guid.js\";\nimport { AbstractEngine } from \"../abstractEngine.js\";\nimport { _GetCompatibleTextureLoader } from \"../../Materials/Textures/Loaders/textureLoaderManager.js\";\nimport { GetExtensionFromUrl } from \"../../Misc/urlTools.js\";\nAbstractEngine.prototype._partialLoadFile = function (url, index, loadedFiles, onfinish, onErrorCallBack = null) {\n    const onload = (data) => {\n        loadedFiles[index] = data;\n        loadedFiles._internalCount++;\n        if (loadedFiles._internalCount === 6) {\n            onfinish(loadedFiles);\n        }\n    };\n    const onerror = (request, exception) => {\n        if (onErrorCallBack && request) {\n            onErrorCallBack(request.status + \" \" + request.statusText, exception);\n        }\n    };\n    this._loadFile(url, onload, undefined, undefined, true, onerror);\n};\nAbstractEngine.prototype._cascadeLoadFiles = function (scene, onfinish, files, onError = null) {\n    const loadedFiles = [];\n    loadedFiles._internalCount = 0;\n    for (let index = 0; index < 6; index++) {\n        this._partialLoadFile(files[index], index, loadedFiles, onfinish, onError);\n    }\n};\nAbstractEngine.prototype._cascadeLoadImgs = function (scene, texture, onfinish, files, onError = null, mimeType) {\n    const loadedImages = [];\n    loadedImages._internalCount = 0;\n    for (let index = 0; index < 6; index++) {\n        this._partialLoadImg(files[index], index, loadedImages, scene, texture, onfinish, onError, mimeType);\n    }\n};\nAbstractEngine.prototype._partialLoadImg = function (url, index, loadedImages, scene, texture, onfinish, onErrorCallBack = null, mimeType) {\n    const tokenPendingData = RandomGUID();\n    const onload = (img) => {\n        loadedImages[index] = img;\n        loadedImages._internalCount++;\n        if (scene) {\n            scene.removePendingData(tokenPendingData);\n        }\n        if (loadedImages._internalCount === 6 && onfinish) {\n            onfinish(texture, loadedImages);\n        }\n    };\n    const onerror = (message, exception) => {\n        if (scene) {\n            scene.removePendingData(tokenPendingData);\n        }\n        if (onErrorCallBack) {\n            onErrorCallBack(message, exception);\n        }\n    };\n    LoadImage(url, onload, onerror, scene ? scene.offlineProvider : null, mimeType);\n    if (scene) {\n        scene.addPendingData(tokenPendingData);\n    }\n};\nAbstractEngine.prototype.createCubeTextureBase = function (rootUrl, scene, files, noMipmap, onLoad = null, onError = null, format, forcedExtension = null, createPolynomials = false, lodScale = 0, lodOffset = 0, fallback = null, beforeLoadCubeDataCallback = null, imageHandler = null, useSRGBBuffer = false, buffer = null) {\n    const texture = fallback ? fallback : new InternalTexture(this, 7 /* InternalTextureSource.Cube */);\n    texture.isCube = true;\n    texture.url = rootUrl;\n    texture.generateMipMaps = !noMipmap;\n    texture._lodGenerationScale = lodScale;\n    texture._lodGenerationOffset = lodOffset;\n    texture._useSRGBBuffer = !!useSRGBBuffer && this._caps.supportSRGBBuffers && (this.version > 1 || this.isWebGPU || !!noMipmap);\n    if (texture !== fallback) {\n        texture.label = rootUrl.substring(0, 60); // default label, can be overriden by the caller\n    }\n    if (!this._doNotHandleContextLost) {\n        texture._extension = forcedExtension;\n        texture._files = files;\n        texture._buffer = buffer;\n    }\n    const originalRootUrl = rootUrl;\n    if (this._transformTextureUrl && !fallback) {\n        rootUrl = this._transformTextureUrl(rootUrl);\n    }\n    const extension = forcedExtension ?? GetExtensionFromUrl(rootUrl);\n    const loaderPromise = _GetCompatibleTextureLoader(extension);\n    const onInternalError = (request, exception) => {\n        if (rootUrl === originalRootUrl) {\n            if (onError && request) {\n                onError(request.status + \" \" + request.statusText, exception);\n            }\n        }\n        else {\n            // fall back to the original url if the transformed url fails to load\n            Logger.Warn(`Failed to load ${rootUrl}, falling back to the ${originalRootUrl}`);\n            this.createCubeTextureBase(originalRootUrl, scene, files, !!noMipmap, onLoad, onError, format, forcedExtension, createPolynomials, lodScale, lodOffset, texture, beforeLoadCubeDataCallback, imageHandler, useSRGBBuffer, buffer);\n        }\n    };\n    if (loaderPromise) {\n        loaderPromise.then((loader) => {\n            const onloaddata = (data) => {\n                if (beforeLoadCubeDataCallback) {\n                    beforeLoadCubeDataCallback(texture, data);\n                }\n                loader.loadCubeData(data, texture, createPolynomials, onLoad, onError);\n            };\n            if (buffer) {\n                onloaddata(buffer);\n            }\n            else if (files && files.length === 6) {\n                if (loader.supportCascades) {\n                    this._cascadeLoadFiles(scene, (images) => onloaddata(images.map((image) => new Uint8Array(image))), files, onError);\n                }\n                else {\n                    if (onError) {\n                        onError(\"Textures type does not support cascades.\");\n                    }\n                    else {\n                        Logger.Warn(\"Texture loader does not support cascades.\");\n                    }\n                }\n            }\n            else {\n                this._loadFile(rootUrl, (data) => onloaddata(new Uint8Array(data)), undefined, undefined, true, onInternalError);\n            }\n        });\n    }\n    else {\n        if (!files || files.length === 0) {\n            throw new Error(\"Cannot load cubemap because files were not defined, or the correct loader was not found.\");\n        }\n        this._cascadeLoadImgs(scene, texture, (texture, imgs) => {\n            if (imageHandler) {\n                imageHandler(texture, imgs);\n            }\n        }, files, onError);\n    }\n    this._internalTexturesCache.push(texture);\n    return texture;\n};\n//# sourceMappingURL=abstractEngine.cubeTexture.js.map"], "names": ["AbstractEngine", "url", "index", "loadedFiles", "onfinish", "onErrorCallBack", "onload", "data", "onerror", "request", "exception", "scene", "files", "onError", "texture", "mimeType", "loadedImages", "tokenPendingData", "RandomGUID", "LoadImage", "img", "message", "rootUrl", "noMipmap", "onLoad", "format", "forcedExtension", "createPolynomials", "lodScale", "lodOffset", "fallback", "beforeLoadCubeDataCallback", "imageHandler", "useSRGBBuffer", "buffer", "InternalTexture", "originalRootUrl", "extension", "GetExtensionFromUrl", "loaderPromise", "_GetCompatibleTextureLoader", "onInternalError", "<PERSON><PERSON>", "loader", "onloaddata", "images", "image", "imgs"], "mappings": "kFAOAA,EAAe,UAAU,iBAAmB,SAAUC,EAAKC,EAAOC,EAAaC,EAAUC,EAAkB,KAAM,CAC7G,MAAMC,EAAUC,GAAS,CACrBJ,EAAYD,CAAK,EAAIK,EACrBJ,EAAY,iBACRA,EAAY,iBAAmB,GAC/BC,EAASD,CAAW,CAEhC,EACUK,EAAU,CAACC,EAASC,IAAc,CAChCL,GAAmBI,GACnBJ,EAAgBI,EAAQ,OAAS,IAAMA,EAAQ,WAAYC,CAAS,CAEhF,EACI,KAAK,UAAUT,EAAKK,EAAQ,OAAW,OAAW,GAAME,CAAO,CACnE,EACAR,EAAe,UAAU,kBAAoB,SAAUW,EAAOP,EAAUQ,EAAOC,EAAU,KAAM,CAC3F,MAAMV,EAAc,CAAA,EACpBA,EAAY,eAAiB,EAC7B,QAASD,EAAQ,EAAGA,EAAQ,EAAGA,IAC3B,KAAK,iBAAiBU,EAAMV,CAAK,EAAGA,EAAOC,EAAaC,EAAUS,CAAO,CAEjF,EACAb,EAAe,UAAU,iBAAmB,SAAUW,EAAOG,EAASV,EAAUQ,EAAOC,EAAU,KAAME,EAAU,CAC7G,MAAMC,EAAe,CAAA,EACrBA,EAAa,eAAiB,EAC9B,QAASd,EAAQ,EAAGA,EAAQ,EAAGA,IAC3B,KAAK,gBAAgBU,EAAMV,CAAK,EAAGA,EAAOc,EAAcL,EAAOG,EAASV,EAAUS,EAASE,CAAQ,CAE3G,EACAf,EAAe,UAAU,gBAAkB,SAAUC,EAAKC,EAAOc,EAAcL,EAAOG,EAASV,EAAUC,EAAkB,KAAMU,EAAU,CACvI,MAAME,EAAmBC,IAmBzBC,EAAUlB,EAlBMmB,GAAQ,CACpBJ,EAAad,CAAK,EAAIkB,EACtBJ,EAAa,iBACTL,GACAA,EAAM,kBAAkBM,CAAgB,EAExCD,EAAa,iBAAmB,GAAKZ,GACrCA,EAASU,EAASE,CAAY,CAE1C,EACoB,CAACK,EAASX,IAAc,CAChCC,GACAA,EAAM,kBAAkBM,CAAgB,EAExCZ,GACAA,EAAgBgB,EAASX,CAAS,CAE9C,EACoCC,EAAQA,EAAM,gBAAkB,KAAMI,CAAQ,EAC1EJ,GACAA,EAAM,eAAeM,CAAgB,CAE7C,EACAjB,EAAe,UAAU,sBAAwB,SAAUsB,EAASX,EAAOC,EAAOW,EAAUC,EAAS,KAAMX,EAAU,KAAMY,EAAQC,EAAkB,KAAMC,EAAoB,GAAOC,EAAW,EAAGC,EAAY,EAAGC,EAAW,KAAMC,EAA6B,KAAMC,EAAe,KAAMC,EAAgB,GAAOC,EAAS,KAAM,CAC9T,MAAMpB,EAAUgB,GAAsB,IAAIK,EAAgB,KAAM,GAChErB,EAAQ,OAAS,GACjBA,EAAQ,IAAMQ,EACdR,EAAQ,gBAAkB,CAACS,EAC3BT,EAAQ,oBAAsBc,EAC9Bd,EAAQ,qBAAuBe,EAC/Bf,EAAQ,eAAiB,CAAC,CAACmB,GAAiB,KAAK,MAAM,qBAAuB,KAAK,QAAU,GAAK,KAAK,UAAY,CAAC,CAACV,GACjHT,IAAYgB,IACZhB,EAAQ,MAAQQ,EAAQ,UAAU,EAAG,EAAE,GAEtC,KAAK,0BACNR,EAAQ,WAAaY,EACrBZ,EAAQ,OAASF,EACjBE,EAAQ,QAAUoB,GAEtB,MAAME,EAAkBd,EACpB,KAAK,sBAAwB,CAACQ,IAC9BR,EAAU,KAAK,qBAAqBA,CAAO,GAE/C,MAAMe,EAAYX,GAAmBY,EAAoBhB,CAAO,EAC1DiB,EAAgBC,EAA4BH,CAAS,EACrDI,EAAkB,CAAChC,EAASC,IAAc,CACxCY,IAAYc,EACRvB,GAAWJ,GACXI,EAAQJ,EAAQ,OAAS,IAAMA,EAAQ,WAAYC,CAAS,GAKhEgC,EAAO,KAAK,kBAAkBpB,CAAO,yBAAyBc,CAAe,EAAE,EAC/E,KAAK,sBAAsBA,EAAiBzB,EAAOC,EAAO,CAAC,CAACW,EAAUC,EAAQX,EAASY,EAAQC,EAAiBC,EAAmBC,EAAUC,EAAWf,EAASiB,EAA4BC,EAAcC,EAAeC,CAAM,EAE5O,EACI,GAAIK,EACAA,EAAc,KAAMI,GAAW,CAC3B,MAAMC,EAAcrC,GAAS,CACrBwB,GACAA,EAA2BjB,EAASP,CAAI,EAE5CoC,EAAO,aAAapC,EAAMO,EAASa,EAAmBH,EAAQX,CAAO,CACrF,EACgBqB,EACAU,EAAWV,CAAM,EAEZtB,GAASA,EAAM,SAAW,EAC3B+B,EAAO,gBACP,KAAK,kBAAkBhC,EAAQkC,GAAWD,EAAWC,EAAO,IAAKC,GAAU,IAAI,WAAWA,CAAK,CAAC,CAAC,EAAGlC,EAAOC,CAAO,EAG9GA,EACAA,EAAQ,0CAA0C,EAGlD6B,EAAO,KAAK,2CAA2C,EAK/D,KAAK,UAAUpB,EAAUf,GAASqC,EAAW,IAAI,WAAWrC,CAAI,CAAC,EAAG,OAAW,OAAW,GAAMkC,CAAe,CAE/H,CAAS,MAEA,CACD,GAAI,CAAC7B,GAASA,EAAM,SAAW,EAC3B,MAAM,IAAI,MAAM,0FAA0F,EAE9G,KAAK,iBAAiBD,EAAOG,EAAS,CAACA,EAASiC,IAAS,CACjDf,GACAA,EAAalB,EAASiC,CAAI,CAE1C,EAAWnC,EAAOC,CAAO,CACpB,CACD,YAAK,uBAAuB,KAAKC,CAAO,EACjCA,CACX", "x_google_ignoreList": [0]}