{"version": 3, "file": "ddsTextureLoader-BTnRDFO5.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/ddsTextureLoader.js"], "sourcesContent": ["import { SphericalPolynomial } from \"../../../Maths/sphericalPolynomial.js\";\nimport { DDSTools } from \"../../../Misc/dds.js\";\n/**\n * Implementation of the DDS Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _DDSTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = true;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * @param imgs contains the cube maps\n     * @param texture defines the BabylonJS internal texture\n     * @param createPolynomials will be true if polynomials have been requested\n     * @param onLoad defines the callback to trigger once the texture is ready\n     */\n    loadCubeData(imgs, texture, createPolynomials, onLoad) {\n        const engine = texture.getEngine();\n        let info;\n        let loadMipmap = false;\n        let maxLevel = 1000;\n        if (Array.isArray(imgs)) {\n            for (let index = 0; index < imgs.length; index++) {\n                const data = imgs[index];\n                info = DDSTools.GetDDSInfo(data);\n                texture.width = info.width;\n                texture.height = info.height;\n                loadMipmap = (info.isRGB || info.isLuminance || info.mipmapCount > 1) && texture.generateMipMaps;\n                engine._unpackFlipY(info.isCompressed);\n                DDSTools.UploadDDSLevels(engine, texture, data, info, loadMipmap, 6, -1, index);\n                if (!info.isFourCC && info.mipmapCount === 1) {\n                    engine.generateMipMapsForCubemap(texture);\n                }\n                else {\n                    maxLevel = info.mipmapCount - 1;\n                }\n            }\n        }\n        else {\n            const data = imgs;\n            info = DDSTools.GetDDSInfo(data);\n            texture.width = info.width;\n            texture.height = info.height;\n            if (createPolynomials) {\n                info.sphericalPolynomial = new SphericalPolynomial();\n            }\n            loadMipmap = (info.isRGB || info.isLuminance || info.mipmapCount > 1) && texture.generateMipMaps;\n            engine._unpackFlipY(info.isCompressed);\n            DDSTools.UploadDDSLevels(engine, texture, data, info, loadMipmap, 6);\n            if (!info.isFourCC && info.mipmapCount === 1) {\n                // Do not unbind as we still need to set the parameters.\n                engine.generateMipMapsForCubemap(texture, false);\n            }\n            else {\n                maxLevel = info.mipmapCount - 1;\n            }\n        }\n        engine._setCubeMapTextureParams(texture, loadMipmap, maxLevel);\n        texture.isReady = true;\n        texture.onLoadedObservable.notifyObservers(texture);\n        texture.onLoadedObservable.clear();\n        if (onLoad) {\n            onLoad({ isDDS: true, width: texture.width, info, data: imgs, texture });\n        }\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    loadData(data, texture, callback) {\n        const info = DDSTools.GetDDSInfo(data);\n        const loadMipmap = (info.isRGB || info.isLuminance || info.mipmapCount > 1) && texture.generateMipMaps && Math.max(info.width, info.height) >> (info.mipmapCount - 1) === 1;\n        callback(info.width, info.height, loadMipmap, info.isFourCC, () => {\n            DDSTools.UploadDDSLevels(texture.getEngine(), texture, data, info, loadMipmap, 1);\n        });\n    }\n}\n//# sourceMappingURL=ddsTextureLoader.js.map"], "names": ["_DDSTextureLoader", "imgs", "texture", "createPolynomials", "onLoad", "engine", "info", "loadMipmap", "maxLevel", "index", "data", "DDSTools", "SphericalPolynomial", "callback"], "mappings": "8LAOO,MAAMA,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAQD,aAAaC,EAAMC,EAASC,EAAmBC,EAAQ,CACnD,MAAMC,EAASH,EAAQ,YACvB,IAAII,EACAC,EAAa,GACbC,EAAW,IACf,GAAI,MAAM,QAAQP,CAAI,EAClB,QAASQ,EAAQ,EAAGA,EAAQR,EAAK,OAAQQ,IAAS,CAC9C,MAAMC,EAAOT,EAAKQ,CAAK,EACvBH,EAAOK,EAAS,WAAWD,CAAI,EAC/BR,EAAQ,MAAQI,EAAK,MACrBJ,EAAQ,OAASI,EAAK,OACtBC,GAAcD,EAAK,OAASA,EAAK,aAAeA,EAAK,YAAc,IAAMJ,EAAQ,gBACjFG,EAAO,aAAaC,EAAK,YAAY,EACrCK,EAAS,gBAAgBN,EAAQH,EAASQ,EAAMJ,EAAMC,EAAY,EAAG,GAAIE,CAAK,EAC1E,CAACH,EAAK,UAAYA,EAAK,cAAgB,EACvCD,EAAO,0BAA0BH,CAAO,EAGxCM,EAAWF,EAAK,YAAc,CAErC,KAEA,CACD,MAAMI,EAAOT,EACbK,EAAOK,EAAS,WAAWD,CAAI,EAC/BR,EAAQ,MAAQI,EAAK,MACrBJ,EAAQ,OAASI,EAAK,OAClBH,IACAG,EAAK,oBAAsB,IAAIM,GAEnCL,GAAcD,EAAK,OAASA,EAAK,aAAeA,EAAK,YAAc,IAAMJ,EAAQ,gBACjFG,EAAO,aAAaC,EAAK,YAAY,EACrCK,EAAS,gBAAgBN,EAAQH,EAASQ,EAAMJ,EAAMC,EAAY,CAAC,EAC/D,CAACD,EAAK,UAAYA,EAAK,cAAgB,EAEvCD,EAAO,0BAA0BH,EAAS,EAAK,EAG/CM,EAAWF,EAAK,YAAc,CAErC,CACDD,EAAO,yBAAyBH,EAASK,EAAYC,CAAQ,EAC7DN,EAAQ,QAAU,GAClBA,EAAQ,mBAAmB,gBAAgBA,CAAO,EAClDA,EAAQ,mBAAmB,QACvBE,GACAA,EAAO,CAAE,MAAO,GAAM,MAAOF,EAAQ,MAAO,KAAAI,EAAM,KAAML,EAAM,QAAAC,CAAS,CAAA,CAE9E,CAOD,SAASQ,EAAMR,EAASW,EAAU,CAC9B,MAAMP,EAAOK,EAAS,WAAWD,CAAI,EAC/BH,GAAcD,EAAK,OAASA,EAAK,aAAeA,EAAK,YAAc,IAAMJ,EAAQ,iBAAmB,KAAK,IAAII,EAAK,MAAOA,EAAK,MAAM,GAAMA,EAAK,YAAc,IAAO,EAC1KO,EAASP,EAAK,MAAOA,EAAK,OAAQC,EAAYD,EAAK,SAAU,IAAM,CAC/DK,EAAS,gBAAgBT,EAAQ,UAAW,EAAEA,EAASQ,EAAMJ,EAAMC,EAAY,CAAC,CAC5F,CAAS,CACJ,CACL", "x_google_ignoreList": [0]}