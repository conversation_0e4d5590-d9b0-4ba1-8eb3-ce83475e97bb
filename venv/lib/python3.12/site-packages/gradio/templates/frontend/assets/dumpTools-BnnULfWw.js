const __vite__fileDeps=["./index-Cb4A4-Xi.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./pass.fragment-Db2jGnBT.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as h}from"./index-Ccc2t4AG.js";import{aj as I,b as m,ak as u,al as b,am as P}from"./index-Cb4A4-Xi.js";import"./svelte/svelte.js";let l,d=null;async function R(){return d||(d=new Promise((a,s)=>{let n,e=null;const o={preserveDrawingBuffer:!0,depth:!1,stencil:!1,alpha:!0,premultipliedAlpha:!1,antialias:!1,failIfMajorPerformanceCaveat:!1};h(()=>import("./index-Cb4A4-Xi.js").then(i=>i.bX),__vite__mapDeps([0,1,2]),import.meta.url).then(({ThinEngine:i})=>{const f=u.Instances.length;try{n=new OffscreenCanvas(100,100),e=new i(n,!1,o)}catch{f<u.Instances.length&&u.Instances.pop()?.dispose(),n=document.createElement("canvas"),e=new i(n,!1,o)}u.Instances.pop(),u.OnEnginesDisposedObservable.add(r=>{e&&r!==e&&!e.isDisposed&&u.Instances.length===0&&A()}),e.getCaps().parallelShaderCompile=void 0;const p=new b(e);h(()=>import("./pass.fragment-Db2jGnBT.js"),__vite__mapDeps([3,0,1,2]),import.meta.url).then(({passPixelShader:r})=>{if(!e){s("Engine is not defined");return}const t=new P({engine:e,name:r.name,fragmentShader:r.shader,samplerNames:["textureSampler"]});l={canvas:n,engine:e,renderer:p,wrapper:t},a(l)})}).catch(s)})),await d}async function v(a,s,n,e,o="image/png",i,f){const p=await n.readPixels(0,0,a,s),r=new Uint8Array(p.buffer);w(a,s,r,e,o,i,!0,void 0,f)}function E(a,s,n,e="image/png",o,i=!1,f=!1,p){return new Promise(r=>{w(a,s,n,t=>r(t),e,o,i,f,p)})}function w(a,s,n,e,o="image/png",i,f=!1,p=!1,r){R().then(t=>{if(t.engine.setSize(a,s,!0),n instanceof Float32Array){const g=new Uint8Array(n.length);let c=n.length;for(;c--;){const D=n[c];g[c]=Math.round(I(D)*255)}n=g}const _=t.engine.createRawTexture(n,a,s,5,!1,!f,1);t.renderer.setViewport(),t.renderer.applyEffectWrapper(t.wrapper),t.wrapper.effect._bindTexture("textureSampler",_),t.renderer.draw(),p?m.ToBlob(t.canvas,g=>{const c=new FileReader;c.onload=D=>{const y=D.target.result;e&&e(y)},c.readAsArrayBuffer(g)},o,r):m.EncodeScreenshotCanvasData(t.canvas,e,o,i,r),_.dispose()})}function A(){l?(l.wrapper.dispose(),l.renderer.dispose(),l.engine.dispose()):d?.then(a=>{a.wrapper.dispose(),a.renderer.dispose(),a.engine.dispose()}),d=null,l=null}const O={DumpData:w,DumpDataAsync:E,DumpFramebuffer:v,Dispose:A},S=()=>{m.DumpData=w,m.DumpDataAsync=E,m.DumpFramebuffer=v};S();export{A as Dispose,w as DumpData,E as DumpDataAsync,v as DumpFramebuffer,O as DumpTools};
//# sourceMappingURL=dumpTools-BnnULfWw.js.map
