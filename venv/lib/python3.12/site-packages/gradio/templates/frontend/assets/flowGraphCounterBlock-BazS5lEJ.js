import{b as i}from"./declarationMapper-r-RREw_K.js";import{b as s}from"./KHR_interactivity-DVSiPm30.js";import{R as o}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class a extends s{constructor(t){super(t),this.count=this.registerDataOutput("count",i),this.reset=this._registerSignalInput("reset")}_execute(t,r){if(r===this.reset){t._setExecutionVariable(this,"count",0),this.count.setValue(0,t);return}const e=t._getExecutionVariable(this,"count",0)+1;t._setExecutionVariable(this,"count",e),this.count.setValue(e,t),this.out._activateSignal(t)}getClassName(){return"FlowGraphCallCounterBlock"}}o("FlowGraphCallCounterBlock",a);export{a as FlowGraphCallCounterBlock};
//# sourceMappingURL=flowGraphCounterBlock-BazS5lEJ.js.map
