import{c as t}from"./KHR_interactivity-DVSiPm30.js";import{R as r}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./declarationMapper-r-RREw_K.js";import"./objectModelMapping-D3Nr8hfO.js";class o extends t{constructor(){super(...arguments),this.initPriority=-1,this.type="SceneReady"}_executeEvent(e,a){return this._execute(e),!0}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphSceneReadyEventBlock"}}r("FlowGraphSceneReadyEventBlock",o);export{o as FlowGraphSceneReadyEventBlock};
//# sourceMappingURL=flowGraphSceneReadyEventBlock-Cs83muND.js.map
