{"version": 3, "file": "chunk-2O5F6CEG-BJ0hfvv0.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-2O5F6CEG.mjs"], "sourcesContent": ["import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n"], "names": ["getDiagramElement", "__name", "id", "securityLevel", "sandboxElement", "select", "setupViewPortForSVG", "svg", "padding", "cssDiagram", "useMaxWidth", "width", "height", "x", "y", "calculateDimensionsWithPadding", "configureSvgSize", "viewBox", "createViewBox", "log", "bounds"], "mappings": "sGAQG,IAACA,EAAoCC,EAAO,CAACC,EAAIC,IAAkB,CACpE,IAAIC,EACJ,OAAID,IAAkB,YACpBC,EAAiBC,EAAO,KAAOH,CAAE,IAEtBC,IAAkB,UAAYE,EAAOD,EAAe,MAAK,EAAG,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAO,MAAM,GAChG,OAAO,QAAQH,CAAE,IAAI,CAExC,EAAG,mBAAmB,EAGlBI,EAAsCL,EAAO,CAACM,EAAKC,EAASC,EAAYC,IAAgB,CAC1FH,EAAI,KAAK,QAASE,CAAU,EAC5B,KAAM,CAAE,MAAAE,EAAO,OAAAC,EAAQ,EAAAC,EAAG,EAAAC,CAAC,EAAKC,EAA+BR,EAAKC,CAAO,EAC3EQ,EAAiBT,EAAKK,EAAQD,EAAOD,CAAW,EAChD,MAAMO,EAAUC,EAAcL,EAAGC,EAAGH,EAAOC,EAAQJ,CAAO,EAC1DD,EAAI,KAAK,UAAWU,CAAO,EAC3BE,EAAI,MAAM,uBAAuBF,CAAO,kBAAkBT,CAAO,EAAE,CACrE,EAAG,qBAAqB,EACpBO,EAAiDd,EAAO,CAACM,EAAKC,IAAY,CAC5E,MAAMY,EAASb,EAAI,KAAM,GAAE,QAAO,GAAM,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAC,EACzE,MAAO,CACL,MAAOa,EAAO,MAAQZ,EAAU,EAChC,OAAQY,EAAO,OAASZ,EAAU,EAClC,EAAGY,EAAO,EACV,EAAGA,EAAO,CACd,CACA,EAAG,gCAAgC,EAC/BF,EAAgCjB,EAAO,CAACY,EAAGC,EAAGH,EAAOC,EAAQJ,IACxD,GAAGK,EAAIL,CAAO,IAAIM,EAAIN,CAAO,IAAIG,CAAK,IAAIC,CAAM,GACtD,eAAe", "x_google_ignoreList": [0]}