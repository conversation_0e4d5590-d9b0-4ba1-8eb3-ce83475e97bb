{"version": 3, "file": "flowGraphSetPropertyBlock-BwaB_U6k.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetPropertyBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * This block will set a property on a given target asset.\n * The property name can include dots (\".\"), which will be interpreted as a path to the property.\n * The target asset is an input and can be changed at any time.\n * The value of the property is an input and can be changed at any time.\n *\n * For example, with an input of a mesh asset, the property name \"position.x\" will set the x component of the position of the mesh.\n *\n * Note that it is recommended to input the object on which you are working on (i.e. a material) than providing a mesh and then getting the material from it.\n */\nexport class FlowGraphSetPropertyBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this.object = this.registerDataInput(\"object\", RichTypeAny, config.target);\n        this.value = this.registerDataInput(\"value\", RichTypeAny);\n        this.propertyName = this.registerDataInput(\"propertyName\", RichTypeAny, config.propertyName);\n        this.customSetFunction = this.registerDataInput(\"customSetFunction\", RichTypeAny);\n    }\n    _execute(context, _callingSignal) {\n        try {\n            const target = this.object.getValue(context);\n            const value = this.value.getValue(context);\n            const setFunction = this.customSetFunction.getValue(context);\n            if (setFunction) {\n                setFunction(target, this.propertyName.getValue(context), value, context);\n            }\n            else {\n                this._setPropertyValue(target, this.propertyName.getValue(context), value);\n            }\n        }\n        catch (e) {\n            this._reportError(context, e);\n        }\n        this.out._activateSignal(context);\n    }\n    _setPropertyValue(target, propertyName, value) {\n        const path = propertyName.split(\".\");\n        let obj = target;\n        for (let i = 0; i < path.length - 1; i++) {\n            const prop = path[i];\n            if (obj[prop] === undefined) {\n                obj[prop] = {};\n            }\n            obj = obj[prop];\n        }\n        obj[path[path.length - 1]] = value;\n    }\n    getClassName() {\n        return \"FlowGraphSetPropertyBlock\" /* FlowGraphBlockNames.SetProperty */;\n    }\n}\nRegisterClass(\"FlowGraphSetPropertyBlock\" /* FlowGraphBlockNames.SetProperty */, FlowGraphSetPropertyBlock);\n//# sourceMappingURL=flowGraphSetPropertyBlock.js.map"], "names": ["FlowGraphSetPropertyBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeAny", "context", "_callingSignal", "target", "value", "setFunction", "e", "propertyName", "path", "obj", "prop", "RegisterClass"], "mappings": "gPAaO,MAAMA,UAAkCC,CAAqC,CAChF,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,OAAS,KAAK,kBAAkB,SAAUC,EAAaD,EAAO,MAAM,EACzE,KAAK,MAAQ,KAAK,kBAAkB,QAASC,CAAW,EACxD,KAAK,aAAe,KAAK,kBAAkB,eAAgBA,EAAaD,EAAO,YAAY,EAC3F,KAAK,kBAAoB,KAAK,kBAAkB,oBAAqBC,CAAW,CACnF,CACD,SAASC,EAASC,EAAgB,CAC9B,GAAI,CACA,MAAMC,EAAS,KAAK,OAAO,SAASF,CAAO,EACrCG,EAAQ,KAAK,MAAM,SAASH,CAAO,EACnCI,EAAc,KAAK,kBAAkB,SAASJ,CAAO,EACvDI,EACAA,EAAYF,EAAQ,KAAK,aAAa,SAASF,CAAO,EAAGG,EAAOH,CAAO,EAGvE,KAAK,kBAAkBE,EAAQ,KAAK,aAAa,SAASF,CAAO,EAAGG,CAAK,CAEhF,OACME,EAAG,CACN,KAAK,aAAaL,EAASK,CAAC,CAC/B,CACD,KAAK,IAAI,gBAAgBL,CAAO,CACnC,CACD,kBAAkBE,EAAQI,EAAcH,EAAO,CAC3C,MAAMI,EAAOD,EAAa,MAAM,GAAG,EACnC,IAAIE,EAAMN,EACV,QAAS,EAAI,EAAG,EAAIK,EAAK,OAAS,EAAG,IAAK,CACtC,MAAME,EAAOF,EAAK,CAAC,EACfC,EAAIC,CAAI,IAAM,SACdD,EAAIC,CAAI,EAAI,IAEhBD,EAAMA,EAAIC,CAAI,CACjB,CACDD,EAAID,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAIJ,CAChC,CACD,cAAe,CACX,MAAO,2BACV,CACL,CACAO,EAAc,4BAAmEd,CAAyB", "x_google_ignoreList": [0]}