{"version": 3, "file": "bone-CgNwSK5F.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Bones/bone.js"], "sourcesContent": ["import { Vector3, <PERSON><PERSON>rn<PERSON>, <PERSON>, TmpVectors } from \"../Maths/math.vector.js\";\nimport { <PERSON><PERSON>Array } from \"../Misc/arrayTools.js\";\nimport { Node } from \"../node.js\";\n/**\n * Class used to store bone information\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\n */\nexport class Bone extends Node {\n    /** @internal */\n    get _matrix() {\n        this._compose();\n        return this._localMatrix;\n    }\n    /** @internal */\n    set _matrix(value) {\n        // skip if the matrices are the same\n        if (value.updateFlag === this._localMatrix.updateFlag && !this._needToCompose) {\n            return;\n        }\n        this._needToCompose = false; // in case there was a pending compose\n        this._localMatrix.copyFrom(value);\n        this._markAsDirtyAndDecompose();\n    }\n    /**\n     * Create a new bone\n     * @param name defines the bone name\n     * @param skeleton defines the parent skeleton\n     * @param parentBone defines the parent (can be null if the bone is the root)\n     * @param localMatrix defines the local matrix (default: identity)\n     * @param restMatrix defines the rest matrix (default: localMatrix)\n     * @param bindMatrix defines the bind matrix (default: localMatrix)\n     * @param index defines index of the bone in the hierarchy (default: null)\n     */\n    constructor(\n    /**\n     * defines the bone name\n     */\n    name, skeleton, parentBone = null, localMatrix = null, restMatrix = null, bindMatrix = null, index = null) {\n        super(name, skeleton.getScene(), false);\n        this.name = name;\n        /**\n         * Gets the list of child bones\n         */\n        this.children = [];\n        /** Gets the animations associated with this bone */\n        this.animations = [];\n        /**\n         * @internal Internal only\n         * Set this value to map this bone to a different index in the transform matrices\n         * Set this value to -1 to exclude the bone from the transform matrices\n         */\n        this._index = null;\n        this._scalingDeterminant = 1;\n        this._needToDecompose = true;\n        this._needToCompose = false;\n        /** @internal */\n        this._linkedTransformNode = null;\n        /** @internal */\n        this._waitingTransformNodeId = null;\n        this._skeleton = skeleton;\n        this._localMatrix = localMatrix?.clone() ?? Matrix.Identity();\n        this._restMatrix = restMatrix ?? this._localMatrix.clone();\n        this._bindMatrix = bindMatrix ?? this._localMatrix.clone();\n        this._index = index;\n        this._absoluteMatrix = new Matrix();\n        this._absoluteBindMatrix = new Matrix();\n        this._absoluteInverseBindMatrix = new Matrix();\n        this._finalMatrix = new Matrix();\n        skeleton.bones.push(this);\n        this.setParent(parentBone, false);\n        this._updateAbsoluteBindMatrices();\n    }\n    /**\n     * Gets the current object class name.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"Bone\";\n    }\n    // Members\n    /**\n     * Gets the parent skeleton\n     * @returns a skeleton\n     */\n    getSkeleton() {\n        return this._skeleton;\n    }\n    get parent() {\n        return this._parentNode;\n    }\n    /**\n     * Gets parent bone\n     * @returns a bone or null if the bone is the root of the bone hierarchy\n     */\n    getParent() {\n        return this.parent;\n    }\n    /**\n     * Returns an array containing the children of the bone\n     * @returns an array containing the children of the bone (can be empty if the bone has no children)\n     */\n    getChildren() {\n        return this.children;\n    }\n    /**\n     * Gets the node index in matrix array generated for rendering\n     * @returns the node index\n     */\n    getIndex() {\n        return this._index === null ? this.getSkeleton().bones.indexOf(this) : this._index;\n    }\n    set parent(newParent) {\n        this.setParent(newParent);\n    }\n    /**\n     * Sets the parent bone\n     * @param parent defines the parent (can be null if the bone is the root)\n     * @param updateAbsoluteBindMatrices defines if the absolute bind and absolute inverse bind matrices must be updated\n     */\n    setParent(parent, updateAbsoluteBindMatrices = true) {\n        if (this.parent === parent) {\n            return;\n        }\n        if (this.parent) {\n            const index = this.parent.children.indexOf(this);\n            if (index !== -1) {\n                this.parent.children.splice(index, 1);\n            }\n        }\n        this._parentNode = parent;\n        if (this.parent) {\n            this.parent.children.push(this);\n        }\n        if (updateAbsoluteBindMatrices) {\n            this._updateAbsoluteBindMatrices();\n        }\n        this.markAsDirty();\n    }\n    /**\n     * Gets the local matrix\n     * @returns the local matrix\n     */\n    getLocalMatrix() {\n        this._compose();\n        return this._localMatrix;\n    }\n    /**\n     * Gets the bind matrix\n     * @returns the bind matrix\n     */\n    getBindMatrix() {\n        return this._bindMatrix;\n    }\n    /**\n     * Gets the bind matrix.\n     * @returns the bind matrix\n     * @deprecated Please use getBindMatrix instead\n     */\n    getBaseMatrix() {\n        return this.getBindMatrix();\n    }\n    /**\n     * Gets the rest matrix\n     * @returns the rest matrix\n     */\n    getRestMatrix() {\n        return this._restMatrix;\n    }\n    /**\n     * Gets the rest matrix\n     * @returns the rest matrix\n     * @deprecated Please use getRestMatrix instead\n     */\n    getRestPose() {\n        return this.getRestMatrix();\n    }\n    /**\n     * Sets the rest matrix\n     * @param matrix the local-space rest matrix to set for this bone\n     */\n    setRestMatrix(matrix) {\n        this._restMatrix.copyFrom(matrix);\n    }\n    /**\n     * Sets the rest matrix\n     * @param matrix the local-space rest to set for this bone\n     * @deprecated Please use setRestMatrix instead\n     */\n    setRestPose(matrix) {\n        this.setRestMatrix(matrix);\n    }\n    /**\n     * Gets the bind matrix\n     * @returns the bind matrix\n     * @deprecated Please use getBindMatrix instead\n     */\n    getBindPose() {\n        return this.getBindMatrix();\n    }\n    /**\n     * Sets the bind matrix\n     * This will trigger a recomputation of the absolute bind and absolute inverse bind matrices for this bone and its children\n     * Note that the local matrix will also be set with the matrix passed in parameter!\n     * @param matrix the local-space bind matrix to set for this bone\n     */\n    setBindMatrix(matrix) {\n        this.updateMatrix(matrix);\n    }\n    /**\n     * Sets the bind matrix\n     * @param matrix the local-space bind to set for this bone\n     * @deprecated Please use setBindMatrix instead\n     */\n    setBindPose(matrix) {\n        this.setBindMatrix(matrix);\n    }\n    /**\n     * Gets the matrix used to store the final world transformation of the bone (ie. the matrix sent to shaders)\n     * @returns the final world matrix\n     */\n    getFinalMatrix() {\n        return this._finalMatrix;\n    }\n    /**\n     * Gets the matrix used to store the final world transformation of the bone (ie. the matrix sent to shaders)\n     * @deprecated Please use getFinalMatrix instead\n     * @returns the final world matrix\n     */\n    getWorldMatrix() {\n        return this.getFinalMatrix();\n    }\n    /**\n     * Sets the local matrix to the rest matrix\n     */\n    returnToRest() {\n        if (this._linkedTransformNode) {\n            const localScaling = TmpVectors.Vector3[0];\n            const localRotation = TmpVectors.Quaternion[0];\n            const localPosition = TmpVectors.Vector3[1];\n            this.getRestMatrix().decompose(localScaling, localRotation, localPosition);\n            this._linkedTransformNode.position.copyFrom(localPosition);\n            this._linkedTransformNode.rotationQuaternion = this._linkedTransformNode.rotationQuaternion ?? Quaternion.Identity();\n            this._linkedTransformNode.rotationQuaternion.copyFrom(localRotation);\n            this._linkedTransformNode.scaling.copyFrom(localScaling);\n        }\n        else {\n            this._matrix = this._restMatrix;\n        }\n    }\n    /**\n     * Gets the inverse of the bind matrix, in world space (relative to the skeleton root)\n     * @returns the inverse bind matrix, in world space\n     */\n    getAbsoluteInverseBindMatrix() {\n        return this._absoluteInverseBindMatrix;\n    }\n    /**\n     * Gets the inverse of the bind matrix, in world space (relative to the skeleton root)\n     * @returns the inverse bind matrix, in world space\n     * @deprecated Please use getAbsoluteInverseBindMatrix instead\n     */\n    getInvertedAbsoluteTransform() {\n        return this.getAbsoluteInverseBindMatrix();\n    }\n    /**\n     * Gets the bone matrix, in world space (relative to the skeleton root)\n     * @returns the bone matrix, in world space\n     */\n    getAbsoluteMatrix() {\n        return this._absoluteMatrix;\n    }\n    /**\n     * Gets the bone matrix, in world space (relative to the skeleton root)\n     * @returns the bone matrix, in world space\n     * @deprecated Please use getAbsoluteMatrix instead\n     */\n    getAbsoluteTransform() {\n        return this._absoluteMatrix;\n    }\n    /**\n     * Links with the given transform node.\n     * The local matrix of this bone is overwritten by the transform of the node every frame.\n     * @param transformNode defines the transform node to link to\n     */\n    linkTransformNode(transformNode) {\n        if (this._linkedTransformNode) {\n            this._skeleton._numBonesWithLinkedTransformNode--;\n        }\n        this._linkedTransformNode = transformNode;\n        if (this._linkedTransformNode) {\n            this._skeleton._numBonesWithLinkedTransformNode++;\n        }\n    }\n    // Properties (matches TransformNode properties)\n    /**\n     * Gets the node used to drive the bone's transformation\n     * @returns a transform node or null\n     */\n    getTransformNode() {\n        return this._linkedTransformNode;\n    }\n    /** Gets or sets current position (in local space) */\n    get position() {\n        this._decompose();\n        return this._localPosition;\n    }\n    set position(newPosition) {\n        this._decompose();\n        this._localPosition.copyFrom(newPosition);\n        this._markAsDirtyAndCompose();\n    }\n    /** Gets or sets current rotation (in local space) */\n    get rotation() {\n        return this.getRotation();\n    }\n    set rotation(newRotation) {\n        this.setRotation(newRotation);\n    }\n    /** Gets or sets current rotation quaternion (in local space) */\n    get rotationQuaternion() {\n        this._decompose();\n        return this._localRotation;\n    }\n    set rotationQuaternion(newRotation) {\n        this.setRotationQuaternion(newRotation);\n    }\n    /** Gets or sets current scaling (in local space) */\n    get scaling() {\n        return this.getScale();\n    }\n    set scaling(newScaling) {\n        this.setScale(newScaling);\n    }\n    /**\n     * Gets the animation properties override\n     */\n    get animationPropertiesOverride() {\n        return this._skeleton.animationPropertiesOverride;\n    }\n    // Methods\n    _decompose() {\n        if (!this._needToDecompose) {\n            return;\n        }\n        this._needToDecompose = false;\n        if (!this._localScaling) {\n            this._localScaling = Vector3.Zero();\n            this._localRotation = Quaternion.Zero();\n            this._localPosition = Vector3.Zero();\n        }\n        this._localMatrix.decompose(this._localScaling, this._localRotation, this._localPosition);\n    }\n    _compose() {\n        if (!this._needToCompose) {\n            return;\n        }\n        if (!this._localScaling) {\n            this._needToCompose = false;\n            return;\n        }\n        this._needToCompose = false;\n        Matrix.ComposeToRef(this._localScaling, this._localRotation, this._localPosition, this._localMatrix);\n    }\n    /**\n     * Update the bind (and optionally the local) matrix\n     * @param bindMatrix defines the new matrix to set to the bind/local matrix, in local space\n     * @param updateAbsoluteBindMatrices defines if the absolute bind and absolute inverse bind matrices must be recomputed (default: true)\n     * @param updateLocalMatrix defines if the local matrix should also be updated with the matrix passed in parameter (default: true)\n     */\n    updateMatrix(bindMatrix, updateAbsoluteBindMatrices = true, updateLocalMatrix = true) {\n        this._bindMatrix.copyFrom(bindMatrix);\n        if (updateAbsoluteBindMatrices) {\n            this._updateAbsoluteBindMatrices();\n        }\n        if (updateLocalMatrix) {\n            this._matrix = bindMatrix;\n        }\n        else {\n            this.markAsDirty();\n        }\n    }\n    /**\n     * @internal\n     */\n    _updateAbsoluteBindMatrices(bindMatrix, updateChildren = true) {\n        if (!bindMatrix) {\n            bindMatrix = this._bindMatrix;\n        }\n        if (this.parent) {\n            bindMatrix.multiplyToRef(this.parent._absoluteBindMatrix, this._absoluteBindMatrix);\n        }\n        else {\n            this._absoluteBindMatrix.copyFrom(bindMatrix);\n        }\n        this._absoluteBindMatrix.invertToRef(this._absoluteInverseBindMatrix);\n        if (updateChildren) {\n            for (let index = 0; index < this.children.length; index++) {\n                this.children[index]._updateAbsoluteBindMatrices();\n            }\n        }\n        this._scalingDeterminant = this._absoluteBindMatrix.determinant() < 0 ? -1 : 1;\n    }\n    /**\n     * Flag the bone as dirty (Forcing it to update everything)\n     * @returns this bone\n     */\n    markAsDirty() {\n        this._currentRenderId++;\n        this._childUpdateId++;\n        this._skeleton._markAsDirty();\n        return this;\n    }\n    /** @internal */\n    _markAsDirtyAndCompose() {\n        this.markAsDirty();\n        this._needToCompose = true;\n    }\n    _markAsDirtyAndDecompose() {\n        this.markAsDirty();\n        this._needToDecompose = true;\n    }\n    _updatePosition(vec, space = 0 /* Space.LOCAL */, tNode, translationMode = true) {\n        const lm = this.getLocalMatrix();\n        if (space == 0 /* Space.LOCAL */) {\n            if (translationMode) {\n                lm.addAtIndex(12, vec.x);\n                lm.addAtIndex(13, vec.y);\n                lm.addAtIndex(14, vec.z);\n            }\n            else {\n                lm.setTranslationFromFloats(vec.x, vec.y, vec.z);\n            }\n        }\n        else {\n            let wm = null;\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\n            if (tNode) {\n                wm = tNode.getWorldMatrix();\n            }\n            this._skeleton.computeAbsoluteMatrices();\n            const tmat = Bone._TmpMats[0];\n            const tvec = Bone._TmpVecs[0];\n            if (this.parent) {\n                if (tNode && wm) {\n                    tmat.copyFrom(this.parent.getAbsoluteMatrix());\n                    tmat.multiplyToRef(wm, tmat);\n                }\n                else {\n                    tmat.copyFrom(this.parent.getAbsoluteMatrix());\n                }\n            }\n            else {\n                Matrix.IdentityToRef(tmat);\n            }\n            if (translationMode) {\n                tmat.setTranslationFromFloats(0, 0, 0);\n            }\n            tmat.invert();\n            Vector3.TransformCoordinatesToRef(vec, tmat, tvec);\n            if (translationMode) {\n                lm.addAtIndex(12, tvec.x);\n                lm.addAtIndex(13, tvec.y);\n                lm.addAtIndex(14, tvec.z);\n            }\n            else {\n                lm.setTranslationFromFloats(tvec.x, tvec.y, tvec.z);\n            }\n        }\n        this._markAsDirtyAndDecompose();\n    }\n    /**\n     * Translate the bone in local or world space\n     * @param vec The amount to translate the bone\n     * @param space The space that the translation is in (default: Space.LOCAL)\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    translate(vec, space = 0 /* Space.LOCAL */, tNode) {\n        this._updatePosition(vec, space, tNode, true);\n    }\n    /**\n     * Set the position of the bone in local or world space\n     * @param position The position to set the bone\n     * @param space The space that the position is in (default: Space.LOCAL)\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setPosition(position, space = 0 /* Space.LOCAL */, tNode) {\n        this._updatePosition(position, space, tNode, false);\n    }\n    /**\n     * Set the absolute position of the bone (world space)\n     * @param position The position to set the bone\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setAbsolutePosition(position, tNode) {\n        this.setPosition(position, 1 /* Space.WORLD */, tNode);\n    }\n    /**\n     * Scale the bone on the x, y and z axes (in local space)\n     * @param x The amount to scale the bone on the x axis\n     * @param y The amount to scale the bone on the y axis\n     * @param z The amount to scale the bone on the z axis\n     * @param scaleChildren sets this to true if children of the bone should be scaled as well (false by default)\n     */\n    scale(x, y, z, scaleChildren = false) {\n        const locMat = this.getLocalMatrix();\n        // Apply new scaling on top of current local matrix\n        const scaleMat = Bone._TmpMats[0];\n        Matrix.ScalingToRef(x, y, z, scaleMat);\n        scaleMat.multiplyToRef(locMat, locMat);\n        // Invert scaling matrix and apply the inverse to all children\n        scaleMat.invert();\n        for (const child of this.children) {\n            const cm = child.getLocalMatrix();\n            cm.multiplyToRef(scaleMat, cm);\n            cm.multiplyAtIndex(12, x);\n            cm.multiplyAtIndex(13, y);\n            cm.multiplyAtIndex(14, z);\n            child._markAsDirtyAndDecompose();\n        }\n        this._markAsDirtyAndDecompose();\n        if (scaleChildren) {\n            for (const child of this.children) {\n                child.scale(x, y, z, scaleChildren);\n            }\n        }\n    }\n    /**\n     * Set the bone scaling in local space\n     * @param scale defines the scaling vector\n     */\n    setScale(scale) {\n        this._decompose();\n        this._localScaling.copyFrom(scale);\n        this._markAsDirtyAndCompose();\n    }\n    /**\n     * Gets the current scaling in local space\n     * @returns the current scaling vector\n     */\n    getScale() {\n        this._decompose();\n        return this._localScaling;\n    }\n    /**\n     * Gets the current scaling in local space and stores it in a target vector\n     * @param result defines the target vector\n     */\n    getScaleToRef(result) {\n        this._decompose();\n        result.copyFrom(this._localScaling);\n    }\n    /**\n     * Set the yaw, pitch, and roll of the bone in local or world space\n     * @param yaw The rotation of the bone on the y axis\n     * @param pitch The rotation of the bone on the x axis\n     * @param roll The rotation of the bone on the z axis\n     * @param space The space that the axes of rotation are in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setYawPitchRoll(yaw, pitch, roll, space = 0 /* Space.LOCAL */, tNode) {\n        if (space === 0 /* Space.LOCAL */) {\n            const quat = Bone._TmpQuat;\n            Quaternion.RotationYawPitchRollToRef(yaw, pitch, roll, quat);\n            this.setRotationQuaternion(quat, space, tNode);\n            return;\n        }\n        const rotMatInv = Bone._TmpMats[0];\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\n            return;\n        }\n        const rotMat = Bone._TmpMats[1];\n        Matrix.RotationYawPitchRollToRef(yaw, pitch, roll, rotMat);\n        rotMatInv.multiplyToRef(rotMat, rotMat);\n        this._rotateWithMatrix(rotMat, space, tNode);\n    }\n    /**\n     * Add a rotation to the bone on an axis in local or world space\n     * @param axis The axis to rotate the bone on\n     * @param amount The amount to rotate the bone\n     * @param space The space that the axis is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    rotate(axis, amount, space = 0 /* Space.LOCAL */, tNode) {\n        const rmat = Bone._TmpMats[0];\n        rmat.setTranslationFromFloats(0, 0, 0);\n        Matrix.RotationAxisToRef(axis, amount, rmat);\n        this._rotateWithMatrix(rmat, space, tNode);\n    }\n    /**\n     * Set the rotation of the bone to a particular axis angle in local or world space\n     * @param axis The axis to rotate the bone on\n     * @param angle The angle that the bone should be rotated to\n     * @param space The space that the axis is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setAxisAngle(axis, angle, space = 0 /* Space.LOCAL */, tNode) {\n        if (space === 0 /* Space.LOCAL */) {\n            const quat = Bone._TmpQuat;\n            Quaternion.RotationAxisToRef(axis, angle, quat);\n            this.setRotationQuaternion(quat, space, tNode);\n            return;\n        }\n        const rotMatInv = Bone._TmpMats[0];\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\n            return;\n        }\n        const rotMat = Bone._TmpMats[1];\n        Matrix.RotationAxisToRef(axis, angle, rotMat);\n        rotMatInv.multiplyToRef(rotMat, rotMat);\n        this._rotateWithMatrix(rotMat, space, tNode);\n    }\n    /**\n     * Set the euler rotation of the bone in local or world space\n     * @param rotation The euler rotation that the bone should be set to\n     * @param space The space that the rotation is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setRotation(rotation, space = 0 /* Space.LOCAL */, tNode) {\n        this.setYawPitchRoll(rotation.y, rotation.x, rotation.z, space, tNode);\n    }\n    /**\n     * Set the quaternion rotation of the bone in local or world space\n     * @param quat The quaternion rotation that the bone should be set to\n     * @param space The space that the rotation is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setRotationQuaternion(quat, space = 0 /* Space.LOCAL */, tNode) {\n        if (space === 0 /* Space.LOCAL */) {\n            this._decompose();\n            this._localRotation.copyFrom(quat);\n            this._markAsDirtyAndCompose();\n            return;\n        }\n        const rotMatInv = Bone._TmpMats[0];\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\n            return;\n        }\n        const rotMat = Bone._TmpMats[1];\n        Matrix.FromQuaternionToRef(quat, rotMat);\n        rotMatInv.multiplyToRef(rotMat, rotMat);\n        this._rotateWithMatrix(rotMat, space, tNode);\n    }\n    /**\n     * Set the rotation matrix of the bone in local or world space\n     * @param rotMat The rotation matrix that the bone should be set to\n     * @param space The space that the rotation is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     */\n    setRotationMatrix(rotMat, space = 0 /* Space.LOCAL */, tNode) {\n        if (space === 0 /* Space.LOCAL */) {\n            const quat = Bone._TmpQuat;\n            Quaternion.FromRotationMatrixToRef(rotMat, quat);\n            this.setRotationQuaternion(quat, space, tNode);\n            return;\n        }\n        const rotMatInv = Bone._TmpMats[0];\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\n            return;\n        }\n        const rotMat2 = Bone._TmpMats[1];\n        rotMat2.copyFrom(rotMat);\n        rotMatInv.multiplyToRef(rotMat, rotMat2);\n        this._rotateWithMatrix(rotMat2, space, tNode);\n    }\n    _rotateWithMatrix(rmat, space = 0 /* Space.LOCAL */, tNode) {\n        const lmat = this.getLocalMatrix();\n        const lx = lmat.m[12];\n        const ly = lmat.m[13];\n        const lz = lmat.m[14];\n        const parent = this.getParent();\n        const parentScale = Bone._TmpMats[3];\n        const parentScaleInv = Bone._TmpMats[4];\n        if (parent && space == 1 /* Space.WORLD */) {\n            if (tNode) {\n                parentScale.copyFrom(tNode.getWorldMatrix());\n                parent.getAbsoluteMatrix().multiplyToRef(parentScale, parentScale);\n            }\n            else {\n                parentScale.copyFrom(parent.getAbsoluteMatrix());\n            }\n            parentScaleInv.copyFrom(parentScale);\n            parentScaleInv.invert();\n            lmat.multiplyToRef(parentScale, lmat);\n            lmat.multiplyToRef(rmat, lmat);\n            lmat.multiplyToRef(parentScaleInv, lmat);\n        }\n        else {\n            if (space == 1 /* Space.WORLD */ && tNode) {\n                parentScale.copyFrom(tNode.getWorldMatrix());\n                parentScaleInv.copyFrom(parentScale);\n                parentScaleInv.invert();\n                lmat.multiplyToRef(parentScale, lmat);\n                lmat.multiplyToRef(rmat, lmat);\n                lmat.multiplyToRef(parentScaleInv, lmat);\n            }\n            else {\n                lmat.multiplyToRef(rmat, lmat);\n            }\n        }\n        lmat.setTranslationFromFloats(lx, ly, lz);\n        this.computeAbsoluteMatrices();\n        this._markAsDirtyAndDecompose();\n    }\n    _getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode) {\n        const scaleMatrix = Bone._TmpMats[2];\n        rotMatInv.copyFrom(this.getAbsoluteMatrix());\n        if (tNode) {\n            rotMatInv.multiplyToRef(tNode.getWorldMatrix(), rotMatInv);\n            Matrix.ScalingToRef(tNode.scaling.x, tNode.scaling.y, tNode.scaling.z, scaleMatrix);\n        }\n        else {\n            Matrix.IdentityToRef(scaleMatrix);\n        }\n        rotMatInv.invert();\n        if (isNaN(rotMatInv.m[0])) {\n            // Matrix failed to invert.\n            // This can happen if scale is zero for example.\n            return false;\n        }\n        scaleMatrix.multiplyAtIndex(0, this._scalingDeterminant);\n        rotMatInv.multiplyToRef(scaleMatrix, rotMatInv);\n        return true;\n    }\n    /**\n     * Get the position of the bone in local or world space\n     * @param space The space that the returned position is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The position of the bone\n     */\n    getPosition(space = 0 /* Space.LOCAL */, tNode = null) {\n        const pos = Vector3.Zero();\n        this.getPositionToRef(space, tNode, pos);\n        return pos;\n    }\n    /**\n     * Copy the position of the bone to a vector3 in local or world space\n     * @param space The space that the returned position is in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 to copy the position to\n     */\n    getPositionToRef(space = 0 /* Space.LOCAL */, tNode, result) {\n        if (space == 0 /* Space.LOCAL */) {\n            const lm = this.getLocalMatrix();\n            result.x = lm.m[12];\n            result.y = lm.m[13];\n            result.z = lm.m[14];\n        }\n        else {\n            let wm = null;\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\n            if (tNode) {\n                wm = tNode.getWorldMatrix();\n            }\n            this._skeleton.computeAbsoluteMatrices();\n            let tmat = Bone._TmpMats[0];\n            if (tNode && wm) {\n                tmat.copyFrom(this.getAbsoluteMatrix());\n                tmat.multiplyToRef(wm, tmat);\n            }\n            else {\n                tmat = this.getAbsoluteMatrix();\n            }\n            result.x = tmat.m[12];\n            result.y = tmat.m[13];\n            result.z = tmat.m[14];\n        }\n    }\n    /**\n     * Get the absolute position of the bone (world space)\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The absolute position of the bone\n     */\n    getAbsolutePosition(tNode = null) {\n        const pos = Vector3.Zero();\n        this.getPositionToRef(1 /* Space.WORLD */, tNode, pos);\n        return pos;\n    }\n    /**\n     * Copy the absolute position of the bone (world space) to the result param\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 to copy the absolute position to\n     */\n    getAbsolutePositionToRef(tNode, result) {\n        this.getPositionToRef(1 /* Space.WORLD */, tNode, result);\n    }\n    /**\n     * Compute the absolute matrices of this bone and its children\n     */\n    computeAbsoluteMatrices() {\n        this._compose();\n        if (this.parent) {\n            this._localMatrix.multiplyToRef(this.parent._absoluteMatrix, this._absoluteMatrix);\n        }\n        else {\n            this._absoluteMatrix.copyFrom(this._localMatrix);\n            const poseMatrix = this._skeleton.getPoseMatrix();\n            if (poseMatrix) {\n                this._absoluteMatrix.multiplyToRef(poseMatrix, this._absoluteMatrix);\n            }\n        }\n        const children = this.children;\n        const len = children.length;\n        for (let i = 0; i < len; i++) {\n            children[i].computeAbsoluteMatrices();\n        }\n    }\n    /**\n     * Compute the absolute matrices of this bone and its children\n     * @deprecated Please use computeAbsoluteMatrices instead\n     */\n    computeAbsoluteTransforms() {\n        this.computeAbsoluteMatrices();\n    }\n    /**\n     * Get the world direction from an axis that is in the local space of the bone\n     * @param localAxis The local direction that is used to compute the world direction\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The world direction\n     */\n    getDirection(localAxis, tNode = null) {\n        const result = Vector3.Zero();\n        this.getDirectionToRef(localAxis, tNode, result);\n        return result;\n    }\n    /**\n     * Copy the world direction to a vector3 from an axis that is in the local space of the bone\n     * @param localAxis The local direction that is used to compute the world direction\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 that the world direction will be copied to\n     */\n    getDirectionToRef(localAxis, tNode = null, result) {\n        let wm = null;\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\n        if (tNode) {\n            wm = tNode.getWorldMatrix();\n        }\n        this._skeleton.computeAbsoluteMatrices();\n        const mat = Bone._TmpMats[0];\n        mat.copyFrom(this.getAbsoluteMatrix());\n        if (tNode && wm) {\n            mat.multiplyToRef(wm, mat);\n        }\n        Vector3.TransformNormalToRef(localAxis, mat, result);\n        result.normalize();\n    }\n    /**\n     * Get the euler rotation of the bone in local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The euler rotation\n     */\n    getRotation(space = 0 /* Space.LOCAL */, tNode = null) {\n        const result = Vector3.Zero();\n        this.getRotationToRef(space, tNode, result);\n        return result;\n    }\n    /**\n     * Copy the euler rotation of the bone to a vector3.  The rotation can be in either local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 that the rotation should be copied to\n     */\n    getRotationToRef(space = 0 /* Space.LOCAL */, tNode = null, result) {\n        const quat = Bone._TmpQuat;\n        this.getRotationQuaternionToRef(space, tNode, quat);\n        quat.toEulerAnglesToRef(result);\n    }\n    /**\n     * Get the quaternion rotation of the bone in either local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The quaternion rotation\n     */\n    getRotationQuaternion(space = 0 /* Space.LOCAL */, tNode = null) {\n        const result = Quaternion.Identity();\n        this.getRotationQuaternionToRef(space, tNode, result);\n        return result;\n    }\n    /**\n     * Copy the quaternion rotation of the bone to a quaternion.  The rotation can be in either local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The quaternion that the rotation should be copied to\n     */\n    getRotationQuaternionToRef(space = 0 /* Space.LOCAL */, tNode = null, result) {\n        if (space == 0 /* Space.LOCAL */) {\n            this._decompose();\n            result.copyFrom(this._localRotation);\n        }\n        else {\n            const mat = Bone._TmpMats[0];\n            const amat = this.getAbsoluteMatrix();\n            if (tNode) {\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\n            }\n            else {\n                mat.copyFrom(amat);\n            }\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\n            mat.decompose(undefined, result, undefined);\n        }\n    }\n    /**\n     * Get the rotation matrix of the bone in local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The rotation matrix\n     */\n    getRotationMatrix(space = 0 /* Space.LOCAL */, tNode) {\n        const result = Matrix.Identity();\n        this.getRotationMatrixToRef(space, tNode, result);\n        return result;\n    }\n    /**\n     * Copy the rotation matrix of the bone to a matrix.  The rotation can be in either local or world space\n     * @param space The space that the rotation should be in\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The quaternion that the rotation should be copied to\n     */\n    getRotationMatrixToRef(space = 0 /* Space.LOCAL */, tNode, result) {\n        if (space == 0 /* Space.LOCAL */) {\n            this.getLocalMatrix().getRotationMatrixToRef(result);\n        }\n        else {\n            const mat = Bone._TmpMats[0];\n            const amat = this.getAbsoluteMatrix();\n            if (tNode) {\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\n            }\n            else {\n                mat.copyFrom(amat);\n            }\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\n            mat.getRotationMatrixToRef(result);\n        }\n    }\n    /**\n     * Get the world position of a point that is in the local space of the bone\n     * @param position The local position\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The world position\n     */\n    getAbsolutePositionFromLocal(position, tNode = null) {\n        const result = Vector3.Zero();\n        this.getAbsolutePositionFromLocalToRef(position, tNode, result);\n        return result;\n    }\n    /**\n     * Get the world position of a point that is in the local space of the bone and copy it to the result param\n     * @param position The local position\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 that the world position should be copied to\n     */\n    getAbsolutePositionFromLocalToRef(position, tNode = null, result) {\n        let wm = null;\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\n        if (tNode) {\n            wm = tNode.getWorldMatrix();\n        }\n        this._skeleton.computeAbsoluteMatrices();\n        const tmat = Bone._TmpMats[0];\n        tmat.copyFrom(this.getAbsoluteMatrix());\n        if (tNode && wm) {\n            tmat.multiplyToRef(wm, tmat);\n        }\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\n    }\n    /**\n     * Get the local position of a point that is in world space\n     * @param position The world position\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @returns The local position\n     */\n    getLocalPositionFromAbsolute(position, tNode = null) {\n        const result = Vector3.Zero();\n        this.getLocalPositionFromAbsoluteToRef(position, tNode, result);\n        return result;\n    }\n    /**\n     * Get the local position of a point that is in world space and copy it to the result param\n     * @param position The world position\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\n     * @param result The vector3 that the local position should be copied to\n     */\n    getLocalPositionFromAbsoluteToRef(position, tNode = null, result) {\n        let wm = null;\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\n        if (tNode) {\n            wm = tNode.getWorldMatrix();\n        }\n        this._skeleton.computeAbsoluteMatrices();\n        const tmat = Bone._TmpMats[0];\n        tmat.copyFrom(this.getAbsoluteMatrix());\n        if (tNode && wm) {\n            tmat.multiplyToRef(wm, tmat);\n        }\n        tmat.invert();\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\n    }\n    /**\n     * Set the current local matrix as the restMatrix for this bone.\n     */\n    setCurrentPoseAsRest() {\n        this.setRestMatrix(this.getLocalMatrix());\n    }\n    /**\n     * Releases associated resources\n     */\n    dispose() {\n        this._linkedTransformNode = null;\n        const index = this._skeleton.bones.indexOf(this);\n        if (index !== -1) {\n            this._skeleton.bones.splice(index, 1);\n        }\n        if (this._parentNode && this._parentNode.children) {\n            const children = this._parentNode.children;\n            const index = children.indexOf(this);\n            if (index !== -1) {\n                children.splice(index, 1);\n            }\n        }\n        super.dispose();\n    }\n}\nBone._TmpVecs = BuildArray(2, Vector3.Zero);\nBone._TmpQuat = Quaternion.Identity();\nBone._TmpMats = BuildArray(5, Matrix.Identity);\n//# sourceMappingURL=bone.js.map"], "names": ["Bone", "Node", "value", "name", "skeleton", "parentBone", "localMatrix", "restMatrix", "bindMatrix", "index", "Matrix", "newParent", "parent", "updateAbsoluteBindMatrices", "matrix", "localScaling", "TmpVectors", "localRotation", "localPosition", "Quaternion", "transformNode", "newPosition", "newRotation", "newScaling", "Vector3", "updateLocalMatrix", "update<PERSON><PERSON><PERSON>n", "vec", "space", "tNode", "translationMode", "lm", "wm", "tmat", "tvec", "position", "x", "y", "z", "scaleChildren", "locMat", "scaleMat", "child", "cm", "scale", "result", "yaw", "pitch", "roll", "quat", "rotMatInv", "rotMat", "axis", "amount", "rmat", "angle", "rotation", "rotMat2", "lmat", "lx", "ly", "lz", "parentScale", "parentScaleInv", "scaleMatrix", "pos", "poseMatrix", "children", "len", "i", "localAxis", "mat", "amat", "BuildArray"], "mappings": "2EAOO,MAAMA,UAAaC,CAAK,CAE3B,IAAI,SAAU,CACV,YAAK,SAAQ,EACN,KAAK,YACf,CAED,IAAI,QAAQC,EAAO,CAEXA,EAAM,aAAe,KAAK,aAAa,YAAc,CAAC,KAAK,iBAG/D,KAAK,eAAiB,GACtB,KAAK,aAAa,SAASA,CAAK,EAChC,KAAK,yBAAwB,EAChC,CAWD,YAIAC,EAAMC,EAAUC,EAAa,KAAMC,EAAc,KAAMC,EAAa,KAAMC,EAAa,KAAMC,EAAQ,KAAM,CACvG,MAAMN,EAAMC,EAAS,SAAU,EAAE,EAAK,EACtC,KAAK,KAAOD,EAIZ,KAAK,SAAW,GAEhB,KAAK,WAAa,GAMlB,KAAK,OAAS,KACd,KAAK,oBAAsB,EAC3B,KAAK,iBAAmB,GACxB,KAAK,eAAiB,GAEtB,KAAK,qBAAuB,KAE5B,KAAK,wBAA0B,KAC/B,KAAK,UAAYC,EACjB,KAAK,aAAeE,GAAa,MAAK,GAAMI,EAAO,WACnD,KAAK,YAAcH,GAAc,KAAK,aAAa,MAAK,EACxD,KAAK,YAAcC,GAAc,KAAK,aAAa,MAAK,EACxD,KAAK,OAASC,EACd,KAAK,gBAAkB,IAAIC,EAC3B,KAAK,oBAAsB,IAAIA,EAC/B,KAAK,2BAA6B,IAAIA,EACtC,KAAK,aAAe,IAAIA,EACxBN,EAAS,MAAM,KAAK,IAAI,EACxB,KAAK,UAAUC,EAAY,EAAK,EAChC,KAAK,4BAA2B,CACnC,CAKD,cAAe,CACX,MAAO,MACV,CAMD,aAAc,CACV,OAAO,KAAK,SACf,CACD,IAAI,QAAS,CACT,OAAO,KAAK,WACf,CAKD,WAAY,CACR,OAAO,KAAK,MACf,CAKD,aAAc,CACV,OAAO,KAAK,QACf,CAKD,UAAW,CACP,OAAO,KAAK,SAAW,KAAO,KAAK,YAAW,EAAG,MAAM,QAAQ,IAAI,EAAI,KAAK,MAC/E,CACD,IAAI,OAAOM,EAAW,CAClB,KAAK,UAAUA,CAAS,CAC3B,CAMD,UAAUC,EAAQC,EAA6B,GAAM,CACjD,GAAI,KAAK,SAAWD,EAGpB,IAAI,KAAK,OAAQ,CACb,MAAMH,EAAQ,KAAK,OAAO,SAAS,QAAQ,IAAI,EAC3CA,IAAU,IACV,KAAK,OAAO,SAAS,OAAOA,EAAO,CAAC,CAE3C,CACD,KAAK,YAAcG,EACf,KAAK,QACL,KAAK,OAAO,SAAS,KAAK,IAAI,EAE9BC,GACA,KAAK,4BAA2B,EAEpC,KAAK,YAAW,EACnB,CAKD,gBAAiB,CACb,YAAK,SAAQ,EACN,KAAK,YACf,CAKD,eAAgB,CACZ,OAAO,KAAK,WACf,CAMD,eAAgB,CACZ,OAAO,KAAK,eACf,CAKD,eAAgB,CACZ,OAAO,KAAK,WACf,CAMD,aAAc,CACV,OAAO,KAAK,eACf,CAKD,cAAcC,EAAQ,CAClB,KAAK,YAAY,SAASA,CAAM,CACnC,CAMD,YAAYA,EAAQ,CAChB,KAAK,cAAcA,CAAM,CAC5B,CAMD,aAAc,CACV,OAAO,KAAK,eACf,CAOD,cAAcA,EAAQ,CAClB,KAAK,aAAaA,CAAM,CAC3B,CAMD,YAAYA,EAAQ,CAChB,KAAK,cAAcA,CAAM,CAC5B,CAKD,gBAAiB,CACb,OAAO,KAAK,YACf,CAMD,gBAAiB,CACb,OAAO,KAAK,gBACf,CAID,cAAe,CACX,GAAI,KAAK,qBAAsB,CAC3B,MAAMC,EAAeC,EAAW,QAAQ,CAAC,EACnCC,EAAgBD,EAAW,WAAW,CAAC,EACvCE,EAAgBF,EAAW,QAAQ,CAAC,EAC1C,KAAK,cAAa,EAAG,UAAUD,EAAcE,EAAeC,CAAa,EACzE,KAAK,qBAAqB,SAAS,SAASA,CAAa,EACzD,KAAK,qBAAqB,mBAAqB,KAAK,qBAAqB,oBAAsBC,EAAW,WAC1G,KAAK,qBAAqB,mBAAmB,SAASF,CAAa,EACnE,KAAK,qBAAqB,QAAQ,SAASF,CAAY,CAC1D,MAEG,KAAK,QAAU,KAAK,WAE3B,CAKD,8BAA+B,CAC3B,OAAO,KAAK,0BACf,CAMD,8BAA+B,CAC3B,OAAO,KAAK,8BACf,CAKD,mBAAoB,CAChB,OAAO,KAAK,eACf,CAMD,sBAAuB,CACnB,OAAO,KAAK,eACf,CAMD,kBAAkBK,EAAe,CACzB,KAAK,sBACL,KAAK,UAAU,mCAEnB,KAAK,qBAAuBA,EACxB,KAAK,sBACL,KAAK,UAAU,kCAEtB,CAMD,kBAAmB,CACf,OAAO,KAAK,oBACf,CAED,IAAI,UAAW,CACX,YAAK,WAAU,EACR,KAAK,cACf,CACD,IAAI,SAASC,EAAa,CACtB,KAAK,WAAU,EACf,KAAK,eAAe,SAASA,CAAW,EACxC,KAAK,uBAAsB,CAC9B,CAED,IAAI,UAAW,CACX,OAAO,KAAK,aACf,CACD,IAAI,SAASC,EAAa,CACtB,KAAK,YAAYA,CAAW,CAC/B,CAED,IAAI,oBAAqB,CACrB,YAAK,WAAU,EACR,KAAK,cACf,CACD,IAAI,mBAAmBA,EAAa,CAChC,KAAK,sBAAsBA,CAAW,CACzC,CAED,IAAI,SAAU,CACV,OAAO,KAAK,UACf,CACD,IAAI,QAAQC,EAAY,CACpB,KAAK,SAASA,CAAU,CAC3B,CAID,IAAI,6BAA8B,CAC9B,OAAO,KAAK,UAAU,2BACzB,CAED,YAAa,CACJ,KAAK,mBAGV,KAAK,iBAAmB,GACnB,KAAK,gBACN,KAAK,cAAgBC,EAAQ,OAC7B,KAAK,eAAiBL,EAAW,OACjC,KAAK,eAAiBK,EAAQ,QAElC,KAAK,aAAa,UAAU,KAAK,cAAe,KAAK,eAAgB,KAAK,cAAc,EAC3F,CACD,UAAW,CACP,GAAK,KAAK,eAGV,IAAI,CAAC,KAAK,cAAe,CACrB,KAAK,eAAiB,GACtB,MACH,CACD,KAAK,eAAiB,GACtBd,EAAO,aAAa,KAAK,cAAe,KAAK,eAAgB,KAAK,eAAgB,KAAK,YAAY,EACtG,CAOD,aAAaF,EAAYK,EAA6B,GAAMY,EAAoB,GAAM,CAClF,KAAK,YAAY,SAASjB,CAAU,EAChCK,GACA,KAAK,4BAA2B,EAEhCY,EACA,KAAK,QAAUjB,EAGf,KAAK,YAAW,CAEvB,CAID,4BAA4BA,EAAYkB,EAAiB,GAAM,CAW3D,GAVKlB,IACDA,EAAa,KAAK,aAElB,KAAK,OACLA,EAAW,cAAc,KAAK,OAAO,oBAAqB,KAAK,mBAAmB,EAGlF,KAAK,oBAAoB,SAASA,CAAU,EAEhD,KAAK,oBAAoB,YAAY,KAAK,0BAA0B,EAChEkB,EACA,QAASjB,EAAQ,EAAGA,EAAQ,KAAK,SAAS,OAAQA,IAC9C,KAAK,SAASA,CAAK,EAAE,4BAA2B,EAGxD,KAAK,oBAAsB,KAAK,oBAAoB,YAAW,EAAK,EAAI,GAAK,CAChF,CAKD,aAAc,CACV,YAAK,mBACL,KAAK,iBACL,KAAK,UAAU,eACR,IACV,CAED,wBAAyB,CACrB,KAAK,YAAW,EAChB,KAAK,eAAiB,EACzB,CACD,0BAA2B,CACvB,KAAK,YAAW,EAChB,KAAK,iBAAmB,EAC3B,CACD,gBAAgBkB,EAAKC,EAAQ,EAAqBC,EAAOC,EAAkB,GAAM,CAC7E,MAAMC,EAAK,KAAK,iBAChB,GAAIH,GAAS,EACLE,GACAC,EAAG,WAAW,GAAIJ,EAAI,CAAC,EACvBI,EAAG,WAAW,GAAIJ,EAAI,CAAC,EACvBI,EAAG,WAAW,GAAIJ,EAAI,CAAC,GAGvBI,EAAG,yBAAyBJ,EAAI,EAAGA,EAAI,EAAGA,EAAI,CAAC,MAGlD,CACD,IAAIK,EAAK,KAELH,IACAG,EAAKH,EAAM,kBAEf,KAAK,UAAU,0BACf,MAAMI,EAAOjC,EAAK,SAAS,CAAC,EACtBkC,EAAOlC,EAAK,SAAS,CAAC,EACxB,KAAK,OACD6B,GAASG,GACTC,EAAK,SAAS,KAAK,OAAO,kBAAmB,CAAA,EAC7CA,EAAK,cAAcD,EAAIC,CAAI,GAG3BA,EAAK,SAAS,KAAK,OAAO,kBAAmB,CAAA,EAIjDvB,EAAO,cAAcuB,CAAI,EAEzBH,GACAG,EAAK,yBAAyB,EAAG,EAAG,CAAC,EAEzCA,EAAK,OAAM,EACXT,EAAQ,0BAA0BG,EAAKM,EAAMC,CAAI,EAC7CJ,GACAC,EAAG,WAAW,GAAIG,EAAK,CAAC,EACxBH,EAAG,WAAW,GAAIG,EAAK,CAAC,EACxBH,EAAG,WAAW,GAAIG,EAAK,CAAC,GAGxBH,EAAG,yBAAyBG,EAAK,EAAGA,EAAK,EAAGA,EAAK,CAAC,CAEzD,CACD,KAAK,yBAAwB,CAChC,CAOD,UAAUP,EAAKC,EAAQ,EAAqBC,EAAO,CAC/C,KAAK,gBAAgBF,EAAKC,EAAOC,EAAO,EAAI,CAC/C,CAOD,YAAYM,EAAUP,EAAQ,EAAqBC,EAAO,CACtD,KAAK,gBAAgBM,EAAUP,EAAOC,EAAO,EAAK,CACrD,CAMD,oBAAoBM,EAAUN,EAAO,CACjC,KAAK,YAAYM,EAAU,EAAqBN,CAAK,CACxD,CAQD,MAAMO,EAAGC,EAAGC,EAAGC,EAAgB,GAAO,CAClC,MAAMC,EAAS,KAAK,iBAEdC,EAAWzC,EAAK,SAAS,CAAC,EAChCU,EAAO,aAAa0B,EAAGC,EAAGC,EAAGG,CAAQ,EACrCA,EAAS,cAAcD,EAAQA,CAAM,EAErCC,EAAS,OAAM,EACf,UAAWC,KAAS,KAAK,SAAU,CAC/B,MAAMC,EAAKD,EAAM,iBACjBC,EAAG,cAAcF,EAAUE,CAAE,EAC7BA,EAAG,gBAAgB,GAAIP,CAAC,EACxBO,EAAG,gBAAgB,GAAIN,CAAC,EACxBM,EAAG,gBAAgB,GAAIL,CAAC,EACxBI,EAAM,yBAAwB,CACjC,CAED,GADA,KAAK,yBAAwB,EACzBH,EACA,UAAWG,KAAS,KAAK,SACrBA,EAAM,MAAMN,EAAGC,EAAGC,EAAGC,CAAa,CAG7C,CAKD,SAASK,EAAO,CACZ,KAAK,WAAU,EACf,KAAK,cAAc,SAASA,CAAK,EACjC,KAAK,uBAAsB,CAC9B,CAKD,UAAW,CACP,YAAK,WAAU,EACR,KAAK,aACf,CAKD,cAAcC,EAAQ,CAClB,KAAK,WAAU,EACfA,EAAO,SAAS,KAAK,aAAa,CACrC,CASD,gBAAgBC,EAAKC,EAAOC,EAAMpB,EAAQ,EAAqBC,EAAO,CAClE,GAAID,IAAU,EAAqB,CAC/B,MAAMqB,EAAOjD,EAAK,SAClBmB,EAAW,0BAA0B2B,EAAKC,EAAOC,EAAMC,CAAI,EAC3D,KAAK,sBAAsBA,EAAMrB,EAAOC,CAAK,EAC7C,MACH,CACD,MAAMqB,EAAYlD,EAAK,SAAS,CAAC,EACjC,GAAI,CAAC,KAAK,uCAAuCkD,EAAWrB,CAAK,EAC7D,OAEJ,MAAMsB,EAASnD,EAAK,SAAS,CAAC,EAC9BU,EAAO,0BAA0BoC,EAAKC,EAAOC,EAAMG,CAAM,EACzDD,EAAU,cAAcC,EAAQA,CAAM,EACtC,KAAK,kBAAkBA,EAAQvB,EAAOC,CAAK,CAC9C,CAQD,OAAOuB,EAAMC,EAAQzB,EAAQ,EAAqBC,EAAO,CACrD,MAAMyB,EAAOtD,EAAK,SAAS,CAAC,EAC5BsD,EAAK,yBAAyB,EAAG,EAAG,CAAC,EACrC5C,EAAO,kBAAkB0C,EAAMC,EAAQC,CAAI,EAC3C,KAAK,kBAAkBA,EAAM1B,EAAOC,CAAK,CAC5C,CAQD,aAAauB,EAAMG,EAAO3B,EAAQ,EAAqBC,EAAO,CAC1D,GAAID,IAAU,EAAqB,CAC/B,MAAMqB,EAAOjD,EAAK,SAClBmB,EAAW,kBAAkBiC,EAAMG,EAAON,CAAI,EAC9C,KAAK,sBAAsBA,EAAMrB,EAAOC,CAAK,EAC7C,MACH,CACD,MAAMqB,EAAYlD,EAAK,SAAS,CAAC,EACjC,GAAI,CAAC,KAAK,uCAAuCkD,EAAWrB,CAAK,EAC7D,OAEJ,MAAMsB,EAASnD,EAAK,SAAS,CAAC,EAC9BU,EAAO,kBAAkB0C,EAAMG,EAAOJ,CAAM,EAC5CD,EAAU,cAAcC,EAAQA,CAAM,EACtC,KAAK,kBAAkBA,EAAQvB,EAAOC,CAAK,CAC9C,CAOD,YAAY2B,EAAU5B,EAAQ,EAAqBC,EAAO,CACtD,KAAK,gBAAgB2B,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAG5B,EAAOC,CAAK,CACxE,CAOD,sBAAsBoB,EAAMrB,EAAQ,EAAqBC,EAAO,CAC5D,GAAID,IAAU,EAAqB,CAC/B,KAAK,WAAU,EACf,KAAK,eAAe,SAASqB,CAAI,EACjC,KAAK,uBAAsB,EAC3B,MACH,CACD,MAAMC,EAAYlD,EAAK,SAAS,CAAC,EACjC,GAAI,CAAC,KAAK,uCAAuCkD,EAAWrB,CAAK,EAC7D,OAEJ,MAAMsB,EAASnD,EAAK,SAAS,CAAC,EAC9BU,EAAO,oBAAoBuC,EAAME,CAAM,EACvCD,EAAU,cAAcC,EAAQA,CAAM,EACtC,KAAK,kBAAkBA,EAAQvB,EAAOC,CAAK,CAC9C,CAOD,kBAAkBsB,EAAQvB,EAAQ,EAAqBC,EAAO,CAC1D,GAAID,IAAU,EAAqB,CAC/B,MAAMqB,EAAOjD,EAAK,SAClBmB,EAAW,wBAAwBgC,EAAQF,CAAI,EAC/C,KAAK,sBAAsBA,EAAMrB,EAAOC,CAAK,EAC7C,MACH,CACD,MAAMqB,EAAYlD,EAAK,SAAS,CAAC,EACjC,GAAI,CAAC,KAAK,uCAAuCkD,EAAWrB,CAAK,EAC7D,OAEJ,MAAM4B,EAAUzD,EAAK,SAAS,CAAC,EAC/ByD,EAAQ,SAASN,CAAM,EACvBD,EAAU,cAAcC,EAAQM,CAAO,EACvC,KAAK,kBAAkBA,EAAS7B,EAAOC,CAAK,CAC/C,CACD,kBAAkByB,EAAM1B,EAAQ,EAAqBC,EAAO,CACxD,MAAM6B,EAAO,KAAK,iBACZC,EAAKD,EAAK,EAAE,EAAE,EACdE,EAAKF,EAAK,EAAE,EAAE,EACdG,EAAKH,EAAK,EAAE,EAAE,EACd9C,EAAS,KAAK,YACdkD,EAAc9D,EAAK,SAAS,CAAC,EAC7B+D,EAAiB/D,EAAK,SAAS,CAAC,EAClCY,GAAUgB,GAAS,GACfC,GACAiC,EAAY,SAASjC,EAAM,eAAgB,CAAA,EAC3CjB,EAAO,kBAAmB,EAAC,cAAckD,EAAaA,CAAW,GAGjEA,EAAY,SAASlD,EAAO,kBAAmB,CAAA,EAEnDmD,EAAe,SAASD,CAAW,EACnCC,EAAe,OAAM,EACrBL,EAAK,cAAcI,EAAaJ,CAAI,EACpCA,EAAK,cAAcJ,EAAMI,CAAI,EAC7BA,EAAK,cAAcK,EAAgBL,CAAI,GAGnC9B,GAAS,GAAuBC,GAChCiC,EAAY,SAASjC,EAAM,eAAgB,CAAA,EAC3CkC,EAAe,SAASD,CAAW,EACnCC,EAAe,OAAM,EACrBL,EAAK,cAAcI,EAAaJ,CAAI,EACpCA,EAAK,cAAcJ,EAAMI,CAAI,EAC7BA,EAAK,cAAcK,EAAgBL,CAAI,GAGvCA,EAAK,cAAcJ,EAAMI,CAAI,EAGrCA,EAAK,yBAAyBC,EAAIC,EAAIC,CAAE,EACxC,KAAK,wBAAuB,EAC5B,KAAK,yBAAwB,CAChC,CACD,uCAAuCX,EAAWrB,EAAO,CACrD,MAAMmC,EAAchE,EAAK,SAAS,CAAC,EAUnC,OATAkD,EAAU,SAAS,KAAK,kBAAmB,CAAA,EACvCrB,GACAqB,EAAU,cAAcrB,EAAM,eAAgB,EAAEqB,CAAS,EACzDxC,EAAO,aAAamB,EAAM,QAAQ,EAAGA,EAAM,QAAQ,EAAGA,EAAM,QAAQ,EAAGmC,CAAW,GAGlFtD,EAAO,cAAcsD,CAAW,EAEpCd,EAAU,OAAM,EACZ,MAAMA,EAAU,EAAE,CAAC,CAAC,EAGb,IAEXc,EAAY,gBAAgB,EAAG,KAAK,mBAAmB,EACvDd,EAAU,cAAcc,EAAad,CAAS,EACvC,GACV,CAOD,YAAYtB,EAAQ,EAAqBC,EAAQ,KAAM,CACnD,MAAMoC,EAAMzC,EAAQ,OACpB,YAAK,iBAAiBI,EAAOC,EAAOoC,CAAG,EAChCA,CACV,CAOD,iBAAiBrC,EAAQ,EAAqBC,EAAOgB,EAAQ,CACzD,GAAIjB,GAAS,EAAqB,CAC9B,MAAMG,EAAK,KAAK,iBAChBc,EAAO,EAAId,EAAG,EAAE,EAAE,EAClBc,EAAO,EAAId,EAAG,EAAE,EAAE,EAClBc,EAAO,EAAId,EAAG,EAAE,EAAE,CACrB,KACI,CACD,IAAIC,EAAK,KAELH,IACAG,EAAKH,EAAM,kBAEf,KAAK,UAAU,0BACf,IAAII,EAAOjC,EAAK,SAAS,CAAC,EACtB6B,GAASG,GACTC,EAAK,SAAS,KAAK,kBAAmB,CAAA,EACtCA,EAAK,cAAcD,EAAIC,CAAI,GAG3BA,EAAO,KAAK,oBAEhBY,EAAO,EAAIZ,EAAK,EAAE,EAAE,EACpBY,EAAO,EAAIZ,EAAK,EAAE,EAAE,EACpBY,EAAO,EAAIZ,EAAK,EAAE,EAAE,CACvB,CACJ,CAMD,oBAAoBJ,EAAQ,KAAM,CAC9B,MAAMoC,EAAMzC,EAAQ,OACpB,YAAK,iBAAiB,EAAqBK,EAAOoC,CAAG,EAC9CA,CACV,CAMD,yBAAyBpC,EAAOgB,EAAQ,CACpC,KAAK,iBAAiB,EAAqBhB,EAAOgB,CAAM,CAC3D,CAID,yBAA0B,CAEtB,GADA,KAAK,SAAQ,EACT,KAAK,OACL,KAAK,aAAa,cAAc,KAAK,OAAO,gBAAiB,KAAK,eAAe,MAEhF,CACD,KAAK,gBAAgB,SAAS,KAAK,YAAY,EAC/C,MAAMqB,EAAa,KAAK,UAAU,cAAa,EAC3CA,GACA,KAAK,gBAAgB,cAAcA,EAAY,KAAK,eAAe,CAE1E,CACD,MAAMC,EAAW,KAAK,SAChBC,EAAMD,EAAS,OACrB,QAASE,EAAI,EAAGA,EAAID,EAAKC,IACrBF,EAASE,CAAC,EAAE,yBAEnB,CAKD,2BAA4B,CACxB,KAAK,wBAAuB,CAC/B,CAOD,aAAaC,EAAWzC,EAAQ,KAAM,CAClC,MAAMgB,EAASrB,EAAQ,OACvB,YAAK,kBAAkB8C,EAAWzC,EAAOgB,CAAM,EACxCA,CACV,CAOD,kBAAkByB,EAAWzC,EAAQ,KAAMgB,EAAQ,CAC/C,IAAIb,EAAK,KAELH,IACAG,EAAKH,EAAM,kBAEf,KAAK,UAAU,0BACf,MAAM0C,EAAMvE,EAAK,SAAS,CAAC,EAC3BuE,EAAI,SAAS,KAAK,kBAAmB,CAAA,EACjC1C,GAASG,GACTuC,EAAI,cAAcvC,EAAIuC,CAAG,EAE7B/C,EAAQ,qBAAqB8C,EAAWC,EAAK1B,CAAM,EACnDA,EAAO,UAAS,CACnB,CAOD,YAAYjB,EAAQ,EAAqBC,EAAQ,KAAM,CACnD,MAAMgB,EAASrB,EAAQ,OACvB,YAAK,iBAAiBI,EAAOC,EAAOgB,CAAM,EACnCA,CACV,CAOD,iBAAiBjB,EAAQ,EAAqBC,EAAQ,KAAMgB,EAAQ,CAChE,MAAMI,EAAOjD,EAAK,SAClB,KAAK,2BAA2B4B,EAAOC,EAAOoB,CAAI,EAClDA,EAAK,mBAAmBJ,CAAM,CACjC,CAOD,sBAAsBjB,EAAQ,EAAqBC,EAAQ,KAAM,CAC7D,MAAMgB,EAAS1B,EAAW,WAC1B,YAAK,2BAA2BS,EAAOC,EAAOgB,CAAM,EAC7CA,CACV,CAOD,2BAA2BjB,EAAQ,EAAqBC,EAAQ,KAAMgB,EAAQ,CAC1E,GAAIjB,GAAS,EACT,KAAK,WAAU,EACfiB,EAAO,SAAS,KAAK,cAAc,MAElC,CACD,MAAM0B,EAAMvE,EAAK,SAAS,CAAC,EACrBwE,EAAO,KAAK,oBACd3C,EACA2C,EAAK,cAAc3C,EAAM,eAAgB,EAAE0C,CAAG,EAG9CA,EAAI,SAASC,CAAI,EAErBD,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,UAAU,OAAW1B,EAAQ,MAAS,CAC7C,CACJ,CAOD,kBAAkBjB,EAAQ,EAAqBC,EAAO,CAClD,MAAMgB,EAASnC,EAAO,WACtB,YAAK,uBAAuBkB,EAAOC,EAAOgB,CAAM,EACzCA,CACV,CAOD,uBAAuBjB,EAAQ,EAAqBC,EAAOgB,EAAQ,CAC/D,GAAIjB,GAAS,EACT,KAAK,eAAc,EAAG,uBAAuBiB,CAAM,MAElD,CACD,MAAM0B,EAAMvE,EAAK,SAAS,CAAC,EACrBwE,EAAO,KAAK,oBACd3C,EACA2C,EAAK,cAAc3C,EAAM,eAAgB,EAAE0C,CAAG,EAG9CA,EAAI,SAASC,CAAI,EAErBD,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,gBAAgB,EAAG,KAAK,mBAAmB,EAC/CA,EAAI,uBAAuB1B,CAAM,CACpC,CACJ,CAOD,6BAA6BV,EAAUN,EAAQ,KAAM,CACjD,MAAMgB,EAASrB,EAAQ,OACvB,YAAK,kCAAkCW,EAAUN,EAAOgB,CAAM,EACvDA,CACV,CAOD,kCAAkCV,EAAUN,EAAQ,KAAMgB,EAAQ,CAC9D,IAAIb,EAAK,KAELH,IACAG,EAAKH,EAAM,kBAEf,KAAK,UAAU,0BACf,MAAMI,EAAOjC,EAAK,SAAS,CAAC,EAC5BiC,EAAK,SAAS,KAAK,kBAAmB,CAAA,EAClCJ,GAASG,GACTC,EAAK,cAAcD,EAAIC,CAAI,EAE/BT,EAAQ,0BAA0BW,EAAUF,EAAMY,CAAM,CAC3D,CAOD,6BAA6BV,EAAUN,EAAQ,KAAM,CACjD,MAAMgB,EAASrB,EAAQ,OACvB,YAAK,kCAAkCW,EAAUN,EAAOgB,CAAM,EACvDA,CACV,CAOD,kCAAkCV,EAAUN,EAAQ,KAAMgB,EAAQ,CAC9D,IAAIb,EAAK,KAELH,IACAG,EAAKH,EAAM,kBAEf,KAAK,UAAU,0BACf,MAAMI,EAAOjC,EAAK,SAAS,CAAC,EAC5BiC,EAAK,SAAS,KAAK,kBAAmB,CAAA,EAClCJ,GAASG,GACTC,EAAK,cAAcD,EAAIC,CAAI,EAE/BA,EAAK,OAAM,EACXT,EAAQ,0BAA0BW,EAAUF,EAAMY,CAAM,CAC3D,CAID,sBAAuB,CACnB,KAAK,cAAc,KAAK,eAAgB,CAAA,CAC3C,CAID,SAAU,CACN,KAAK,qBAAuB,KAC5B,MAAMpC,EAAQ,KAAK,UAAU,MAAM,QAAQ,IAAI,EAI/C,GAHIA,IAAU,IACV,KAAK,UAAU,MAAM,OAAOA,EAAO,CAAC,EAEpC,KAAK,aAAe,KAAK,YAAY,SAAU,CAC/C,MAAM0D,EAAW,KAAK,YAAY,SAC5B1D,EAAQ0D,EAAS,QAAQ,IAAI,EAC/B1D,IAAU,IACV0D,EAAS,OAAO1D,EAAO,CAAC,CAE/B,CACD,MAAM,QAAO,CAChB,CACL,CACAT,EAAK,SAAWyE,EAAW,EAAGjD,EAAQ,IAAI,EAC1CxB,EAAK,SAAWmB,EAAW,WAC3BnB,EAAK,SAAWyE,EAAW,EAAG/D,EAAO,QAAQ", "x_google_ignoreList": [0]}