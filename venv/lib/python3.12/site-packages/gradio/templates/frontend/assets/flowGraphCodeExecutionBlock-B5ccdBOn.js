import{F as u}from"./KHR_interactivity-DVSiPm30.js";import{R as e}from"./declarationMapper-r-RREw_K.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./index-Cb4A4-Xi.js";import"./objectModelMapping-D3Nr8hfO.js";class h extends u{constructor(t){super(t),this.config=t,this.executionFunction=this.registerDataInput("function",e),this.value=this.registerDataInput("value",e),this.result=this.registerDataOutput("result",e)}_updateOutputs(t){const s=this.executionFunction.getValue(t),i=this.value.getValue(t);s&&this.result.setValue(s(i,t),t)}getClassName(){return"FlowGraphCodeExecutionBlock"}}export{h as FlowGraphCodeExecutionBlock};
//# sourceMappingURL=flowGraphCodeExecutionBlock-B5ccdBOn.js.map
