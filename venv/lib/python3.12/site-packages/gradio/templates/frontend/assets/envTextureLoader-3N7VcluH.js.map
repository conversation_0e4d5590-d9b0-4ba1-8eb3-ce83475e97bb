{"version": 3, "file": "envTextureLoader-3N7VcluH.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/envTextureLoader.js"], "sourcesContent": ["import { GetEnvInfo, UploadEnvLevelsAsync, UploadEnvSpherical } from \"../../../Misc/environmentTextureTools.js\";\n/**\n * Implementation of the ENV Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _ENVTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param createPolynomials will be true if polynomials have been requested\n     * @param onLoad defines the callback to trigger once the texture is ready\n     * @param onError defines the callback to trigger in case of error\n     */\n    loadCubeData(data, texture, createPolynomials, onLoad, onError) {\n        if (Array.isArray(data)) {\n            return;\n        }\n        const info = GetEnvInfo(data);\n        if (info) {\n            texture.width = info.width;\n            texture.height = info.width;\n            try {\n                UploadEnvSpherical(texture, info);\n                UploadEnvLevelsAsync(texture, data, info).then(() => {\n                    texture.isReady = true;\n                    texture.onLoadedObservable.notifyObservers(texture);\n                    texture.onLoadedObservable.clear();\n                    if (onLoad) {\n                        onLoad();\n                    }\n                }, (reason) => {\n                    onError?.(\"Can not upload environment levels\", reason);\n                });\n            }\n            catch (e) {\n                onError?.(\"Can not upload environment file\", e);\n            }\n        }\n        else if (onError) {\n            onError(\"Can not parse the environment file\", null);\n        }\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     */\n    loadData() {\n        // eslint-disable-next-line no-throw-literal\n        throw \".env not supported in 2d.\";\n    }\n}\n//# sourceMappingURL=envTextureLoader.js.map"], "names": ["_ENVTextureLoader", "data", "texture", "createPolynomials", "onLoad", "onError", "info", "GetEnvInfo", "UploadEnvSpherical", "UploadEnvLevelsAsync", "reason", "e"], "mappings": "2LAMO,MAAMA,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CASD,aAAaC,EAAMC,EAASC,EAAmBC,EAAQC,EAAS,CAC5D,GAAI,MAAM,QAAQJ,CAAI,EAClB,OAEJ,MAAMK,EAAOC,EAAWN,CAAI,EAC5B,GAAIK,EAAM,CACNJ,EAAQ,MAAQI,EAAK,MACrBJ,EAAQ,OAASI,EAAK,MACtB,GAAI,CACAE,EAAmBN,EAASI,CAAI,EAChCG,EAAqBP,EAASD,EAAMK,CAAI,EAAE,KAAK,IAAM,CACjDJ,EAAQ,QAAU,GAClBA,EAAQ,mBAAmB,gBAAgBA,CAAO,EAClDA,EAAQ,mBAAmB,QACvBE,GACAA,GAEP,EAAGM,GAAW,CACXL,IAAU,oCAAqCK,CAAM,CACzE,CAAiB,CACJ,OACMC,EAAG,CACNN,IAAU,kCAAmCM,CAAC,CACjD,CACJ,MACQN,GACLA,EAAQ,qCAAsC,IAAI,CAEzD,CAID,UAAW,CAEP,KAAM,2BACT,CACL", "x_google_ignoreList": [0]}