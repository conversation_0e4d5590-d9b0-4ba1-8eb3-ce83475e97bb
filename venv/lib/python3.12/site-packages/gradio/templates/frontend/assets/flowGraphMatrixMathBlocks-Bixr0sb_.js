import{F as M}from"./KHR_interactivity-DVSiPm30.js";import{g as e,b as T,h as g,e as u,i as w,c as k}from"./declarationMapper-r-RREw_K.js";import{M as d,R as r,V as n,Q as x}from"./index-Cb4A4-Xi.js";import{F as V}from"./flowGraphUnaryOperationBlock-BsZVallq.js";import{F as D}from"./flowGraphBinaryOperationBlock-DaBPP43z.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";import"./flowGraphCachedOperationBlock-D--7yusk.js";class b extends V{constructor(t){super(e(t?.matrixType||"Matrix"),e(t?.matrixType||"Matrix"),i=>i.transpose?i.transpose():d.Transpose(i),"FlowGraphTransposeBlock",t)}}r("FlowGraphTransposeBlock",b);class E extends V{constructor(t){super(e(t?.matrixType||"Matrix"),T,i=>i.determinant(),"FlowGraphDeterminantBlock",t)}}r("FlowGraphDeterminantBlock",E);class _ extends V{constructor(t){super(e(t?.matrixType||"Matrix"),e(t?.matrixType||"Matrix"),i=>i.inverse?i.inverse():d.Invert(i),"FlowGraphInvertMatrixBlock",t)}}r("FlowGraphInvertMatrixBlock",_);class v extends D{constructor(t){super(e(t?.matrixType||"Matrix"),e(t?.matrixType||"Matrix"),e(t?.matrixType||"Matrix"),(i,a)=>a.multiply(i),"FlowGraphMatrixMultiplicationBlock",t)}}r("FlowGraphMatrixMultiplicationBlock",v);class Q extends M{constructor(t){super(t),this.input=this.registerDataInput("input",g),this.position=this.registerDataOutput("position",u),this.rotationQuaternion=this.registerDataOutput("rotationQuaternion",w),this.scaling=this.registerDataOutput("scaling",u),this.isValid=this.registerDataOutput("isValid",k,!1)}_updateOutputs(t){const i=t._getExecutionVariable(this,"executionId",-1),a=t._getExecutionVariable(this,"cachedPosition",null),s=t._getExecutionVariable(this,"cachedRotation",null),p=t._getExecutionVariable(this,"cachedScaling",null);if(i===t.executionId&&a&&s&&p)this.position.setValue(a,t),this.rotationQuaternion.setValue(s,t),this.scaling.setValue(p,t);else{const l=this.input.getValue(t),h=a||new n,c=s||new x,m=p||new n,F=Math.round(l.m[3]*1e4)/1e4,G=Math.round(l.m[7]*1e4)/1e4,y=Math.round(l.m[11]*1e4)/1e4,B=Math.round(l.m[15]*1e4)/1e4;if(F!==0||G!==0||y!==0||B!==1){this.isValid.setValue(!1,t),this.position.setValue(n.Zero(),t),this.rotationQuaternion.setValue(x.Identity(),t),this.scaling.setValue(n.One(),t);return}const I=l.decompose(m,c,h);this.isValid.setValue(I,t),this.position.setValue(h,t),this.rotationQuaternion.setValue(c,t),this.scaling.setValue(m,t),t._setExecutionVariable(this,"cachedPosition",h),t._setExecutionVariable(this,"cachedRotation",c),t._setExecutionVariable(this,"cachedScaling",m),t._setExecutionVariable(this,"executionId",t.executionId)}}getClassName(){return"FlowGraphMatrixDecompose"}}r("FlowGraphMatrixDecompose",Q);class R extends M{constructor(t){super(t),this.position=this.registerDataInput("position",u),this.rotationQuaternion=this.registerDataInput("rotationQuaternion",w),this.scaling=this.registerDataInput("scaling",u),this.value=this.registerDataOutput("value",g)}_updateOutputs(t){const i=t._getExecutionVariable(this,"executionId",-1),a=t._getExecutionVariable(this,"cachedMatrix",null);if(i===t.executionId&&a)this.value.setValue(a,t);else{const s=d.Compose(this.scaling.getValue(t),this.rotationQuaternion.getValue(t),this.position.getValue(t));this.value.setValue(s,t),t._setExecutionVariable(this,"cachedMatrix",s),t._setExecutionVariable(this,"executionId",t.executionId)}}getClassName(){return"FlowGraphMatrixCompose"}}r("FlowGraphMatrixCompose",R);export{E as FlowGraphDeterminantBlock,_ as FlowGraphInvertMatrixBlock,R as FlowGraphMatrixComposeBlock,Q as FlowGraphMatrixDecomposeBlock,v as FlowGraphMatrixMultiplicationBlock,b as FlowGraphTransposeBlock};
//# sourceMappingURL=flowGraphMatrixMathBlocks-Bixr0sb_.js.map
