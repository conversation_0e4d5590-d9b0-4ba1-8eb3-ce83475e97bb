import{b as l}from"./KHR_interactivity-DVSiPm30.js";import{R as a}from"./declarationMapper-r-RREw_K.js";import{R as u}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class h extends l{constructor(t){super(t),this.config=t,this.object=this.registerDataInput("object",a,t.target),this.value=this.registerDataInput("value",a),this.propertyName=this.registerDataInput("propertyName",a,t.propertyName),this.customSetFunction=this.registerDataInput("customSetFunction",a)}_execute(t,p){try{const s=this.object.getValue(t),r=this.value.getValue(t),e=this.customSetFunction.getValue(t);e?e(s,this.propertyName.getValue(t),r,t):this._setPropertyValue(s,this.propertyName.getValue(t),r)}catch(s){this._reportError(t,s)}this.out._activateSignal(t)}_setPropertyValue(t,p,s){const r=p.split(".");let e=t;for(let i=0;i<r.length-1;i++){const o=r[i];e[o]===void 0&&(e[o]={}),e=e[o]}e[r[r.length-1]]=s}getClassName(){return"FlowGraphSetPropertyBlock"}}u("FlowGraphSetPropertyBlock",h);export{h as FlowGraphSetPropertyBlock};
//# sourceMappingURL=flowGraphSetPropertyBlock-BwaB_U6k.js.map
