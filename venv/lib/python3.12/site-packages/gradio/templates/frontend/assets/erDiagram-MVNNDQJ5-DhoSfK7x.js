import{g as vt,s as Dt}from"./chunk-2O5F6CEG-BJ0hfvv0.js";import{_ as l,s as wt,g as Vt,c as Lt,b as Mt,n as Bt,o as Ft,d as $,l as D,t as Yt,r as Pt,B as zt,y as Gt,z as Kt,u as Zt,A as Ut}from"./mermaid.core-DGK6UhOk.js";import{c as jt}from"./channel-BVcFyT3i.js";import{s as Wt}from"./select-BigU4G0v.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./dispatch-kxCwF96_.js";import"./step-Ce-xBr2D.js";var ut=function(){var e=l(function(N,n,a,c){for(a=a||{},c=N.length;c--;a[N[c]]=n);return a},"o"),i=[6,8,10,22,24,26,28,33,34,35,36,37,40,43,44,50],d=[1,10],o=[1,11],h=[1,12],p=[1,13],R=[1,20],g=[1,21],m=[1,22],w=[1,23],K=[1,24],k=[1,19],tt=[1,25],Z=[1,26],S=[1,18],V=[1,33],et=[1,34],st=[1,35],it=[1,36],rt=[1,37],dt=[6,8,10,13,15,17,20,21,22,24,26,28,33,34,35,36,37,40,43,44,50,63,64,65,66,67],T=[1,42],O=[1,43],L=[1,52],M=[40,50,68,69],B=[1,63],F=[1,61],A=[1,58],Y=[1,62],P=[1,64],U=[6,8,10,13,17,22,24,26,28,33,34,35,36,37,40,41,42,43,44,48,49,50,63,64,65,66,67],pt=[63,64,65,66,67],ft=[1,81],yt=[1,80],_t=[1,78],gt=[1,79],bt=[6,10,42,47],C=[6,10,13,41,42,47,48,49],j=[1,89],W=[1,88],Q=[1,87],z=[19,56],mt=[1,98],Et=[1,97],nt=[19,56,58,60],at={trace:l(function(){},"trace"),yy:{},symbols_:{error:2,start:3,ER_DIAGRAM:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,entityName:11,relSpec:12,COLON:13,role:14,STYLE_SEPARATOR:15,idList:16,BLOCK_START:17,attributes:18,BLOCK_STOP:19,SQS:20,SQE:21,title:22,title_value:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,direction:29,classDefStatement:30,classStatement:31,styleStatement:32,direction_tb:33,direction_bt:34,direction_rl:35,direction_lr:36,CLASSDEF:37,stylesOpt:38,separator:39,UNICODE_TEXT:40,STYLE_TEXT:41,COMMA:42,CLASS:43,STYLE:44,style:45,styleComponent:46,SEMI:47,NUM:48,BRKT:49,ENTITY_NAME:50,attribute:51,attributeType:52,attributeName:53,attributeKeyTypeList:54,attributeComment:55,ATTRIBUTE_WORD:56,attributeKeyType:57,",":58,ATTRIBUTE_KEY:59,COMMENT:60,cardinality:61,relType:62,ZERO_OR_ONE:63,ZERO_OR_MORE:64,ONE_OR_MORE:65,ONLY_ONE:66,MD_PARENT:67,NON_IDENTIFYING:68,IDENTIFYING:69,WORD:70,$accept:0,$end:1},terminals_:{2:"error",4:"ER_DIAGRAM",6:"EOF",8:"SPACE",10:"NEWLINE",13:"COLON",15:"STYLE_SEPARATOR",17:"BLOCK_START",19:"BLOCK_STOP",20:"SQS",21:"SQE",22:"title",23:"title_value",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"direction_tb",34:"direction_bt",35:"direction_rl",36:"direction_lr",37:"CLASSDEF",40:"UNICODE_TEXT",41:"STYLE_TEXT",42:"COMMA",43:"CLASS",44:"STYLE",47:"SEMI",48:"NUM",49:"BRKT",50:"ENTITY_NAME",56:"ATTRIBUTE_WORD",58:",",59:"ATTRIBUTE_KEY",60:"COMMENT",63:"ZERO_OR_ONE",64:"ZERO_OR_MORE",65:"ONE_OR_MORE",66:"ONLY_ONE",67:"MD_PARENT",68:"NON_IDENTIFYING",69:"IDENTIFYING",70:"WORD"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,9],[9,7],[9,7],[9,4],[9,6],[9,3],[9,5],[9,1],[9,3],[9,7],[9,9],[9,6],[9,8],[9,4],[9,6],[9,2],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[9,1],[29,1],[29,1],[29,1],[29,1],[30,4],[16,1],[16,1],[16,3],[16,3],[31,3],[32,4],[38,1],[38,3],[45,1],[45,2],[39,1],[39,1],[39,1],[46,1],[46,1],[46,1],[46,1],[11,1],[11,1],[18,1],[18,2],[51,2],[51,3],[51,3],[51,4],[52,1],[53,1],[54,1],[54,3],[57,1],[55,1],[12,3],[61,1],[61,1],[61,1],[61,1],[61,1],[62,1],[62,1],[14,1],[14,1],[14,1]],performAction:l(function(n,a,c,r,u,t,G){var s=t.length-1;switch(u){case 1:break;case 2:this.$=[];break;case 3:t[s-1].push(t[s]),this.$=t[s-1];break;case 4:case 5:this.$=t[s];break;case 6:case 7:this.$=[];break;case 8:r.addEntity(t[s-4]),r.addEntity(t[s-2]),r.addRelationship(t[s-4],t[s],t[s-2],t[s-3]);break;case 9:r.addEntity(t[s-8]),r.addEntity(t[s-4]),r.addRelationship(t[s-8],t[s],t[s-4],t[s-5]),r.setClass([t[s-8]],t[s-6]),r.setClass([t[s-4]],t[s-2]);break;case 10:r.addEntity(t[s-6]),r.addEntity(t[s-2]),r.addRelationship(t[s-6],t[s],t[s-2],t[s-3]),r.setClass([t[s-6]],t[s-4]);break;case 11:r.addEntity(t[s-6]),r.addEntity(t[s-4]),r.addRelationship(t[s-6],t[s],t[s-4],t[s-5]),r.setClass([t[s-4]],t[s-2]);break;case 12:r.addEntity(t[s-3]),r.addAttributes(t[s-3],t[s-1]);break;case 13:r.addEntity(t[s-5]),r.addAttributes(t[s-5],t[s-1]),r.setClass([t[s-5]],t[s-3]);break;case 14:r.addEntity(t[s-2]);break;case 15:r.addEntity(t[s-4]),r.setClass([t[s-4]],t[s-2]);break;case 16:r.addEntity(t[s]);break;case 17:r.addEntity(t[s-2]),r.setClass([t[s-2]],t[s]);break;case 18:r.addEntity(t[s-6],t[s-4]),r.addAttributes(t[s-6],t[s-1]);break;case 19:r.addEntity(t[s-8],t[s-6]),r.addAttributes(t[s-8],t[s-1]),r.setClass([t[s-8]],t[s-3]);break;case 20:r.addEntity(t[s-5],t[s-3]);break;case 21:r.addEntity(t[s-7],t[s-5]),r.setClass([t[s-7]],t[s-2]);break;case 22:r.addEntity(t[s-3],t[s-1]);break;case 23:r.addEntity(t[s-5],t[s-3]),r.setClass([t[s-5]],t[s]);break;case 24:case 25:this.$=t[s].trim(),r.setAccTitle(this.$);break;case 26:case 27:this.$=t[s].trim(),r.setAccDescription(this.$);break;case 32:r.setDirection("TB");break;case 33:r.setDirection("BT");break;case 34:r.setDirection("RL");break;case 35:r.setDirection("LR");break;case 36:this.$=t[s-3],r.addClass(t[s-2],t[s-1]);break;case 37:case 38:case 56:case 64:this.$=[t[s]];break;case 39:case 40:this.$=t[s-2].concat([t[s]]);break;case 41:this.$=t[s-2],r.setClass(t[s-1],t[s]);break;case 42:this.$=t[s-3],r.addCssStyles(t[s-2],t[s-1]);break;case 43:this.$=[t[s]];break;case 44:t[s-2].push(t[s]),this.$=t[s-2];break;case 46:this.$=t[s-1]+t[s];break;case 54:case 76:case 77:this.$=t[s].replace(/"/g,"");break;case 55:case 78:this.$=t[s];break;case 57:t[s].push(t[s-1]),this.$=t[s];break;case 58:this.$={type:t[s-1],name:t[s]};break;case 59:this.$={type:t[s-2],name:t[s-1],keys:t[s]};break;case 60:this.$={type:t[s-2],name:t[s-1],comment:t[s]};break;case 61:this.$={type:t[s-3],name:t[s-2],keys:t[s-1],comment:t[s]};break;case 62:case 63:case 66:this.$=t[s];break;case 65:t[s-2].push(t[s]),this.$=t[s-2];break;case 67:this.$=t[s].replace(/"/g,"");break;case 68:this.$={cardA:t[s],relType:t[s-1],cardB:t[s-2]};break;case 69:this.$=r.Cardinality.ZERO_OR_ONE;break;case 70:this.$=r.Cardinality.ZERO_OR_MORE;break;case 71:this.$=r.Cardinality.ONE_OR_MORE;break;case 72:this.$=r.Cardinality.ONLY_ONE;break;case 73:this.$=r.Cardinality.MD_PARENT;break;case 74:this.$=r.Identification.NON_IDENTIFYING;break;case 75:this.$=r.Identification.IDENTIFYING;break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},e(i,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,22:d,24:o,26:h,28:p,29:14,30:15,31:16,32:17,33:R,34:g,35:m,36:w,37:K,40:k,43:tt,44:Z,50:S},e(i,[2,7],{1:[2,1]}),e(i,[2,3]),{9:27,11:9,22:d,24:o,26:h,28:p,29:14,30:15,31:16,32:17,33:R,34:g,35:m,36:w,37:K,40:k,43:tt,44:Z,50:S},e(i,[2,5]),e(i,[2,6]),e(i,[2,16],{12:28,61:32,15:[1,29],17:[1,30],20:[1,31],63:V,64:et,65:st,66:it,67:rt}),{23:[1,38]},{25:[1,39]},{27:[1,40]},e(i,[2,27]),e(i,[2,28]),e(i,[2,29]),e(i,[2,30]),e(i,[2,31]),e(dt,[2,54]),e(dt,[2,55]),e(i,[2,32]),e(i,[2,33]),e(i,[2,34]),e(i,[2,35]),{16:41,40:T,41:O},{16:44,40:T,41:O},{16:45,40:T,41:O},e(i,[2,4]),{11:46,40:k,50:S},{16:47,40:T,41:O},{18:48,19:[1,49],51:50,52:51,56:L},{11:53,40:k,50:S},{62:54,68:[1,55],69:[1,56]},e(M,[2,69]),e(M,[2,70]),e(M,[2,71]),e(M,[2,72]),e(M,[2,73]),e(i,[2,24]),e(i,[2,25]),e(i,[2,26]),{13:B,38:57,41:F,42:A,45:59,46:60,48:Y,49:P},e(U,[2,37]),e(U,[2,38]),{16:65,40:T,41:O,42:A},{13:B,38:66,41:F,42:A,45:59,46:60,48:Y,49:P},{13:[1,67],15:[1,68]},e(i,[2,17],{61:32,12:69,17:[1,70],42:A,63:V,64:et,65:st,66:it,67:rt}),{19:[1,71]},e(i,[2,14]),{18:72,19:[2,56],51:50,52:51,56:L},{53:73,56:[1,74]},{56:[2,62]},{21:[1,75]},{61:76,63:V,64:et,65:st,66:it,67:rt},e(pt,[2,74]),e(pt,[2,75]),{6:ft,10:yt,39:77,42:_t,47:gt},{40:[1,82],41:[1,83]},e(bt,[2,43],{46:84,13:B,41:F,48:Y,49:P}),e(C,[2,45]),e(C,[2,50]),e(C,[2,51]),e(C,[2,52]),e(C,[2,53]),e(i,[2,41],{42:A}),{6:ft,10:yt,39:85,42:_t,47:gt},{14:86,40:j,50:W,70:Q},{16:90,40:T,41:O},{11:91,40:k,50:S},{18:92,19:[1,93],51:50,52:51,56:L},e(i,[2,12]),{19:[2,57]},e(z,[2,58],{54:94,55:95,57:96,59:mt,60:Et}),e([19,56,59,60],[2,63]),e(i,[2,22],{15:[1,100],17:[1,99]}),e([40,50],[2,68]),e(i,[2,36]),{13:B,41:F,45:101,46:60,48:Y,49:P},e(i,[2,47]),e(i,[2,48]),e(i,[2,49]),e(U,[2,39]),e(U,[2,40]),e(C,[2,46]),e(i,[2,42]),e(i,[2,8]),e(i,[2,76]),e(i,[2,77]),e(i,[2,78]),{13:[1,102],42:A},{13:[1,104],15:[1,103]},{19:[1,105]},e(i,[2,15]),e(z,[2,59],{55:106,58:[1,107],60:Et}),e(z,[2,60]),e(nt,[2,64]),e(z,[2,67]),e(nt,[2,66]),{18:108,19:[1,109],51:50,52:51,56:L},{16:110,40:T,41:O},e(bt,[2,44],{46:84,13:B,41:F,48:Y,49:P}),{14:111,40:j,50:W,70:Q},{16:112,40:T,41:O},{14:113,40:j,50:W,70:Q},e(i,[2,13]),e(z,[2,61]),{57:114,59:mt},{19:[1,115]},e(i,[2,20]),e(i,[2,23],{17:[1,116],42:A}),e(i,[2,11]),{13:[1,117],42:A},e(i,[2,10]),e(nt,[2,65]),e(i,[2,18]),{18:118,19:[1,119],51:50,52:51,56:L},{14:120,40:j,50:W,70:Q},{19:[1,121]},e(i,[2,21]),e(i,[2,9]),e(i,[2,19])],defaultActions:{52:[2,62],72:[2,57]},parseError:l(function(n,a){if(a.recoverable)this.trace(n);else{var c=new Error(n);throw c.hash=a,c}},"parseError"),parse:l(function(n){var a=this,c=[0],r=[],u=[null],t=[],G=this.table,s="",q=0,kt=0,Rt=2,St=1,It=t.slice.call(arguments,1),f=Object.create(this.lexer),I={yy:{}};for(var ct in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ct)&&(I.yy[ct]=this.yy[ct]);f.setInput(n,I.yy),I.yy.lexer=f,I.yy.parser=this,typeof f.yylloc>"u"&&(f.yylloc={});var ot=f.yylloc;t.push(ot);var xt=f.options&&f.options.ranges;typeof I.yy.parseError=="function"?this.parseError=I.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ct(_){c.length=c.length-2*_,u.length=u.length-_,t.length=t.length-_}l(Ct,"popStack");function Tt(){var _;return _=r.pop()||f.lex()||St,typeof _!="number"&&(_ instanceof Array&&(r=_,_=r.pop()),_=a.symbols_[_]||_),_}l(Tt,"lex");for(var y,x,b,lt,v={},H,E,Ot,J;;){if(x=c[c.length-1],this.defaultActions[x]?b=this.defaultActions[x]:((y===null||typeof y>"u")&&(y=Tt()),b=G[x]&&G[x][y]),typeof b>"u"||!b.length||!b[0]){var ht="";J=[];for(H in G[x])this.terminals_[H]&&H>Rt&&J.push("'"+this.terminals_[H]+"'");f.showPosition?ht="Parse error on line "+(q+1)+`:
`+f.showPosition()+`
Expecting `+J.join(", ")+", got '"+(this.terminals_[y]||y)+"'":ht="Parse error on line "+(q+1)+": Unexpected "+(y==St?"end of input":"'"+(this.terminals_[y]||y)+"'"),this.parseError(ht,{text:f.match,token:this.terminals_[y]||y,line:f.yylineno,loc:ot,expected:J})}if(b[0]instanceof Array&&b.length>1)throw new Error("Parse Error: multiple actions possible at state: "+x+", token: "+y);switch(b[0]){case 1:c.push(y),u.push(f.yytext),t.push(f.yylloc),c.push(b[1]),y=null,kt=f.yyleng,s=f.yytext,q=f.yylineno,ot=f.yylloc;break;case 2:if(E=this.productions_[b[1]][1],v.$=u[u.length-E],v._$={first_line:t[t.length-(E||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(E||1)].first_column,last_column:t[t.length-1].last_column},xt&&(v._$.range=[t[t.length-(E||1)].range[0],t[t.length-1].range[1]]),lt=this.performAction.apply(v,[s,kt,q,I.yy,b[1],u,t].concat(It)),typeof lt<"u")return lt;E&&(c=c.slice(0,-1*E*2),u=u.slice(0,-1*E),t=t.slice(0,-1*E)),c.push(this.productions_[b[1]][0]),u.push(v.$),t.push(v._$),Ot=G[c[c.length-2]][c[c.length-1]],c.push(Ot);break;case 3:return!0}}return!0},"parse")},Nt=function(){var N={EOF:1,parseError:l(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:l(function(n,a){return this.yy=a||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:l(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var a=n.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:l(function(n){var a=n.length,c=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var u=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===r.length?this.yylloc.first_column:0)+r[r.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[u[0],u[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:l(function(){return this._more=!0,this},"more"),reject:l(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:l(function(n){this.unput(this.match.slice(n))},"less"),pastInput:l(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:l(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:l(function(){var n=this.pastInput(),a=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:l(function(n,a){var c,r,u;if(this.options.backtrack_lexer&&(u={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(u.yylloc.range=this.yylloc.range.slice(0))),r=n[0].match(/(?:\r\n?|\n).*/g),r&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in u)this[t]=u[t];return!1}return!1},"test_match"),next:l(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,a,c,r;this._more||(this.yytext="",this.match="");for(var u=this._currentRules(),t=0;t<u.length;t++)if(c=this._input.match(this.rules[u[t]]),c&&(!a||c[0].length>a[0].length)){if(a=c,r=t,this.options.backtrack_lexer){if(n=this.test_match(c,u[t]),n!==!1)return n;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(n=this.test_match(a,u[r]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:l(function(){var a=this.next();return a||this.lex()},"lex"),begin:l(function(a){this.conditionStack.push(a)},"begin"),popState:l(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:l(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:l(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:l(function(a){this.begin(a)},"pushState"),stateStackSize:l(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:l(function(a,c,r,u){switch(r){case 0:return this.begin("acc_title"),24;case 1:return this.popState(),"acc_title_value";case 2:return this.begin("acc_descr"),26;case 3:return this.popState(),"acc_descr_value";case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 33;case 8:return 34;case 9:return 35;case 10:return 36;case 11:return 10;case 12:break;case 13:return 8;case 14:return 50;case 15:return 70;case 16:return 4;case 17:return this.begin("block"),17;case 18:return 49;case 19:return 49;case 20:return 42;case 21:return 15;case 22:return 13;case 23:break;case 24:return 59;case 25:return 56;case 26:return 56;case 27:return 60;case 28:break;case 29:return this.popState(),19;case 30:return c.yytext[0];case 31:return 20;case 32:return 21;case 33:return this.begin("style"),44;case 34:return this.popState(),10;case 35:break;case 36:return 13;case 37:return 42;case 38:return 49;case 39:return this.begin("style"),37;case 40:return 43;case 41:return 63;case 42:return 65;case 43:return 65;case 44:return 65;case 45:return 63;case 46:return 63;case 47:return 64;case 48:return 64;case 49:return 64;case 50:return 64;case 51:return 64;case 52:return 65;case 53:return 64;case 54:return 65;case 55:return 66;case 56:return 66;case 57:return 66;case 58:return 66;case 59:return 63;case 60:return 64;case 61:return 65;case 62:return 67;case 63:return 68;case 64:return 69;case 65:return 69;case 66:return 68;case 67:return 68;case 68:return 68;case 69:return 41;case 70:return 47;case 71:return 40;case 72:return 48;case 73:return c.yytext[0];case 74:return 6}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:[\s]+)/i,/^(?:"[^"%\r\n\v\b\\]+")/i,/^(?:"[^"]*")/i,/^(?:erDiagram\b)/i,/^(?:\{)/i,/^(?:#)/i,/^(?:#)/i,/^(?:,)/i,/^(?::::)/i,/^(?::)/i,/^(?:\s+)/i,/^(?:\b((?:PK)|(?:FK)|(?:UK))\b)/i,/^(?:([^\s]*)[~].*[~]([^\s]*))/i,/^(?:([\*A-Za-z_\u00C0-\uFFFF][A-Za-z0-9\-\_\[\]\(\)\u00C0-\uFFFF\*]*))/i,/^(?:"[^"]*")/i,/^(?:[\n]+)/i,/^(?:\})/i,/^(?:.)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:style\b)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?::)/i,/^(?:,)/i,/^(?:#)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:one or zero\b)/i,/^(?:one or more\b)/i,/^(?:one or many\b)/i,/^(?:1\+)/i,/^(?:\|o\b)/i,/^(?:zero or one\b)/i,/^(?:zero or more\b)/i,/^(?:zero or many\b)/i,/^(?:0\+)/i,/^(?:\}o\b)/i,/^(?:many\(0\))/i,/^(?:many\(1\))/i,/^(?:many\b)/i,/^(?:\}\|)/i,/^(?:one\b)/i,/^(?:only one\b)/i,/^(?:1\b)/i,/^(?:\|\|)/i,/^(?:o\|)/i,/^(?:o\{)/i,/^(?:\|\{)/i,/^(?:\s*u\b)/i,/^(?:\.\.)/i,/^(?:--)/i,/^(?:to\b)/i,/^(?:optionally to\b)/i,/^(?:\.-)/i,/^(?:-\.)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:;)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:[0-9])/i,/^(?:.)/i,/^(?:$)/i],conditions:{style:{rules:[34,35,36,37,38,69,70],inclusive:!1},acc_descr_multiline:{rules:[5,6],inclusive:!1},acc_descr:{rules:[3],inclusive:!1},acc_title:{rules:[1],inclusive:!1},block:{rules:[23,24,25,26,27,28,29,30],inclusive:!1},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,31,32,33,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,74],inclusive:!0}}};return N}();at.lexer=Nt;function X(){this.yy={}}return l(X,"Parser"),X.prototype=at,at.Parser=X,new X}();ut.parser=ut;var Qt=ut,Xt=class{constructor(){this.entities=new Map,this.relationships=[],this.classes=new Map,this.direction="TB",this.Cardinality={ZERO_OR_ONE:"ZERO_OR_ONE",ZERO_OR_MORE:"ZERO_OR_MORE",ONE_OR_MORE:"ONE_OR_MORE",ONLY_ONE:"ONLY_ONE",MD_PARENT:"MD_PARENT"},this.Identification={NON_IDENTIFYING:"NON_IDENTIFYING",IDENTIFYING:"IDENTIFYING"},this.setAccTitle=wt,this.getAccTitle=Vt,this.setAccDescription=Lt,this.getAccDescription=Mt,this.setDiagramTitle=Bt,this.getDiagramTitle=Ft,this.getConfig=l(()=>$().er,"getConfig"),this.clear(),this.addEntity=this.addEntity.bind(this),this.addAttributes=this.addAttributes.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setDirection=this.setDirection.bind(this),this.addCssStyles=this.addCssStyles.bind(this),this.addClass=this.addClass.bind(this),this.setClass=this.setClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}static{l(this,"ErDB")}addEntity(e,i=""){return this.entities.has(e)?!this.entities.get(e)?.alias&&i&&(this.entities.get(e).alias=i,D.info(`Add alias '${i}' to entity '${e}'`)):(this.entities.set(e,{id:`entity-${e}-${this.entities.size}`,label:e,attributes:[],alias:i,shape:"erBox",look:$().look??"default",cssClasses:"default",cssStyles:[]}),D.info("Added new entity :",e)),this.entities.get(e)}getEntity(e){return this.entities.get(e)}getEntities(){return this.entities}getClasses(){return this.classes}addAttributes(e,i){const d=this.addEntity(e);let o;for(o=i.length-1;o>=0;o--)i[o].keys||(i[o].keys=[]),i[o].comment||(i[o].comment=""),d.attributes.push(i[o]),D.debug("Added attribute ",i[o].name)}addRelationship(e,i,d,o){const h=this.entities.get(e),p=this.entities.get(d);if(!h||!p)return;const R={entityA:h.id,roleA:i,entityB:p.id,relSpec:o};this.relationships.push(R),D.debug("Added new relationship :",R)}getRelationships(){return this.relationships}getDirection(){return this.direction}setDirection(e){this.direction=e}getCompiledStyles(e){let i=[];for(const d of e){const o=this.classes.get(d);o?.styles&&(i=[...i,...o.styles??[]].map(h=>h.trim())),o?.textStyles&&(i=[...i,...o.textStyles??[]].map(h=>h.trim()))}return i}addCssStyles(e,i){for(const d of e){const o=this.entities.get(d);if(!i||!o)return;for(const h of i)o.cssStyles.push(h)}}addClass(e,i){e.forEach(d=>{let o=this.classes.get(d);o===void 0&&(o={id:d,styles:[],textStyles:[]},this.classes.set(d,o)),i&&i.forEach(function(h){if(/color/.exec(h)){const p=h.replace("fill","bgFill");o.textStyles.push(p)}o.styles.push(h)})})}setClass(e,i){for(const d of e){const o=this.entities.get(d);if(o)for(const h of i)o.cssClasses+=" "+h}}clear(){this.entities=new Map,this.classes=new Map,this.relationships=[],Yt()}getData(){const e=[],i=[],d=$();for(const h of this.entities.keys()){const p=this.entities.get(h);p&&(p.cssCompiledStyles=this.getCompiledStyles(p.cssClasses.split(" ")),e.push(p))}let o=0;for(const h of this.relationships){const p={id:Pt(h.entityA,h.entityB,{prefix:"id",counter:o++}),type:"normal",curve:"basis",start:h.entityA,end:h.entityB,label:h.roleA,labelpos:"c",thickness:"normal",classes:"relationshipLine",arrowTypeStart:h.relSpec.cardB.toLowerCase(),arrowTypeEnd:h.relSpec.cardA.toLowerCase(),pattern:h.relSpec.relType=="IDENTIFYING"?"solid":"dashed",look:d.look};i.push(p)}return{nodes:e,edges:i,other:{},config:d,direction:"TB"}}},At={};zt(At,{draw:()=>qt});var qt=l(async function(e,i,d,o){D.info("REF0:"),D.info("Drawing er diagram (unified)",i);const{securityLevel:h,er:p,layout:R}=$(),g=o.db.getData(),m=vt(i,h);g.type=o.type,g.layoutAlgorithm=Gt(R),g.config.flowchart.nodeSpacing=p?.nodeSpacing||140,g.config.flowchart.rankSpacing=p?.rankSpacing||80,g.direction=o.db.getDirection(),g.markers=["only_one","zero_or_one","one_or_more","zero_or_more"],g.diagramId=i,await Kt(g,m),g.layoutAlgorithm==="elk"&&m.select(".edges").lower();const w=m.selectAll('[id*="-background"]');Array.from(w).length>0&&w.each(function(){const k=Wt(this),Z=k.attr("id").replace("-background",""),S=m.select(`#${CSS.escape(Z)}`);if(!S.empty()){const V=S.attr("transform");k.attr("transform",V)}});const K=8;Zt.insertTitle(m,"erDiagramTitleText",p?.titleTopMargin??25,o.db.getDiagramTitle()),Dt(m,K,"erDiagram",p?.useMaxWidth??!0)},"draw"),Ht=l((e,i)=>{const d=jt,o=d(e,"r"),h=d(e,"g"),p=d(e,"b");return Ut(o,h,p,i)},"fade"),Jt=l(e=>`
  .entityBox {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
  }

  .relationshipLabelBox {
    fill: ${e.tertiaryColor};
    opacity: 0.7;
    background-color: ${e.tertiaryColor};
      rect {
        opacity: 0.5;
      }
  }

  .labelBkg {
    background-color: ${Ht(e.tertiaryColor,.5)};
  }

  .edgeLabel .label {
    fill: ${e.nodeBorder};
    font-size: 14px;
  }

  .label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }

  .edge-pattern-dashed {
    stroke-dasharray: 8,8;
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon
  {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }

  .relationshipLine {
    stroke: ${e.lineColor};
    stroke-width: 1;
    fill: none;
  }

  .marker {
    fill: none !important;
    stroke: ${e.lineColor} !important;
    stroke-width: 1;
  }
`,"getStyles"),$t=Jt,oe={parser:Qt,get db(){return new Xt},renderer:At,styles:$t};export{oe as diagram};
//# sourceMappingURL=erDiagram-MVNNDQJ5-DhoSfK7x.js.map
