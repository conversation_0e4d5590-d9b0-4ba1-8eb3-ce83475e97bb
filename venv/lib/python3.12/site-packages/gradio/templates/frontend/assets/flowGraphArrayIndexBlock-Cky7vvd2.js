import{F as s,g as i}from"./KHR_interactivity-DVSiPm30.js";import{R as t,F as l}from"./declarationMapper-r-RREw_K.js";import{R as u}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class o extends s{constructor(e){super(e),this.config=e,this.array=this.registerDataInput("array",t),this.index=this.registerDataInput("index",t,new l(-1)),this.value=this.registerDataOutput("value",t)}_updateOutputs(e){const r=this.array.getValue(e),a=i(this.index.getValue(e));r&&a>=0&&a<r.length?this.value.setValue(r[a],e):this.value.setValue(null,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphArrayIndexBlock"}}u("FlowGraphArrayIndexBlock",o);export{o as FlowGraphArrayIndexBlock};
//# sourceMappingURL=flowGraphArrayIndexBlock-Cky7vvd2.js.map
