import{F as r}from"./KHR_interactivity-DVSiPm30.js";import{R as s,b as t}from"./declarationMapper-r-RREw_K.js";import{R as a}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./objectModelMapping-D3Nr8hfO.js";class i extends r{constructor(e){super(e),this.userVariables=this.registerDataOutput("userVariables",s),this.executionId=this.registerDataOutput("executionId",t)}_updateOutputs(e){this.userVariables.setValue(e.userVariables,e),this.executionId.setValue(e.executionId,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphContextBlock"}}a("FlowGraphContextBlock",i);export{i as FlowGraphContextBlock};
//# sourceMappingURL=flowGraphContextBlock-DRZMpl2V.js.map
