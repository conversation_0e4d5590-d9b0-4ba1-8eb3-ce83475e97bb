{"version": 3, "file": "flowGraphGetVariableBlock-BAu4mxNU.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetVariableBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * A block that gets the value of a variable.\n * Variables are an stored in the context of the flow graph.\n */\nexport class FlowGraphGetVariableBlock extends FlowGraphBlock {\n    /**\n     * Construct a FlowGraphGetVariableBlock.\n     * @param config construction parameters\n     */\n    constructor(config) {\n        super(config);\n        this.config = config;\n        // The output connection has to have the name of the variable.\n        this.value = this.registerDataOutput(\"value\", RichTypeAny, config.initialValue);\n    }\n    /**\n     * @internal\n     */\n    _updateOutputs(context) {\n        const variableNameValue = this.config.variable;\n        if (context.hasVariable(variableNameValue)) {\n            this.value.setValue(context.getVariable(variableNameValue), context);\n        }\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n        serializationObject.config.variable = this.config.variable;\n    }\n    getClassName() {\n        return \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */;\n    }\n}\nRegisterClass(\"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */, FlowGraphGetVariableBlock);\n//# sourceMappingURL=flowGraphGetVariableBlock.js.map"], "names": ["FlowGraphGetVariableBlock", "FlowGraphBlock", "config", "RichTypeAny", "context", "variableNameValue", "serializationObject", "RegisterClass"], "mappings": "gPAOO,MAAMA,UAAkCC,CAAe,CAK1D,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,OAASA,EAEd,KAAK,MAAQ,KAAK,mBAAmB,QAASC,EAAaD,EAAO,YAAY,CACjF,CAID,eAAeE,EAAS,CACpB,MAAMC,EAAoB,KAAK,OAAO,SAClCD,EAAQ,YAAYC,CAAiB,GACrC,KAAK,MAAM,SAASD,EAAQ,YAAYC,CAAiB,EAAGD,CAAO,CAE1E,CAKD,UAAUE,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,OAAO,SAAW,KAAK,OAAO,QACrD,CACD,cAAe,CACX,MAAO,2BACV,CACL,CACAC,EAAc,4BAAmEP,CAAyB", "x_google_ignoreList": [0]}