{"version": 3, "file": "flowGraphSendCustomEventBlock-DfkJ-s1L.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSendCustomEventBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * A block that sends a custom event.\n * To receive this event you need to use the ReceiveCustomEvent block.\n * This block has no output, but does have inputs based on the eventData from the configuration.\n * @see FlowGraphReceiveCustomEventBlock\n */\nexport class FlowGraphSendCustomEventBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        for (const key in this.config.eventData) {\n            this.registerDataInput(key, this.config.eventData[key].type, this.config.eventData[key].value);\n        }\n    }\n    _execute(context) {\n        const eventId = this.config.eventId;\n        // eventData is a map with the key being the data input's name, and value being the data input's value\n        const eventData = {};\n        this.dataInputs.forEach((port) => {\n            eventData[port.name] = port.getValue(context);\n        });\n        context.configuration.coordinator.notifyCustomEvent(eventId, eventData);\n        this.out._activateSignal(context);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */;\n    }\n}\nRegisterClass(\"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */, FlowGraphSendCustomEventBlock);\n//# sourceMappingURL=flowGraphSendCustomEventBlock.js.map"], "names": ["FlowGraphSendCustomEventBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "key", "context", "eventId", "eventData", "port", "RegisterClass"], "mappings": "oOAQO,MAAMA,UAAsCC,CAAqC,CACpF,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,UAAWC,KAAO,KAAK,OAAO,UAC1B,KAAK,kBAAkBA,EAAK,KAAK,OAAO,UAAUA,CAAG,EAAE,KAAM,KAAK,OAAO,UAAUA,CAAG,EAAE,KAAK,CAEpG,CACD,SAASC,EAAS,CACd,MAAMC,EAAU,KAAK,OAAO,QAEtBC,EAAY,CAAA,EAClB,KAAK,WAAW,QAASC,GAAS,CAC9BD,EAAUC,EAAK,IAAI,EAAIA,EAAK,SAASH,CAAO,CACxD,CAAS,EACDA,EAAQ,cAAc,YAAY,kBAAkBC,EAASC,CAAS,EACtE,KAAK,IAAI,gBAAgBF,CAAO,CACnC,CAID,cAAe,CACX,MAAO,kCACV,CACL,CACAI,EAAc,mCAAiFR,CAA6B", "x_google_ignoreList": [0]}