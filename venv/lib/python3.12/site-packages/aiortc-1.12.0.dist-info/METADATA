Metadata-Version: 2.4
Name: aiortc
Version: 1.12.0
Summary: An implementation of WebRTC and ORTC
Author-email: <PERSON> <<EMAIL>>
License-Expression: BSD-3-Clause
Project-URL: homepage, https://github.com/aiortc/aiortc
Project-URL: changelog, https://aiortc.readthedocs.io/en/stable/changelog.html
Project-URL: documentation, https://aiortc.readthedocs.io/
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: aioice<1.0.0,>=0.10.1
Requires-Dist: av<15.0.0,>=14.0.0
Requires-Dist: cffi>=1.0.0
Requires-Dist: cryptography>=44.0.0
Requires-Dist: google-crc32c>=1.1
Requires-Dist: pyee>=13.0.0
Requires-Dist: pylibsrtp>=0.10.0
Requires-Dist: pyopenssl>=25.0.0
Provides-Extra: dev
Requires-Dist: aiohttp>=3.7.0; extra == "dev"
Requires-Dist: coverage[toml]>=7.2.2; extra == "dev"
Requires-Dist: numpy>=1.19.0; extra == "dev"
Dynamic: license-file

.. image:: docs/_static/aiortc.svg
   :width: 120px
   :alt: aiortc

.. image:: https://img.shields.io/pypi/l/aiortc.svg
   :target: https://pypi.python.org/pypi/aiortc
   :alt: License

.. image:: https://img.shields.io/pypi/v/aiortc.svg
   :target: https://pypi.python.org/pypi/aiortc
   :alt: Version

.. image:: https://img.shields.io/pypi/pyversions/aiortc.svg
   :target: https://pypi.python.org/pypi/aiortc
   :alt: Python versions

.. image:: https://github.com/aiortc/aiortc/workflows/tests/badge.svg
   :target: https://github.com/aiortc/aiortc/actions
   :alt: Tests

.. image:: https://img.shields.io/codecov/c/github/aiortc/aiortc.svg
   :target: https://codecov.io/gh/aiortc/aiortc
   :alt: Coverage

.. image:: https://readthedocs.org/projects/aiortc/badge/?version=latest
   :target: https://aiortc.readthedocs.io/
   :alt: Documentation

What is ``aiortc``?
-------------------

``aiortc`` is a library for `Web Real-Time Communication (WebRTC)`_ and
`Object Real-Time Communication (ORTC)`_ in Python. It is built on top of
``asyncio``, Python's standard asynchronous I/O framework.

The API closely follows its Javascript counterpart while using pythonic
constructs:

- promises are replaced by coroutines
- events are emitted using ``pyee.EventEmitter``

To learn more about ``aiortc`` please `read the documentation`_.

.. _Web Real-Time Communication (WebRTC): https://webrtc.org/
.. _Object Real-Time Communication (ORTC): https://ortc.org/
.. _read the documentation: https://aiortc.readthedocs.io/en/latest/

Why should I use ``aiortc``?
----------------------------

The main WebRTC and ORTC implementations are either built into web browsers,
or come in the form of native code. While they are extensively battle tested,
their internals are complex and they do not provide Python bindings.
Furthermore they are tightly coupled to a media stack, making it hard to plug
in audio or video processing algorithms.

In contrast, the ``aiortc`` implementation is fairly simple and readable. As
such it is a good starting point for programmers wishing to understand how
WebRTC works or tinker with its internals. It is also easy to create innovative
products by leveraging the extensive modules available in the Python ecosystem.
For instance you can build a full server handling both signaling and data
channels or apply computer vision algorithms to video frames using OpenCV.

Furthermore, a lot of effort has gone into writing an extensive test suite for
the ``aiortc`` code to ensure best-in-class code quality.

Implementation status
---------------------

``aiortc`` allows you to exchange audio, video and data channels and
interoperability is regularly tested against both Chrome and Firefox. Here are
some of its features:

- SDP generation / parsing
- Interactive Connectivity Establishment, with half-trickle and mDNS support
- DTLS key and certificate generation
- DTLS handshake, encryption / decryption (for SCTP)
- SRTP keying, encryption and decryption for RTP and RTCP
- Pure Python SCTP implementation
- Data Channels
- Sending and receiving audio (Opus / PCMU / PCMA)
- Sending and receiving video (VP8 / H.264)
- Bundling audio / video / data channels
- RTCP reports, including NACK / PLI to recover from packet loss

Installing
----------

The easiest way to install ``aiortc`` is to run:

.. code:: bash

    pip install aiortc

License
-------

``aiortc`` is released under the `BSD license`_.

.. _BSD license: https://aiortc.readthedocs.io/en/latest/license.html
